/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/mana/SofdecSprite.h"


namespace cricocos2d
{
	namespace mana
	{
		namespace
		{
			const float	   quad_initial_size = 128.0f;
#if !CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
			unsigned short quad_indices[]	 = {0, 1, 2, 3, 2, 1};
#endif
		}


        SofdecSprite::SofdecSprite() CRICOCOS2D_NOEXCEPT
			: render_additive_(false)
		{}


        SofdecSprite::~SofdecSprite()
		{
            CC_SAFE_RELEASE_NULL(player_);
        }


        SofdecSprite* SofdecSprite::create()
		{
            SofdecSprite* sprite = new(std::nothrow)SofdecSprite();
			if (sprite == nullptr) {
				return nullptr;
			}
			if (!sprite->init()) {
				delete sprite;
				return nullptr;
			}
			return sprite;
		}


        SofdecSprite* SofdecSprite::createWithPlayer()
		{
			auto player = new SofdecPlayer(SofdecPlayer::getDefaultConfig());
			auto sprite = create();
			sprite->setPlayer(player);
			return sprite;
		}


		bool SofdecSprite::init()
		{
			if (!cocos2d::Node::init()) {
				return false;
			}
			{

				custom_command_.func = [this]
				{
					if (player_ != nullptr) {
#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
						player_->draw(polygon_info_.triangles, transform_, render_additive_);
#else
						cocos2d::TrianglesCommand::Triangles triangles;
						triangles.verts		 = (cocos2d::V3F_C4B_T2F*)&quad_;
						triangles.indices	 = quad_indices;
						triangles.vertCount	 = 4;
						triangles.indexCount = 6;
						player_->draw(triangles, transform_, render_additive_);
#endif
					}
				};
				memset(&quad_, 0, sizeof(quad_));
				setQuadVertices({0.0f, 0.0f, quad_initial_size, quad_initial_size});
				setQuadTexCoords({0.0f, 0.0f, 1.0f, 1.0f});
				updateColor();
			}
			autorelease();
			return true;
		}


        SofdecPlayer* SofdecSprite::getPlayer() const CRICOCOS2D_NOEXCEPT
		{
			return player_;
		}


		void SofdecSprite::setPlayer(SofdecPlayer* player) CRICOCOS2D_NOEXCEPT
		{
			player_ = player;
		}


		void SofdecSprite::setAdditive(bool sw) CRICOCOS2D_NOEXCEPT
		{
			render_additive_ = sw;
		}


		void SofdecSprite::setQuadVertices(const cocos2d::Rect& rect)
		{
			float x1 = rect.origin.x;
			float y1 = rect.origin.y;
			float x2 = rect.origin.x + rect.size.width;
			float y2 = rect.origin.y + rect.size.height;

			quad_.bl.vertices.set(x1, y1, 0.0f);
			quad_.br.vertices.set(x2, y1, 0.0f);
			quad_.tl.vertices.set(x1, y2, 0.0f);
			quad_.tr.vertices.set(x2, y2, 0.0f);

			setContentSize(rect.size);

#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
			polygon_info_.setQuad(&quad_);
#endif
		}


		void SofdecSprite::setQuadTexCoords(const cocos2d::Rect& rect)
		{
			float left	 = rect.origin.x;
			float right	 = rect.origin.x + rect.size.width;
			float top	 = rect.origin.y;
			float bottom = rect.origin.y + rect.size.height;

			quad_.bl.texCoords.u = left;
			quad_.bl.texCoords.v = bottom;
			quad_.br.texCoords.u = right;
			quad_.br.texCoords.v = bottom;
			quad_.tl.texCoords.u = left;
			quad_.tl.texCoords.v = top;
			quad_.tr.texCoords.u = right;
			quad_.tr.texCoords.v = top;

#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
			polygon_info_.setQuad(&quad_);
#endif
		}


#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
		cocos2d::PolygonInfo SofdecSprite::getPolygonInfo() const
		{
			return polygon_info_;
		}


		void SofdecSprite::setPolygonInfo(const cocos2d::PolygonInfo& polygon_info)
		{
			polygon_info_ = polygon_info;
		}


#endif


		void SofdecSprite::draw(cocos2d::Renderer* renderer, const cocos2d::Mat4& transform, uint32_t transformUpdated)
		{
			if (player_ == nullptr) {
				return;
			}

			// Culling check
			bool inside_bounds = renderer->checkVisibility(transform, _contentSize);
			if (!inside_bounds) {
				return;
			}

#if (COCOS2D_VERSION > 0x00030300)
			custom_command_.init(_globalZOrder, transform, transformUpdated);
#else
			custom_command_.init(_globalZOrder);
#endif
			transform_ = transform;
			renderer->addCommand(&custom_command_);
		}


		void SofdecSprite::updateColor()
		{
			cocos2d::Color4B color4(_displayedColor.r, _displayedColor.g, _displayedColor.b, _displayedOpacity);
#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
			for (ssize_t i = 0; i < polygon_info_.triangles.vertCount; i++) {
				polygon_info_.triangles.verts[i].colors = color4;
			}
#else
			quad_.bl.colors = color4;
			quad_.br.colors = color4;
			quad_.tl.colors = color4;
			quad_.tr.colors = color4;
#endif
		}


	}
}
