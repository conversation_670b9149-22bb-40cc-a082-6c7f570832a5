#include "CrashMsgCollector.h"
#include "cocos2d.h"
#include <sstream>

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
#include <jni.h>

extern "C" {

JNIEXPORT jstring JNICALL
Java_org_cocos2dx_lua_CustomCrashHandler_getCollectedMsg(JNIEnv *env, jclass clazz)
{
    return env->NewStringUTF(CrashMsgCollector::getInstance()->toString().c_str());
}

JNIEXPORT jstring JNICALL
Java_com_frontier_sdkbase_SDKManager_getCollectedMsg(JNIEnv *env, jclass clazz)
{
    return env->NewStringUTF(CrashMsgCollector::getInstance()->toString().c_str());
}

JNIEXPORT void JNICALL
Java_com_frontier_sdkbase_SDKManager_testNativeCrash(JNIEnv *env, jclass clazz)
{
    CrashMsgCollector::getInstance()->testCrash();
}

std::string jstring2string(JNIEnv *env, jstring jstr)
{
    const char *cstr = env->GetStringUTFChars(jstr, NULL);
    std::string str = std::string(cstr);
    env->ReleaseStringUTFChars(jstr, cstr);
    return str;
}

JNIEXPORT void JNICALL
Java_com_frontier_sdkbase_SDKManager_collectTest(JNIEnv *env, jclass clazz, jstring key, jstring value)
{
    CrashMsgCollector::getInstance()->addMsg(jstring2string(env, key), jstring2string(env, value));
}

JNIEXPORT void JNICALL
Java_com_frontier_sdkbase_SDKManager_clearCollectedMsg(JNIEnv *env, jclass clazz)
{
    CrashMsgCollector::getInstance()->clear();
}
}

#endif

CrashMsgCollector::CrashMsgCollector()
{
    interface_index = 0;
}

CrashMsgCollector *CrashMsgCollector::getInstance()
{
    if(!ins) {
        ins = new CrashMsgCollector();
    }
    return ins;
}

void CrashMsgCollector::addMsg(const std::string key, const char *value)
{
    addMsg(key, std::string(value));
}

void CrashMsgCollector::addMsg(const std::string key, const std::string value)
{
    std::lock_guard<std::mutex> locking(lock);
    if(key == "interface") {
        std::ostringstream ss;
        ss << "interface" << interface_index;
        ++ interface_index;
        std::string new_key = ss.str();
        msgs[new_key] = value;
    } else {
        msgs[key] = value;
    }
}

void CrashMsgCollector::addMsg(const std::string key, const int value)
{
    std::stringstream ss;
    ss << value;
    std::string value_str;
    ss >> value_str;
    addMsg(key, value_str);
}

void CrashMsgCollector::addMsg(const std::string key, const unsigned int value)
{
    std::stringstream ss;
    ss << value;
    std::string value_str;
    ss >> value_str;
    addMsg(key, value_str);
}

void CrashMsgCollector::addMsg(const std::string key, bool value)
{
    addMsg(key, value ? "true" : "false");
}

void CrashMsgCollector::addMsg(const std::string key, const float value)
{
    std::stringstream ss;
    ss << value;
    std::string value_str;
    ss >> value_str;
    addMsg(key, value_str);
}

void CrashMsgCollector::addMsg(const std::string key, const unsigned char *rawData, const int len)
{
    std::ostringstream ss;
    for(int i = 0; i < len; ++ i) {
        if(i % 16 == 0) {
            ss << "\n";
        }
        ss << std::hex << int(rawData[i]) << " ";
    }
    std::string value;
    value = ss.str();
    addMsg(key, value);
}

void CrashMsgCollector::removeMsg(const std::string key)
{
    std::lock_guard<std::mutex> locking(lock);
    msgs.erase(key);
}

std::string CrashMsgCollector::getInterface0()
{
    std::lock_guard<std::mutex> locking(lock);
    auto iter = msgs.find("interface0");
    if(iter == msgs.end()) {
        return "";
    }
    return msgs["interface0"];
}

void CrashMsgCollector::clear()
{
    std::lock_guard<std::mutex> locking(lock);
    interface_index = 0;
    msgs.clear();
}

std::string CrashMsgCollector::toString()
{
    std::lock_guard<std::mutex> locking(lock);
    std::ostringstream ss;
    for(auto iter = msgs.begin(); iter != msgs.end(); ++ iter) {
        ss << iter->first;
        ss << ":";
        ss << iter->second;
        ss << "\n";
    }
    std::string ret;
    ret = ss.str();

    return ret;
}

void CrashMsgCollector::testCrash()
{
    int *p = 0;
    *p = 9527;
}

CrashMsgCollector *CrashMsgCollector::ins = 0;