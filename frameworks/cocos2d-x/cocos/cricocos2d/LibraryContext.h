/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/**
 * @addtogroup CRICOCOS2D
 * @{
 */

/**
 * @file cricocos2d/LibraryContext.h
 */


/* Prevention of redefinition */
#ifndef CRICOCOS2D_LIBRARYCONTEXT_H
#define CRICOCOS2D_LIBRARYCONTEXT_H


#include "cricocos2d/cricocos2d.h"

#include <memory>


namespace cricocos2d
{
	/**
	 * @headerfile cricocos2d/LibraryContext.h
	 *
	 * @~japanese
	 * @brief CRIWARE の各ライブラリ及び Cricocos2d モジュールの動作に必要な初期化処理、終了処理、及びサスペンド、リジューム処理を行なうクラス
	 *
	 * CRIWARE の各ライブラリ及び Cricocos2d モジュールの動作に必要な初期化処理、終了処理、及びサスペンド、リジューム処理を行なうクラスです。\n
	 * コンストラクタで各ライブラリの初期化処理、デストラクタで終了処理を行ないます。\n
	 * サスペンド、リジューム時に必要な処理( #onApplicationDidEnterBackgroundProc, #onApplicationWillEnterForegroundProc )
	 * はアプリケーション( AppDelegate など )から呼び出す必要があります。
	 *
	 * このクラスのインスタンスを保持して、CRIWARE の各ライブラリの生存期間を制御してください。\n
	 * 以下のように Cocos2d の AppDelegate のデータメンバとして保持することをおすすめします。
	 *
	 * 进行@brief CRIWARE的各库及Cricocos2d模块的动作所需的初始化处理、结束处理、以及挂起、重构处理的类
	*对CRIWARE的各库以及Cricocos2d模块的动作进行必要的初始化处理，结束处理，以及挂起，重构处理的类。\ n
	用构造器进行各库的初始化处理，用析构器进行结束处理。\ n
	*サスペンドリジューム时所需的处理(# o n a p p l i c a t i ondidenterbackgroundproc, # o n a p p l i c a t i onwillenterforegroundproc)
	*需要从应用程序(AppDelegate等)中调用。
	*
	*保留该类实例，以控制CRIWARE各库的生存期。\ n
	*推荐如下作为Cocos2d的AppDelegate的数据成员保持。
	 *
	 * - AppDelegate.h
	 *   @code
	 *   #include <cricocos2d/LibraryContext.h>
	 *
	 *   class AppDelegate : private cocos2d::Application
	 *   {
	 *
	 *   // ...
	 *
	 *   private:
	 *       std::unique_ptr<cricocos2d::LibraryContext> criLibraryContext;
	 *   };
	 *   @endcode
	 *
	 * - AppDelegate.cpp
	 *   @code
	 *   AppDelegate::~AppDelegate()
	 *   {
	 *       // データメンバ criLibraryContext のデストラクタによって CRIWARE の終了処理が行なわれる
	 *   }
	 *
	 *   bool AppDelegate::applicationDidFinishLaunching()
	 *   {
	 *
	 *       // ...
	 *
	 *       cricocos2d::LibraryContext::Config criLibraryConfig;
	 *       // criLibraryConfig に各種パラーメータを設定
	 *
	 *       // ...
	 *
	 *       // cricocos2d::LibraryContext の生成 (CRIWARE の初期化処理)
	 *       criLibraryContext.reset(new cricocos2d::LibraryContext(criLibraryConfig));
	 *
	 *       director->runWithScene(cricocos2d_sample::SelectSample::createScene());
	 *
	 *       return true;
	 *   }
	 *
	 *   void AppDelegate::applicationDidEnterBackground()
	 *   {
	 *       Director::getInstance()->stopAnimation();
	 *
	 *       // CRIWARE の バックグラウンド遷移処理
	 *       criLibraryContext->onApplicationDidEnterBackgroundProc();
	 *   }
	 *
	 *   void AppDelegate::applicationWillEnterForeground()
	 *   {
	 *       Director::getInstance()->startAnimation();
	 *
	 *       // CRIWARE の フォアグラウンド遷移処理
	 *       criLibraryContext->onApplicationWillEnterForegroundProc();
	 *   }
	 *   @endcode
	 */
	class LibraryContext
	{
	public:
		/**
		 * @headerfile cricocos2d/LibraryContext.h
		 *
		 * @~japanese
		 * @brief 各ライブラリの初期化コンフィグ
		 *
		 * LibraryContext のコンストラクタに渡す CRIWARE の各ライブラリの初期化コンフィグ、パラメータ群です。\n
		 * 必要に応じて各データメンバの値を変更して LibraryContext のコンストラクタに渡してください。
		 */
		struct Config
		{
			/**
			 * @~japanese
			 * @brief デフォルトコンストラクタ(@ref Config::Config)で使用される max_movie_streams
			 */
			static const CriUint32 default_max_movie_streams = 16;
			/**
			 * @~japanese
			 * @brief デフォルトコンストラクタ(@ref Config::Config)で使用される max_movie_bitrate
			 */
			static const CriUint32 default_max_movie_bitrate = 2000 * 1000 * default_max_movie_streams;
			/**
			 * @~japanese
			 * @brief デフォルトコンストラクタ(@ref Config::Config)で使用される max_audio_streams
			 */
			static const CriUint32 default_max_audio_streams = 8;
			/**
			 * @~japanese
			 * @brief デフォルトコンストラクタ(@ref Config::Config)で使用される max_audio_bitrate
			 */
			static const CriUint32 default_max_audio_bitrate = 1000 * 1000 * default_max_audio_streams;
			/**
			 * @~japanese
			 * @brief デフォルトコンストラクタ(@ref Config::Config)で使用される max_awb_files
			 */
			static const CriUint32 default_max_awb_files	 = 8;

			/**
			 * @~japanese
			 * @brief 各ライブラリで使用するアロケータ
			 */
			Allocator allocator = getDefaultAllocator();

			/**
			 * @~japanese
			 * @brief エラーコールバック関数
			 */
			CriErrCbFunc error_callback_func = defaultErrorCallback;

			/**
			 * @~japanese
			 * @brief CriMana ライブラリを有効にするか
			 */

			bool enable_mana = true;

			/**
			 * @~japanese
			 * @brief D-BAS 作成コンフィグ
			 */
			CriAtomExDbasConfig dbas;

			/**
			 * @~japanese
			 * @brief CriFs ライブラリの初期化コンフィグ
			 */
			CriFsConfig fs;

#if defined(XPT_TGT_PC)
			/**
			 * @~japanese
			 * @brief CriAtomEx ライブラリの初期化コンフィグ
			 */
			CriAtomExConfig_WASAPI atomex;

			/**
			 * @~japanese
			 * @brief CriMana ライブラリの初期化コンフィグ
			 */
			CriManaLibConfig_PC mana;
#elif defined(XPT_TGT_MACOSX)
			/**
			 * @~japanese
			 * @brief CriAtomEx ライブラリの初期化コンフィグ
			 */
			CriAtomExConfig_MACOSX atomex;

			/**
			 * @~japanese
			 * @brief CriMana ライブラリの初期化コンフィグ
			 */
			CriManaLibConfig mana;
#elif defined(XPT_TGT_IOS)
			/**
			 * @~japanese
			 * @brief CriAtomEx ライブラリの初期化コンフィグ
			 */
			CriAtomExConfig_IOS atomex;

			/**
			 * @~japanese
			 * @brief CriMana ライブラリの初期化コンフィグ
			 */
			CriManaLibConfig mana;
#elif defined(XPT_TGT_ANDROID)
			/**
			 * @~japanese
			 * @brief CriAtomEx ライブラリの初期化コンフィグ
			 */
			CriAtomExConfig_ANDROID atomex;

			/**
			 * @~japanese
			 * @brief CriMana ライブラリの初期化コンフィグ
			 */
			CriManaLibConfig mana;
#endif

			/**
			 * @~japanese
			 * @brief デフォルトコンストラクタ
			 *
			 * 各データメンバにデフォルト値をセットします。
			 */
			Config() CRICOCOS2D_NOEXCEPT;

			/**
			 * @~japanese
			 * @brief コンストラクタ
			 *
			 * 各データメンバにパラメータに応じた適切な値をセットします。
			 *
			 * @param[in]	max_audio_streams	オーディオの最大ストリーム本数  音频的最大流数
			 * @param[in]	max_audio_bitrate	オーディオの最大ビットレート  音频的最大比特率
			 * @param[in]	max_movie_streams	ムービの最大ストリーム本数
			 * @param[in]	max_movie_bitrate	ムービの最大ビットレート
			 * @param[in]	max_awb_files		AWBファイルの最大数  AWB文件的最大数量
			 */
			Config(
				CriUint32 max_audio_streams,
				CriUint32 max_audio_bitrate,
				CriUint32 max_movie_streams,
				CriUint32 max_movie_bitrate,
				CriUint32 max_awb_files
				  ) CRICOCOS2D_NOEXCEPT;
		};

		// 单例实现
		static LibraryContext* getInstance();

		// 结束单例
		static void end();

		LibraryContext(const LibraryContext&)			   = delete;
		LibraryContext& operator = (const LibraryContext&) = delete;

		/**
		 * @~japanese
		 * @brief コンストラクタ
		 *
		 * CRIWARE の各ライブラリの初期化処理を行ないます。
		 *
		 * @param[in]	config	初期化コンフィグ
		 */
		explicit
		LibraryContext(const Config& config);

		/**
		 * @~japanese
		 * @brief デストラクタ
		 *
		 * CRIWARE の各ライブラリの終了処理を行ないます。
		 */
		~LibraryContext();

		/**
		 * @~japanese
		 * @brief CRIWARE のバックグラウンド遷移処理
		 *
		 * CRIWARE のバックグラウンド遷移処理です。
		 * アプリケーションのバックグラウンド遷移時に呼び出す必要があります。\n
		 * AppDelegate::applicationDidEnterBackground などから呼び出してください。
		 */
		void onApplicationDidEnterBackgroundProc();

		/**
		 * @~japanese
		 * @brief CRIWARE のフォアグラウンド遷移処理
		 *
		 * CRIWARE のフォアグラウンド遷移処理です。
		 * アプリケーションのフォアグラウンド遷移時に呼び出す必要があります。\n
		 * AppDelegate::applicationWillEnterForeground などから呼び出してください。
		 */
		void onApplicationWillEnterForegroundProc();

        void criAtomExecuteMain();
	private:
		class Impl;
		std::shared_ptr<Impl> impl_;
	};
}


namespace cricocos2d
{
	inline
	LibraryContext::Config::Config() CRICOCOS2D_NOEXCEPT
		: Config(
			default_max_audio_streams,
			default_max_audio_bitrate,
			default_max_movie_streams,
			default_max_movie_bitrate,
			default_max_awb_files
				)
	{}


	inline
	LibraryContext::Config::Config(
		CriUint32 max_audio_streams,
		CriUint32 max_audio_bitrate,
		CriUint32 max_movie_streams,
		CriUint32 max_movie_bitrate,
		CriUint32 max_awb_files
								  ) CRICOCOS2D_NOEXCEPT
	{
		criFs_SetDefaultConfig(&fs);
		fs.num_loaders = max_audio_streams + max_movie_streams + 4;
		fs.num_binders = max_awb_files + 4;
		fs.max_files   = max_awb_files + max_movie_streams + 4;

		criAtomExDbas_SetDefaultConfig(&dbas);
		dbas.max_mana_streams = max_movie_streams;
		dbas.max_mana_bps	  = max_movie_bitrate;
		dbas.max_streams	  = max_audio_streams + max_movie_streams;
		dbas.max_bps		  = max_audio_bitrate + max_movie_bitrate;

		const CriUint32 mana_max_decoder_handles = max_movie_streams * 2;

#if defined(XPT_TGT_PC)
		criAtomEx_SetDefaultConfig_WASAPI(&atomex);
		criMana_SetDefaultLibConfig_PC(&mana);
		mana.mana.max_decoder_handles = mana_max_decoder_handles;
#elif defined(XPT_TGT_MACOSX)
		criAtomEx_SetDefaultConfig_MACOSX(&atomex);
		criMana_SetDefaultLibConfig(&mana);
		mana.max_decoder_handles = mana_max_decoder_handles;
#elif defined(XPT_TGT_IOS)
		criAtomEx_SetDefaultConfig_IOS(&atomex);
		criMana_SetDefaultLibConfig(&mana);
		mana.max_decoder_handles = mana_max_decoder_handles;
#elif defined(XPT_TGT_ANDROID)
		criAtomEx_SetDefaultConfig_ANDROID(&atomex);
		criMana_SetDefaultLibConfig(&mana);
		mana.max_decoder_handles = mana_max_decoder_handles;
#endif
	}


}


#endif


/**
 * @}
 */
