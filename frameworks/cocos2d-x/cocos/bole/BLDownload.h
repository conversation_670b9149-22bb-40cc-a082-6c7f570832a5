//
//  BLDownload.h
//  cocos2d_libs
//
//  Created by wodes on 14-9-23.
//
//

#ifndef __BLDOWNLOAD_H__
#define __BLDOWNLOAD_H__

#include "../platform/CCPlatformMacros.h"
//#include "cocos2d.h"
#include "../platform/CCStdC.h"
#include "../network/HttpClient.h"



NS_CC_BEGIN

namespace network {
	class CC_DLL BLDownload :public Ref
    {
    public:
        static BLDownload* getInstance();
        BLDownload();
        void download(const std::string& url,const std::string& tag);
        void download(const std::string& url,const std::string& tag, HttpRequest::Type type);
        // 如下获取http的返回码，用于确认下载状态 【sunyungao 25-05-13】
        long getResponseCode() { return _responseCode; }
        std::string getResponseHeader() { return _responseHeader; }
        std::string getResponseContentType() { return _responseContentType; }
        bool isSuccess() { return _isSuccess; }
        void addFilter(std::string filterStr);
        void clearFilter();

    private:
        void init();
        void response(HttpClient *client, HttpResponse *response);
        static BLDownload *instance;
        long _responseCode;
        std::string _responseHeader;
        std::string _responseContentType;
        bool _isSuccess;
        std::vector<std::string> _filterVector;
    };
    
}
NS_CC_END

#endif /* defined(__BLDownload_H__) */