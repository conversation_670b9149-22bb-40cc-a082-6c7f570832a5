//
//  BoleOrder.cpp
//  cocos2d_libs
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/2/27.
//

#include "../SeaCommon.h"
#include "BoleOrder.h"

namespace sea {
BoleOrder::BoleOrder() {
    pRoot = cocos2d::Node::create();
    pRoot->setName("root");
    CC_SAFE_RETAIN(pRoot);
    this->setAnchorPoint(cocos2d::Vec2(0.5, 0.5));
    this->addChild(pRoot);
}

BoleOrder::BoleOrder(const cocos2d::Value& config) {
    cocos2d::ValueMap cfg;

    if (config.getType() == ccValueType::MAP) {
        cfg = config.asValueMap();
    }

    cocos2d::Value initText;
    bool keyFlag = false, textFlag = false, transFlag = false;

    for (const auto& iter : cfg) {
        auto k = iter.first;
        auto v = iter.second;

        if (k == "config") {
            if (v.getType() == ccValueType::MAP) {
                vConfig = v.asValueMap();
            }
        } else if (k == "data") {
            initText = v;
            textFlag = true;
        } else if (k == "text") {
            if (!textFlag) {
                initText = v;
            }

            textFlag = true;
        } else if (k == "keys") {
            setKeys(v);
            keyFlag = true;
        } else if (k == "keys_trans_text") {
            if (!keyFlag) {
                setKeysMap(v);
            }

            transFlag = true;
        } else if (k == "anchor_x") {
            vAnchorX = v;
        } else if (k == "anchor_y") {
            vAnchorY = v;
        } else if (k == "offset") {
            vOffset = v;
        } else if (k == "space") {
            vSpace = v;
        } else if (k == "width") {
            vWidth = v;
        } else if (k == "vertical") {
            if (v.getType() == ccValueType::BOOLEAN) {
                bVertical = v.asBool();
            }
        }
    }

    if (textFlag) {
        setText(initText);
    } else if (transFlag) {
        setText();
    }
}

BoleOrder::~BoleOrder() {
    CC_SAFE_RELEASE_NULL(pRoot);
}

bool BoleOrder::initWithData(const cocos2d::Value& config, const cocos2d::Value& keys, const cocos2d::Value& keys_trans_text, const cocos2d::Value& anchor_x, const cocos2d::Value& anchor_y, const cocos2d::Value& offset, const cocos2d::Value& space, const cocos2d::Value& width, const cocos2d::Value& text, bool vertical) {
    if (config.getType() == ccValueType::MAP) {
        vConfig = config.asValueMap();
    }

    vAnchorX = anchor_x;
    vAnchorY = anchor_y;
    vOffset = offset;
    vSpace = space;
    vWidth = width;
    bVertical = vertical;

    if (!keys_trans_text.isNull()) {
        setKeysMap(keys_trans_text);
        setText();
    } else {
        if (!keys.isNull()) {
            setKeys(keys);
        }

        if (!text.isNull()) {
            setText(text);
        }
    }

    return true;
}

BoleOrder * BoleOrder::create(const cocos2d::Value& config) {
    auto ret = new (std::nothrow) BoleOrder(config);

    if (ret && ret->init()) {
        ret->autorelease();
    } else {
        CC_SAFE_DELETE(ret);
    }

    return ret;
}

bool BoleOrder::isInKeyInConfig(const std::string& key) {
    if (vConfig.find(key) == vConfig.end()) {
        return false;
    }

    return true;
}

void BoleOrder::setUserAnchorX(const cocos2d::Value& x) {
    vAnchorX = x;
}

void BoleOrder::setUserAnchorY(const cocos2d::Value& y) {
    vAnchorY = y;
}

void BoleOrder::setOffset(const cocos2d::Value& offset) {
    vOffset = offset;
}

void BoleOrder::setSpace(const cocos2d::Value& space) {
    vSpace = space;
}

void BoleOrder::setWidth(const cocos2d::Value& width) {
    vWidth = width;
}

void BoleOrder::updateString() {
    if (!vData.isNull()) {
        setText(vData);
    } else if (!vKeysMap.isNull()) {
        setText();
    }
}

void BoleOrder::setKeysMapAndUpdate(const cocos2d::Value& data) {
    setKeysMap(data);
    setText();
}

cocos2d::Node * BoleOrder::getKeyNodeByName(const std::string& key) {
    if (!pRoot) {
        return nullptr;
    }

    return pRoot->getChildByName(key);
}

void BoleOrder::setKeys(const cocos2d::Value& data) {
    vKeys = data;
    vKeysMap = cocos2d::Value::Null;
}

void BoleOrder::setKeysMap(const cocos2d::Value& data) {
    if (data.isNull()) {
        vKeysMap = cocos2d::Value::Null;
        return;
    }

    bVertical = true; //目前必须使用竖版，暂不考虑不使用的情况
    vKeys = cocos2d::Value::Null;

    if (data.isTable()) {
        auto vec = sea::getVectorFromCCValue(data, 2);
    }
}

void BoleOrder::setText(const cocos2d::Value& data) {
    if (!pRoot) {
        return;
    }

    pRoot->removeAllChildren();

    auto funCreate = [](cocos2d::Value luaFun, cocos2d::Value arg0, cocos2d::Value arg1, sea::BoleOrder *orderNode, std::string key) -> cocos2d::Node *{
            if (luaFun.getType() == ccValueType::LUA_FUNCTION) {
                return sea::excuteLuaFunctionToNode(luaFun, 4, [ & ](cocos2d::LuaStack *stack) {
                    sea::pushValueToStack(stack, arg0);
                    sea::pushValueToStack(stack, arg1);
                    sea::pushValueToStack(stack, orderNode, "sea.BoleOrder");
                    stack->pushString(key.c_str());
                });
            }
            else if (luaFun.getType() == ccValueType::C_FUNCTION) {
                auto ret = luaFun.executeCFunction(cocos2d::Value({ arg0, arg1, cocos2d::Value(orderNode, "sea.BoleOrder"), cocos2d::Value(key)}));
                if (ret.getType() == ccValueType::CC_REF) {
                    return ret.asNode();
                }
                return nullptr;
            }
            else {
                return nullptr;
            }

            
        };

    std::vector<std::vector<cocos2d::Node *> > children;

    if (!vKeysMap.isNull() ) {
        if (vKeysMap.getType() != ccValueType::VECTOR ) { //|| vConfig.size() == 0
            return;
        }

        cocos2d::ValueMap kData;

        if (data.getType() == ccValueType::MAP) {
            kData = data.asValueMap();
        } else {
            kData["1"] = data;
        }

        auto tKeysMap = vKeysMap.asValueVector();

        for (int i = 1; i <= tKeysMap.size(); ++i) {
            auto index = i - 1;
            auto k = tKeysMap[index];

            if (k.isTable()) {
                bool isFixKey = false;
                std::string fixKey = "";
                cocos2d::ValueVector vec;
                std::vector<cocos2d::Node *> arr;

                if (k.getType() == ccValueType::MAP) {
                    auto m = k.asValueMap();

                    if (m.find("fix") != m.end()) {
                        auto fix = m.at("fix");

                        if (fix.getType() == ccValueType::STRING) {
                            isFixKey = true;
                            fixKey = fix.asString();
                        }
                    }
                }

                vec = sea::getVectorFromCCValue(k, 0);

                for (int ii = 1; ii <= vec.size(); ++ii) {
                    auto index2 = ii - 1;
                    auto kkArrValue = vec[index2];

                    if (!kkArrValue.isTable()) {
#if COCOS2D_DEBUG >= 1
                        std::stringstream ss;
                        ss << "BoleOrder的 keys_trans_text 数据格式不对。期望是table，但实际上是" << int(kkArrValue.getType());
                        sea::showMessageBox(ss.str());
#endif
                        continue;
                    }

                    auto kkArr = sea::getVectorFromCCValue(kkArrValue, 2);
                    auto kk = sea::getStringFromVector(kkArr, 0);
                    do {
                        auto kkIter = vConfig.find(kk);

                        if (kkIter == vConfig.end()) {
                            if (kk.size() > 0) {
                                kk = kk.at(0);
                                kkIter = vConfig.find(kk);
                            }
                        }

                        if (kkIter != vConfig.end()) {
                            cocos2d::Value& arg0 = kkArr[1];

                            if (arg0.isNull()) {
                                if (kData.find(kk) != kData.end()) {
                                    arg0 = kData.at(kk);
                                }
                            }

                            auto arg1 = cocos2d::Value(cocos2d::ValueVector({ cocos2d::Value(i), cocos2d::Value(ii) }));

                            auto node = funCreate(kkIter->second, arg0, arg1, this, kk);

                            if (node) {
                                if (node->getName().size() == 0) {
                                    node->setName(kk);
                                }

                                pRoot->addChild(node);
                                arr.push_back(node);

                                if (isFixKey && fixKey == kk) {
                                    node->setSeaUserData("user_fix_node_20230628", cocos2d::Value(true));
                                }
                            }
                        }
                    } while (0);
                }

                children.push_back(arr);
            } else {
#if COCOS2D_DEBUG >= 1
                std::stringstream ss;
                ss << "BoleOrder的 keys_trans_text 数据格式不对。期望是table，但实际上是" << int(k.getType());
                sea::showMessageBox(ss.str());
#endif
            }
        }
    } else if (!vKeys.isNull()) {
        cocos2d::Value tKeysValue = vKeys;

        if (tKeysValue.getType() == ccValueType::LUA_FUNCTION) {
            tKeysValue = sea::excuteLuaFunction(tKeysValue);
        }

        if (tKeysValue.isNull() || !tKeysValue.isTable()) { //|| vConfig.size() == 0
            return;
        }

        auto tKeys = sea::getVectorFromCCValue(tKeysValue, 0);

        for (int i = 1; i <= tKeys.size(); ++i) {
            auto index = i - 1;
            auto k = tKeys[index];

            if (k.isTable()) {
                bool isFixKey = false;
                std::string fixKey = "";
                cocos2d::ValueVector vec;
                std::vector<cocos2d::Node *> arr;

                if (k.getType() == ccValueType::MAP) {
                    auto m = k.asValueMap();

                    if (m.find("fix") != m.end()) {
                        auto fix = m.at("fix");

                        if (fix.getType() == ccValueType::STRING) {
                            isFixKey = true;
                            fixKey = fix.asString();
                        }
                    }
                }

                vec = sea::getVectorFromCCValue(k, 0);

                for (int ii = 1; ii <= vec.size(); ++ii) {
                    auto index2 = ii - 1;
                    auto kkValue = vec[index2];

                    if (kkValue.getType() != ccValueType::STRING) {
#if COCOS2D_DEBUG >= 1
                        std::stringstream ss;
                        ss << "BoleOrder的 keys 数据格式不对。期望是string，但实际上是" << int(k.getType());
                        sea::showMessageBox(ss.str());
#endif
                        continue;
                    }

                    auto kk = kkValue.asString();

                    auto kkFunIter = vConfig.find(kk);

                    if (kkFunIter != vConfig.end()) {
                        auto arg1 = cocos2d::Value(cocos2d::ValueVector({ cocos2d::Value(i), cocos2d::Value(ii) }));
                        auto node = funCreate(kkFunIter->second, data, arg1, this, kk);

                        if (node) {
                            if (node->getName().size() == 0) {
                                node->setName(kk);
                            }

                            pRoot->addChild(node);
                            arr.push_back(node);

                            if (isFixKey && fixKey == kk) {
                                node->setSeaUserData("user_fix_node_20230628", cocos2d::Value(true));
                            }
                        }
                    }
                }

                children.push_back(arr);
            } else if (k.getType() == ccValueType::STRING) {
                auto kStr = k.asString();
                auto kkFunIter = vConfig.find(kStr);

                if (kkFunIter != vConfig.end()) {
                    auto node = funCreate(kkFunIter->second, data, cocos2d::Value(i), this, kStr);

                    if (node) {
                        if (node->getName().size() == 0) {
                            node->setName(kStr);
                        }

                        pRoot->addChild(node);
                        children.push_back({ node });
                    }
                }
            }
        }
    }

    float limitTotalWidth = 0.0f;
    std::vector<float> limitChildrenWidth;
    float width = 0.0f, height = 0.0f;

    if (!vWidth.isNull()) {
        if (vWidth.isTable()) {
            auto vec = sea::getVectorFromCCValue(vWidth, 2);

            if (vec[0].isNumber()) {
                limitTotalWidth = vec[0].asFloat();
            }

            if (vec[1].isTable()) {
                auto arr = sea::getVectorFromCCValue(vec[1], 0);

                for (auto& v : arr) {
                    if (v.isNumber()) {
                        limitChildrenWidth.push_back(v.asFloat());
                    }
                }
            }
        } else {
            if (vWidth.isNumber()) {
                limitTotalWidth = vWidth.asFloat();
            }
        }
    }

    if (bVertical) {
        //外层是竖向排列
        //首先竖向排列
        std::vector<cocos2d::Node *> verChildren;
        do {
            for (auto& item : children) {
                cocos2d::Node *choseNode = nullptr;

                if (item.size() == 0) {
                    continue;
                }

                if (item.size() > 1) {
                    float maxHeight = 0.0f;

                    for (auto& node : item) {
                        auto s = node->getContentSize();

                        if (node->getSeaUserData("user_fix_node_20230628")) {
                            choseNode = node;
                            maxHeight = s.height;
                            node->setSeaUserData("origin_pos_25030716", cocos2d::Value(node->getPosition()));
                            break;
                        }

                        if (s.height > maxHeight) {
                            choseNode = node;
                            maxHeight = s.height;
                            node->setSeaUserData("origin_pos_25030716", cocos2d::Value(node->getPosition()));
                        }
                    }
                } else {
                    choseNode = item[0];
                    choseNode->setSeaUserData("origin_pos_25030716", cocos2d::Value(choseNode->getPosition()));
                }

                if (choseNode) {
                    verChildren.push_back(choseNode);
                }
            }

            //间隔值
            auto spaceValue = sea::getTableIndexValue(vSpace, 0, vSpace);
            float space = spaceValue.isTable() ? sea::getFloatFromCCValue(spaceValue, 0, 0.0f) : (spaceValue.isNumber() ? spaceValue.asFloat() : 0);

            //偏移值
            cocos2d::Vec2 offset = sea::getVec2FromCCValue(sea::getTableIndexValue(vOffset, 0, vOffset));

            auto anchorY = vAnchorY.isNumber() ? vAnchorY.asFloat() : 0.5;
            height = sea::arrangeVerticalOnly(pRoot, verChildren, space, offset, anchorY);
        } while (0);

        //然后依次对横向进行排列
        auto childNum = children.size();

        if (verChildren.size() == childNum) {
            for (int i = 0; i < childNum; ++i) {
                auto item = children[i];

                if (item.size() == 0) {
                    continue;
                }

                auto fixNode = verChildren[i];

                if (fixNode) {
                    if (item.size() > 1) {
                        auto offY = fixNode->getPositionY();
                        auto originPos = fixNode->getSeaUserData("origin_pos_25030716");

                        if (originPos) {
                            auto pos = sea::getVec2FromCCValue(*originPos);
                            offY -= pos.y;
                        }

                        for (auto& node : item) {
                            if (node != fixNode) {
                                auto y = node->getPositionY() + offY;
                                node->setPositionY(y);
                            }
                        }

                        auto spaceValue = sea::getTableIndexValue(vSpace, 1);
                        float space = spaceValue.isTable() ? sea::getFloatFromCCValue(spaceValue, i, 0) : (spaceValue.isNumber() ? spaceValue.asFloat() : 0.0f);

                        auto anchorXValue = sea::getTableIndexValue(vAnchorX, 1);
                        float anchor_x = anchorXValue.isTable() ? sea::getFloatFromCCValue(anchorXValue, i, 0) : (anchorXValue.isNumber() ? anchorXValue.asFloat() : 0.5f);

                        auto offsetValue = sea::getTableIndexValue(vOffset, 1, vOffset);
                        cocos2d::Vec2 offset = sea::getVec2FromCCValue(offsetValue, i);

                        float w = sea::arrangeHorizontalNew(pRoot, item, space, anchor_x, offset, limitChildrenWidth.size() > i ? cocos2d::Value(limitChildrenWidth[i]) : cocos2d::Value::Null);

                        if (w > width) {
                            width = w;
                        }
                    } else {
                        auto s = fixNode->getContentSize();

                        if (s.width > width) {
                            width = s.width;
                        }
                    }
                }
            }
        } else {
            sea::showMessageBox("createOrderNodes配置错误");
        }
    } else {
        //外层横向排列
        //首先横向排列
        std::vector<cocos2d::Node *> horChildren;
        do {
            for (auto& item : children) {
                cocos2d::Node *choseNode = nullptr;

                if (item.size() == 0) {
                    continue;
                }

                if (item.size() > 1) {
                    float maxWidth = 0.0f;

                    for (auto& node : item) {
                        auto s = node->getContentSize();

                        if (node->getSeaUserData("user_fix_node_20230628")) {
                            choseNode = node;
                            maxWidth = s.width;
                            node->setSeaUserData("origin_pos_25030716", cocos2d::Value(node->getPosition()));
                            break;
                        }

                        if (s.width > maxWidth) {
                            choseNode = node;
                            maxWidth = s.width;
                            node->setSeaUserData("origin_pos_25030716", cocos2d::Value(node->getPosition()));
                        }
                    }
                } else {
                    choseNode = item[0];

                    if (choseNode) {
                        choseNode->setSeaUserData("origin_pos_25030716", cocos2d::Value(choseNode->getPosition()));
                    }
                }

                if (choseNode) {
                    horChildren.push_back(choseNode);
                }
            }

            //间隔值
            auto spaceValue = sea::getTableIndexValue(vSpace, 0, vSpace);
            float space = spaceValue.isTable() ? sea::getFloatFromCCValue(spaceValue, 0, 0) : (spaceValue.isNumber() ? spaceValue.asFloat() : 0.0f);

            //偏移值
            auto offsetValue = sea::getTableIndexValue(vOffset, 0, vOffset);
            auto offset = sea::getVec2FromCCValue(offsetValue);

            auto anchor_x = sea::getFloatFromCCValue(vAnchorX, 1, 0.5f);
            width = sea::arrangeHorizontalNew(pRoot, horChildren, space, anchor_x, offset, cocos2d::Value::Null);
        } while (0);

        //然后依次对竖向进行排列
        auto childNum = children.size();

        if (childNum == horChildren.size()) {
            for (int i = 0; i < childNum; ++i) {
                auto item = children[i];

                if (item.size() == 0) {
                    continue;
                }

                auto fixNode = horChildren[i];

                if (fixNode) {
                    if (item.size() > 1) {
                        float offX = fixNode->getPositionX();
                        auto originPos = fixNode->getSeaUserData("origin_pos_25030716");

                        if (originPos) {
                            auto pos = sea::getVec2FromCCValue(*originPos);
                            offX -= pos.x;
                        }

                        for (auto& node : item) {
                            if (node != fixNode) {
                                auto x = node->getPositionX() + offX;
                                node->setPositionX(x);
                            }
                        }

                        auto spaceValue = sea::getTableIndexValue(vSpace, 1);
                        float space = spaceValue.isTable() ? sea::getFloatFromCCValue(spaceValue, i, 0) : (spaceValue.isNumber() ? spaceValue.asFloat() : 0.0f);

                        auto anchorYValue = sea::getTableIndexValue(vAnchorY, 1);
                        float anchor_y = anchorYValue.isTable() ? sea::getFloatFromCCValue(anchorYValue, i, 0) : (anchorYValue.isNumber() ? anchorYValue.asFloat() : 0.5f);

                        auto offsetValue = sea::getTableIndexValue(vOffset, 1, vOffset);
                        cocos2d::Vec2 offset = sea::getVec2FromCCValue(offsetValue, i);


                        auto h = sea::arrangeVerticalOnly(pRoot, item, space, offset, anchor_y);

                        if (h > height) {
                            height = h;
                        }
                    } else {
                        auto s = fixNode->getContentSize();

                        if (s.height > height) {
                            height = s.height;
                        }
                    }
                }
            }
        } else {
            sea::showMessageBox("createOrderNodes配置错误");
        }
    }

    //缩放值限宽
    do {
        float scale = 1;

        if (!vWidth.isNull() && width > limitTotalWidth && width > 0) {
            scale = limitTotalWidth / width;
        }

        pRoot->setScale(scale);
        auto newSize = cocos2d::Size(width * scale, height * scale);
        this->setContentSize(newSize);
        pRoot->setPosition(newSize.width * 0.5, newSize.height * 0.5);
    } while (0);

    vData = data;
}
}
