﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="UserMacros">
    <EngineRoot>$(MSBuildThisFileDirectory)..\..\..\</EngineRoot>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(EngineRoot)cocos\editor-support;$(EngineRoot)cocos\platform\winrt;$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\angle\include;$(EngineRoot)external\openssl\include\$(COCOS2D_PLATFORM);$(EngineRoot)external\freetype2\include\$(COCOS2D_PLATFORM);$(EngineRoot)external\freetype2\include\$(COCOS2D_PLATFORM)\freetype2;$(EngineRoot)external\curl\include\$(COCOS2D_PLATFORM);$(EngineRoot)external\websockets\include\$(COCOS2D_PLATFORM);$(EngineRoot)external\chipmunk\include;$(EngineRoot)external\sqlite3\include;$(EngineRoot)cocos;$(EngineRoot)extensions;$(EngineRoot)external;$(EngineRoot)external\edtaa3func;$(EngineRoot)external\unzip;$(EngineRoot)external\tinyxml2;$(EngineRoot);$(EngineRoot)external\ConvertUTF;$(EngineRoot)external\xxhash;$(EngineRoot)external\poly2tri;$(EngineRoot)external\poly2tri\common;$(EngineRoot)external\poly2tri\sweep;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <ClCompile>
      <PreprocessorDefinitions>WINRT;_VARIADIC_MAX=10;NOMINMAX;GL_GLEXT_PROTOTYPES;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;_UNICODE;UNICODE;RAPIDJSON_ENDIAN=RAPIDJSON_LITTLEENDIAN;_USRJSSTATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <CompileAsWinRT>true</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4056;4244;4251;4756;4453;28204;4099;</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <AdditionalDependencies>ogg.lib;vorbis.lib;vorbisfile.lib;libGLESv2.lib;libEGL.lib;ws2_32.lib;websockets.lib;libcurl_imp.lib;chipmunk.lib;zlib.lib;freetype.lib;sqlite3.lib;d2d1.lib;d3d11.lib;dxgi.lib;windowscodecs.lib;dwrite.lib;dxguid.lib;xaudio2.lib;mfcore.lib;mfplat.lib;mfreadwrite.lib;mfuuid.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\OggDecoder\prebuilt\$(Platform);$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\angle\prebuilt\$(Platform);$(EngineRoot)external\curl\prebuilt\$(COCOS2D_PLATFORM)\$(Platform);$(EngineRoot)external\websockets\prebuilt\$(COCOS2D_PLATFORM)\$(Platform);$(EngineRoot)external\websockets\prebuilt\$(COCOS2D_PLATFORM)\$(Platform);$(EngineRoot)external\chipmunk\prebuilt\$(COCOS2D_PLATFORM)\$(Platform);$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\zlib\prebuilt\$(Platform);$(EngineRoot)external\sqlite3\libraries\$(COCOS2D_PLATFORM)\$(Platform);$(EngineRoot)external\freetype2\prebuilt\$(COCOS2D_PLATFORM)\$(Platform);$(EngineRoot)external\poly2tri;$(EngineRoot)external\poly2tri\common;$(EngineRoot)external\poly2tri\sweep;%(AdditionalLibraryDirectories);</AdditionalLibraryDirectories>
      <AdditionalOptions>/IGNORE:4264 %(AdditionalOptions)</AdditionalOptions>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <BuildMacro Include="EngineRoot">
      <Value>$(EngineRoot)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
  </ItemGroup>
</Project>
