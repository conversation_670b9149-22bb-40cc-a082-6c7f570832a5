#include "AppDelegate.h"
#include "scripting/lua-bindings/manual/CCLuaEngine.h"
#include "cocos2d.h"
#include "scripting/lua-bindings/manual/lua_module_register.h"
 #include "lua_sea.h"


#include "../plugin/luabindings/auto/lua_cocos2dx_pluginx_auto.hpp"
#include "../plugin/luabindings/manual/lua_pluginx_manual_callback.h"
#include "../plugin/luabindings/manual/lua_pluginx_manual_protocols.h"

#include "lua_bole_util.h"
#include "bole/ResourcesDecode.h"
#include "bole/BoleSocket.h"
#include "base/CCScriptSupport.h"

#include "cricocos2d/LibraryContext.h" // CRIWARE音效插件

#if defined(__i386__) || defined(_M_IX86)
#define CC_X86 1
#else
#define CC_X86 0
#endif

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID) && CC_X86 == 0
#include "shadowhook/ShadowHookHelper.h"
#endif

#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
#include "lua_bole_util_ios.h"
#endif

#define USE_AUDIO_ENGINE 1
#define USE_SIMPLE_AUDIO_ENGINE 1

// #if USE_AUDIO_ENGINE && USE_SIMPLE_AUDIO_ENGINE
// #error "Don't use AudioEngine and SimpleAudioEngine at the same time. Please just select one in your game!"
// #endif

// #if USE_AUDIO_ENGINE
//#include "audio/include/AudioEngine.h"
//using namespace cocos2d::experimental;
// #elif USE_SIMPLE_AUDIO_ENGINE
//#include "audio/include/SimpleAudioEngine.h"
//using namespace CocosDenshion;
// #endif

USING_NS_CC;
using namespace std;

AppDelegate::AppDelegate()
{

}

AppDelegate::~AppDelegate()
{
    cricocos2d::LibraryContext::end();
//#if USE_AUDIO_ENGINE
//    AudioEngine::end();
//#elif USE_SIMPLE_AUDIO_ENGINE
//    SimpleAudioEngine::end();
//#endif

#if (COCOS2D_DEBUG > 0) && (CC_CODE_IDE_DEBUG_SUPPORT > 0)
    // NOTE:Please don't remove this call if you want to debug with Cocos Code IDE
    RuntimeEngine::getInstance()->end();
#endif

}

// if you want a different context, modify the value of glContextAttrs
// it will affect all platforms
void AppDelegate::initGLContextAttrs()
{
    // set OpenGL context attributes: red,green,blue,alpha,depth,stencil
    GLContextAttrs glContextAttrs = {8, 8, 8, 8, 24, 8};

    GLView::setGLContextAttrs(glContextAttrs);
}

// if you want to use the package manager to install more packages, 
// don't modify or remove this function
static int register_all_packages()
{
     auto engine = LuaEngine::getInstance();
     lua_State* L = engine->getLuaStack()->getLuaState();
     lua_register_all_sea(L);
    return 0; //flag for packages manager
}

bool AppDelegate::applicationDidFinishLaunching()
{
    // set default FPS
    Director::getInstance()->setAnimationInterval(1.0 / 60.0f);
    
#if COCOS2D_DEBUG >= 1
    Director::getInstance()->setDisplayStats(true);
#endif

    // register lua module
    auto engine = LuaEngine::getInstance();
    ScriptEngineManager::getInstance()->setScriptEngine(engine);
    LuaStack* stack = engine->getLuaStack();
    stack->setXXTEAKeyAndSign("BLGAMES", strlen("BLGAMES"), "T^pt%~CwG", strlen("T^pt%~CwG"));
    
    lua_State* L = engine->getLuaStack()->getLuaState();
    lua_module_register(L);

    register_all_packages();    

    ResourcesDecode::sharedDecode()->setXXTeaKey("BLGAMES", strlen("BLGAMES"), "T^pt%~CwG", strlen("T^pt%~CwG"));
    
    //    FileUtils::getInstance()->addSearchPath();
    
#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
    auto DeviceInfo = std::string(iOSUtil::getInstance()->getDeviceInfo(1));
    printf("DeviceInfo :%s\n",DeviceInfo.c_str());
    FilesExtUtiles::setMd5Random(DeviceInfo);
//    auto test = FilesExtUtiles::getMD5("login_bg");
//    printf("dafucasino test getMD5: %s\n",test.c_str());
//    auto name = FilesExtUtiles::newFilePath("login/login_bg.png");
//    printf("dafucasino test newFilePath: %s\n",name.c_str());
#endif

    //register custom function
    //LuaStack* stack = engine->getLuaStack();
    //register_custom_function(stack->getLuaState());
    
    //register custom function
    lua_getglobal(L, "_G");
    register_all_pluginx_protocols(L);
    register_all_pluginx_manual_callback(L);
    register_all_pluginx_manual_protocols(L);
    register_bole_util(L);
    
    cricocos2d::LibraryContext::getInstance();
    
    int arch = get_device_arch();
    CCLOG("arch  = %d\n", arch);

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID) && CC_X86 == 0
    ShadowHookHelper::getInstance();
#endif

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
    std::function<void(void*)> cocosThread = [=](void*)
    {
        engine->executeScriptFile("src/main.lua");
    };
    AsyncTaskPool::getInstance()->enqueue(AsyncTaskPool::TaskType::TASK_IO, std::move(cocosThread), nullptr, [=]()
    {
        UserDefault::getInstance()->getIntegerForKey("xx");
    });
    return false;
#else
    if (engine->executeScriptFile("src/main.lua"))
    {
        return false;
    }
#endif
    // cocos2d::LuaEngine::getInstance()->executeGlobalFunction("G_appLaunchingiOS");
    return true;
}

// This function will be called when the app is inactive. Note, when receiving a phone call it is invoked.
void AppDelegate::applicationDidEnterBackground()
{
    Director::getInstance()->stopAnimation();

//#if USE_AUDIO_ENGINE
//    AudioEngine::pauseAll();
//#elif USE_SIMPLE_AUDIO_ENGINE
//    SimpleAudioEngine::getInstance()->pauseBackgroundMusic();
//    SimpleAudioEngine::getInstance()->pauseAllEffects();
//#endif
    cricocos2d::LibraryContext::getInstance()->onApplicationDidEnterBackgroundProc();
    
    if(ScriptEngineManager::getInstance()->getScriptEngine() != nullptr){
        const char* name = "lifeng";
        Value vv(name);
        
        BLMessage* msg = new BLMessage(BL_WS_MSG_TO_UITHREAD_ENTER_BACKGROUND,ScriptEngineManager::getInstance()->getScriptEngine()->convertCppValueToLuaValue(&vv));
        BasicScriptData data(cocos2d::network::BLSocket::getInstance(), msg);
        ScriptEvent scriptEvent(kBLSocketEvent, &data);
        ScriptEngineManager::getInstance()->getScriptEngine()->sendEvent(&scriptEvent);
//        msg->release();
    }
    
}

// this function will be called when the app is active again
void AppDelegate::applicationWillEnterForeground()
{
    Director::getInstance()->startAnimation();

//#if USE_AUDIO_ENGINE
//    AudioEngine::resumeAll();
//#elif USE_SIMPLE_AUDIO_ENGINE
//    SimpleAudioEngine::getInstance()->resumeBackgroundMusic();
//    SimpleAudioEngine::getInstance()->resumeAllEffects();
//#endif
    cricocos2d::LibraryContext::getInstance()->onApplicationWillEnterForegroundProc();
    
    const char* name = "kunwu";
    Value vv(name);
    BLMessage* msg = new BLMessage(BL_WS_MSG_TO_UITHREAD_ENTER_FOREGROUND,ScriptEngineManager::getInstance()->getScriptEngine()->convertCppValueToLuaValue(&vv));
    BasicScriptData data(cocos2d::network::BLSocket::getInstance(), msg);
    ScriptEvent scriptEvent(kBLSocketEvent, &data);
    ScriptEngineManager::getInstance()->getScriptEngine()->sendEvent(&scriptEvent);
//    msg->release();
    
}
