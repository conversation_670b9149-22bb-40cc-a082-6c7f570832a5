
set(COCOS_SPINE_SRC
  editor-support/spine/Animation.c
  editor-support/spine/AnimationState.c
  editor-support/spine/AnimationStateData.c
  editor-support/spine/Atlas.c
  editor-support/spine/AtlasAttachmentLoader.c
  editor-support/spine/Attachment.c
  editor-support/spine/AttachmentLoader.c
  editor-support/spine/AttachmentVertices.cpp
  editor-support/spine/Bone.c
  editor-support/spine/BoneData.c
  editor-support/spine/BoundingBoxAttachment.c
  editor-support/spine/Cocos2dAttachmentLoader.cpp
  editor-support/spine/Event.c
  editor-support/spine/EventData.c
  editor-support/spine/IkConstraint.c
  editor-support/spine/IkConstraintData.c
  editor-support/spine/Json.c
  editor-support/spine/MeshAttachment.c
  editor-support/spine/PathAttachment.c
  editor-support/spine/PathConstraint.c
  editor-support/spine/PathConstraintData.c
  editor-support/spine/RegionAttachment.c
  editor-support/spine/Skeleton.c
  editor-support/spine/SkeletonAnimation.cpp
  editor-support/spine/SkeletonDataManager.cpp
  editor-support/spine/SkeletonBatch.cpp
  editor-support/spine/SkeletonBinary.c
  editor-support/spine/SkeletonBounds.c
  editor-support/spine/SkeletonData.c
  editor-support/spine/SkeletonJson.c
  editor-support/spine/SkeletonRenderer.cpp
  editor-support/spine/Skin.c
  editor-support/spine/Slot.c
  editor-support/spine/SlotData.c
  editor-support/spine/TransformConstraint.c
  editor-support/spine/TransformConstraintData.c
  editor-support/spine/VertexAttachment.c
  editor-support/spine/extension.c
  editor-support/spine/spine-cocos2dx.cpp
)

include_directories( editor-support )
