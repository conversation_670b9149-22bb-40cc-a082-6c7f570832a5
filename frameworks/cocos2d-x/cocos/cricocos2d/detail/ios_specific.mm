/****************************************************************************
 *
 * Copyright (c) CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#import "cricocos2d/LibraryContext.h"
#import "cricocos2d/detail/log.h"

#import <AVFoundation/AVAudioSession.h>


@interface Cricocos2dIosSpecificContext : NSObject
@end

@implementation Cricocos2dIosSpecificContext
{
	bool		enable_mana_;
	CriUint32	atom_buffering_time_;
	CriSint32	atom_output_sampling_rate_;
}

- (id)init:(const cricocos2d::LibraryContext::Config&)config
{
	CRICOCOS2D_DEBUG_LOG_PLACE;
	if (self = [super init]) {
		enable_mana_						= config.enable_mana;
		atom_buffering_time_				= config.atomex.buffering_time;
		atom_output_sampling_rate_			= config.atomex.output_sampling_rate;

		NSNotificationCenter* notificationCenter = [NSNotificationCenter defaultCenter];

		/* suspend 時の処理を登録 */
		[notificationCenter addObserver:self
							   selector:@selector(onApplicationWillResignActive:)
								   name:UIApplicationWillResignActiveNotification
								 object:nil
		];

		/* resume 時の処理を登録 */
		[notificationCenter addObserver:self
							   selector:@selector(onApplicationDidBecomeActive:)
								   name:UIApplicationDidBecomeActiveNotification
								 object:nil
		];

        [self setupAudioSession];
		[self setActiveAudioSession:YES];
	}
	return self;
}

- (void)dealloc
{
	CRICOCOS2D_DEBUG_LOG_PLACE;
//	[self setActiveAudioSession:NO]; // 尝试解决ios声音概率不播放（wrapper没考虑和其他音频系统一起用，cri关闭的时候销毁audioSession可能有问题）

	/* 全ての Observer 登録を解除 */
	[[NSNotificationCenter defaultCenter] removeObserver:self];

	[super dealloc];
}


- (void)onApplicationWillResignActive:(NSNotification*)notification
{
	CRICOCOS2D_DEBUG_LOG_PLACE;

	if (enable_mana_) {
		criMana_SetAllPauseFlag(CRI_TRUE);
	}
}

- (void)onApplicationDidBecomeActive:(NSNotification*)notification
{
	CRICOCOS2D_DEBUG_LOG_PLACE;

	if (enable_mana_) {
		criMana_SetAllPauseFlag(CRI_FALSE);
	}
}

- (void)setupAudioSession
{
	CRICOCOS2D_DEBUG_LOG_PLACE;
	NSError*		ns_error	  = nil;
	AVAudioSession* audio_session = [AVAudioSession sharedInstance];

	/* オーディオコールバック間隔を固定 */
	{
		const NSTimeInterval duration = 1E-3 * 0.5 * atom_buffering_time_;
		CRICOCOS2D_DEBUG_LOG("before active: audio_session.preferredIOBufferDuration = %f", audio_session.preferredIOBufferDuration);
		CRICOCOS2D_DEBUG_LOG("before active: audio_session.IOBufferDuration          = %f", audio_session.IOBufferDuration);
		CRICOCOS2D_DEBUG_LOG("config       : 1E-3 * 0.5 * atom_buffering_time_       = %f", duration);
		[audio_session setPreferredIOBufferDuration:duration error:&ns_error];
		if (ns_error) {
			cricocos2d::detail::printLog(cricocos2d::detail::LogSeverity::Error, "failed [AVAudioSession setPreferredIOBufferDuration]");
			NSLog(@"Error %ld, %@", (long)ns_error.code, ns_error.localizedDescription);
		}
	}

	/* 出力サンプリングレートを設定 */
	{
		const double sample_rate = atom_output_sampling_rate_;
		CRICOCOS2D_DEBUG_LOG("before active: audio_session.preferredSampleRate = %f", audio_session.preferredSampleRate);
		CRICOCOS2D_DEBUG_LOG("before active: audio_session.sampleRate          = %f", audio_session.sampleRate);
		CRICOCOS2D_DEBUG_LOG("config       : atom_output_sampling_rate_        = %lu", atom_output_sampling_rate_);
		[audio_session setPreferredSampleRate:sample_rate error:&ns_error];
		if (ns_error) {
			cricocos2d::detail::printLog(cricocos2d::detail::LogSeverity::Error, "failed [AVAudioSession setPreferredSampleRate]");
			NSLog(@"Error %ld, %@", (long)ns_error.code, ns_error.localizedDescription);
		}
	}
}


- (void)setActiveAudioSession:(BOOL)active
{
	CRICOCOS2D_DEBUG_LOG_PLACE;
	CRICOCOS2D_DEBUG_LOG("setActiveAudioSession: %d", active);
	NSError*		ns_error	  = nil;
	AVAudioSession* audio_session = [AVAudioSession sharedInstance];
	for (int retry_count = 0; retry_count < 20; ++retry_count) {
		[audio_session setActive:active error:&ns_error];
		if (!ns_error) {
			if (active) {
				CRICOCOS2D_DEBUG_LOG("after active:  audio_session.preferredIOBufferDuration = %f", audio_session.preferredIOBufferDuration);
				CRICOCOS2D_DEBUG_LOG("after active:  audio_session.IOBufferDuration          = %f", audio_session.IOBufferDuration);
				CRICOCOS2D_DEBUG_LOG("after active:  audio_session.preferredSampleRate = %f", audio_session.preferredSampleRate);
				CRICOCOS2D_DEBUG_LOG("after active:  audio_session.sampleRate          = %f", audio_session.sampleRate);
			}
			return;
		}
		cricocos2d::detail::printLog(cricocos2d::detail::LogSeverity::Error, "failed [AVAudioSession setActive]");
		NSLog(@"Error %ld, %@", (long)ns_error.code, ns_error.localizedDescription);
		usleep(1000);
	}
}
@end


namespace cricocos2d
{
	namespace detail
	{
		std::shared_ptr<void> createPlatformSpecificResource(const cricocos2d::LibraryContext::Config& config)
		{
			return {
					   [[Cricocos2dIosSpecificContext alloc] init:config],
					   [](void* p) { [static_cast<Cricocos2dIosSpecificContext*>(p)release]; }
			};
		}
	}
}
