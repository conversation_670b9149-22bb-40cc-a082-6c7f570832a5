//
//  Common.cpp
//  Slots
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/12/17.
//

#include "scripting/lua-bindings/manual/CCLuaEngine.h"
#include "scripting/lua-bindings/manual/LuaBasicConversions.h"
#include "sea/dialog/Dialog.h"
#include "SeaCommon.h"
#include "UI/BoleButton.h"
#include "UI/BoleLabel.h"
#include "TranslateTextManager.h"
#include "PurchaseManager.h"
#include <iomanip>

namespace sea {
//typedef cocos2d::Value::Type ccValueType;
std::unordered_map<std::string, std::string> Global::sDefaultLangMap;
std::unordered_map<std::string, std::string> Global::sFontTypeMap;
std::string Global::sLocalLang = "";
std::unordered_map<int, std::unordered_map<std::string, std::string> > Global::sFontToPath;
std::unordered_map<std::string, bool> Global::sFontPathExist;
std::unordered_map<std::string, int> Global::sFontMap;
std::unordered_map<std::string, std::pair<std::string, bool> > Global::sFontDefaultPath;
int Global::sFunSetCountdownLabel = 0;
int Global::sFunIsInTheme = 0;
int Global::sFunGetScreenRatio = 0;
int Global::sFunIsInLobby = 0;
int Global::sFunPlayBtnClickSound = 0;
int Global::sFunIsGtm = 0;
IntBool Global::iIsGtm = IntBool::None;
int Global::sIntTouchDistance = 0;
std::unordered_map<std::string, int> Global::sBindObjectLuaFun;
std::string Global::sTextureSuffix = ".png";
std::unordered_map<std::string, int> Global::sFontButtonIndex;
std::unordered_map<std::string, cocos2d::Value> Global::sFontButtonType;
std::unordered_map<std::string, std::string> Global::sMapResCommonPath;
int Global::sFunEventCenterRegister = 0;
int Global::sFunEventCenterRemove = 0;
int Global::sFunRedEventControlRegister = 0;
int Global::sFunRedEventControlRemove = 0;
int Global::sFunCanUseBucks = 0;
bool Global::sBoolLogAble = true;

/**--------------------Global-----------------------**/
int Global::getVersion() {
    return 32;
};

std::string Global::translateImage(const std::string& key, const std::string& themeId /*=""*/, const sea::IntBool& isActivity /*=sea::IntBool::None*/) {
    auto getPath = [ = ](const std::string& lang) {
            if (isActivity == sea::IntBool::True) {
                return "inner_download/multi_language/lang_"  + lang + "/" + key + "_" + lang + ".png";
            } else {
                if (themeId.empty()) {
                    return "lang_" + lang + "/" + key + "_" + lang + ".png";
                } else {
                    return "theme_resource/theme" + themeId + "/lang_" + lang + "/" + key + "_" + lang + ".png";
                }
            }
        };

    std::string lang = sLocalLang;
    std::string path = getPath(lang);

    while (!sea::isFileExist(path)) {
        if (lang == "EN") {
            return "";
        }

        try {
            lang = sDefaultLangMap.at(lang);
        } catch (const std::out_of_range& e) {
            return "";
        }
        path = getPath(lang);
    }
    return path;
}

cocos2d::ValueMap Global::translateImagePlus(const std::string& key, const std::string& themeId /*=""*/, const sea::IntBool& isActivity /*=sea::IntBool::None*/) {
    auto getPath = [ = ](const std::string& lang) {
            if (isActivity == sea::IntBool::True) {
                return "inner_download/multi_language/lang_"  + lang + "/" + key + "_" + lang + ".png";
            } else {
                if (themeId.empty()) {
                    return "lang_" + lang + "/" + key + "_" + lang + ".png";
                } else {
                    return "theme_resource/theme" + themeId + "/lang_" + lang + "/" + key + "_" + lang + ".png";
                }
            }
        };

    std::string lang = sLocalLang;
    std::string path = getPath(lang);

    while (!sea::isFileExist(path)) {
        if (lang == "EN") {
            return { { "path", cocos2d::Value("") }, { "lang", cocos2d::Value(lang) } };
        }

        try {
            lang = sDefaultLangMap.at(lang);
        } catch (const std::out_of_range& e) {
            return { { "path", cocos2d::Value("") }, { "lang", cocos2d::Value(lang) } };
        }
        path = getPath(lang);
    }
    return { { "path", cocos2d::Value(path) }, { "lang", cocos2d::Value(lang) } };
}

std::string Global::translateSpriteFrame(const std::string& key) {
    auto getPath = [ = ](const std::string& lang) {
            return key + "_" + lang + ".png";
        };

    std::string lang = sLocalLang;
    std::string path = getPath(lang);

    while (!cocos2d::SpriteFrameCache::getInstance()->getSpriteFrameByName(path)) {
        if (lang == "EN") {
            return "";
        }

        try {
            lang = sDefaultLangMap.at(lang);
        } catch (const std::out_of_range& e) {
            return "";
        }
        path = getPath(lang);
    }
    return path;
}

std::vector<std::string> Global::getAnimationPath(const std::string& path) {
    cocos2d::ValueMap params;
    return getAnimationPath(path, params);
}
std::vector<std::string> Global::getAnimationPath(const std::string& path, cocos2d::ValueMap& params) {
    bool multi = false;
    bool binary = true;
    sea::IntBool activity = sea::IntBool::None;
    std::string themeId = "";

    if (params.find("binary") != params.end()) {
        binary = params["binary"].asBool();
    }

    if (params.find("multi_act") != params.end()) {
        multi = true;
        activity = sea::IntBool::True;
    } else if (params.find("multi") != params.end()) {
        auto multiVal = params["multi"];

        if (multiVal.getType() == ccValueType::MAP) {
            multi = true;
            auto mVal = multiVal.asValueMap();

            if (mVal.find("id") != mVal.end()) {
                themeId = mVal["id"].asInt();
            }

            if (mVal.find("activity") != mVal.end()) {
                activity = static_cast<sea::IntBool>(mVal["activity"].asBool() ? 1 : 0);
            }
        } else if (multiVal.getType() == ccValueType::BOOLEAN) {
            multi = params["multi"].asBool();
        } else {
            multi = true;
        }
    }

    if (multi) {
        return sea::Global::translateSkeleton(path, themeId, activity);
    } else {
        if (binary) {
            return { path + ".skel", path + ".atlas" };
        }

        return { path + ".json", path + ".atlas" };
    }
}

std::vector<std::string> Global::translateSkeleton(const std::string& key, const std::string& themeId /*=""*/, const sea::IntBool& isActivity /*=sea::IntBool::None*/, bool binary /* = true*/) {
    std::vector<std::string> ret;
    std::string ext1 = binary ? ".skel" : ".json";
    std::string ext2 = ".atlas";
    auto getPath = [ = ](const std::string& lang) {
            if (themeId.empty()) {
                if (isActivity == sea::IntBool::True) {
                    std::string s = "inner_download/multi_language/lang_" + lang + "/" + key + "_" + lang;
                    return std::make_tuple(s + ext1, s + ext2);
                } else {
                    std::string s = "lang_" + lang + "/" + key + "_" + lang;
                    return std::make_tuple(s + ext1, s + ext2);
                }
            } else {
                std::string s = "theme_resource/theme" + themeId + "/animation/lang_" + lang + "/" + key + "_" + lang;
                return std::make_tuple(s + ext1, s + ext2);
            }
        };

    std::string lang = sLocalLang;
    std::string p1, p2;

    std::tie(p1, p2) = getPath(lang);

    while (!(sea::isFileExist(p1) && sea::isFileExist(p2))) {
        if (lang == "EN") {
            return ret;
        }

        try {
            lang = sDefaultLangMap.at(lang);
        } catch (const std::out_of_range& e) {
            return ret;
        }
        std::tie(p1, p2) = getPath(lang);
    }
    return { p1, p2, lang };
}

void Global::registerLuaFunctionVersion(const std::string& name, int handler, int version) {
    auto iter = sea::sLuaFunctionVersionKey.find(name);

    if (iter != sea::sLuaFunctionVersionKey.end()) {
        auto cData = iter->second;

        if (version <= cData.first) {
            return;
        }
    }

    if (name.size() > 7 and name.substr(0, 7) == "Dialog.") { //Dialog类的
        auto typeStr = name.substr(7);
        int type = 0;
        try {
            type = std::stoi(typeStr);
        } catch (std::invalid_argument& e) {
            std::cout << "Invalid argument: " << typeStr << " cannot be converted to int" << std::endl;
#if COCOS2D_DEBUG >= 1
            std::ostringstream oss;
            oss << "调用registerLuaFunctionVersion含义非法字符" << name;
            sea::showMessageBox(oss.str());
#endif
            return;
        } catch (std::out_of_range& e) {
            std::cout << "Out of range: " << typeStr << " is too large for int" << std::endl;
#if COCOS2D_DEBUG >= 1
            std::ostringstream oss;
            oss << "调用registerLuaFunctionVersion含义非法字符" << name;
            sea::showMessageBox(oss.str());
#endif
            return;
        }
        Dialog::registerLuaFunctionVersion(type, handler);
    }
}

void Global::registerBindObjectLuaFun(const std::string& funName, int hander) {
    sBindObjectLuaFun[funName] = hander;
}

const std::string Global::getSysFont() {
    auto platform = cocos2d::Application::getInstance()->getTargetPlatform();

    switch (platform) {
        case cocos2d::Application::Platform::OS_IPHONE:
        case cocos2d::Application::Platform::OS_IPAD:
            return "Avenir-Black";

            break;

        case cocos2d::Application::Platform::OS_ANDROID:
            return "Droid Serif";

            break;

        default:
            return "Arial";

            break;
    }
}

std::unordered_map<std::string, std::string> Global::getFontPathConfig(const cocos2d::Value& value) {
    std::unordered_map<std::string, std::string> ret;

    if (value.isNumber()) {
        auto type = value.asInt();
        auto pathConfigIter = sFontToPath.find(type); //{EN = "path", CN = "path"}

        if (pathConfigIter != sFontToPath.end()) {
            for (const auto& iter : pathConfigIter->second) {
                auto path = iter.second; //"path"
                auto lang = iter.first; //"EN"

                if (sFontPathExist.find(path) != sFontPathExist.end()) {
                    ret[lang] = path;
                } else {
                    if (sea::isFileExist(path)) {
                        sFontPathExist[path] = true;
                        ret[lang] = path;
                    } else {
                        sea::showMessageBox("ERROR Global::getFontPathConfig-1");
                    }
                }
            }
        }
    } else if (value.getType() == ccValueType::MAP) {
        for (const auto& iter : value.asValueMap()) {
            auto lang = iter.first; //"EN"
            auto fontTypeValue = iter.second; // FONT.AKK (0)

            if (fontTypeValue.isNumber()) {
                auto fontType = fontTypeValue.asInt(); //FONT.AKK (0)
                auto fontIter = sFontToPath.find(fontType); //{EN = "path", CN = "path"}

                if (fontIter != sFontToPath.end()) {
                    auto langMap = fontIter->second; //{EN = "path", CN = "path"}
                    auto langIter = langMap.find(lang); // EN = "path"

                    if (langIter != langMap.end()) {
                        auto path = langIter->second; // "path"

                        if (sFontPathExist.find(path) != sFontPathExist.end()) {
                            ret[lang] = path;
                        } else {
                            if (sea::isFileExist(path)) {
                                sFontPathExist[path] = true;
                                ret[lang] = path;
                            } else {
                                sea::showMessageBox("ERROR Global::getFontPathConfig-2");
                            }
                        }
                    }
                }
            } else {
                sea::showMessageBox("ERROR Global::getFontPathConfig-3");
            }
        }
    } else {
        sea::showMessageBox("ERROR Global::getFontPathConfig-4");
    }

    return ret;
}

bool Global::checkFontValueByName(const int& value, const std::string& fontName) {
    auto iter = sFontMap.find(fontName);

    if (iter != sFontMap.end() && iter->second == value) {
        return true;
    }

    return false;
}

void Global::setConfig(const cocos2d::ValueMap& config) {
    for (const auto iter : config) {
        auto key = iter.first;
        auto value = iter.second;

        if (key == "font_path") {
            if (value.getType() == ccValueType::MAP) {
                sFontToPath.clear();

                for (const auto& iterType : value.asValueMap()) {
                    int fntType = std::stoi(iterType.first);
                    auto cfg = iterType.second;

                    if (cfg.getType() == ccValueType::MAP) {
                        std::unordered_map<std::string, std::string> langMap;

                        for (const auto& iterLang : cfg.asValueMap()) {
                            langMap[iterLang.first] = iterLang.second.asString();
                        }

                        sFontToPath[fntType] = langMap;
                    }
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-font_path");
            }
        } else if (key == "font") {
            if (value.getType() == ccValueType::MAP) {
                sFontMap.clear();

                for (const auto& iterType : value.asValueMap()) {
                    auto k = iterType.first;
                    auto val = iterType.second;

                    if (val.isNumber()) {
                        sFontMap[k] = val.asInt();
                    } else {
                        sea::showMessageBox("ERROR Global::setConfig-font-1");
                    }
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-font-2");
            }
        } else if (key == "fun_translate_text") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sea::TranslateTextManager::getInstance()->setLuaFun(value.asInt());
            }
        } else if (key == "font_default_path") {
            if (value.getType() == ccValueType::MAP) {
                sFontDefaultPath.clear();

                for (const auto& iterType : value.asValueMap()) {
                    auto _key = iterType.first;
                    auto _val = iterType.second;

                    if (_val.getType() == ccValueType::STRING) {
                        sFontDefaultPath[_key] = { _val.asString(), false };
                    } else {
                        sea::showMessageBox("ERROR Global::setConfig-font_default_path-1");
                    }
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-font_default_path-2");
            }
        } else if (key == "lang_default") {
            sDefaultLangMap.clear();

            if (value.getType() == ccValueType::MAP) {
                for (const auto& pair : value.asValueMap()) {
                    sDefaultLangMap[pair.first] = pair.second.asString();
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-lang_default");
            }
        } else if (key == "font_type") {
            sFontTypeMap.clear();

            if (value.getType() == ccValueType::MAP) {
                for (const auto& pair : value.asValueMap()) {
                    sFontTypeMap[pair.first] = pair.second.asString();
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-font_type");
            }
        } else if (key == "res_common_path") {
            if (value.getType() == ccValueType::MAP) {
                for (const auto& iterType : value.asValueMap()) {
                    auto k = iterType.first;
                    auto val = iterType.second;
                    sMapResCommonPath[k] = val.asString();
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-res_common_path");
            }
        } else if (key == "event_center") {
            if (value.getType() == ccValueType::VECTOR) {
                auto vec = value.asValueVector();

                if (vec.size() >= 2) {
                    auto reg = vec[0];

                    if (reg.getType() == ccValueType::LUA_FUNCTION) {
                        sFunEventCenterRegister = reg.asLuaFunction().hander;
                    }

                    auto rem = vec[1];

                    if (rem.getType() == ccValueType::LUA_FUNCTION) {
                        sFunEventCenterRemove = rem.asLuaFunction().hander;
                    }
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-event_center");
            }
        } else if (key == "red_event_control") {
            if (value.getType() == ccValueType::VECTOR) {
                auto vec = value.asValueVector();

                if (vec.size() >= 2) {
                    auto reg = vec[0];

                    if (reg.getType() == ccValueType::LUA_FUNCTION) {
                        sFunRedEventControlRegister = reg.asLuaFunction().hander;
                    }

                    auto rem = vec[1];

                    if (rem.getType() == ccValueType::LUA_FUNCTION) {
                        sFunRedEventControlRemove = rem.asLuaFunction().hander;
                    }
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-event_center");
            }
        } else if (key == "local_lang") {
            if (value.getType() == ccValueType::STRING) {
                sLocalLang = value.asString();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-local_lang");
            }
        } else if (key == "fun_set_countdown_label") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunSetCountdownLabel = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_set_countdown_label");
            }
        } else if (key == "fun_is_in_theme") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunIsInTheme = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_is_in_theme");
            }
        } else if (key == "fun_get_screen_ratio") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunGetScreenRatio = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_get_screen_ratio");
            }
        } else if (key == "fun_is_in_lobby") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunIsInLobby = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_is_in_lobby");
            }
        } else if (key == "fun_is_gtm") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunIsGtm = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_is_gtm");
            }
        } else if (key == "fun_can_use_bucks") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunCanUseBucks = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_can_use_bucks");
            }
        } else if (key == "fonts_button_index") {
            if (value.getType() == ccValueType::MAP) {
                auto map = value.asValueMap();

                for (const auto& _iter : map) {
                    auto v = _iter.second.asInt();
                    auto k = _iter.first;
                    sFontButtonIndex[k] = v;
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fonts_button_index");
            }
        } else if (key == "fonts_button_type") {
            if (value.getType() == ccValueType::MAP) {
                auto map = value.asValueMap();

                for (const auto& _iter : map) {
                    auto v = _iter.second;
                    auto k = _iter.first;
                    sFontButtonType[k] = v;
                }
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fonts_button_type");
            }
        } else if (key == "purchase") {
            if (value.getType() == ccValueType::MAP) {
                auto map = value.asValueMap();
                for (const auto& _iter : map) {
                    auto _k = _iter.first;
                    auto _v = _iter.second;
                    if (_k == "get_data") {
                        if (_v.getType() == ccValueType::LUA_FUNCTION) {
                            PurchaseManager::getInstance()->setInitDataFun(_v);
                        }
                        else {
                            sea::showMessageBox("ERROR Global::setConfig-purchase:get_data");
                        }
                    }
                    else if (_k == "start_pay") {
                        if (_v.getType() == ccValueType::LUA_FUNCTION) {
                            PurchaseManager::getInstance()->setStartFun(_v);
                        }
                        else {
                            sea::showMessageBox("ERROR Global::setConfig-purchase:start_pay");
                        }
                    }
                }
                
                
            } else {
                sea::showMessageBox("ERROR Global::setConfig-purchase");
            }
        } else if (key == "fun_play_btn_click_sound") {
            if (value.getType() == ccValueType::LUA_FUNCTION) {
                sFunPlayBtnClickSound = value.asInt();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-fun_play_btn_click_sound");
            }
        } else if (key == "texture_suffix") {
            if (value.getType() == ccValueType::STRING) {
                sTextureSuffix = value.asString();
            } else {
                sea::showMessageBox("ERROR Global::setConfig-texture_suffix");
            }
        } else if (key == "bole_button_loading_png_path") {
            if (value.getType() == ccValueType::STRING) {
                auto path = value.asString();
                BoleButton::setLoadingPngPath(path);
            } else {
                sea::showMessageBox("ERROR Global::setConfig-bole_button_loading_png_path");
            }
        } else if (key == "bole_button_auto_close_png_path") {
            if (value.getType() == ccValueType::STRING) {
                auto path = value.asString();
                BoleButton::setAutoClosePngPath(path);
            } else {
                sea::showMessageBox("ERROR Global::setConfig-bole_button_auto_close_png_path");
            }
        }
    }
}

const std::string Global::getNextLang(const std::string& lang) {
    if (sDefaultLangMap.find(lang) == sDefaultLangMap.end()) {
        return "";
    }

    return sDefaultLangMap.at(lang);
}

void Global::bindObjectLuaFun(cocos2d::Ref *object, const char *objName, const std::string& funName, std::function<void(cocos2d::LuaStack *)> fun, int argNums) {
    auto iter = sBindObjectLuaFun.find(funName);

    if (iter == sBindObjectLuaFun.end()) {
        return;
    }

    excuteLuaFunction(iter->second, argNums + 1, [ = ](cocos2d::LuaStack *stack) {
            stack->pushObject(object, objName);
            fun(stack);
        });
}

void Global::setAnimationLoopWithDelay(spine::SkeletonAnimation *spine, int index, std::string name, float delay) {
    ScriptHandlerMgr::getInstance()->removeObjectHandler((void *)spine, ScriptHandlerMgr::HandlerType::EVENT_SPINE_ANIMATION_COMPLETE);
    spine->stopActionByTag(109927);

    if (delay <= 0) {
        spine->setAnimation(index, name, true);
    } else {
        spine->setAnimation(index, name, false);

        spine->setCompleteListener([ = ](spTrackEntry *entry) {
                if (entry && entry->animation && entry->animation->name == name) {
                    sea::createDelayAction(spine, [ = ](cocos2d::Node *node) {
                        auto _spine = dynamic_cast<spine::SkeletonAnimation *>(node);

                        if (_spine) {
                            _spine->setAnimation(index, name, false);
                        }
                    }, delay, 109927);
                }
            });
    }
}

const std::string Global::getFontType(const std::string& lang) {
    if (sFontTypeMap.find(lang) == sFontTypeMap.end()) {
        return "";
    }

    return sFontTypeMap.at(lang);
}

std::string Global::getFontPath(const std::string& fontName, const std::string& lang) {
    auto iter = sFontMap.find(fontName);

    if (iter == sFontMap.end()) {
        return "";
    }

    auto iFont = iter->second;
    auto typeIter = sFontToPath.find(iFont);

    if (typeIter == sFontToPath.end()) {
        return "";
    }

    auto langMap = typeIter->second;
    auto langPathIter = langMap.find(lang);

    if (langPathIter == langMap.end()) {
        return "";
    }

    return langPathIter->second;
}

std::string Global::getLangDefaultPath(const std::string& lang) {
    auto iter = sFontDefaultPath.find(lang);

    if (iter == sFontDefaultPath.end()) {
        return "";
    }

    auto val = iter->second;

    if (val.second) {
        return val.first;
    }

    if (sea::isFileExist(val.first)) {
        val.second = true;
        return val.first;
    }

    return "";
}

cocos2d::Value Global::translateTable(const cocos2d::Value& table) {
    cocos2d::ValueMap params;

    return translateTable(table, params);
}

cocos2d::Value Global::translateTable(const cocos2d::Value& table, const cocos2d::ValueMap& params) {
    if (table.isNull()) {
        return table;
    }

    std::unordered_map<std::string, cocos2d::Value> newTable;
    auto type = table.getType();

    if (type == ccValueType::VECTOR) {
        auto arr = table.asValueVector();
        auto length = arr.size();

        for (int i = 0; i < length; ++i) {
            auto val = arr[i];

            if (val.getType() == ccValueType::VECTOR) {
                auto valArr = val.asValueVector();

                if (valArr.size() == 2) {
                    auto valData = valArr[0];
                    auto valLang = valArr[1];
                    auto valArrType = valLang.getType();

                    if (valArrType == ccValueType::STRING) {
                        newTable[valLang.asString()] = valData;
                    } else if (valArrType == ccValueType::VECTOR) {
                        for (const auto& iter: valLang.asValueVector()) {
                            newTable[iter.asString()] = valData;
                        }
                    } else {
                        sea::showMessageBox("ERROR Global::translateTable");
                    }
                }
            }
        }
    } else if (type == ccValueType::MAP) {
        for (const auto& iter : table.asValueMap()) {
            newTable[iter.first] = iter.second;
        }
    } else {
        return table;
    }

#if COCOS2D_DEBUG >= 1

    if (newTable.size() == 0) {
        sea::showMessageBox("ERROR Global::translateTable talbe-size is 0");
        return table;
    }

#endif

    //需要查找的语言
    std::string lang = "";

    if (params.find("priority") != params.end()) {
        lang = params.at("priority").asString();
    } else {
        lang = sea::Global::getLocalLang();
    }

#if COCOS2D_DEBUG >= 1

    if (lang == "") {
        sea::showMessageBox("ERROR Global::translateTable lang is ''");
        return table;
    }

#endif

    //如果要查的语言直接能找到
    auto langIter = newTable.find(lang);

    if (langIter != newTable.end()) {
        return langIter->second;
    }

    //如果有转换
    if (params.find("trans") != params.end()) {
        auto trans = params.at("trans");

        if (trans.getType() != ccValueType::MAP) {
            sea::showMessageBox("ERROR Global::translateTable trans is not map");
        } else {
            auto transMap = trans.asValueMap();

            if (transMap.find(lang) != transMap.end()) {
                lang = transMap.at(lang).asString();

                //如果要查的语言直接能找到
                auto langIter = newTable.find(lang);

                if (langIter != newTable.end()) {
                    return langIter->second;
                }
            }
        }
    }

    //如果还是没有找到，则看下是否有循环逻辑
    sea::IntBool loop = sea::IntBool::None;

    if (params.find("loop") != params.end()) {
        loop = sea::getIntBoolFromCCValue(params.at("loop"));
    }

    cocos2d::Value defaultVal;

    if (newTable.find("default") != newTable.end()) {
        defaultVal = newTable.at("default");
    }

    //无循环逻辑且有默认值则直接返回默认值
    if (loop != sea::IntBool::True && !defaultVal.isNull()) {
        return defaultVal;
    }
    
    if (loop == sea::IntBool::None) {
        loop = sea::IntBool::True;
    }

    //有循环逻辑则循环查找
    if (loop == sea::IntBool::True) {
        do {
            if (lang == "EN") {
                return defaultVal;
            }

            lang = sDefaultLangMap[lang];
        } while (newTable.find(lang) == newTable.end());

        if (newTable.find(lang) == newTable.end()) {
            return defaultVal;
        } else {
            return newTable.at(lang);
        }
    } else {
        return defaultVal;
    }
}

template<typename T, typename K>
T translateTableTemplate(const std::unordered_map<K, T>& table, const cocos2d::ValueMap& params, T& defaultVal) {
    if (table.size() == 0) {
        return defaultVal;
    }

    std::unordered_map<K, T> newTable;

    for (const auto& iter : table) {
        newTable[iter.first] = iter.second;
    }

#if COCOS2D_DEBUG >= 1

    if (newTable.size() == 0) {
        sea::showMessageBox("ERROR translateTableTemplate talbe-size is 0");
        return defaultVal;
    }

#endif

    //需要查找的语言
    std::string lang = "";

    if (params.find("priority") != params.end()) {
        lang = params.at("priority").asString();
    } else {
        lang = sea::Global::getLocalLang();
    }

#if COCOS2D_DEBUG >= 1

    if (lang == "") {
        sea::showMessageBox("ERROR translateTableTemplate lang is ''");
        return defaultVal;
    }

#endif

    //如果要查的语言直接能找到
    auto langIter = newTable.find(lang);

    if (langIter != newTable.end()) {
        return langIter->second;
    }

    //如果有转换
    if (params.find("trans") != params.end()) {
        auto trans = params.at("trans");

        if (trans.getType() != ccValueType::MAP) {
            sea::showMessageBox("ERROR Global::translateTable trans is not map");
        } else {
            auto transMap = trans.asValueMap();

            if (transMap.find(lang) != transMap.end()) {
                lang = transMap.at(lang).asString();

                //如果要查的语言直接能找到
                auto _langIter = newTable.find(lang);

                if (_langIter != newTable.end()) {
                    return _langIter->second;
                }
            }
        }
    }

    //如果还是没有找到，则看下是否有循环逻辑
    sea::IntBool loop = sea::IntBool::None;

    if (params.find("loop") != params.end()) {
        loop = sea::getIntBoolFromCCValue(params.at("loop"));
    }

    bool isSetDefault = false;

    if (newTable.find("default") != newTable.end()) {
        defaultVal = newTable.at("default");
        isSetDefault = true;
    }

    //无循环逻辑且有默认值则直接返回默认值
    if (loop != sea::IntBool::True && isSetDefault) {
        return defaultVal;
    }

    //有循环逻辑则循环查找
    if (loop == sea::IntBool::True) {
        do {
            if (lang == "EN") {
                return defaultVal;
            }

            lang = sea::Global::getNextLang(lang);
        } while (newTable.find(lang) == newTable.end());

        if (newTable.find(lang) == newTable.end()) {
            return defaultVal;
        } else {
            return newTable.at(lang);
        }
    } else {
        return defaultVal;
    }
}

std::string Global::translateTable(const std::unordered_map<std::string, std::string>& table, const cocos2d::ValueMap& params) {
    std::string defaultVal = "";

    return translateTableTemplate(table, params, defaultVal);
}

std::string Global::translateTable(const std::unordered_map<std::string, std::string>& table) {
    cocos2d::ValueMap params;

    return translateTable(table, params);
}

bool Global::isOnlyEnglishCode(const std::string& str) {
    for (char c : str) {
        if (!isascii(static_cast<unsigned char>(c))) {
            return false;
        }
    }

    return true;
}

std::string Global::translateText(const std::string& key, const cocos2d::ValueVector& params) {
    return sea::TranslateTextManager::getInstance()->translateText(key, params);
}

std::string Global::translateText(const std::string& key, const cocos2d::Value& params /* = cocos2d::Value::Null*/) {
    return sea::TranslateTextManager::getInstance()->translateText(key, params);
}

void Global::setCountdownLabel(std::function<void(cocos2d::LuaStack *)> funPushNode, const std::string& timer, const cocos2d::Value& endCal /*l = cocos2d::Value::Null*/, const cocos2d::Value& setTextFun /* = cocos2d::Value::Null*/, const cocos2d::Value& params /* = cocos2d::Value::Null*/) {
    if (sFunSetCountdownLabel > 0) {
        sea::excuteLuaFunction(sFunSetCountdownLabel, 5, [ = ](cocos2d::LuaStack *stack) {
                funPushNode(stack);
                stack->pushString(timer.c_str());
                sea::pushValueToStack(stack, endCal);
                sea::pushValueToStack(stack, setTextFun);
                sea::pushValueToStack(stack, params);
            });
    }
}

bool Global::isInTheme() {
    if (sFunIsInTheme > 0) {
        auto flag = sea::excuteLuaFunction(sFunIsInTheme);

        if (flag.getType() == ccValueType::BOOLEAN) {
            return flag.asBool();
        }
    }

    return false;
}

float Global::getScreenRatio() {
    if (sFunGetScreenRatio > 0) {
        auto flag = sea::excuteLuaFunction(sFunGetScreenRatio);

        if (flag.isNumber()) {
            return flag.asFloat();
        }
    }

    return 1;
}

bool Global::isInLobby() {
    if (sFunIsInLobby > 0) {
        auto flag = sea::excuteLuaFunction(sFunIsInLobby);

        if (flag.getType() == ccValueType::BOOLEAN) {
            return flag.asBool();
        }
    }

    return false;
}

void Global::playBtnClickSound() {
    if (sFunPlayBtnClickSound > 0) {
        sea::excuteLuaFunction(sFunPlayBtnClickSound);
    }
}

int Global::getTouchDistance() {
    if (sIntTouchDistance > 0) {
        return sIntTouchDistance;
    }

    auto platform = cocos2d::Application::getInstance()->getTargetPlatform();
    switch (platform) {
        case cocos2d::ApplicationProtocol::Platform::OS_IPHONE:
        case cocos2d::ApplicationProtocol::Platform::OS_IPAD:
            sIntTouchDistance = 2;
            break;

        case cocos2d::ApplicationProtocol::Platform::OS_ANDROID:
            sIntTouchDistance = 4;
            break;

        default:
            break;
    }
    return sIntTouchDistance;
}

bool Global::isGtm() {
    if (iIsGtm == IntBool::None) {
        if (sFunIsGtm > 0) {
            auto value = sea::excuteLuaFunction(sFunIsGtm);

            if (value.getType() == ccValueType::BOOLEAN) {
                iIsGtm = value.asBool() ? IntBool::True : IntBool::False;
            }
        }
    }

    return iIsGtm == IntBool::True ? true : false;
}

int Global::getFontButtonIndex(std::string key) {
    auto iter = sFontButtonIndex.find(key);

    if (iter != sFontButtonIndex.end()) {
        return iter->second;
    }

    return -1;
}

cocos2d::Value Global::getFontButtonTypeValue(std::list<const char *> keys) {
    cocos2d::Value ret = cocos2d::Value::Null;
    std::unordered_map<std::string, cocos2d::Value> retMap = sFontButtonType;

    for (const auto &key : keys) {
        auto iter = retMap.find(key);

        if (iter != retMap.end()) {
            ret = iter->second;

            if (ret.getType() == ccValueType::MAP) {
                retMap = ret.asValueMap();
            }
        } else {
            ret = cocos2d::Value::Null;
            break;
        }
    }

    return ret;
}

int Global::getIntFontButtonTypeValue(const char *key, ...) {
    va_list params;

    va_start(params, key);

    std::list<const char *> arr;
    arr.push_back(key);

    while (key) {
        key = va_arg(params, const char *);

        if (key) {
            arr.push_back(key);
        }
    }

    va_end(params);

    auto v = getFontButtonTypeValue(arr);
    return v.asInt();
}

std::string Global::getStringFontButtonTypeValue(const char *key, ...) {
    va_list params;

    va_start(params, key);

    std::list<const char *> arr;
    arr.push_back(key);

    while (key) {
        key = va_arg(params, const char *);

        if (key) {
            arr.push_back(key);
        }
    }

    va_end(params);

    auto v = getFontButtonTypeValue(arr);
    return v.asString();
}

std::string Global::getCommonResPath(std::string key) {
    auto iter = sMapResCommonPath.find(key);

    if (iter != sMapResCommonPath.end()) {
        return iter->second;
    }

    return "";
}

void Global::registerEvent(std::string key, std::string luaFunName, cocos2d::Ref *object) {
    if (sFunEventCenterRegister <= 0) {
        return;
    }

    sea::excuteLuaFunction(sFunEventCenterRegister, 3, [ = ](cocos2d::LuaStack *stack) {
            stack->pushString(key.c_str());
            stack->pushString(luaFunName.c_str());
            sea::pushValueToStack(stack, object, "cc.Ref");
        });
}

void Global::removeEvent(std::string key, std::string luaFunName, cocos2d::Ref *object) {
    if (sFunEventCenterRemove <= 0) {
        return;
    }

    sea::excuteLuaFunction(sFunEventCenterRemove, 3, [ = ](cocos2d::LuaStack *stack) {
            stack->pushString(key.c_str());
            stack->pushString(luaFunName.c_str());
            sea::pushValueToStack(stack, object, "cc.Ref");
        });
}

void Global::registerRedEvent(const char *key, cocos2d::Ref *object) {
    if (sFunRedEventControlRegister <= 0) {
        return;
    }

    sea::excuteLuaFunction(sFunRedEventControlRegister, 2, [ = ](cocos2d::LuaStack *stack) {
            if (key) {
                stack->pushString(key);
            } else {
                stack->pushNil();
            }

            sea::pushValueToStack(stack, object, "cc.Ref");
        });
}

void Global::removeRedEvent(const char *key, cocos2d::Ref *object) {
    if (sFunRedEventControlRemove <= 0) {
        return;
    }

    sea::excuteLuaFunction(sFunRedEventControlRemove, 2, [ = ](cocos2d::LuaStack *stack) {
            if (key) {
                stack->pushString(key);
            } else {
                stack->pushNil();
            }

            sea::pushValueToStack(stack, object, "cc.Ref");
        });
}

bool Global::canUseBucks(float price) {
    if (sFunCanUseBucks > 0) {
        auto flag = sea::excuteLuaFunction(sFunCanUseBucks, 1, [=](cocos2d::LuaStack* stack){
            stack->pushFloat(price);
        });

        if (flag.getType() == ccValueType::BOOLEAN) {
            return flag.asBool();
        }
    }

    return false;
}

void Global::setLogAble(bool able) {
    sBoolLogAble = able;
}

bool Global::getLogAble() {
    return sBoolLogAble;
}

void setEnableRecursiveCascading(cocos2d::Node *node, bool able) {
    if (nullptr == node) {
        return;
    }

    node->setCascadeColorEnabled(able);
    node->setCascadeOpacityEnabled(able);

    for (auto& child : node->getChildren()) {
        sea::setEnableRecursiveCascading(child, able);
    }
}

cocos2d::Vec2 getVec2FromCCValue(const cocos2d::Value& val, const cocos2d::Vec2& defaultVal) {
    if (val.getType() == ccValueType::MAP) {
        auto map = val.asValueMap();

        if (map.find("x") != map.end() && map.find("y") != map.end()) {
            float x = map.at("x").asFloat();
            float y = map.at("y").asFloat();
            return cocos2d::Vec2(x, y);
        }
    }

    return defaultVal;
}

cocos2d::Size getSizeFromCCValue(const cocos2d::Value& val, const cocos2d::Size& defaultVal) {
    cocos2d::Size ret = defaultVal;

    if (val.getType() == ccValueType::MAP) {
        auto map = val.asValueMap();

        if (map.find("width") != map.end() && map.find("height") != map.end()) {
            ret.width = map.at("width").asFloat();
            ret.height = map.at("height").asFloat();
        }
    }

    return ret;
}

cocos2d::Size getSizeFromCCValue(const cocos2d::ValueMap& val, const cocos2d::Size& defaultVal) {
    cocos2d::Size ret = defaultVal;

    if (val.find("width") != val.end() && val.find("height") != val.end()) {
        ret.width = val.at("width").asFloat();
        ret.height = val.at("height").asFloat();
    }

    return ret;
}

cocos2d::Value getTableIndexValue(const cocos2d::Value& val, int index, const cocos2d::Value& defaultVal) {
    if (index < 0) {
        return defaultVal;
    }

    switch (val.getType()) {
        case ccValueType::MAP:{
            auto map = val.asValueMap();
            std::string indexStr = std::to_string(index + 1);
            auto iter = map.find(indexStr);

            if (iter != map.end()) {
                return iter->second;
            }
        }
        break;

        case ccValueType::VECTOR:{
            auto vec = val.asValueVector();

            if (vec.size() > index) {
                return vec[index];
            }
        }

        default:
            break;
    }

    return defaultVal;
}

cocos2d::Sprite * createSprite(const std::string& path, sea::TextureResType type) {
    cocos2d::Sprite *spr = nullptr;

    if (type == sea::TextureResType::PLIST) {
        spr = cocos2d::Sprite::createWithSpriteFrameName(path);
    } else {
        spr = cocos2d::Sprite::create(sea::checkResTypeName(path));
    }
    
    if (!spr) {
        spr = cocos2d::Sprite::create();
    }

    return spr;
}

cocos2d::Node* createFnt(const std::string& path, const std::string& text) {
    if (sea::isFileExist(path)) {
        return cocos2d::Label::createWithBMFont(path, text);
    }
    return sea::BoleLabel::create(cocos2d::Value::Null, text, cocos2d::Value(24), cocos2d::Color4B::WHITE);
}

cocos2d::ValueVector getVectorFromCCValue(const cocos2d::Value& val, int size, bool force/* = false*/) {
    switch (val.getType()) {
        case ccValueType::VECTOR:
        {
            cocos2d::ValueVector vec = val.asValueVector();
            if (force && vec.size() < size) {
                for (int i = (int)vec.size(); i < size; ++i) {
                    vec.push_back(cocos2d::Value::Null);
                }
            }
            return vec;
        }

            break;

        case ccValueType::MAP:{
            auto map = val.asValueMap();
            cocos2d::ValueVector vec;

            if (size > 0) {
                for (int i = 1; i <= size; ++i) {
                    auto key = std::to_string(i);

                    if (map.find(key) != map.end()) {
                        vec.push_back(map.at(key));
                    } else {
                        vec.push_back(cocos2d::Value::Null);
                    }
                }
            } else {
                auto iter = map.find("1");
                int i = 1;

                while (iter != map.end()) {
                    vec.push_back(iter->second);
                    ++i;
                    iter = map.find(std::to_string(i));
                }
            }

            return vec;
        }
        break;

        default:
            if (force && size > 1) {
                cocos2d::ValueVector vec = cocos2d::ValueVector({ val });
                for (int i = (int)vec.size(); i < size; ++i) {
                    vec.push_back(cocos2d::Value::Null);
                }
                return vec;
            }
            return cocos2d::ValueVector({ val });
    }
}

sea::TextureResType getTextureResTypeFromCCValue(const cocos2d::Value& val, const sea::TextureResType& defaultVal/* = sea::TextureResType::LOCAL*/) {
    if (val.isNumber()) {
        auto type = val.asInt();
        switch(type) {
            case -1:
                return sea::TextureResType::NONE;
            case 0:
                return sea::TextureResType::LOCAL;
            case 1:
                return sea::TextureResType::PLIST;
        }
    }
    
    return defaultVal;
}

sea::IntBool getIntBoolFromCCValue(const cocos2d::Value& val, const sea::IntBool& defaultVal/* = sea::IntBool::None*/) {
    if (val.getType() == ccValueType::BOOLEAN) {
        return val.asBool() ? sea::IntBool::True : sea::IntBool::False ;
    }
    
    return defaultVal;
}


bool getBoolFromVector(const cocos2d::ValueVector& vec, int index, bool def /* = false*/) {
    auto count = vec.size();

    if (count == 0 || count <= index || index < -1) {
        return def;
    }

    auto val = vec[index];

    if (val.isNull() || val.getType() != ccValueType::BOOLEAN) {
        sea::showMessageBox("getBoolFromVector 获取到的数据不是boolean型");
        return def;
    }

    return val.asBool();
}

float getFloatFromVector(const cocos2d::ValueVector& vec, int index, float def /* = -1.0f*/) {
    auto count = vec.size();

    if (count == 0 || count <= index || index < -1) {
        return def;
    }

    auto val = vec[index];

    if (val.isNull() || !val.isNumber()) {
        sea::showMessageBox("getFloatFromVector 获取到的数据不是数字型");
        return def;
    }

    return val.asFloat();
}

int getIntFromVector(const cocos2d::ValueVector& vec, int index, int def /* = -1*/) {
    auto count = vec.size();

    if (count == 0 || count <= index || index < -1) {
        return def;
    }

    auto val = vec[index];

    if (val.isNull() || !val.isNumber()) {
        sea::showMessageBox("getFloatFromVector 获取到的数据不是数字型");
        return def;
    }

    return val.asInt();
}

std::string getStringFromVector(const cocos2d::ValueVector& vec, int index, std::string def /* = ""*/) {
    auto count = vec.size();

    if (count == 0 || count <= index || index < -1) {
        return def;
    }

    auto val = vec[index];

    if (val.isNull() || val.getType() != ccValueType::STRING) {
        sea::showMessageBox("getFloatFromVector 获取到的数据不是字符型");
        return def;
    }

    return val.asString();
}

std::string getStringFromMap(const cocos2d::Value& value, const std::string& key, const std::string& defaultVal) {
    if(value.getType() == ccValueType::MAP) {
        return getStringFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

std::string getStringFromMap(const cocos2d::ValueMap& map, const std::string& key, const std::string& defaultVal) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    auto val = iter->second;
    switch (val.getType()) {
        case ccValueType::STRING:
        case ccValueType::BYTE:
        case ccValueType::INTEGER:
        case ccValueType::UNSIGNED:
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
        case ccValueType::BOOLEAN:
            return val.asString();
            break;
            
        default:
            break;
    }
    
    return defaultVal;
}

int getIntFromMap(const cocos2d::Value& value, const std::string& key, int defaultVal/* = 0*/) {
    if(value.getType() == ccValueType::MAP) {
        return getIntFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

int getIntFromMap(const cocos2d::ValueMap& map, const std::string& key, int defaultVal/* = 0*/) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    auto val = iter->second;
    switch(val.getType())
    {
        case ccValueType::INTEGER:
            return val.asInt();
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
        case ccValueType::BYTE:
        case ccValueType::STRING:
        case ccValueType::UNSIGNED:
        case ccValueType::BOOLEAN:
            return int(val.asFloat());
        default:
            break;
    }
    
    return defaultVal;
}

bool getBoolFromMap(const cocos2d::Value& value, const std::string& key, bool defaultVal/* = false*/) {
    if(value.getType() == ccValueType::MAP) {
        return getBoolFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

bool getBoolFromMap(const cocos2d::ValueMap& map, const std::string& key, bool defaultVal/* = false*/) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    auto val = iter->second;
    switch(val.getType())
    {
        case ccValueType::BOOLEAN:
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
        case ccValueType::BYTE:
        case ccValueType::STRING:
        case ccValueType::INTEGER:
        case ccValueType::UNSIGNED:
            return val.asBool();
        default:
            break;
    }
    
    return defaultVal;
}

float getFloatFromMap(const cocos2d::Value& value, const std::string& key, float defaultVal/* = 0.0*/) {
    if(value.getType() == ccValueType::MAP) {
        return getFloatFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

float getFloatFromMap(const cocos2d::ValueMap& map, const std::string& key, float defaultVal/* = 0.0*/){
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    auto val = iter->second;
    switch(val.getType())
    {
        case ccValueType::INTEGER:
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
            return val.asFloat();
        default:
            break;
    }
    
    return defaultVal;
}

cocos2d::Vec2 getVec2FromMap(const cocos2d::Value& value, const std::string& key, const cocos2d::Vec2& defaultVal/*= cocos2d::Vec2::ZERO*/) {
    if(value.getType() == ccValueType::MAP) {
        return getVec2FromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

cocos2d::Vec2 getVec2FromMap(const cocos2d::ValueMap& map, const std::string& key, const cocos2d::Vec2& defaultVal /*= cocos2d::Vec2::ZERO*/) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    
    return sea::getVec2FromCCValue(iter->second, defaultVal);
}

cocos2d::Size getSizeFromMap(const cocos2d::Value& value, const std::string& key, const cocos2d::Size& defaultVal/* = cocos2d::Size::ZERO*/) {
    if(value.getType() == ccValueType::MAP) {
        return getSizeFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

cocos2d::Size getSizeFromMap(const cocos2d::ValueMap& map, const std::string& key, const cocos2d::Size& defaultVal/* = cocos2d::Size::ZERO*/) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    
    return sea::getSizeFromCCValue(iter->second, defaultVal);
}

const cocos2d::Value& getValueFromMap(const cocos2d::Value& value, const std::string& key, const cocos2d::Value& defaultVal/* = cocos2d::Value::Null */) {
    if(value.getType() == ccValueType::MAP) {
        return getValueFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

const cocos2d::Value& getValueFromMap(const cocos2d::ValueMap& map, const std::string& key, const cocos2d::Value& defaultVal/* = cocos2d::Value::Null*/) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    
    return iter->second;
}

cocos2d::Color4B getColorFromMap(const cocos2d::Value& value, const std::string& key, const cocos2d::Color4B& defaultVal/* = cocos2d::Color4B::WHITE*/) {
    if(value.getType() == ccValueType::MAP) {
        return getColorFromMap(value.asValueMap(), key, defaultVal);
    }
    return defaultVal;
}

cocos2d::Color4B getColorFromMap(const cocos2d::ValueMap& map, const std::string& key, const cocos2d::Color4B& defaultVal/* = cocos2d::Color4B::WHITE*/) {
    auto iter = map.find(key);
    if (iter == map.end()) {
        return defaultVal;
    }
    auto val = iter->second;
    return getColor(val, cocos2d::Value::Null, 255, defaultVal);
}

std::string getStringFromValue(const cocos2d::Value& value, const std::string& defaultVal/* = ""*/) {
    switch (value.getType()) {
        case ccValueType::STRING:
        case ccValueType::BYTE:
        case ccValueType::INTEGER:
        case ccValueType::UNSIGNED:
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
        case ccValueType::BOOLEAN:
            return value.asString();
            break;
            
        default:
            break;
    }
    
    return defaultVal;
}

int getIntFromValue(const cocos2d::Value& value, const int& defaultVal) {
    switch(value.getType())
    {
        case ccValueType::INTEGER:
            return value.asInt();
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
        case ccValueType::BYTE:
        case ccValueType::STRING:
        case ccValueType::UNSIGNED:
        case ccValueType::BOOLEAN:
            return int(value.asFloat());
        default:
            break;
    }
    
    return defaultVal;
}

float getFloatFromValue(const cocos2d::Value& value, const float& defaultVal) {
    switch(value.getType())
    {
        case ccValueType::INTEGER:
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
            return value.asFloat();
        default:
            break;
    }
    
    return defaultVal;
}

bool getBoolFromValue(const cocos2d::Value& value, const bool& defaultVal/* = false*/) {
    switch(value.getType())
    {
        case ccValueType::BOOLEAN:
        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
        case ccValueType::BYTE:
        case ccValueType::STRING:
        case ccValueType::INTEGER:
        case ccValueType::UNSIGNED:
            return value.asBool();
        default:
            break;
    }
    
    return defaultVal;
}

void checkResTypeName(std::string *fileName) {
    if (!fileName) {
        return;
    }
    *fileName = checkResTypeName(*fileName);
}

std::string checkResTypeName(const std::string& fileName) {
    auto len = fileName.length();
    if (len <= 4) {
        return fileName;
    }
#if DEBUG_ONLY_USE_PNG > 0
    return fileName;
#else
    if (!sea::endsWith(fileName, ".png")) {
        return fileName;
    }
    std::string ret = fileName;
    Director::getInstance()->getTextureCache()->checkCompressedASTCType(&ret);
    return ret;

#endif
}

bool isFileExist(std::string *fileName) {
    if (!fileName) {
        return false;
    }
    return isFileExist(*fileName);
}

bool isFileExist(const std::string& fileName) {
    auto len = fileName.length();
    if (len <= 4) {
        return false;
    }
#if DEBUG_ONLY_USE_PNG > 0
    return cocos2d::FileUtils::getInstance()->isFileExist(fileName);
#else
    if (!sea::endsWith(fileName, ".png")) {
        return cocos2d::FileUtils::getInstance()->isFileExist(fileName);
    }
    std::string name = fileName;
    Director::getInstance()->getTextureCache()->checkCompressedASTCType(&name);
    return cocos2d::FileUtils::getInstance()->isFileExist(name);
#endif
}

bool endsWith(const std::string& str, const std::string& suffix) {
    if (suffix.size() > str.size()) {
        return false;
    }
    return std::equal(suffix.rbegin(), suffix.rend(), str.rbegin());
}

void mergeMap(cocos2d::ValueMap& dest, const cocos2d::ValueMap& src) {
    for (const auto& iter : src) {
        dest[iter.first] = iter.second;
    }
}

void showMessageBox(const std::string& msg, const std::string title /* = ""*/) {
#if COCOS2D_DEBUG >= 1
    MessageBox(msg.c_str(), title.c_str());
#endif
}

std::string convertFloatToString(float val, int len) {
    std::ostringstream oss;
    // 设置小数点后的位数
    oss << std::fixed << std::setprecision(len);
    // 输出浮点数到字符串流
    oss << val;
    // 返回结果字符串
    return oss.str();
}

cocos2d::LuaValue convertValue(const cocos2d::Value& value) {
    auto type = value.getType();

    switch (type) {
        case ccValueType::BYTE:
        case ccValueType::INTEGER:
        case ccValueType::UNSIGNED:
            return cocos2d::LuaValue::intValue(value.asInt());

        case ccValueType::LUA_FUNCTION:
            return cocos2d::LuaValue::functionValue(value.asInt());

        case ccValueType::FLOAT:
        case ccValueType::DOUBLE:
            return cocos2d::LuaValue::floatValue(value.asFloat());

        case ccValueType::BOOLEAN:
            return cocos2d::LuaValue::booleanValue(value.asBool());

            break;

        case ccValueType::STRING:
            return cocos2d::LuaValue::stringValue(value.asString());

            break;

        case ccValueType::VECTOR:
            do{
                cocos2d::LuaValueArray array;

                for (const auto& arrayVal : value.asValueVector()) {
                    auto aVal = convertValue(arrayVal);
                    array.push_back(aVal);
                }

                return cocos2d::LuaValue::arrayValue(array);
            }while(0);
            break;

        case ccValueType::MAP:
            do{
                cocos2d::LuaValueDict dict;

                for (const auto& arrayVal : value.asValueMap()) {
                    auto aVal = convertValue(arrayVal.second);
                    dict[arrayVal.first] = aVal;
                }

                return cocos2d::LuaValue::dictValue(dict);
            }while(0);
            break;

        case ccValueType::CC_REF:
            do {
                auto ccRefValue = value.asValueCCRef();
                return cocos2d::LuaValue::ccobjectValue(ccRefValue.pObject, ccRefValue.sName);
            } while (0);
            break;
        case ccValueType::NONE:
            return cocos2d::LuaValue::noneValue();
            break;
        default:
#if COCOS2D_DEBUG >= 1
            CCLOGERROR("Cannot support type: %d, in DialogHelper::convertValue", type);
#endif
            break;
    }

    return cocos2d::LuaValue::intValue(0);
}

void pushValueToStack(cocos2d::LuaStack *stack, const cocos2d::Value& value) {
    if (stack) {
        if (value.isNull()) {
            stack->pushNil();
        } else {
            switch (value.getType()) {
                case ccValueType::INTEGER:
                    stack->pushInt(value.asInt());
                    break;

                case ccValueType::BYTE:
                    stack->pushInt(value.asByte());
                    break;

                case ccValueType::UNSIGNED:
                    stack->pushLong(value.asLong());
                    break;

                case ccValueType::FLOAT:
                    stack->pushFloat(value.asFloat());
                    break;

                case ccValueType::DOUBLE:
                    stack->pushFloat(value.asDouble());
                    break;

                case ccValueType::STRING:
                    stack->pushString(value.asString().c_str());
                    break;

                case ccValueType::BOOLEAN:
                    stack->pushBoolean(value.asBool());
                    break;

                case ccValueType::CC_REF:{
                    auto ref = value.asValueCCRef();
                    stack->pushObject(ref.pObject, ref.sName.c_str());
                }
                break;

                case ccValueType::LUA_FUNCTION:
                {
                    auto luaFun = cocos2d::LuaValue::functionValue(value.asInt());
                    stack->pushLuaValue(luaFun);
                }
                    break;

                case ccValueType::VECTOR:
                    do{
                        cocos2d::LuaValueArray array;

                        for (const auto& arrayVal : value.asValueVector()) {
                            auto aVal = convertValue(arrayVal);
                            array.push_back(aVal);
                        }

                        stack->pushLuaValueArray(array);
                    }while(0);
                    break;

                case ccValueType::MAP:
                    do{
                        cocos2d::LuaValueDict dict;

                        for (const auto& arrayVal : value.asValueMap()) {
                            auto aVal = convertValue(arrayVal.second);
                            dict[arrayVal.first] = aVal;
                        }

                        stack->pushLuaValueDict(dict);
                    }while(0);
                    break;

                default:
                    stack->pushNil();
                    break;
            }
        }
    }
}

void pushValueToStack(cocos2d::LuaStack *stack, const cocos2d::ValueMap& value) {
    if (!stack) {
        return;
    }

    cocos2d::LuaValueDict dict;

    for (const auto& arrayVal : value) {
        auto aVal = convertValue(arrayVal.second);
        dict[arrayVal.first] = aVal;
    }

    stack->pushLuaValueDict(dict);
}

void pushValueToStack(cocos2d::LuaStack *stack, const cocos2d::ValueVector& value) {
    if (!stack) {
        return;
    }

    cocos2d::LuaValueArray array;

    for (const auto& arrayVal : value) {
        auto aVal = convertValue(arrayVal);
        array.push_back(aVal);
    }

    stack->pushLuaValueArray(array);
}

void pushValueToStack(cocos2d::LuaStack *stack, const cocos2d::Value *value) {
    if (!stack) {
        return;
    }

    if (!value) {
        stack->pushNil();
    } else {
        pushValueToStack(stack, *value);
    }
}

void pushValueToStack(cocos2d::LuaStack *stack, const cocos2d::ValueMap *value) {
    if (!stack) {
        return;
    }

    if (!value) {
        stack->pushNil();
    } else {
        pushValueToStack(stack, *value);
    }
}

void pushValueToStack(cocos2d::LuaStack *stack, const cocos2d::ValueVector *value) {
    if (!stack) {
        return;
    }

    if (!value) {
        stack->pushNil();
    } else {
        pushValueToStack(stack, *value);
    }
}

void pushValueToStack(cocos2d::LuaStack *stack, cocos2d::Ref *value, const char *name) {
    if (!stack) {
        return;
    }

    if (!value) {
        stack->pushNil();
    } else {
        stack->pushObject(value, name);
    }
}

void createEnterAndExitForNode(cocos2d::Node *node, const std::function<void(cocos2d::Node *)>& enter, const std::function<void(cocos2d::Node *)>& exit /* = nullptr*/) {
    if (!node) {
        return;
    }

    if (enter) {
        node->setOnEnterCallback([ = ]() {
                enter(node);
            });
    }

    if (exit) {
        node->setOnExitCallback([ = ]() {
                exit(node);
            });
    }
}

void createDelayAction(cocos2d::Node *node, seaCallback3 fun, float delay, int tag /* = -1*/, const float speed /* = -1*/) {
    if (!node || delay <= 0 || !fun) {
        return;
    }

    cocos2d::ActionInterval *action1 = cocos2d::Sequence::create(
        cocos2d::DelayTime::create(delay),
        cocos2d::CallFuncN::create(fun),
        nullptr
        );
    cocos2d::Action *action = action1;

    if (speed > 0) {
        action = cocos2d::Speed::create(action1, speed);
    }

    if (tag > 0) {
        action->setTag(tag);
    }

    node->runAction(action);
}

void createUpdateAction(cocos2d::Node *node, seaCallback3 fun, float delay, int tag /* = -1*/) {
    if (!node || !fun) {
        return;
    }
    
    if (delay <= 0) {
        delay = 0.5;
    }

    auto action = cocos2d::RepeatForever::create(
        cocos2d::Sequence::create(
            cocos2d::DelayTime::create(delay),
            cocos2d::CallFuncN::create(fun),
            nullptr
            ));

    if (tag > 0) {
        action->setTag(tag);
    }

    node->runAction(action);
}

void delayRemove(cocos2d::Node* node, float delay) {
    if (!node) {
        return;
    }
    auto action = cocos2d::Sequence::createWithTwoActions(cocos2d::DelayTime::create(delay), cocos2d::RemoveSelf::create());
    node->runAction(action);
}

cocos2d::Value excuteLuaFunction(const cocos2d::Value& value, int argNums /*=0*/, std::function<void(cocos2d::LuaStack *)> fun /* = nullptr*/) {
    cocos2d::Value ret = value;

    if (value.getType() != ccValueType::LUA_FUNCTION) {
        return ret;
    }

    int handler = value.asLuaFunction().hander;
    return excuteLuaFunction(handler, argNums, fun);
}

cocos2d::Value excuteLuaFunction(const int& handler, int argNums /*=0*/, std::function<void(cocos2d::LuaStack *)> fun /* = nullptr*/) {
    cocos2d::Value ret;
    cocos2d::LuaStack *stack = cocos2d::LuaEngine::getInstance()->getLuaStack();
    auto L = stack->getLuaState();
    auto top = lua_gettop(L);

    toluafix_get_function_by_refid(L, handler);

    int funIndex = -1;

    if (argNums > 0 && fun) {
        funIndex = -(argNums + 1);
        fun(stack);
    }

    if (lua_isfunction(L, funIndex)) {
        try {
            if (LUA_OK == lua_pcall(L, argNums, 1, 0)) {
                luaval_to_ccvalue_new(L, lua_gettop(L), &ret, "Global::excuteLuaFunction");
            }
    #if COCOS2D_DEBUG >= 1
            else{
                CCLOG("[LUA ERROR] %s", lua_tostring(L, -1)); /* L: ... error */
                const char *errst = lua_tostring(L, -1);
                CCLOG("[LUA ERROR] %s", errst);               /* L: ... error */
                
                std::stringstream ss;
                ss << "[LUA ERROR]" << errst;
                sea::showMessageBox(ss.str(), "执行Lua程序失败！");
                lua_pop(L, 1); // remove error message from stack
            }
    #endif
        } catch (...) {
#if COCOS2D_DEBUG >= 1
            CCLOG("[LUA ERROR] %s", lua_tostring(L, -1)); /* L: ... error */
            const char *errst = lua_tostring(L, -1);
            CCLOG("[LUA ERROR] %s", errst);               /* L: ... error */
            
            std::stringstream ss;
            ss << "[LUA ERROR]" << errst;
            sea::showMessageBox(ss.str(), "执行Lua程序失败！");
            lua_pop(L, 1); // remove error message from stack
#endif
        }
        
    }

    lua_settop(L, top);
    return ret;
}

cocos2d::Node * excuteLuaFunctionToNode(const cocos2d::Value& value, int argNums /*=0*/, std::function<void(cocos2d::LuaStack *)> fun /* = nullptr*/) {
    cocos2d::Node *ret = nullptr;

    if (value.getType() != ccValueType::LUA_FUNCTION) {
        return ret;
    }

    int handler = value.asLuaFunction().hander;
    return excuteLuaFunctionToNode(handler, argNums, fun);
}

cocos2d::Node * excuteLuaFunctionToNode(const int& handler, int argNums /*=0*/, std::function<void(cocos2d::LuaStack *)> fun /* = nullptr*/) {
    cocos2d::Node *ret = nullptr;

    cocos2d::LuaStack *stack = cocos2d::LuaEngine::getInstance()->getLuaStack();
    auto L = stack->getLuaState();
    auto top = lua_gettop(L);

    toluafix_get_function_by_refid(L, handler);

    int funIndex = -1;

    if (argNums > 0 && fun) {
        funIndex = -(argNums + 1);
        fun(stack);
    }

    if (lua_isfunction(L, funIndex)) {
        int traceback = 0;
        lua_getglobal(L, "__G__TRACKBACK__");                    /* L: ... func arg1 arg2 ... G */

        if (!lua_isfunction(L, -1)) {
            lua_pop(L, 1);                                       /* L: ... func arg1 arg2 ... */
        } else {
            lua_insert(L, funIndex - 1);                         /* L: ... G func arg1 arg2 ... */
            traceback = funIndex - 1;
        }

        try {
            auto error = lua_pcall(L, argNums, 1, traceback);

            if (LUA_OK == error) {
                luaval_to_node(L, lua_gettop(L), "cc.Node", &ret);
            } else {
                if (traceback == 0) {
    #if COCOS2D_DEBUG >= 1
                    CCLOG("[LUA ERROR] %s", lua_tostring(L, -1)); /* L: ... error */
                    const char *errst = lua_tostring(L, -1);
                    CCLOG("[LUA ERROR] %s", errst);               /* L: ... error */

                    std::stringstream ss;
                    ss << "[LUA ERROR]" << errst;
                    sea::showMessageBox("运行Lua代码出错", ss.str());
    #endif
                    lua_pop(L, 1); // remove error message from stack
                } else {                                          /* L: ... G error */
                    lua_pop(L, 2); // remove __G__TRACKBACK__ and error message from stack
                }

                lua_settop(L, top);
            }
        }
        catch(...) {
#if COCOS2D_DEBUG >= 1
                CCLOG("[LUA ERROR] %s", lua_tostring(L, -1)); /* L: ... error */
                const char *errst = lua_tostring(L, -1);
                CCLOG("[LUA ERROR] %s", errst);               /* L: ... error */

                std::stringstream ss;
                ss << "[LUA ERROR]" << errst;
                sea::showMessageBox(ss.str(), "运行Lua代码出错");
#endif
        }
    }

    lua_settop(L, top);
    return ret;
}

cocos2d::Value excuteNodeFunction(cocos2d::Node* node, const std::string& key, const cocos2d::ValueVector& argList /*= cocos2d::ValueVectorNull*/) {
    cocos2d::Value ret = cocos2d::Value::Null;
    
    if (node)
    {
        auto fun = node->getSeaUserData(key);
        if (fun) {
            if (fun->getType() == ccValueType::C_FUNCTION) {
                ret = fun->executeCFunction(cocos2d::Value(argList));
            }
            else if (fun->getType() == ccValueType::LUA_FUNCTION) {
                auto count = (int)argList.size();
                if (count > 0) {
                    ret = sea::excuteLuaFunction(*fun, count, [=](cocos2d::LuaStack* stack){
                        for (const auto& obj : argList) {
                            sea::pushValueToStack(stack, obj);
                        }
                    });
                }
                else {
                    ret = sea::excuteLuaFunction(*fun);
                }
                
            }
        }
    }
    return ret;
}

cocos2d::Color4B getColor(const cocos2d::Value& color, const cocos2d::Value& params /*=cocos2d::Value::Null*/, int opacity /*=255*/, const cocos2d::Color4B& defalutVal/* = cocos2d::Color4B::WHITE*/) {
    cocos2d::Color4B ret = defalutVal;
    auto type = color.getType();

    switch (type) {
        case ccValueType::STRING:
            do{
                std::string colorStr = color.asString();

                if (colorStr.at(0) == '#') {
                    colorStr = colorStr.substr(1);
                }

                if (colorStr.size() != 6) {
#if COCOS2D_DEBUG >= 1
                    sea::showMessageBox("color值必须是6位十六进制形式的字符串或者前面加#");
#endif
                    break;
                }

                auto r = std::stoi(colorStr.substr(0, 2), nullptr, 16);
                auto g = std::stoi(colorStr.substr(2, 2), nullptr, 16);
                auto b = std::stoi(colorStr.substr(4, 2), nullptr, 16);
                ret.set(r, g, b, opacity);
            }while(0);
            break;

        case ccValueType::LUA_FUNCTION:
            do{
                ret = getColor(sea::excuteLuaFunction(color, 2, [ = ](cocos2d::LuaStack *stack) {
                    sea::pushValueToStack(stack, params);
                    stack->pushInt(opacity);
                }));
            }while(0);
            break;

        case ccValueType::MAP:
            do{
                auto map = color.asValueMap();

                if (map.find("r") != map.end() && map.find("g") != map.end() && map.find("b") != map.end()) {
                    if (map.find("a") != map.end()) {
                        opacity = map["a"].asInt(true);
                    }
                    else {
                        opacity = sea::getRangeValue((int)(opacity * 2.55), 0, 255);
                    }

                    
                    ret.r = map["r"].asInt(true);
                    ret.g = map["g"].asInt(true);
                    ret.b = map["b"].asInt(true);
                    ret.a = opacity;
                }
            }while(0);
            break;

        default:
            break;
    }

    return ret;
}

float arrangeVerticalOnly(cocos2d::Node *father, std::vector<cocos2d::Node *> nodeList, float spacing /* = 0.0f*/, cocos2d::Vec2 offset /* = cocos2d::Vec2::ZERO*/, float anchorY /* = 0.5*/) {
    //计算总高度
    float heightSum = 0.0f;

    for (int i = 0; i < nodeList.size(); ++i) {
        heightSum += sea::getNodeBoundingBox(nodeList[i]).height;

        if (i > 0) {
            heightSum += spacing;
        }
    }

    //排序
    float heightToUp = 0.0f;

    for (auto& node : nodeList) {
        auto selfHeight = sea::getNodeBoundingBox(node).height;
        auto anchor = node->getAnchorPoint();
        node->setAnchorPoint(cocos2d::Vec2(anchor.x, 0.5));
        node->setPositionY(heightSum * (1 - anchorY) - heightToUp - selfHeight * 0.5 + offset.y);
        heightToUp += selfHeight + spacing;
    }

    return heightSum;
}

float arrangeHorizontalNew(cocos2d::Node *father, std::vector<cocos2d::Node *> nodeList, float spacing, float anchor_x, cocos2d::Vec2 off, cocos2d::Value width) {
    float widthSum = 0.0f;

    for (int i = 0; i < nodeList.size(); ++i) {
        widthSum += sea::getNodeBoundingBox(nodeList[i]).width;

        if (i > 0) {
            widthSum += spacing;
        }
    }
    
    float scale = 1.0f;
    bool _bIsSetScale = false;
    if (!width.isNull() && width.isNumber()) {
        auto w = width.asFloat();
        if (widthSum > w) {
            _bIsSetScale = true;
            scale = w / widthSum;
            widthSum = w;
        }
    }
    
    //排序
    float widthToLeft = 0.0f;
    for (auto& node : nodeList) {
        auto selfWidth = sea::getNodeBoundingBox(node).width * scale;

        auto pos = node->getPosition();
        node->setPositionX(widthSum * (-anchor_x) + widthToLeft + selfWidth * 0.5 + off.x);
        if (_bIsSetScale) {
            node->setScale(scale);
        }
        
        if (off.y > 0.0001 || off.y < -0.0001) {
            node->setPositionY(pos.y + off.y);
        }
        
        widthToLeft += selfWidth + spacing;
    }
    
    return widthSum;
}

struct BoundingBox {
    float fMinX, fMinY, fMaxX, fMaxY;
    BoundingBox(float minX, float minY, float maxX, float maxY) {
        fMinX = minX;
        fMinY = minY;
        fMaxX = maxX;
        fMaxY = maxY;
    }

    ~BoundingBox() {
    }
};

cocos2d::Size getNodeBoundingBox(cocos2d::Node *node) {
    if (!node) {
        return cocos2d::Size::ZERO;
    }

    std::function<BoundingBox(cocos2d::Node *n, float scaleX, float scaleY)> getBounding = nullptr;
    getBounding = [&](cocos2d::Node *n, float scaleX, float scaleY) -> BoundingBox {
            auto box = n->getBoundingBox();
            auto anchor = n->getAnchorPoint();

            if (n->getChildrenCount() == 0) {
                return BoundingBox(box.size.width * -anchor.x * scaleX,
                                   box.size.height * -anchor.y * scaleY,
                                   box.size.width * (1 - anchor.x) * scaleX,
                                   box.size.height * (1 - anchor.y) * scaleY);
            } else {
                float minX = box.size.width * -anchor.x * scaleX;
                float minY = box.size.height * -anchor.y * scaleY;
                float maxX = box.size.width * (1 - anchor.x) * scaleX;
                float maxY = box.size.height * (1 - anchor.y) * scaleY;

                for (auto& v : n->getChildren()) {
                    auto res = getBounding(v, scaleX * n->getScaleX(), scaleY * n->getScaleY());
                    auto pos = v->getPosition();
                    auto _minX = res.fMinX + pos.x * n->getScaleX();
                    auto _minY = res.fMinY + pos.x * n->getScaleY();
                    auto _maxX = res.fMaxX + pos.x * n->getScaleX();
                    auto _maxY = res.fMaxY + pos.x * n->getScaleY();

                    if (_minX < minX) {
                        minX = _minX;
                    }

                    if (_minY < minY) {
                        minY = _minY;
                    }

                    if (_maxX > maxX) {
                        maxX = _maxX;
                    }

                    if (_maxY > maxY) {
                        maxY = _maxY;
                    }
                }

                return BoundingBox(minX, minY, maxX, maxY);
            }
        };

    auto res = getBounding(node, 1, 1);
    return cocos2d::Size(res.fMaxX - res.fMinX, res.fMaxY - res.fMinY);
}

cocos2d::Vec2 getVec2FromCCValue(const cocos2d::Value& val, int index, const cocos2d::Vec2& defaultVal) {
    switch (val.getType()) {
        case ccValueType::VECTOR:{
            auto vec = val.asValueVector();

            if (index >= vec.size()) {
                return defaultVal;
            }

            auto posValue = vec[index];
            return getVec2FromCCValue(posValue, defaultVal);
        }
        break;

        case ccValueType::MAP:{
            auto map = val.asValueMap();

            if (map.find("x") != map.end()) {
                return getVec2FromCCValue(val, defaultVal);
            }

            auto posValue = getTableIndexValue(val, index);
            return getVec2FromCCValue(posValue, defaultVal);
        }
        break;

        default:
            break;
    }
    return defaultVal;
}

float getFloatFromCCValue(const cocos2d::Value& val, int index, float defaultVal) {
    switch (val.getType()) {
        case ccValueType::VECTOR:{
            auto vec = val.asValueVector();

            if (index >= vec.size()) {
                return defaultVal;
            }

            auto posValue = vec[index];

            if (posValue.isNumber()) {
                return posValue.asFloat();
            }
        }
        break;

        case ccValueType::MAP:{
            auto intValue = getTableIndexValue(val, index);

            if (intValue.isNumber()) {
                return intValue.asFloat();
            }
        }
        break;

        default:

            if (val.isNumber()) {
                return val.asFloat();
            }

            break;
    }
    return defaultVal;
}
}
