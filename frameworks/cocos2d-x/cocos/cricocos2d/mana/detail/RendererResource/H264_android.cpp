/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/mana/detail/RendererResourceFactory.h"
#include "cricocos2d/mana/detail/util.h"
#include "cricocos2d/detail/log.h"

#include <list>


#if defined(XPT_TGT_ANDROID)


namespace
{
	namespace gl_util = cricocos2d::mana::detail::gl;

	const CriUint32 decode_buf_quantization_width  = 32;
	const CriUint32 decode_buf_quantization_height = 8;

	/***************************************************************************
	 *      Shader Definitions for YUV -> RGB (considering alpha channel)
	 ***************************************************************************/
	const GLchar* const frag_shader_rgb =
		R"*!(
#extension GL_OES_EGL_image_external : require
precision mediump float;
varying vec2 v_TexCoord;
varying vec4 v_Color;
uniform samplerExternalOES texture_ex;
void main()
{
  gl_FragColor = texture2D(texture_ex, v_TexCoord) * v_Color;;
}
)*!";

	const GLchar* const frag_shader_rgba =
		R"*!(
#extension GL_OES_EGL_image_external : require
precision mediump float;
varying vec2 v_TexCoord;
varying vec2 v_TexCoordAlpha;
varying vec4 v_Color;
uniform samplerExternalOES texture_ex;
uniform sampler2D texture_a;
void main()
{
  gl_FragColor = texture2D(texture_ex, v_TexCoord);
  gl_FragColor.w = texture2D(texture_a, v_TexCoordAlpha).x;
  gl_FragColor = gl_FragColor * v_Color;
}
)*!";

	struct ShaderLocation
	{
		gl_util::GenericShaderLocation generic;
		GLint						   texture_ex;
		GLint						   texture_a;
	};

	inline
	GLsizei movieWidthToTextureWidth(CriUint32 movie_width) CRICOCOS2D_NOEXCEPT
	{
		/* 動画サイズを、デコードバッファの最小単位の2倍に切り上げた値を取得 */
		CriUint32 quantized_width = (movie_width + (2 * decode_buf_quantization_width - 1)) & ~(2 * decode_buf_quantization_width - 1);

		/* テクスチャの作成 (テクスチャサイズはムービサイズを2のべき乗で切り上げ) */
		return cricocos2d::mana::detail::roundUpPowerOfTwo(quantized_width);
	}

	inline
	GLsizei movieHeightToTextureHeight(CriUint32 movie_height) CRICOCOS2D_NOEXCEPT
	{
		/* 動画サイズを、デコードバッファの最小単位の2倍に切り上げた値を取得 */
		CriUint32 quantized_height = (movie_height + (2 * decode_buf_quantization_height - 1)) & ~(2 * decode_buf_quantization_height - 1);

		/* テクスチャの作成 (テクスチャサイズはムービサイズを2のべき乗で切り上げ) */
		return cricocos2d::mana::detail::roundUpPowerOfTwo(quantized_height);
	}

	/***************************************************************************
	 *      Implementation of Renderer
	 ***************************************************************************/

	class RendererImpl
	{
	public:
		RendererImpl()
		{
			createShaders();
		}


		~RendererImpl()
		{
			cocos2d::GL::deleteProgram(shader_rgb_);
		}


		void createShaders()
		{
			{
				shader_rgb_ = gl_util::createShaderProgram(
					gl_util::generic_vertex_shader,
					frag_shader_rgb
														  );
				gl_util::getShaderLocation(
					shader_rgb_,
				{
					{shader_location_rgb_.generic.position, "a_Position"},
					{shader_location_rgb_.generic.texcoord, "a_TexCoord"},
					{shader_location_rgb_.generic.color, "a_Color"},
				},
				{
					{shader_location_rgb_.generic.mvp_matrix, "u_MVPMatrix"},
					{shader_location_rgb_.generic.movie_uv_transform, "u_MovieUvTransform"},
					{shader_location_rgb_.texture_ex, "texture_ex"},
				}
										  );
			}
			{
				shader_rgba_ = gl_util::createShaderProgram(
					gl_util::generic_vertex_shader_with_alpha,
					frag_shader_rgba
														  );
				gl_util::getShaderLocation(
					shader_rgba_,
				{
					{shader_location_rgba_.generic.position, "a_Position"},
					{shader_location_rgba_.generic.texcoord, "a_TexCoord"},
					{shader_location_rgba_.generic.color, "a_Color"},
				},
				{
					{shader_location_rgba_.generic.mvp_matrix, "u_MVPMatrix"},
					{shader_location_rgba_.generic.movie_uv_transform, "u_MovieUvTransform"},
					{shader_location_rgba_.generic.movie_uv_transform_alpha, "u_MovieUvTransformAlpha"},
					{shader_location_rgba_.texture_ex, "texture_ex"},
					{shader_location_rgba_.texture_a, "texture_a"},
				}
										  );
			}
		}


		void draw(
			const CriManaPlayerHn						mana_player,
			const GLint									texture_ex,
			const GLint									texture_a,
			const cocos2d::TrianglesCommand::Triangles& triangles,
			const cocos2d::Mat4&						mv,
			const cocos2d::Mat4&						movie_uv_transform,
			const cocos2d::Mat4&						movie_uv_transform_alpha,
			const bool									additive,
			const bool									use_alpha
				 ) const
		{
			if (use_alpha) {
				cocos2d::GL::useProgram(shader_rgba_);
				bindOESTextureAndSetUniform(0, texture_ex, shader_location_rgba_.texture_ex);
				gl_util::bindTextureAndSetUniform(1, texture_a, shader_location_rgba_.texture_a);
				gl_util::genericDrawMovieSpriteAlpha(triangles, mv, movie_uv_transform, movie_uv_transform_alpha, shader_location_rgba_.generic, use_alpha, additive);
			} else {
				cocos2d::GL::useProgram(shader_rgb_);
				bindOESTextureAndSetUniform(0, texture_ex, shader_location_rgb_.texture_ex);
				gl_util::genericDrawMovieSprite(triangles, mv, movie_uv_transform, shader_location_rgb_.generic, use_alpha, additive);
		}
    }


	private:
		static void bindOESTextureAndSetUniform(GLuint tex_unit, GLuint tex_obj, GLint uniform_loc)
		{
#    if (COCOS2D_VERSION <= 0x00030500)
			GLint tex_unit_mem;
			glGetIntegerv(GL_ACTIVE_TEXTURE, &tex_unit_mem);
			GLint tex_obj_mem;
			glGetIntegerv(GL_TEXTURE_BINDING_2D, &tex_obj_mem);
			// Activate and Bind texture
			glActiveTexture(GL_TEXTURE0 + tex_unit);
			glBindTexture(GL_TEXTURE_EXTERNAL_OES, tex_obj);
			// Restore activation and binded texture
			glActiveTexture((GLuint)tex_unit_mem);
			glBindTexture(GL_TEXTURE_2D, tex_obj_mem);
#    else
			// Set state cache, Activate and Bind texture
			cocos2d::GL::bindTextureN(tex_unit, tex_obj, GL_TEXTURE_EXTERNAL_OES);
#    endif
			CHECK_GL_ERROR_DEBUG();
		}


	private:
		GLuint		   shader_rgb_;
		ShaderLocation shader_location_rgb_;
		GLuint		   shader_rgba_;
		ShaderLocation shader_location_rgba_;
	};

	/***************************************************************************
	 *      Implementation of Renderer Resource
	 ***************************************************************************/

	class RendererResourceImpl
		: public cricocos2d::mana::detail::RendererResource
	{
	public:
		RendererResourceImpl(const CriManaMovieInfo& movie_info, CriManaPlayerHn mana_player, RendererImpl* renderer)
			: mana_player_(mana_player)
			, renderer_(renderer)
			, has_alpha_(movie_info.num_alpha_streams == 1)
		{}


		~RendererResourceImpl()
		{
			detachOESTexture();
			deleteGLTexture();
		}


		void attachToPlayer() override
		{
			is_attached_ = true;
			createOESTexture();
			criManaPlayer_SetGLTextureForJNIMediaCodec_ANDROID(mana_player_, oes_texture_);
		}


		void onSuspend()
		{
			detachOESTexture();
		}


		bool isSuitable(const CriManaMovieInfo& movie_info) const override
		{
			bool is_codec_suitable	   = movie_info.video_prm[0].codec_type == CRIMANA_VIDEO_CODEC_H264;
			return is_codec_suitable;
		}


		bool isPrepared() const override
		{
			// Immediately prepared
			return true;
		}


		bool isDrawable() const override
		{
			return is_drawable_ && isValidOESTexture();
		}


		void continuePreparation() override
		{
			// Do nothing
		}


		bool updateFrame(CriManaFrameInfo& frame_info) override
		{
			restoreOESTextureIfNeed();

			CriManaFrameInfo frame_info_tmp;
			if (criManaPlayer_ReferFrame(mana_player_, &frame_info_tmp)) {
				if (criManaPlayer_IsFrameOnTime(mana_player_, &frame_info_tmp)) {
					frame_info = frame_info_tmp;
					criManaPlayer_UpdateTextureImageForJNIMediaCodec_ANDROID(mana_player_);
					if (has_alpha_) {
						if (gl_texture_ == gl_util::invalid_texture) {
							a_width_ = movieWidthToTextureWidth(frame_info_tmp.image_info[1].line_pitch);
							a_height_ = movieHeightToTextureHeight(frame_info_tmp.image_info[1].num_lines);
							createGLTexture(1, a_width_, a_height_);

							movie_uv_transform_alpha_.m[0] = static_cast<float>(frame_info_tmp.disp_width - 1) / a_width_;
							movie_uv_transform_alpha_.m[5] = static_cast<float>(frame_info_tmp.disp_height - 1) / a_height_;
						}
						uploadTexture(1, gl_texture_, frame_info_tmp.image_info[1]);
					}
					criManaPlayer_DiscardFrame(mana_player_, &frame_info_tmp);

					const float disp_scale_u = (frame_info_tmp.disp_width  - 0.5f) / frame_info.width;
					const float disp_scale_v = (frame_info_tmp.disp_height - 0.5f) / frame_info.height;

					criManaPlayer_GetTransMatrixForJNIMediaCodec_ANDROID(mana_player_, movie_uv_transform_.m);

					movie_uv_transform_.m[12] += movie_uv_transform_.m[4];
					movie_uv_transform_.m[13] += movie_uv_transform_.m[5];
					movie_uv_transform_.m[14] += movie_uv_transform_.m[6];
					movie_uv_transform_.m[15] += movie_uv_transform_.m[7];

					movie_uv_transform_.m[0]  *= disp_scale_u;
					movie_uv_transform_.m[1]  *= disp_scale_u;
					movie_uv_transform_.m[2]  *= disp_scale_u;
					movie_uv_transform_.m[3]  *= disp_scale_u;

					movie_uv_transform_.m[4]  *= -disp_scale_v;
					movie_uv_transform_.m[5]  *= -disp_scale_v;
					movie_uv_transform_.m[6]  *= -disp_scale_v;
					movie_uv_transform_.m[7]  *= -disp_scale_v;

					is_drawable_			   = true;
					return true;
				}
			}
			return false;
		}


		void draw(
			const cocos2d::TrianglesCommand::Triangles& triangles,
			const cocos2d::Mat4&						mv,
			const bool									additive
				 ) override
		{
			restoreOESTextureIfNeed();

			if (isDrawable()) {
				renderer_->draw(mana_player_, oes_texture_, gl_texture_, triangles, mv, movie_uv_transform_, movie_uv_transform_alpha_, additive, has_alpha_);
			}
		}


	private:
		void createOESTexture()
		{
			if (!isValidOESTexture()) {
				glGenTextures(1, &oes_texture_);
				CHECK_GL_ERROR_DEBUG();
				setupOESTexture(0, oes_texture_);
			}
		}


		void  createGLTexture(const GLuint tex_unit, const GLsizei width, const GLsizei height)
		{
			glGenTextures(tex_unit, &gl_texture_);
			CHECK_GL_ERROR_DEBUG();
			setupGLTexture(tex_unit, gl_texture_, width, height);
		}


		void deleteGLTexture()
		{
			if (gl_texture_ != gl_util::invalid_texture) {
				cocos2d::GL::deleteTexture(gl_texture_);
				gl_texture_ = gl_util::invalid_texture;
			}
		}

		void detachOESTexture()
		{
			if (isValidOESTexture()) {
				criManaPlayer_DetachFromGLContextForJNIMediaCodec_ANDROID(mana_player_); // In this call, textures will be deleted
				oes_texture_ = gl_util::invalid_texture;
			}
		}


		void setupOESTexture(const GLuint tex_unit, const GLuint tex_obj)
		{
			/* EXTERNAL_OESテクスチャは、テクスチャユニットに関連付いて生成される模様 */
#    if (COCOS2D_VERSION <= 0x00030500)
			GLint tex_unit_mem;
			glGetIntegerv(GL_ACTIVE_TEXTURE, &tex_unit_mem);
			GLint tex_obj_mem;
			glGetIntegerv(GL_TEXTURE_BINDING_2D, &tex_obj_mem);
			// Activate texture unit
			glActiveTexture(GL_TEXTURE0 + tex_unit);
			// Bind texture to GL_TEXTURE_EXTERNAL_OES
			glBindTexture(GL_TEXTURE_EXTERNAL_OES, tex_obj);
			glTexParameterf(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
			glTexParameterf(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
			glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
			glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
			// Restore activation and binded texture
			glActiveTexture((GLuint)tex_unit_mem);
			glBindTexture(GL_TEXTURE_2D, tex_obj_mem);
#    else
			// Set state cache, Activate and Bind texture
			cocos2d::GL::bindTextureN(tex_unit, tex_obj, GL_TEXTURE_EXTERNAL_OES);
			glTexParameterf(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
			glTexParameterf(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
			glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
			glTexParameteri(GL_TEXTURE_EXTERNAL_OES, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
#    endif
			CHECK_GL_ERROR_DEBUG();
		}


		void setupGLTexture(const GLuint tex_unit, const GLuint tex_obj, const GLsizei width, const GLsizei height)
		{
			cocos2d::GL::activeTexture(GL_TEXTURE0 + tex_unit);
			cocos2d::GL::bindTexture2DN(tex_unit, tex_obj);
			glTexParameterf(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
			glTexParameterf(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
			glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
			glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
			glTexImage2D(GL_TEXTURE_2D, 0, GL_LUMINANCE, width, height, 0, GL_LUMINANCE, GL_UNSIGNED_BYTE, nullptr);
			CHECK_GL_ERROR_DEBUG();
		}


		static void uploadTexture(const GLuint tex_unit, const GLuint tex_obj, const CriManaImageBufferInfo& bufinfo)
		{
			cocos2d::GL::activeTexture(GL_TEXTURE0 + tex_unit);
			cocos2d::GL::bindTexture2DN(tex_unit, tex_obj);
			glTexSubImage2D(GL_TEXTURE_2D, 0, 0, 0, bufinfo.line_pitch, bufinfo.num_lines,
							GL_LUMINANCE, GL_UNSIGNED_BYTE, bufinfo.imageptr);
			CHECK_GL_ERROR_DEBUG();
		}


		void restoreOESTextureIfNeed()
		{
			if (is_attached_ && !isValidOESTexture()) {
				createOESTexture();
				criManaPlayer_AttachToGLContextForJNIMediaCodec_ANDROID(mana_player_, oes_texture_);
			}
		}


		bool isValidOESTexture() const noexcept
		{
			return oes_texture_ != gl_util::invalid_texture;
		}


	private:
		CriManaPlayerHn mana_player_;
		RendererImpl*	renderer_;
		cocos2d::Mat4	movie_uv_transform_;
		cocos2d::Mat4	movie_uv_transform_alpha_;
		GLuint			oes_texture_ = gl_util::invalid_texture;
		GLuint			gl_texture_ = gl_util::invalid_texture;
		bool			is_attached_ = false;
		bool			is_drawable_ = false;
		bool			has_alpha_ = false;
		GLsizei			a_width_;
		GLsizei			a_height_;
		float			alpha_scale_u_;
		float			alpha_scale_v_;
	};

	/***************************************************************************
	 *      Implementation of Renderer Resource Factory
	 ***************************************************************************/

	class RendererResourceFactoryImpl
		: public cricocos2d::mana::detail::RendererResourceFactory
	{
	public:
		~RendererResourceFactoryImpl()
		{
			CC_ASSERT(renderer_resources_.empty());
		}


		std::shared_ptr<cricocos2d::mana::detail::RendererResource> createRenderResource(
			const CriManaMovieInfo& movie_info,
			const CriManaPlayerHn	mana_player
																						) override
		{
			bool is_codec_suitable = movie_info.video_prm[0].codec_type == CRIMANA_VIDEO_CODEC_H264;
			if (!is_codec_suitable) {
				return nullptr;
			}

			std::unique_ptr<RendererResourceImpl> renderer_resource_u(new RendererResourceImpl(movie_info, mana_player, std::addressof(renderer_)));
			auto								  iter = renderer_resources_.insert(std::end(renderer_resources_), renderer_resource_u.get());
			std::shared_ptr<RendererResourceImpl> renderer_resource(
				renderer_resource_u.release(),
				[this, iter](RendererResourceImpl* p)
			{
				renderer_resources_.erase(iter);
				delete p;
			}
																   );
			return renderer_resource;
		}


		void onSuspend() override
		{
			for (auto renderer_resource : renderer_resources_) {
				renderer_resource->onSuspend();
			}
		}


		void onResume() override
		{}


		void onRendererRecreated() override
		{
			renderer_.createShaders();
		}


	private:
		RendererImpl					 renderer_;
		std::list<RendererResourceImpl*> renderer_resources_;
	};
}


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactoryH264Android()
			{
				return std::make_shared<RendererResourceFactoryImpl>();
			}


		}
	}
}


#endif
