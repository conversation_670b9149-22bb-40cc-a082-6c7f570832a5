#!/usr/bin/python

from GlobalCfg import *
from optparse import OptionParser
import sys
import os

compile_cmd_template = '''%s -j8 -C %s/frameworks/runtime-src/proj.android-studio/app NDK_DEBUG=%d \
NDK_MODULE_PATH=%s/frameworks/cocos2d-x:\
%s/frameworks/cocos2d-x/cocos:\
%s/frameworks/cocos2d-x/external:\
%s/frameworks/cocos2d-x/cocos/scripting'''

def verify_mode(mode):
    return mode == 'debug' or mode == 'release' or mode == 'all'

def compile(mode):
	compile_mode = 1 if mode == 'debug' else 0
	cmd = compile_cmd_template % (ndk_path, proj_path, compile_mode, proj_path, proj_path, proj_path, proj_path)
	print(cmd)
	os.system(cmd)

	if os.path.exists('app/src/main/jniLibs-%s/' % mode):
		os.system('rm -fr app/src/main/jniLibs-%s/' % mode)

	os.mkdir('app/src/main/jniLibs-%s' % mode)

	os.system('mv app/libs/x86 app/src/main/jniLibs-%s/' % mode)
	os.system('mv app/libs/armeabi-v7a app/src/main/jniLibs-%s/' % mode)
	os.system('mv app/libs/arm64-v8a app/src/main/jniLibs-%s/' % mode)

def main():
    parser = OptionParser()
    parser.add_option('-m', '--mode', default='debug', dest='mode', help='mode, debug or release')

    os.chdir(os.path.join(proj_path, 'frameworks/runtime-src/proj.android-studio'))

    (opts, args) = parser.parse_args()
    if not verify_mode(opts.mode):
        print('invalid mode, debug or release or all please')
        return 1

    if opts.mode == 'all':
        compile('debug')
        compile('release')
    else:
        compile(opts.mode)
main()
