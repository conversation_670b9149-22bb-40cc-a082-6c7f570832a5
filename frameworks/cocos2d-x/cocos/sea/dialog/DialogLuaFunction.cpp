//
//  DialogLuaFunction.cpp
//  cocos2d_libs
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/1/23.
//

#include "components/DialogComponentsHeader.h"
#include "Dialog.h"
#include "DialogHelper.h"
#include "scripting/lua-bindings/manual/CCLuaEngine.h"
#include "scripting/lua-bindings/manual/LuaBasicConversions.h"
#include "sea/common/SeaCommon.h"

#include "scripting/lua-bindings/manual/LuaBasicConversions.h"
#include "sea/dialog/components/DialogComponentsHeader.h"

#ifdef __cplusplus
extern "C" {
#endif
#include "scripting/lua-bindings/manual/tolua_fix.h"
#ifdef __cplusplus
}
#endif

static std::unordered_map<std::string, int> sLuaFunMap;

int lua_sea_Dialog_functionList(lua_State *tolua_S, int handler, int returnCount = 1) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;
    
    if (true) {
        toluafix_get_function_by_refid(tolua_S, handler);
//        auto count = lua_gettop(tolua_S);
        lua_insert(tolua_S, 1);
//        auto count1 = lua_gettop(tolua_S);
        lua_settop(tolua_S, argc + 2);
//        auto count2 = lua_gettop(tolua_S);
        if (lua_isfunction(tolua_S, 1)) {
            if (LUA_OK == lua_pcall(tolua_S, argc + 1, returnCount, 0)) {
//                CCLOG("Angus 成功");
//                luaval_to_ccvalue_new(L, lua_gettop(L), &ret, "Global::excuteLuaFunction");
            }
#if COCOS2D_DEBUG >= 1
            else{
                CCLOG("[LUA ERROR] %s", lua_tostring(tolua_S, -1)); /* L: ... error */
                const char *errst = lua_tostring(tolua_S, -1);
                CCLOG("[LUA ERROR] %s", errst);               /* L: ... error */
                
                std::stringstream ss;
                ss << "[LUA ERROR]" << errst;
                sea::showMessageBox(ss.str(), "执行Lua程序失败！");
                lua_pop(tolua_S, 1); // remove error message from stack
            }
#endif
        }
        return 1;
    }

    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getConfig'.", &tolua_err);
#endif
    
    return 0;
}

int lua_sea_Dialog_functionList_hide(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("hide");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_show(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("show");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_enableContentTouchMask(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("enableContentTouchMask");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_hideMask(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("hideMask");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_loadControls(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("loadControls");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_getVisibleSize(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("getVisibleSize");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_showMask(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("showMask");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_adaptChromeScale(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("adaptChromeScale");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_registerLanguage(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("registerLanguage");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_onExit(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("onExit");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_onEnter(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("onEnter");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setPushPopData(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setPushPopData");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_changeLanguage(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("changeLanguage");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setPopupDialog(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setPopupDialog");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setShowCall(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setShowCall");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_getShowCall(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("getShowCall");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setHideCallback(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setHideCallback");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_getHideCallback(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("getHideCallback");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_isNoKeyBoard(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("isNoKeyBoard");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_checkShowForHorizontal(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("checkShowForHorizontal");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_showQuickly(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("showQuickly");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_fadeMask(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("fadeMask");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_pauseForMask(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("pauseForMask");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_resumeForMask(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("resumeForMask");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_getIsMaxScreen(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("getIsMaxScreen");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setNotMaxScreen(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setNotMaxScreen");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_deletePopupDialog(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("deletePopupDialog");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_getIsPopupEsc(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("getIsPopupEsc");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setIsPopupDialog(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setIsPopupDialog");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_checkOnlyHorizontal(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("checkOnlyHorizontal");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_isTestVerticalVersion(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("isTestVerticalVersion");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setTapAnywhereToHide(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setTapAnywhereToHide");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setMaskSize(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setMaskSize");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}

int lua_sea_Dialog_functionList_setMaskOpacity(lua_State *tolua_S) {
    auto iter = sLuaFunMap.find("setMaskOpacity");
    if (iter == sLuaFunMap.end()) {
        return 0;
    }
    return lua_sea_Dialog_functionList(tolua_S, iter->second);
}


namespace sea {
void Dialog::registerLuaFunctionList(const cocos2d::ValueMap& map) {
    cocos2d::LuaStack *stack = cocos2d::LuaEngine::getInstance()->getLuaStack();
    auto L = stack->getLuaState();
    
    lua_pushstring(L, "sea.Dialog");
    lua_rawget(L, LUA_REGISTRYINDEX);
    if (lua_istable(L,-1))
    {
        for (auto & iter : map) {
            std::string key = iter.first;
            auto funValue = iter.second;
            if (funValue.getType() != ccValueType::LUA_FUNCTION) {
                continue;
            }
            auto funHandler = funValue.asLuaFunction().hander;
            sLuaFunMap[key] = funHandler;
            
            if (key == "hide") {
                tolua_function(L, "hide", lua_sea_Dialog_functionList_hide);
            }
            else if (key == "show") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_show);
            }
            else if (key == "enableContentTouchMask") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_enableContentTouchMask);
            }
            else if (key == "hideMask") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_hideMask);
            }
            else if (key == "loadControls") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_loadControls);
            }
            else if (key == "getVisibleSize") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_getVisibleSize);
            }
            else if (key == "showMask") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_showMask);
            }
            else if (key == "adaptChromeScale") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_adaptChromeScale);
            }
            else if (key == "registerLanguage") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_registerLanguage);
            }
            else if (key == "onExit") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_onExit);
            }
            else if (key == "onEnter") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_onEnter);
            }
            else if (key == "setPushPopData") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setPushPopData);
            }
            else if (key == "changeLanguage") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_changeLanguage);
            }
            else if (key == "setPopupDialog") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setPopupDialog);
            }
            else if (key == "setShowCall") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setShowCall);
            }
            else if (key == "getShowCall") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_getShowCall);
            }
            else if (key == "setHideCallback") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setHideCallback);
            }
            else if (key == "getHideCallback") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_getHideCallback);
            }
            else if (key == "isNoKeyBoard") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_isNoKeyBoard);
            }
            else if (key == "checkShowForHorizontal") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_checkShowForHorizontal);
            }
            else if (key == "showQuickly") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_showQuickly);
            }
            else if (key == "fadeMask") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_fadeMask);
            }
            else if (key == "pauseForMask") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_pauseForMask);
            }
            else if (key == "resumeForMask") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_resumeForMask);
            }
            else if (key == "getIsMaxScreen") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_getIsMaxScreen);
            }
            else if (key == "setNotMaxScreen") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setNotMaxScreen);
            }
            else if (key == "deletePopupDialog") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_deletePopupDialog);
            }
            else if (key == "getIsPopupEsc") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_getIsPopupEsc);
            }
            else if (key == "setIsPopupDialog") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setIsPopupDialog);
            }
            else if (key == "checkOnlyHorizontal") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_checkOnlyHorizontal);
            }
            else if (key == "isTestVerticalVersion") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_isTestVerticalVersion);
            }
            else if (key == "setTapAnywhereToHide") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setTapAnywhereToHide);
            }
            else if (key == "setMaskSize") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setMaskSize);
            }
            else if (key == "setMaskOpacity") {
                tolua_function(L, key.c_str(), lua_sea_Dialog_functionList_setMaskOpacity);
            }
        }
    }
    lua_pop(L, 1);
}

}
