/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/mana/detail/util.h"

#include "cricocos2d/detail/log.h"


const GLchar* const cricocos2d::mana::detail::gl::generic_vertex_shader =
	R"*!(
#ifndef GL_ES
#define mediump
#define lowp
#endif

attribute vec4 a_Position;
attribute vec2 a_TexCoord;
attribute vec4 a_Color;
uniform   mat4 u_MVPMatrix;
uniform   mat4 u_MovieUvTransform;

varying   mediump vec2 v_TexCoord;
varying   lowp vec4    v_Color;

void main()
{
    gl_Position = u_MVPMatrix * a_Position;
    v_TexCoord  = (u_MovieUvTransform * vec4(a_TexCoord, 0.0, 1.0)).xy;
    v_Color     = a_Color;
}
)*!";

const GLchar* const cricocos2d::mana::detail::gl::generic_vertex_shader_with_alpha =
	R"*!(
#ifndef GL_ES
#define mediump
#define lowp
#endif

attribute vec4 a_Position;
attribute vec2 a_TexCoord;
attribute vec4 a_Color;
uniform   mat4 u_MVPMatrix;
uniform   mat4 u_MovieUvTransform;
uniform   mat4 u_MovieUvTransformAlpha;

varying   mediump vec2 v_TexCoord;
varying   mediump vec2 v_TexCoordAlpha;
varying   lowp vec4    v_Color;

void main()
{
    gl_Position = u_MVPMatrix * a_Position;
    v_TexCoord  = (u_MovieUvTransform * vec4(a_TexCoord, 0.0, 1.0)).xy;
    v_TexCoordAlpha  = (u_MovieUvTransformAlpha * vec4(a_TexCoord, 0.0, 1.0)).xy;
    v_Color     = a_Color;
}
)*!";

GLuint cricocos2d::mana::detail::gl::createShaderProgram(const GLchar* vs_src, const GLchar* fs_src)
{
	auto compile = [](const char* src, GLenum shader_type)
				   {
					   GLuint shader = glCreateShader(shader_type);
					   glShaderSource(shader, 1, &src, nullptr);
					   glCompileShader(shader);
					   GLint  compile_status;
					   glGetShaderiv(shader, GL_COMPILE_STATUS, &compile_status);
					   if (compile_status != GL_TRUE) {
						   char	   log[512];
						   GLsizei log_size;
						   glGetShaderInfoLog(shader, sizeof(log), &log_size, log);
						   if (log_size > 0) {
							   cricocos2d::detail::printLog(cricocos2d::detail::LogSeverity::Error, log);
						   }
						   CC_ASSERT(compile_status == GL_TRUE);
					   }
					   return shader;
				   };

	/* バーテックスシェーダをコンパイル */
	GLuint vert_shader = compile(vs_src, GL_VERTEX_SHADER);

	/* フラグメントシェーダをコンパイル */
	GLuint frag_shader = compile(fs_src, GL_FRAGMENT_SHADER);

	/* シェーダプログラムの作成 */
	GLuint program = glCreateProgram();
	glAttachShader(program, vert_shader);
	glAttachShader(program, frag_shader);
	CHECK_GL_ERROR_DEBUG();

	/* シェーダオブジェクトの削除 */
	glDeleteShader(vert_shader);
	CHECK_GL_ERROR_DEBUG();
	glDeleteShader(frag_shader);
	CHECK_GL_ERROR_DEBUG();

	/* シェーダプログラムのリンク */
	glLinkProgram(program);
	GLint res_link;
	glGetProgramiv(program, GL_LINK_STATUS, &res_link);
	CHECK_GL_ERROR_DEBUG();

	return program;
}


void cricocos2d::mana::detail::gl::getShaderLocation(
	GLuint shader,
	std::initializer_list<std::pair<GLint&, const char*>> attributes,
	std::initializer_list<std::pair<GLint&, const char*>> uniforms
													)
{
	for (auto const& x : attributes) {
		x.first = glGetAttribLocation(shader, x.second);
		CC_ASSERT(x.first != -1);
	}
	for (auto const& x : uniforms) {
		x.first = glGetUniformLocation(shader, x.second);
		CC_ASSERT(x.first != -1);
	}
}


void cricocos2d::mana::detail::gl::bindTextureAndSetUniform(GLuint tex_unit, GLuint tex_obj, GLint uniform_loc)
{
	cocos2d::GL::bindTexture2DN(tex_unit, tex_obj);
	glUniform1i(uniform_loc, static_cast<GLint>(tex_unit));
	CHECK_GL_ERROR_DEBUG();
}


void cricocos2d::mana::detail::gl::genericDrawMovieSprite(
	const cocos2d::TrianglesCommand::Triangles& triangles,
	const cocos2d::Mat4&						mv,
	const cocos2d::Mat4&						movie_uv_transform,
	const GenericShaderLocation&				location,
	bool										alpha,
	bool										additive
														 )
{
	/* ブレンドモードを設定 */
	if (alpha) {
		if (additive) {
			cocos2d::GL::blendFunc(GL_ONE, GL_ONE_MINUS_SRC_ALPHA);
		} else {
			cocos2d::GL::blendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
		}
	} else {
		if (additive) {
			cocos2d::GL::blendFunc(GL_SRC_ALPHA, GL_ONE);
		} else {
			cocos2d::GL::blendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
		}
	}

	/* mvp_matrix をセット */
	{
		auto const& p_matrix   = cocos2d::Director::getInstance()->getMatrix(cocos2d::MATRIX_STACK_TYPE::MATRIX_STACK_PROJECTION);
		auto		mvp_matrix = p_matrix * mv;
		glUniformMatrix4fv(location.mvp_matrix, 1, GL_FALSE, mvp_matrix.m);
		CHECK_GL_ERROR_DEBUG();
	}

	/* movie_uv_transform をセット */
	{
		glUniformMatrix4fv(location.movie_uv_transform, 1, GL_FALSE, movie_uv_transform.m);
		CHECK_GL_ERROR_DEBUG();
	}

	/* 頂点を有効化 */
	cocos2d::GL::enableVertexAttribs(cocos2d::GL::VERTEX_ATTRIB_FLAG_POS_COLOR_TEX);
	CHECK_GL_ERROR_DEBUG();

	/* 頂点情報を設定 */
	glVertexAttribPointer(location.position, 3, GL_FLOAT, GL_FALSE, sizeof(cocos2d::V3F_C4B_T2F), (GLvoid*)&triangles.verts[0].vertices);
	glVertexAttribPointer(location.texcoord, 2, GL_FLOAT, GL_FALSE, sizeof(cocos2d::V3F_C4B_T2F), (GLvoid*)&triangles.verts[0].texCoords);
	glVertexAttribPointer(location.color, 4, GL_UNSIGNED_BYTE, GL_TRUE, sizeof(cocos2d::V3F_C4B_T2F), (GLvoid*)&triangles.verts[0].colors);
	CHECK_GL_ERROR_DEBUG();

	/* 描画実行 */
	glDrawElements(GL_TRIANGLES, (GLsizei)triangles.indexCount, GL_UNSIGNED_SHORT, triangles.indices);
	CHECK_GL_ERROR_DEBUG();

}

void cricocos2d::mana::detail::gl::genericDrawMovieSpriteAlpha(
	const cocos2d::TrianglesCommand::Triangles& triangles,
	const cocos2d::Mat4&						mv,
	const cocos2d::Mat4&						movie_uv_transform,
	const cocos2d::Mat4&						movie_uv_transform_alpha,
	const GenericShaderLocation&				location,
	bool										alpha,
	bool										additive
														 )
{
	CC_ASSERT(alpha);
	/* ブレンドモードを設定 */
	if (additive) {
		cocos2d::GL::blendFunc(GL_ONE, GL_ONE_MINUS_SRC_ALPHA);
	} else {
		cocos2d::GL::blendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
	}

	/* mvp_matrix をセット */
	{
		auto const& p_matrix   = cocos2d::Director::getInstance()->getMatrix(cocos2d::MATRIX_STACK_TYPE::MATRIX_STACK_PROJECTION);
		auto		mvp_matrix = p_matrix * mv;
		glUniformMatrix4fv(location.mvp_matrix, 1, GL_FALSE, mvp_matrix.m);
		CHECK_GL_ERROR_DEBUG();
	}

	/* movie_uv_transform をセット */
	{
		glUniformMatrix4fv(location.movie_uv_transform, 1, GL_FALSE, movie_uv_transform.m);
		CHECK_GL_ERROR_DEBUG();
	}
	{
		glUniformMatrix4fv(location.movie_uv_transform_alpha, 1, GL_FALSE, movie_uv_transform_alpha.m);
		CHECK_GL_ERROR_DEBUG();
	}

	/* 頂点を有効化 */
	cocos2d::GL::enableVertexAttribs(cocos2d::GL::VERTEX_ATTRIB_FLAG_POS_COLOR_TEX);
	CHECK_GL_ERROR_DEBUG();

	/* 頂点情報を設定 */
	glVertexAttribPointer(location.position, 3, GL_FLOAT, GL_FALSE, sizeof(cocos2d::V3F_C4B_T2F), (GLvoid*)&triangles.verts[0].vertices);
	glVertexAttribPointer(location.texcoord, 2, GL_FLOAT, GL_FALSE, sizeof(cocos2d::V3F_C4B_T2F), (GLvoid*)&triangles.verts[0].texCoords);
	glVertexAttribPointer(location.color, 4, GL_UNSIGNED_BYTE, GL_TRUE, sizeof(cocos2d::V3F_C4B_T2F), (GLvoid*)&triangles.verts[0].colors);
	CHECK_GL_ERROR_DEBUG();

	/* 描画実行 */
	glDrawElements(GL_TRIANGLES, (GLsizei)triangles.indexCount, GL_UNSIGNED_SHORT, triangles.indices);
	CHECK_GL_ERROR_DEBUG();
}