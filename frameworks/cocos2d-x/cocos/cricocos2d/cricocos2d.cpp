/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/cricocos2d.h"

#include "cricocos2d/detail/log.h"

#include "cri_error.h"

#include <cstdlib>


namespace cricocos2d
{
	namespace
	{
		void* defaultAlloc(void* obj, CriUint32 size)
		{
			return std::malloc(size);
		};


		void defaultFree(void* obj, void* ptr)
		{
			std::free(ptr);
		};
	}
}


namespace cricocos2d
{
	Allocator getDefaultAllocator() CRICOCOS2D_NOEXCEPT
	{
		return {defaultAllo<PERSON>, defaultFree, nullptr};
	}


	void defaultErrorCallback(const CriChar8* errid, CriUint32 p1, CriUint32 p2, CriUint32* parray)
	{
		const CriChar8* errmsg = criErr_ConvertIdToMessage(errid, p1, p2);
		cricocos2d::detail::printLog(cricocos2d::detail::LogSeverity::Error, errmsg);
	}


	std::string getResourcePath(std::string path)
	{
#if defined(XPT_TGT_MACOSX) || defined(XPT_TGT_IOS) || defined(XPT_TGT_PC)
		return cocos2d::FileUtils::getInstance()->fullPathForFilename(path);
#elif defined(XPT_TGT_ANDROID)
		// 'Resources' directory corresponds to 'assets' directory in Android project.
		// CRI Filesystem accesses files in 'assets' directory by relative path from 'assets' directory.
		// "assets/res/res/Basic.acb" "/sdcard/testlua/res/res/NewProject.acf"
        // "/data/user/0/com.bole.testlua.criware/files/"
        // "assets/res/" "assets/src/" "assets/"
        // "/sdcard/testlua/res/"
        std::string filePath = cocos2d::FileUtils::getInstance()->fullPathForFilename(path);
        if (filePath.size() >= 7 && filePath.substr(0, 7).compare("assets/") == 0)
        {
            filePath = filePath.substr(7, filePath.size()); // 是 "assets/"
        }
		return filePath;
#endif
	}


}
