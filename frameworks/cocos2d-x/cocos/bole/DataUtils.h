#ifndef __DATAUTILS_H__
#define __DATAUTILS_H__

#include "cocos2d.h"
#include "scripting/lua-bindings/manual/CCLuaValue.h"

#if (CC_TARGET_PLATFORM != CC_PLATFORM_WIN32 && CC_TARGET_PLATFORM != CC_PLATFORM_WP8)
typedef long long   LONG;
#endif

#define SOCKET_HEAD_LENGTH		4

#define DUMP_HEAD_LENGTH(out,len) { \
out[0] = (unsigned char)(len >> 24); \
out[1] = (unsigned char)(len >> 16); \
out[2] = (unsigned char)(len >> 8); \
out[3] = (unsigned char)(len ); \
}

#define LOAD_HEAD_LENGTH(in) { \
(unsigned char)(in[0] << 24) | (unsigned char)(in[1] << 16) | (unsigned char)(in[2] << 8) | (unsigned char)(in[3]) \
}

class DataUtils
{
public:
	static void dump(int value,unsigned char* out,int *len);
	static void dump(LONG value,unsigned char* out,int *len);
    static void dump(float value,unsigned char* out,int *len);
    static void dump(double value,unsigned char* out,int *len);
	static void dump(unsigned char value[], int srcLen, unsigned char* out,int *len);
	static void dump(int value[], int srcLen, unsigned char* out,int *len);
	static void dump(std::string str,unsigned char* out,int *len);
    static void autoReSize(unsigned char **cache, int pos, int addSize, const char *tag = "nothing");
    static void dump(cocos2d::Value &pObject,unsigned char **out,int *len);
    
    static cocos2d::LuaValue* loadLuaValue(unsigned char *in, int *outLen);
    static void* convertCppValueToLuaValue(void*);

	static cocos2d::Value load(unsigned char *in, int *outLen);
    static std::string md5(void *data, unsigned long size);
private:
	static cocos2d::Value loadContent(unsigned char **in, int * outLen);
};
	
#endif
