//
//  BLDownload.cpp
//  cocos2d_libs
//
//  Created by wodes on 14-9-23.
//
//

#include "BLDownload.h"
#include "BoleSocket.h"
#include "../base/CCScriptSupport.h"
#include "BLConfig.h"
#include <sys/stat.h>
NS_CC_BEGIN

using namespace std;

namespace network {
    
    BLDownload* BLDownload::instance = nullptr;
    
    BLDownload* BLDownload::getInstance()
    {
       
        if (!instance) {
            instance = new BLDownload();
            instance->init();
        }
        return instance;
    }

    BLDownload::BLDownload()
    : _responseCode(-1)
    , _responseHeader("")
    , _responseContentType("")
    , _isSuccess(false) {
        _filterVector.clear();
    }
    
    void BLDownload::init() {
        string path = FileUtils::getInstance()->getWritablePath();
        string filePath = path + BLConfig::getInstance()->getConfig("cachedir"); //getCacheDir();
		FileUtils::getInstance()->createDirectory(filePath.c_str());
        FileUtils::getInstance()->skipiCloudBackup(filePath);
        //mkdir(filePath.c_str(), S_IRWXU | S_IRWXG | S_IRWXO);
        FileUtils::getInstance()->addSearchPath(filePath);
    }

    void BLDownload::addFilter(std::string filterStr)
    {
        _filterVector.push_back(filterStr);
    }

    void BLDownload::clearFilter()
    {
        _filterVector.clear();
    }
    
    void BLDownload::response(HttpClient *client, HttpResponse *response) {
        if(response->isSucceed())
           CCLOG("bldownload responsed success");
        else
           CCLOG("boldownload failed");

        std::vector<char> *headers = response->getResponseHeader();
        std::string header(headers->begin(), headers->end());
        _responseHeader = header;
        std::vector<char> *contentTypeVector = response->getResponseContentType();
        std::string contentTypeStr(contentTypeVector->begin(), contentTypeVector->end());
        _responseContentType = contentTypeStr;
        _responseCode = response->getResponseCode();
        _isSuccess = response->isSucceed();

        const char* name = response->getHttpRequest()->getTag();
        Value vv(name);
        CCLOG("tag is %s",name);
        BLMessage* msg = new BLMessage(BL_WS_MSG_TO_UITHREAD_DOWNLOAD,ScriptEngineManager::getInstance()->getScriptEngine()->convertCppValueToLuaValue(&vv));
        BasicScriptData data(this, msg);
        ScriptEvent scriptEvent(kBLSocketEvent, &data);
        ScriptEngineManager::getInstance()->getScriptEngine()->sendEvent(&scriptEvent);
//        msg->release();
    }
    
    void BLDownload::download(const std::string& url, const std::string& tag) {
        download(url, tag,  HttpRequest::Type::GET);
    }
    
//    void BLDownload::download(const std::string& server, const std::string& name, HttpRequest::Type type) {
//        string  url = server;
//        url += "/";
//        url += name;
//        download(url, type);
//    }

    void BLDownload::download(const std::string& url, const std::string& tag, HttpRequest::Type type) {
        HttpRequest *req = new HttpRequest();
        req->setUrl(url.c_str());
        req->setRequestType(type);
        req->setTag(tag.c_str());
        req->setCache(true);
        req->setCacheFilters(_filterVector);
        req->setResponseCallback([this](HttpClient* client, HttpResponse* response){
            this->response(client, response);
        });
        HttpClient::getInstance()->send(req);
        req->release();
    }

}
NS_CC_END