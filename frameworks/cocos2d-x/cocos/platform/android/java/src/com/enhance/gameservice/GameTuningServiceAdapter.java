package com.enhance.gameservice;

import android.content.Context;

/**
 * Adapter class that implements IGameTuningService interface
 * but uses Messenger for cross-process communication internally
 * This allows existing code to work without changes
 */
public class GameTuningServiceAdapter implements IGameTuningService {
    private GameTuningClient mClient;
    
    public GameTuningServiceAdapter(Context context) {
        mClient = new GameTuningClient(context);
    }
    
    public boolean bindService() {
        return mClient.bindService();
    }
    
    public void unbindService() {
        mClient.unbindService();
    }
    
    @Override
    public int setPreferredResolution(int resolution) {
        return mClient.setPreferredResolution(resolution);
    }
    
    @Override
    public int setFramePerSecond(int fps) {
        return mClient.setFramePerSecond(fps);
    }
    
    @Override
    public int boostUp(int seconds) {
        return mClient.boostUp(seconds);
    }
    
    @Override
    public int getAbstractTemperature() {
        return mClient.getAbstractTemperature();
    }
    
    @Override
    public int setGamePowerSaving(boolean enable) {
        return mClient.setGamePowerSaving(enable);
    }
}
