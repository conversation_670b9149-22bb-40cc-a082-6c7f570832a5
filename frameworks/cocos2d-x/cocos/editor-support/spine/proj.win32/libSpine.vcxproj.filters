﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Animation.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\AnimationState.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\AnimationStateData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Atlas.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\AtlasAttachmentLoader.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Attachment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\AttachmentLoader.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\AttachmentVertices.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Bone.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\BoneData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\BoundingBoxAttachment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Cocos2dAttachmentLoader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Event.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\EventData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\extension.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\IkConstraint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\IkConstraintData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Json.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\MeshAttachment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\PathAttachment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\PathConstraint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\PathConstraintData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\RegionAttachment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Skeleton.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonAnimation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonBatch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonBinary.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonBounds.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonJson.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SkeletonRenderer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Skin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\Slot.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\SlotData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\spine-cocos2dx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\TransformConstraint.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\TransformConstraintData.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\VertexAttachment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Animation.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\AnimationState.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\AnimationStateData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Atlas.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\AtlasAttachmentLoader.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Attachment.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\AttachmentLoader.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\AttachmentVertices.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Bone.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\BoneData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\BoundingBoxAttachment.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Cocos2dAttachmentLoader.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Event.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\EventData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\extension.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\IkConstraint.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\IkConstraintData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Json.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\MeshAttachment.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\PathAttachment.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\PathConstraint.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\PathConstraintData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\RegionAttachment.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Skeleton.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonAnimation.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonBatch.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonBinary.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonBounds.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonJson.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SkeletonRenderer.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Skin.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\Slot.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SlotData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\spine.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\spine-cocos2dx.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\TransformConstraint.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\TransformConstraintData.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="..\VertexAttachment.h">
      <Filter>Source Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
