/****************************************************************************
 Copyright (c) 2013 cocos2d-x.org
 
 http://www.cocos2d-x.org
 
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:
 
 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.
 
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
 ****************************************************************************/
#include "AssetsManager.h"

#include <thread>
#include "base/CCDirector.h"
#include "base/CCScheduler.h"
#include "base/CCUserDefault.h"
#include "network/CCDownloader.h"
#include "platform/CCFileUtils.h"
#include "platform/CCApplication.h"
#include "scripting/lua-bindings/manual/CCLuaEngine.h"
#include "base/CrashMsgCollector.h"
#ifdef MINIZIP_FROM_SYSTEM
#include <minizip/unzip.h>
#else // from our embedded sources
#include "unzip.h"
#endif
NS_CC_EXT_BEGIN;
using namespace std;
using namespace cocos2d;
using namespace cocos2d::network;

#define KEY_OF_VERSION                  "current-version-code"
#define KEY_OF_DOWNLOADED_VERSION       "downloaded-version-code"
#define TEMP_PACKAGE_FILE_NAME          keyOfTempFile()
#define BUFFER_SIZE    8192
#define MAX_FILENAME   512

static const std::string TEXTURE_TYPE_KEY[] = {"", "", "etc1", "etc2", "astc"};

// Implementation of AssetsManager

AssetsManager::AssetsManager(const char* packageUrl/* =nullptr */, const char* versionFileUrl/* =nullptr */, const char* storagePath/* =nullptr */, bool continueDownloading/* =true */, bool isMd6/* = false*/)
:  _storagePath(storagePath ? storagePath : "")
, _version("")
, _packageUrl(packageUrl ? packageUrl : "")
, _versionFileUrl(versionFileUrl ? versionFileUrl : "")
, _downloadedVersion("")
, _downloader(new Downloader())
, _connectionTimeout(0)
, _delegate(nullptr)
, _isDownloading(false)
, _shouldDeleteDelegateWhenExit(false)
, _resumable(true)
, _fileMd5("")
, _uncompressPath("")
,_isUseUncompressPath(false)
,_isMd6(isMd6)
,_isCheckJsonLength(false)
,_textureType(TextureTypeCode::PNG)
,_isCheckWriteLength(false)
,_onProgressCount(100)
{
    if(_isMd6 == true){
        if(_versionFileUrl != ""){
            std::string::size_type lastSlashIndex = _versionFileUrl.rfind(".md5");
            _versionFileUrl.replace(lastSlashIndex, 4, ".md6");
        }
    }
    
    checkStoragePath();
    // convert downloader error code to AssetsManager::ErrorCode
    _downloader->onTaskError = [this](const DownloadTask& task,
                                      int errorCode,
                                      int errorCodeInternal,
                                      const std::string& errorStr)
    {
        _isDownloading = false;
        
        if (nullptr == _delegate)
        {
            return;
        }
        auto err = ErrorCode::NETWORK;
        if(errorCodeInternal == 416) {
            // range error
            err = ErrorCode::RANGE_ERROR;
        } else if(errorCodeInternal == 404 && errorCode == -3) {
            // file not found
            err = ErrorCode::NO_NEW_VERSION;
        } else if(DownloadTask::ERROR_FILE_OP_FAILED == errorCode) {
            // cannot create file
            err = ErrorCode::CREATE_FILE;
        }else if(errorCodeInternal == 403 && errorCode == -3) {
            // file not found
            err = ErrorCode::FORBIDDEN;
        }
//        CCLOG("error errorCode=%d   errorCodeInternal=%d  errorStr=%s", errorCode, errorCodeInternal, errorStr.c_str());
        _delegate->onError(err);
    };
    
    // progress callback
    _downloader->onTaskProgress = [this](const DownloadTask& task,
                                         int64_t /*bytesReceived*/,
                                         int64_t totalBytesReceived,
                                         int64_t totalBytesExpected)
    {
        if(FileUtils::getInstance()->getFileExtension(task.requestURL) != ".zip")
        {
            // get version progress don't report
            return;
        }
        
        if (nullptr == _delegate)
        {
            return;
        }
        
        int percent = totalBytesExpected ? int(totalBytesReceived * 100 / totalBytesExpected) : 0;
        _delegate->onProgress(percent, totalBytesExpected);
        CCLOG("downloading... %d%%", percent);
    };
    
    // get version from version file when get data success
    _downloader->onDataTaskSuccess = [this](const DownloadTask& /*task*/,
                                            std::vector<unsigned char>& data)
    {
        CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ON_FINISH_SUCCESS);
        //读取.md5文件的数据
        // store version info to member _version
        const char *p = (char *)data.data();
        
        std::vector<std::string> res;
        if(_isMd6 == true){
            std:string str = "";
            str.insert(str.end(), p, p+data.size());
            
            std::string limit = "&";
            res = split(str, limit);
            for(int i=0; i<res.size(); i++){
                std::string::size_type index = res[i].find(":");
                if (std::string::npos == index){
                    log("no md5 version");
                    _version.insert(_version.end(), p, p + data.size());
                    break;
                }
                std::string version = res[i].substr(0, index);
                std::string md5 = res[i].substr(index+1);
                
                if(i==0){
                    _version = version;
                    _fileMd5 = md5;
                    break;
                }
            }
            
            log("_fileMd5: %s, _version:%s",_fileMd5.c_str(), _version.c_str());
        }else{
            _version.insert(_version.end(), p, p + data.size());
        }

        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ON_FINISH_SUCCESS, "s1");
        
        string versionNewKey = UserDefault::getInstance()->getStringForKey(keyOfVersion().c_str(),"");
        if (versionNewKey == "")
        {
            string oldKey = _versionFileUrl;
            std::string::size_type lastSlashIndex = oldKey.rfind("/");
            std::string::size_type underlineIndex = oldKey.find("_", lastSlashIndex+1);
            underlineIndex = oldKey.find("_", underlineIndex+1);
#if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
            oldKey = oldKey.replace(underlineIndex+1, 3, "210");
#else
            oldKey = oldKey.replace(underlineIndex+1, 3, "106");
#endif
            log("oldKey: %s",oldKey.c_str());
            string oldKeyHash = getKeyWithHash(oldKey);
            string versionOldKey = UserDefault::getInstance()->getStringForKey(oldKeyHash.c_str(),"");
            if (versionOldKey != "")
            {
                UserDefault::getInstance()->setStringForKey(keyOfVersion().c_str(),versionOldKey.c_str());
                UserDefault::getInstance()->setStringForKey(oldKeyHash.c_str(),"");
            }
        }
        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ON_FINISH_SUCCESS, "s2");
        
        string recordedVersion = UserDefault::getInstance()->getStringForKey(keyOfVersion().c_str(),"");
        
        log("%s %s local version is: %s, server version is: %s",_versionFileUrl.c_str(),keyOfVersion().c_str(), recordedVersion.c_str(),_version.c_str());
        
        bool hasVersionContent = false;
        
        std::string moduleName = getModuleName();
        log("module name: %s", moduleName.c_str());
        
        if (_version.length() >= moduleName.length() && _version.substr(0,moduleName.length()) == moduleName)
            hasVersionContent = true;
        
        if (recordedVersion == _version || !hasVersionContent)
        {
            _isDownloading = false;
            if (_delegate)
            {
                _delegate->onError(ErrorCode::NO_NEW_VERSION);
            }
            log("there is not new version");
            // Set resource search path.
//            setSearchPath();
            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ON_FINISH_SUCCESS);
            return;
        }

        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ON_FINISH_SUCCESS, "s3");

        // start download new version assets
        // 1. Urls of package and version should be valid;
        // 2. Package should be a zip file.
        if (_versionFileUrl.empty()
            || _packageUrl.empty()
            || FileUtils::getInstance()->getFileExtension(_packageUrl) != ".zip"
            )
        {
            CCLOG("no version file url, or no package url, or the package is not a zip file");
            _isDownloading = false;
            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ON_FINISH_SUCCESS);
            return;
        }
        
        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ON_FINISH_SUCCESS, "s4");
        //xubing 进行下载地址的替换。
        if (_packageUrl.size() != 0)
        {
            //如果本地没有记录 下载整体包
            std::string replaceStr = _version;
            CCLOG("_version1 %s",replaceStr.c_str());
            std::string Tag = "_";
            std::string::size_type sharp_index = replaceStr.find(Tag);
//            std::string recordedVersion = getVersion();
            if (std::string::npos != sharp_index)
            {
                if (recordedVersion == "")
                {
                    //整体包
                }
                else
                {
                    //本地版本号的拆分。日期_commit-platform 20171102_sdhkfhewfe-ios
                    std::string::size_type index1 = _version.find("_");
                    std::string::size_type index2 = _version.rfind("_");
                    std::string::size_type platformindex1 = recordedVersion.find("_");
                    std::string::size_type platformindex2 = recordedVersion.rfind("_");
                    
                    if ((index1 != std::string::npos) and (index2 != std::string::npos) and (platformindex1 != std::string::npos))
                    {
                        std::string date = recordedVersion.substr(0,index1);
                        std::string currentGit = recordedVersion.substr(index2+1,platformindex1-index2-1);
                        //tm currentVersionDate = getTimeByStr(date);
                        
                        //服务器version数据拆分
                        index1 = _version.find("_");
                        index2 = _version.rfind("_");
                        platformindex1 = recordedVersion.find("_");
                        platformindex2 = recordedVersion.rfind("_");
                        
                        
                        if ((index1 != std::string::npos )and (index2 != std::string::npos) and (platformindex1 != std::string::npos))
                        {
                            std::string module = _version.substr(0,index2-5);
                            std::string server_module_version = _version.substr(index2-4,4);
                            int number1 = std::atoi(server_module_version.c_str());
                            std::string server_version_time = _version.substr(index2+1,std::string::npos);
                            printf("index:%zu,index2:%zu",index1,index2);
                            
                            
                            
                            std::string local_module_version = recordedVersion.substr(platformindex2-4,4);
                            int number2 = std::atoi(local_module_version.c_str());
                            std::string local_version_time = recordedVersion.substr(platformindex2+1,std::string::npos);
                            
                            if (number1 <= (number2 + 5))//对比版本号之间的差异
                            {
                                CCLOG("dooo: number2 is %d,number1 is %d",number2,number1);
                                replaceStr = module+"_"+server_module_version+"_"+server_version_time+"_"+local_module_version+"_"+local_version_time;
                                
                                CCLOG("dooo: module is %s,server_module_version is %s,server_version_time is %s",module.c_str(),server_module_version.c_str(),server_version_time.c_str());
                                //分量热更包
                                for(int i=0; i<res.size(); i++){
                                    std::string::size_type index = res[i].find(":");
                                    if (std::string::npos == index){
                                        log("no md5 version model1->model2");
                                        break;
                                    }
                                    std::string version = res[i].substr(0, index);
                                    std::string md5 = res[i].substr(index+1);
                                    
                                    if(version == replaceStr){
                                        _fileMd5 = md5;
                                        log("have md5 version model1->model2");
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                //全量热更包
                            }
                            
                            
                            /*
                             std::string serverdate = _version.substr(0,index1);
                             std::string serverGit = _version.substr(index2+1, platformindex - index2-1);
                             tm serverVersionDate = getTimeByStr(serverdate);
                             printf("index1 is %zu, index2 is %zu, _version is: %s",index1,index2,_version.c_str());
                             
                             //比较时间大于2周，整体包
                             time_t t1 = mktime(&currentVersionDate);
                             time_t t2 = mktime(&serverVersionDate);
                             double dt = std::abs(difftime(t1, t2));
                             if (dt < 60*60*24*14)
                             {
                             replaceStr.replace(sharp_index, Tag.size(), string("_")+currentGit+string("-"));
                             }
                             
                             
                             else{
                             //整体包
                             }
                             */
                            
                        }
                        
                    }
                }
            }
            
            std::string defaultName = "DYNAMICALLY_REPLACED_FILE_NAME";
            std::string::size_type str_index = _packageUrl.find(defaultName);
            if (std::string::npos != str_index)
            {
                _packageUrl.replace(str_index, defaultName.size(), replaceStr);
                CCLOG("downloading new package url: %s", _packageUrl.c_str());
            }
        }
        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ON_FINISH_SUCCESS, "s5");

        
        // Is package already downloaded?
        _downloadedVersion = UserDefault::getInstance()->getStringForKey(keyOfDownloadedVersion().c_str());
        if (_downloadedVersion == _version)
        {
            downloadAndUncompress();
            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ON_FINISH_SUCCESS);
            return;
        }
        
        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ON_FINISH_SUCCESS, "s6");
        // start download;
        const string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
        _downloader->createDownloadFileTask(_packageUrl, outFileName, "", this->_resumable, _onProgressCount);
        CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ON_FINISH_SUCCESS);
    };
    
    // after download package, do uncompress operation
    _downloader->onFileTaskSuccess = [this](const DownloadTask& /*task*/)
    {
        CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ON_FINISH_SUCCESS_2);
        downloadAndUncompress();
        CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ON_FINISH_SUCCESS_2);
    };
}

AssetsManager::~AssetsManager()
{
    
    if (_shouldDeleteDelegateWhenExit)
    {
        delete _delegate;
    }
    CC_SAFE_DELETE(_downloader);
}

void AssetsManager::checkStoragePath()
{
    if (_storagePath.size() > 0 && _storagePath[_storagePath.size() - 1] != '/')
    {
        _storagePath.append("/");
    }
    if(_storagePath.size() > 3 && _storagePath.substr(_storagePath.size() - 4,3) != "src"){
        string path = _storagePath;
        if (path.size() > 0){
            path[path.size()-1]='2'; //compressTmpPath
        }
        setUncompressPath(path.c_str());
    }
    
}
void AssetsManager::checkUncompressPath()
{
    if (_uncompressPath.size() > 0 && _uncompressPath[_uncompressPath.size() - 1] != '/')
    {
        _uncompressPath.append("/");
    }
}
//通过时间字符串，获取时间
tm AssetsManager::getTimeByStr(const std::string& dateStr)
{
    tm t;
    //拆分datestr,组合time
    string str = dateStr;
    string substring;
    string::size_type start = 0, index;
    std::string separator = "-";
    vector<int> vecDate;
    do
    {
        index = str.find_first_of(separator,start);
        if (index != string::npos)
        {
            substring = str.substr(start,index-start);
            vecDate.push_back(atoi(substring.c_str()));
            start = str.find_first_not_of(separator,index);
            if (start == string::npos)
            {
                break;
            }
        }
    }while(index != string::npos);
    
    //the last token
    substring = str.substr(start);
    vecDate.push_back(atoi(substring.c_str()));
    
    t.tm_year = vecDate.at(0);
    t.tm_mon = vecDate.at(1);
    t.tm_mday = vecDate.at(2);
    t.tm_hour=vecDate.at(3);
    t.tm_min = vecDate.at(4);
    t.tm_sec = vecDate.at(5);
    return t;
}

// Multiple key names
static std::string keyWithHash( const char* prefix, const std::string& url )
{
    char buf[256];
    sprintf(buf,"%s%zd",prefix,std::hash<std::string>()(url));
    return buf;
}

std::string AssetsManager::getKeyWithHash(std::string key) const
{
    return keyWithHash(KEY_OF_VERSION, key);
}

std::string AssetsManager::getModuleName() const
{
    std::string::size_type lastSlashIndex = _versionFileUrl.rfind("/");
    std::string::size_type lastDotIndex = _versionFileUrl.rfind(".");
    std::string md5Name = _versionFileUrl.substr(lastSlashIndex+1,lastDotIndex-lastSlashIndex-1);
    std::string moduleName = "";
    std::string::size_type underlineIndex = md5Name.find("_");
    underlineIndex = md5Name.find("_", underlineIndex+1);
    underlineIndex = md5Name.find("_", underlineIndex+1);
    if (underlineIndex == std::string::npos)
        moduleName = "src";
    else
        moduleName = md5Name.substr(underlineIndex+1);
    return moduleName;
}

std::string AssetsManager::getPlatformName() const
{
    std::string::size_type lastSlashIndex = _versionFileUrl.rfind("/");
    std::string::size_type lastDotIndex = _versionFileUrl.rfind(".");
    std::string md5Name = _versionFileUrl.substr(lastSlashIndex+1,lastDotIndex-lastSlashIndex-1);
    std::string::size_type underlineIndex = md5Name.find("_");
    underlineIndex = md5Name.find("_", underlineIndex+1);
    std::string platformName = md5Name.substr(0,underlineIndex);
    return platformName;
}

// hashed version
std::string AssetsManager::keyOfVersion() const
{
    std::string versionKey = getPlatformName()+"_"+getModuleName() + TEXTURE_TYPE_KEY[static_cast<int>(_textureType)];
    //    log("keyOfVersion: %s", versionKey.c_str());
    return keyWithHash(KEY_OF_VERSION, versionKey);
}

// hashed version
std::string AssetsManager::keyOfDownloadedVersion() const
{
    std::string versionKey = getPlatformName()+"_"+getModuleName() + TEXTURE_TYPE_KEY[static_cast<int>(_textureType)];
    return keyWithHash(KEY_OF_DOWNLOADED_VERSION,versionKey);
}

std::string AssetsManager::keyOfTempFile() const
{
    std::string versionKey = getPlatformName()+"_"+getModuleName();
    return keyWithHash("update",versionKey)+"-temp.zip";
}

std::string AssetsManager::getZipFileName(){
    return keyOfTempFile();
}

bool AssetsManager::checkUpdate()
{
    CrashMsgCollector::getInstance()->clearAssetManagerMsg();
    auto start = std::chrono::high_resolution_clock::now();

    if (_versionFileUrl.size() == 0 || _isDownloading) return false;
    
    // Clear _version before assign new value.
    _version.clear();
    _isDownloading = true;
    _downloader->createDownloadDataTask(_versionFileUrl, "", true, _onProgressCount);

    // log
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    if (duration >= 300)
    {
        CrashMsgCollector::getInstance()->pushAssetManagerMsg("AssetsManager::update_end", int(duration));
        CrashMsgCollector::getInstance()->printAssetManagerMsg();
    }
    
    
    return true;
}

void AssetsManager::setOnProgressCount(unsigned int count)
{
    _onProgressCount = count;
}

unsigned int AssetsManager::getOnProgressCount()
{
    return _onProgressCount;
}

void AssetsManager::downloadAndUncompress()
{
    std::thread([this]()
    {
        do
        {
            //对比md5如果不一样返回更新失败
            bool isMd5Compare = true;
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
            if (Application::getInstance()->getMemoryTotal() < 5000)
            {
                isMd5Compare = false;
            }    
#endif
            if (isMd5Compare)
            {
                string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
                string md5 = FileUtils::getInstance()->getMd5FromFile(outFileName);
                string dataMd5 = _fileMd5;
                if (dataMd5!="" && md5.compare(dataMd5) != 0)
                {
                   
                    Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                        CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ASSETS_SUCCESS_6);
                        string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
                        string temp = _versionFileUrl+"_error_md5";
                        UserDefault::getInstance()->setStringForKey(temp.c_str(),md5.c_str());
                        UserDefault::getInstance()->setStringForKey(this->keyOfDownloadedVersion().c_str(),"");
                        UserDefault::getInstance()->flush();
                        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS_6, "s1");
                        if (this->_delegate)
                            this->_delegate->onError(ErrorCode::NO_MD5_COMPARE);
                        CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ASSETS_SUCCESS_6);
                    });
                    break;
                }
            }
            
            // Uncompress zip file.
            if(_isUseUncompressPath){
                ErrorCode errorCode = uncompressWithPath();
                if (errorCode != ErrorCode::FILE_OK){
                    if (errorCode == ErrorCode::UNCOMPRESS or errorCode == ErrorCode::FILE_MOVE_ERROR)
                    {
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                            CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ASSETS_SUCCESS_5);
                                    UserDefault::getInstance()->setStringForKey(this->keyOfDownloadedVersion().c_str(),"");
                                    UserDefault::getInstance()->flush();
                            CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS_5, "s1");
                            if (this->_delegate)
                                this->_delegate->onError(errorCode);
                            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ASSETS_SUCCESS_5);
                        });
                        // Delete unloaded zip file.
                        string zipfileName = this->_storagePath + TEMP_PACKAGE_FILE_NAME;
                        if (remove(zipfileName.c_str()) != 0)
                        {
                            CCLOG("can not remove downloaded zip file %s", zipfileName.c_str());
                        }
                          

                    }
                    else if(errorCode == ErrorCode::FOLDER_CREATE_FAIL)
                    {
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                            if (this->_delegate)
                                this->_delegate->onError(errorCode);
                        });

                    }
                    break;
                }
            }
            else if (_isCheckJsonLength){
                ErrorCode errorCode = uncompressWithCheckJson();
                if (errorCode != ErrorCode::FILE_OK){
                    if (errorCode == ErrorCode::UNCOMPRESS or errorCode == ErrorCode::JSON_CHECK_ERROR)
                    {
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                            CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ASSETS_SUCCESS_4);
                            UserDefault::getInstance()->setStringForKey(this->keyOfDownloadedVersion().c_str(),"");
                            UserDefault::getInstance()->flush();
                            CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS_4, "s1");
                            if (this->_delegate)
                                this->_delegate->onError(errorCode);
                            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ASSETS_SUCCESS_4);
                        });
                        // Delete unloaded zip file.
                        string zipfileName = this->_storagePath + TEMP_PACKAGE_FILE_NAME;
                        if (remove(zipfileName.c_str()) != 0)
                        {
                            CCLOG("can not remove downloaded zip file %s", zipfileName.c_str());
                        }
                        
                        
                    }
                    else if(errorCode == ErrorCode::FOLDER_CREATE_FAIL)
                    {
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                            if (this->_delegate)
                                this->_delegate->onError(errorCode);
                        });
                        
                    }
                    break;
                }
            }
            else if (_isCheckWriteLength){
                CCLOG("USE CheckWrite");
                ErrorCode errorCode = uncompressWithCheckWrite();
                if (errorCode != ErrorCode::FILE_OK){
                    if (errorCode == ErrorCode::UNCOMPRESS or errorCode == ErrorCode::WRITE_CHECK_ERROR or errorCode == ErrorCode::FLUSH_ERROR)
                    {
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                            CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ASSETS_SUCCESS_3);
                                    UserDefault::getInstance()->setStringForKey(this->keyOfDownloadedVersion().c_str(),"");
                                    UserDefault::getInstance()->flush();
                            CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS_3, "s1");
                            if (this->_delegate)
                                this->_delegate->onError(errorCode);
                            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ASSETS_SUCCESS_3);
                        });
                        // Delete unloaded zip file.
                        string zipfileName = this->_storagePath + TEMP_PACKAGE_FILE_NAME;
                        if (remove(zipfileName.c_str()) != 0)
                        {
                            CCLOG("can not remove downloaded zip file %s", zipfileName.c_str());
                        }
                          

                    }
                    else if(errorCode == ErrorCode::FOLDER_CREATE_FAIL)
                    {
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=]{
                            if (this->_delegate)
                                this->_delegate->onError(errorCode);
                        });

                    }
                    break;
                }
                
            }
            else{
                if(!Uncompress()){
                    Director::getInstance()->getScheduler()->performFunctionInCocosThread([&, this]{
                        CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ASSETS_SUCCESS_2);
                                   UserDefault::getInstance()->setStringForKey(this->keyOfDownloadedVersion().c_str(),"");
                                   UserDefault::getInstance()->flush();
                        CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS_2, "s1");
                       if (this->_delegate)
                           this->_delegate->onError(ErrorCode::UNCOMPRESS);
                        CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ASSETS_SUCCESS_2);
                    });
                    break;
                }
            }
//
            Director::getInstance()->getScheduler()->performFunctionInCocosThread([&, this] {
                CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::ASSETS_SUCCESS);
                // Record new version code.
                UserDefault::getInstance()->setStringForKey(this->keyOfVersion().c_str(), this->_version.c_str());
                
                // Unrecord downloaded version code.
                UserDefault::getInstance()->setStringForKey(this->keyOfDownloadedVersion().c_str(), "");
                UserDefault::getInstance()->flush();
                
                CCLOG("keyOfVersion:%s, value:%s, downloadedVersion:%s", this->keyOfVersion().c_str(), this->_version.c_str(),this->keyOfDownloadedVersion().c_str());
                CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS, "s1");
                // Set resource search path.
                this->setSearchPath();
                
                // Delete unloaded zip file.
                string zipfileName = this->_storagePath + TEMP_PACKAGE_FILE_NAME;
                if (remove(zipfileName.c_str()) != 0)
                {
                    CCLOG("can not remove downloaded zip file %s", zipfileName.c_str());
                }
                CrashMsgCollector::getInstance()->timeConsumeStep(CrashMsgCollector::ASSETS_SUCCESS, "s2");
                if (this->_delegate) this->_delegate->onSuccess();
                CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::ASSETS_SUCCESS);
            });
            
        } while (0);
        
        _isDownloading = false;

    }).detach();
}

void AssetsManager::update()
{
    // all operation in checkUpdate, nothing need to do
    // keep this function for compatibility
    checkUpdate();
}

AssetsManager::ErrorCode AssetsManager::uncompressWithPath()
{
    // Open the zip file
    string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
    unzFile zipfile = unzOpen(FileUtils::getInstance()->getSuitableFOpen(outFileName).c_str());
    if (! zipfile)
    {
        CCLOG("can not open downloaded zip file %s", outFileName.c_str());
        return ErrorCode::UNCOMPRESS;
    }

    // Get info about the zip file
    unz_global_info global_info;
    if (unzGetGlobalInfo(zipfile, &global_info) != UNZ_OK)
    {
        CCLOG("can not read file global info of %s", outFileName.c_str());
        unzClose(zipfile);
        return ErrorCode::UNCOMPRESS;
    }

    // Buffer to hold data read from the zip file
    char readBuffer[BUFFER_SIZE];
    string uncompressName[global_info.number_entry]; //save file names
    CCLOG("start uncompressing");

    // Loop to extract all files.
    uLong i;
    for (i = 0; i < global_info.number_entry; ++i)
    {
        // Get info about current file.
        unz_file_info fileInfo;
        char fileName[MAX_FILENAME];
        if (unzGetCurrentFileInfo(zipfile,
                                  &fileInfo,
                                  fileName,
                                  MAX_FILENAME,
                                  nullptr,
                                  0,
                                  nullptr,
                                  0) != UNZ_OK)
        {
            CCLOG("can not read file info");
            unzClose(zipfile);
            return ErrorCode::UNCOMPRESS;
        }

        const string fullPath = _storagePath + fileName;

        // Check if this entry is a directory or a file.
        const size_t filenameLength = strlen(fileName);
        if (fileName[filenameLength-1] == '/')
        {
            // Entry is a directory, so create it.
            // If the directory exists, it will failed silently.
            if (!FileUtils::getInstance()->createDirectory(fullPath))
            {
                CCLOG("can not create directory %s", fullPath.c_str());
                unzClose(zipfile);
                return ErrorCode::FOLDER_CREATE_FAIL;
            }
            if ( _isUseUncompressPath && _uncompressPath.size()>0 ){
                if (!FileUtils::getInstance()->createDirectory(_uncompressPath + fileName))
                {
                    CCLOG("can not create uncompress path directory %s", (_uncompressPath + fileName).c_str());
                }
            }
        }
        else
        {
            //There are not directory entry in some case.
            //So we need to test whether the file directory exists when uncompressing file entry
            //, if does not exist then create directory
            const string fileNameStr(fileName);
            string curUncompressPath;
            size_t startIndex=0;

            size_t index=fileNameStr.find("/",startIndex);

            while(index != std::string::npos)
            {
                CCLOG("Uncompress 5 fileNameStr %s index %d", fileNameStr.c_str(), (int)index);
                const string dir=_storagePath+fileNameStr.substr(0,index);

                FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(dir).c_str(), "r");

                if(!out)
                {
                    if (!FileUtils::getInstance()->createDirectory(dir))
                    {
                        CCLOG("can not create directory %s", dir.c_str());
                        unzClose(zipfile);
                        return ErrorCode::FOLDER_CREATE_FAIL;
                    }
                    else
                    {
                        CCLOG("create directory %s",dir.c_str());
                    }
                }
                else
                {
                    fclose(out);
                }

                if (_isUseUncompressPath && _uncompressPath.size() > 0 ){
                    CCLOG("Uncompress 4 fileNameStr %s index %d", fileNameStr.c_str(), (int)index);
                    const string dir2=_uncompressPath+fileNameStr.substr(0,index);
                    curUncompressPath = dir2;
                    FILE *out2 = fopen(FileUtils::getInstance()->getSuitableFOpen(dir2).c_str(), "r");
                    if(!out2)
                    {
                        if (!FileUtils::getInstance()->createDirectory(dir2))
                        {
                            CCLOG("can not create uncompress directory  %s", dir2.c_str());
                        }
                        else
                        {
                            CCLOG("create uncompress directory %s",dir2.c_str());
                        }
                    }
                    else
                    {
                        fclose(out2);
                    }
                }


                startIndex=index+1;

                index=fileNameStr.find("/",startIndex);

            }

            // Entry is a file, so extract it.

            // Open current file.
            if (unzOpenCurrentFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not open file %s", fileName);
                unzClose(zipfile);
                return ErrorCode::UNCOMPRESS;
            }

            // Create a file to store current file.
            FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(fullPath).c_str(), "wb");
            if (! out)
            {
                CCLOG("can not open destination file %s", fullPath.c_str());
                unzCloseCurrentFile(zipfile);
                unzClose(zipfile);
                return ErrorCode::FOLDER_CREATE_FAIL;
            }
            bool isUseUncompressFolder = false;
            FILE *outUncompress ;
            if(_isUseUncompressPath && _uncompressPath.size() > 0){

                const string fullPathUncompress = _uncompressPath + fileName;
                outUncompress = fopen(FileUtils::getInstance()->getSuitableFOpen(fullPathUncompress).c_str(), "wb");

                if (! outUncompress)
                {
                    CCLOG("can not open Uncomprese destination file %s", fullPathUncompress.c_str());
                    //then use old function
                    isUseUncompressFolder = false;
                }
                else{
                    uncompressName[i] = fileName;
                    isUseUncompressFolder = true;
                }
            }
            // Write current file content to destinate file.
            int error = UNZ_OK;
            do
            {
                error = unzReadCurrentFile(zipfile, readBuffer, BUFFER_SIZE);
                if (error < 0)
                {
                    CCLOG("can not read zip file %s, error code is %d", fileName, error);
                    unzCloseCurrentFile(zipfile);
                    unzClose(zipfile);
//                    //empty uncompressPath
                    if(_isUseUncompressPath && _uncompressPath.size() > 0 && curUncompressPath.size() > 0){
                        FileUtils::getInstance()->removeDirectory(curUncompressPath);
                    }

                    return ErrorCode::UNCOMPRESS;
                }

                if (error > 0)
                {
                    //put in tmp folder
//                    fwrite(readBuffer, error, 1, out);
                    if(isUseUncompressFolder){
                        fwrite(readBuffer, error, 1, outUncompress);
                    }
                    else{
                        fwrite(readBuffer, error, 1, out);
                    }

                }
            } while(error > 0);

            fclose(out);
            if(isUseUncompressFolder){
                //if no error before then close the right fileout
                fclose(outUncompress);
            }

        }

        unzCloseCurrentFile(zipfile);

        // Goto next entry listed in the zip file.
        if ((i+1) < global_info.number_entry)
        {
            if (unzGoToNextFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not read next file");
                unzClose(zipfile);
                return ErrorCode::UNCOMPRESS;
            }
        }
    }
    bool moveFalse = false;
    string errorName = "";
    string recoverLog = "";
    if(_isUseUncompressPath && _uncompressPath.size() > 0 && _uncompressPath.c_str() != _storagePath.c_str()){
        for(i = 0; i < global_info.number_entry; ++i){
            if(uncompressName[i].size() > 0){

                //move
                const string fullPath = _storagePath + uncompressName[i];
                const string fullPathUncompress = _uncompressPath +  uncompressName[i];
                int tryCount = 0;
                bool tryFalse = false;
                while(tryCount < 2 && tryFalse == false){
                    tryCount += 1;
                    FILE *target = fopen(FileUtils::getInstance()->getSuitableFOpen(fullPath).c_str(), "wb");
                    if(!target){
                        CCLOG("打开失败 %s",fullPath.c_str());
                        if(tryCount >= 2){
                            errorName+=fullPathUncompress;
                            errorName+="==type is openFile error";
                            errorName+="====";
                        }
                    }
                    else{
                        fclose(target);
                        Data d;
                        FileUtils::Status status = FileUtils::getInstance()->getContents(fullPathUncompress, &d);
                        if(status != FileUtils::Status::OK){
                            CCLOG("文件读取失败 %s",fullPathUncompress.c_str());
                            if(tryCount >= 2){
                                errorName+=fullPathUncompress;
                                errorName+="==type is readFile error";
                                errorName+="====";
                            }
                        }
                        else if((unsigned char*)d.getBytes() == NULL){
                            CCLOG("要移动的文件内容为空 %s",fullPathUncompress.c_str());
                            if(uncompressName[i].find(".png") == -1 and uncompressName[i].find(".jpg") == -1 and uncompressName[i].find(".skel") == -1 and uncompressName[i].find(".atlas") == -1 and uncompressName[i].find(".json") == -1 and uncompressName[i].find(".csb") == -1 and uncompressName[i].find(".fnt") == -1 ){
                                CCLOG("空文件略过 %s",fullPathUncompress.c_str());
                                tryFalse = true;
                                break;
                            }
                            else if(tryCount >= 2){
                                errorName+=fullPathUncompress;
                                errorName+="==type is byte empty";
                                errorName+="====";
                            }

                        }
                        else{
                            if(!FileUtils::getInstance()->writeDataToFile(d,fullPath)){
                                CCLOG("文件写入失败 %s",fullPathUncompress.c_str());
                                if(tryCount >= 2){
                                    errorName+=fullPathUncompress;
                                    errorName+="==type is writeDataToFile error";
                                    errorName+="====";
                                }
                            }
                            else{
                                tryFalse = true;
                                if(tryCount >= 2){
                                    recoverLog+= fullPathUncompress + "--";
                                }
                                if(!FileUtils::getInstance()->removeFile(fullPathUncompress)){
                                    CCLOG("源文件删除失败 %s",fullPathUncompress.c_str());
                                }
                                break;
                            }
                        }
                    }
                }
                if(!tryFalse){
                    moveFalse = true;
                }
            }
        }
    }

    
    if(moveFalse && errorName != ""){
        string arg = "AssetsManager uncomoress Error:" + errorName;
//        sprintf(arg, "AssetsManager uncomoress Error:%s", errorName.c_str());
        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
            cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
        });
    }
    if(recoverLog != ""){
//        static char arg[512];
        string arg = "AssetsManager uncomoress Twice recover:"+ recoverLog;
        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
            cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
        });
    }
//    FileUtils::getInstance()->removeDirectory(_uncompressPath);
    CCLOG("end uncompressing");
    unzClose(zipfile);
    if (moveFalse == true){
        return ErrorCode::FILE_MOVE_ERROR;
    }
    return ErrorCode::FILE_OK;
}

AssetsManager::JsonCompareCode AssetsManager::compareLengthJson(std::string saveJson,std::string *errorStr,std::string zip)
{
    rapidjson::Document doc;
    doc.Parse<0>(saveJson.c_str());
    if(doc.HasParseError()){
        CCLOG("json解析出错啦");
        return JsonCompareCode::JSON_PARSE_ERROR;
    }
    string erorFileNames = "";
    bool haveCheckError = false;
    if(doc.IsObject()){
        for (rapidjson::Value::ConstMemberIterator itr = doc.MemberBegin(); itr != doc.MemberEnd(); ++itr)
        {
            std::string filenName = itr->name.GetString();
            int fileLength = itr->value.GetInt();
            string fullFilenName = _storagePath + filenName;
            FileUtils::getInstance()->eraseFileFullPathCache(filenName);
            int curLength = (int)FileUtils::getInstance()->getFileSize(fullFilenName);
            if( curLength!=fileLength ){
                ssize_t pSize;
                unsigned char* content = FileUtils::getInstance()->getFileDataFromZip(zip,filenName,&pSize);
                FileUtils::getInstance()->eraseFileFullPathCache(filenName);
                if(pSize == fileLength){
                    //写进去
                    FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(_storagePath + filenName).c_str(), "wb");
                    if(out != NULL){
                        fwrite(content, pSize, 1, out);
                    }
                    fclose(out);
                }
                FileUtils::getInstance()->eraseFileFullPathCache(filenName);
                curLength = (int)FileUtils::getInstance()->getFileSize(fullFilenName);
                if( curLength!=fileLength ){
                    haveCheckError = true;
                    erorFileNames += "Length CheckError: %s should:%d have:&d ==",filenName.c_str(),fileLength,curLength;
                        CCLOG("文件长度校验不通过 %s 应有%d 实际%d",filenName.c_str(),fileLength,curLength);
                }
                else{
                    CCLOG("通过从zip重新获取 修复一次 %s",filenName.c_str());
                    string arg = "AssetsManager uncomoress JSON Twice recover:" + filenName;
                    Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
                       cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
                   });
                }
            }
        }
    }
    else{
        return JsonCompareCode::JSON_NO_OBJECT;
    }

    if (errorStr)
    {
        *errorStr = erorFileNames;
    }
    if(haveCheckError){
        return JsonCompareCode::JSON_CHECK_ERROR;
    }
    
    return JsonCompareCode::JSON_CHECK_OK;
}
AssetsManager::ErrorCode AssetsManager::uncompressWithCheckJson()
{
    // Open the zip file
    string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
    unzFile zipfile = unzOpen(FileUtils::getInstance()->getSuitableFOpen(outFileName).c_str());
    if (! zipfile)
    {
        CCLOG("can not open downloaded zip file %s", outFileName.c_str());
        return ErrorCode::UNCOMPRESS;
    }

    // Get info about the zip file
    unz_global_info global_info;
    if (unzGetGlobalInfo(zipfile, &global_info) != UNZ_OK)
    {
        CCLOG("can not read file global info of %s", outFileName.c_str());
        unzClose(zipfile);
        return ErrorCode::UNCOMPRESS;
    }

    // Buffer to hold data read from the zip file
    char readBuffer[BUFFER_SIZE];
    CCLOG("start uncompressing");
   
    // Loop to extract all files.
    uLong i;
    for (i = 0; i < global_info.number_entry; ++i)
    {
        // Get info about current file.
        unz_file_info fileInfo;
        char fileName[MAX_FILENAME];
        if (unzGetCurrentFileInfo(zipfile,
                                  &fileInfo,
                                  fileName,
                                  MAX_FILENAME,
                                  nullptr,
                                  0,
                                  nullptr,
                                  0) != UNZ_OK)
        {
            CCLOG("can not read file info");
            unzClose(zipfile);
            return ErrorCode::UNCOMPRESS;
        }
        const string fullPath = _storagePath + fileName;

        // Check if this entry is a directory or a file.
        const size_t filenameLength = strlen(fileName);
        if (fileName[filenameLength-1] == '/')
        {
            // Entry is a directory, so create it.
            // If the directory exists, it will failed silently.
            if (!FileUtils::getInstance()->createDirectory(fullPath))
            {
                CCLOG("can not create directory %s", fullPath.c_str());
                unzClose(zipfile);
                return ErrorCode::FOLDER_CREATE_FAIL;
            }
        }
        else
        {
            //There are not directory entry in some case.
            //So we need to test whether the file directory exists when uncompressing file entry
            //, if does not exist then create directory
            const string fileNameStr(fileName);
            string curUncompressPath;
            size_t startIndex=0;

            size_t index=fileNameStr.find("/",startIndex);

            while(index != std::string::npos)
            {
                CCLOG("Uncompress 3 fileNameStr %s index %d", fileNameStr.c_str(), (int)index);
                const string dir=_storagePath+fileNameStr.substr(0,index);

                FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(dir).c_str(), "r");

                if(!out)
                {
                    if (!FileUtils::getInstance()->createDirectory(dir))
                    {
                        CCLOG("can not create directory %s", dir.c_str());
                        unzClose(zipfile);
                        return ErrorCode::FOLDER_CREATE_FAIL;
                    }
                    else
                    {
                        CCLOG("create directory %s",dir.c_str());
                    }
                }
                else
                {
                    fclose(out);
                }
                startIndex=index+1;
                index=fileNameStr.find("/",startIndex);

            }

            // Entry is a file, so extract it.

            // Open current file.
            if (unzOpenCurrentFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not open file %s", fileName);
                unzClose(zipfile);
                return ErrorCode::UNCOMPRESS;
            }
           
            // Create a file to store current file.
            FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(fullPath).c_str(), "wb");
            if (!out)
            {
                CCLOG("can not open destination file %s", fullPath.c_str());
                unzCloseCurrentFile(zipfile);
                unzClose(zipfile);
                return ErrorCode::FOLDER_CREATE_FAIL;
            }
            
            int error = UNZ_OK;
            do
            {
                error = unzReadCurrentFile(zipfile, readBuffer, BUFFER_SIZE);
                if (error < 0)
                {
                    CCLOG("can not read zip file %s, error code is %d", fileName, error);
                    unzCloseCurrentFile(zipfile);
                    unzClose(zipfile);
                    return ErrorCode::UNCOMPRESS;
                }

                if (error > 0)
                {
                    fwrite(readBuffer, error, 1, out);
                }
            } while(error > 0);
            fclose(out);
           
        }

        unzCloseCurrentFile(zipfile);

        // Goto next entry listed in the zip file.
        if ((i+1) < global_info.number_entry)
        {
            if (unzGoToNextFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not read next file");
                unzClose(zipfile);
                return ErrorCode::UNCOMPRESS;
            }
        }
    }

    CCLOG("end uncompressing");
    unzClose(zipfile);
    if(_isCheckJsonLength){
        ssize_t pSize;
        unsigned char* saveJson = FileUtils::getInstance()->getFileDataFromZip(outFileName,"lengthRecord.json",&pSize);
        if(saveJson != NULL ){
            string val = (char*)saveJson;
            val.erase(val.find("}") + 1);
            std::string errorInfo;
            AssetsManager::JsonCompareCode ret = compareLengthJson(val,&errorInfo,outFileName);
            if(ret == JsonCompareCode::JSON_CHECK_ERROR){
                string arg = "AssetsManager uncomoress JSON Compare Error:" + errorInfo;
                Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
                   cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
               });
                
                return ErrorCode::JSON_CHECK_ERROR;
            }
            else if(ret != JsonCompareCode::JSON_CHECK_OK){
               
                string arg = &"AssetsManager uncomoress JSON Other Error Code:"[ (int)ret];
                Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
                   cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
               });
                
            }
        }
        else{
            string arg = "AssetsManager uncomoress JSON COMPARE ERROR: no json file" + outFileName ;
            Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
               cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
           });
        }
    }
    return ErrorCode::FILE_OK;
}
AssetsManager::ErrorCode AssetsManager::uncompressWithCheckWrite()
{
    // Open the zip file
    string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
    unzFile zipfile = unzOpen(FileUtils::getInstance()->getSuitableFOpen(outFileName).c_str());
    if (! zipfile)
    {
        CCLOG("can not open downloaded zip file %s", outFileName.c_str());
        return ErrorCode::UNCOMPRESS;
    }

    // Get info about the zip file
    unz_global_info global_info;
    if (unzGetGlobalInfo(zipfile, &global_info) != UNZ_OK)
    {
        CCLOG("can not read file global info of %s", outFileName.c_str());
        unzClose(zipfile);
        return ErrorCode::UNCOMPRESS;
    }

    // Buffer to hold data read from the zip file
    char readBuffer[BUFFER_SIZE];
    CCLOG("start uncompressing");
   
    // Loop to extract all files.
    uLong i;
    bool checckFalse = false;
    bool flushFalse = false;
    for (i = 0; i < global_info.number_entry; ++i)
    {
        // Get info about current file.
        unz_file_info fileInfo;
        char fileName[MAX_FILENAME];
        if (unzGetCurrentFileInfo(zipfile,
                                  &fileInfo,
                                  fileName,
                                  MAX_FILENAME,
                                  nullptr,
                                  0,
                                  nullptr,
                                  0) != UNZ_OK)
        {
            CCLOG("can not read file info");
            unzClose(zipfile);
            return ErrorCode::UNCOMPRESS;
        }
        const string fullPath = _storagePath + fileName;

        // Check if this entry is a directory or a file.
        const size_t filenameLength = strlen(fileName);
        if (fileName[filenameLength-1] == '/')
        {
            // Entry is a directory, so create it.
            // If the directory exists, it will failed silently.
            if (!FileUtils::getInstance()->createDirectory(fullPath))
            {
                CCLOG("can not create directory %s", fullPath.c_str());
                unzClose(zipfile);
                return ErrorCode::FOLDER_CREATE_FAIL;
            }
        }
        else
        {
            //There are not directory entry in some case.
            //So we need to test whether the file directory exists when uncompressing file entry
            //, if does not exist then create directory
            const string fileNameStr(fileName);
            string curUncompressPath;
            size_t startIndex=0;

            size_t index=fileNameStr.find("/",startIndex);

            while(index != std::string::npos)
            {
                // CCLOG("Uncompress 2 fileNameStr %s index %d", fileNameStr.c_str(), (int)index);
                const string dir=_storagePath+fileNameStr.substr(0,index);

                FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(dir).c_str(), "r");

                if(!out)
                {
                    if (!FileUtils::getInstance()->createDirectory(dir))
                    {
                        CCLOG("can not create directory %s", dir.c_str());
                        unzClose(zipfile);
                        return ErrorCode::FOLDER_CREATE_FAIL;
                    }
                    else
                    {
                        CCLOG("create directory %s",dir.c_str());
                    }
                }
                else
                {
                    fclose(out);
                }
                startIndex=index+1;
                index=fileNameStr.find("/",startIndex);

            }

            // Entry is a file, so extract it.

            // Open current file.
            if (unzOpenCurrentFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not open file %s", fileName);
                unzClose(zipfile);
                return ErrorCode::UNCOMPRESS;
            }
           
            // Create a file to store current file.
            FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(fullPath).c_str(), "wb");
            if (! out)
            {
                CCLOG("can not open destination file %s", fullPath.c_str());
                unzCloseCurrentFile(zipfile);
                unzClose(zipfile);
                string arg = "AssetsManager uncomoress WRITE CHECK OPEN FILE ERROR:" + outFileName;
                Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
                   cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg.c_str(),true);
                });
                return ErrorCode::FOLDER_CREATE_FAIL;
            }
            
            // Write current file content to destinate file.
            int error = UNZ_OK;
            
            size_t totalBytesWritten = 0; // Variable to track total bytes written
            
            do
            {
                error = unzReadCurrentFile(zipfile, readBuffer, BUFFER_SIZE);
                if (error < 0)
                {
                    CCLOG("can not read zip file %s, error code is %d", fileName, error);
                    fclose(out);
                    unzCloseCurrentFile(zipfile);
                    unzClose(zipfile);
                    return ErrorCode::UNCOMPRESS;
                }
                if (error > 0)
                {
                    //put in tmp folder
                    size_t bytesWritten = fwrite(readBuffer, 1, error, out);
                    if(bytesWritten != error){
//                        fclose(out);
//                        unzCloseCurrentFile(zipfile);
//                        unzClose(zipfile);
                        
                        static char arg[512];
                        sprintf(arg, "AssetsManager uncompress WRITE CHECK INNER WRITE ERROR:zipName:%s  filename:%s Bytes Written:%d  Expected:%d",outFileName.c_str(),fileNameStr.c_str(),bytesWritten,error);
                        Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
                           cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg,true);
                        });
                        checckFalse = true;
//                        return ErrorCode::WRITE_CHECK_ERROR;
                    }
                    totalBytesWritten += bytesWritten; // update total bytes written
                }
                
            } while(error > 0);
            #if (CC_TARGET_PLATFORM == CC_PLATFORM_IOS)
                fflush(out);
                if(ferror(out)) {
                    CCLOG("flush file error");
                    flushFalse = true;
                }
            #endif
            
            
            long fileSize = ftell(out);
            if(fileSize < 0) {
                CCLOG("get file size error");
                flushFalse = true;
            }
            fclose(out);
            
            // Verify the file size
            totalBytesWritten = totalBytesWritten * sizeof(char);
            if (fileSize != totalBytesWritten)
            {
                CCLOG("File size mismatch! Written: %zu, Actual: %ld", totalBytesWritten, fileSize);
//                unzCloseCurrentFile(zipfile);
//                unzClose(zipfile);
                static char arg[512];
                sprintf(arg, "AssetsManager uncompress WRITE CHECK OUTER WRITE ERROR:zipName:%s  fileSize:%d  totalBytesWritten:%d",outFileName.c_str(),fileSize,totalBytesWritten);
                Director::getInstance()->getScheduler()->performFunctionInCocosThread([=](){
                   cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",arg,true);
                });
                checckFalse = true;
//                return ErrorCode::WRITE_CHECK_ERROR;
               
            }
        }
        
        unzCloseCurrentFile(zipfile);
        
        // Goto next entry listed in the zip file.
        if ((i+1) < global_info.number_entry)
        {
            if (unzGoToNextFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not read next file");
                unzClose(zipfile);
                return ErrorCode::UNCOMPRESS;
            }
        }
    }
    
    CCLOG("end uncompressing");
    unzClose(zipfile);
    if(checckFalse == true){
        return ErrorCode::WRITE_CHECK_ERROR;
    }
    if(flushFalse == true){
        return ErrorCode::FLUSH_ERROR;
    }
    return ErrorCode::FILE_OK;
}
bool AssetsManager::Uncompress()
{
    // Open the zip file
    string outFileName = _storagePath + TEMP_PACKAGE_FILE_NAME;
    unzFile zipfile = unzOpen(FileUtils::getInstance()->getSuitableFOpen(outFileName).c_str());
    if (! zipfile)
    {
        CCLOG("can not open downloaded zip file %s", outFileName.c_str());
        return false;
    }
    
    // Get info about the zip file
    unz_global_info global_info;
    if (unzGetGlobalInfo(zipfile, &global_info) != UNZ_OK)
    {
        CCLOG("can not read file global info of %s", outFileName.c_str());
        unzClose(zipfile);
        return false;
    }
    
    // Buffer to hold data read from the zip file
    char readBuffer[BUFFER_SIZE];
    
    CCLOG("start uncompressing");
    
    // Loop to extract all files.
    uLong i;
    for (i = 0; i < global_info.number_entry; ++i)
    {
        // Get info about current file.
        unz_file_info fileInfo;
        char fileName[MAX_FILENAME];
        if (unzGetCurrentFileInfo(zipfile,
                                  &fileInfo,
                                  fileName,
                                  MAX_FILENAME,
                                  nullptr,
                                  0,
                                  nullptr,
                                  0) != UNZ_OK)
        {
            CCLOG("can not read file info");
            unzClose(zipfile);
            return false;
        }
        
        const string fullPath = _storagePath + fileName;
        
        // Check if this entry is a directory or a file.
        const size_t filenameLength = strlen(fileName);
        if (fileName[filenameLength-1] == '/')
        {
            // Entry is a directory, so create it.
            // If the directory exists, it will failed silently.
            if (!FileUtils::getInstance()->createDirectory(fullPath))
            {
                CCLOG("can not create directory %s", fullPath.c_str());
                unzClose(zipfile);
                return false;
            }
            
        }
        else
        {
            //There are not directory entry in some case.
            //So we need to test whether the file directory exists when uncompressing file entry
            //, if does not exist then create directory
            const string fileNameStr(fileName);
            size_t startIndex=0;
            
            size_t index=fileNameStr.find("/",startIndex);
            
            while(index != std::string::npos)
            {
                CCLOG("Uncompress 1 fileNameStr %s index %d", fileNameStr.c_str(), (int)index);
                const string dir=_storagePath+fileNameStr.substr(0,index);
                
                FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(dir).c_str(), "r");
                
                if(!out)
                {
                    if (!FileUtils::getInstance()->createDirectory(dir))
                    {
                        CCLOG("can not create directory %s", dir.c_str());
                        unzClose(zipfile);
                        return false;
                    }
                    else
                    {
                        CCLOG("create directory %s",dir.c_str());
                    }
                }
                else
                {
                    fclose(out);
                }
                startIndex=index+1;
                
                index=fileNameStr.find("/",startIndex);
                
            }
            
            // Entry is a file, so extract it.
            
            // Open current file.
            if (unzOpenCurrentFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not open file %s", fileName);
                unzClose(zipfile);
                return false;
            }
            
            // Create a file to store current file.
            FILE *out = fopen(FileUtils::getInstance()->getSuitableFOpen(fullPath).c_str(), "wb");
            if (! out)
            {
                CCLOG("can not open destination file %s", fullPath.c_str());
                unzCloseCurrentFile(zipfile);
                unzClose(zipfile);
                return false;
            }

            // Write current file content to destinate file.
            int error = UNZ_OK;
            do
            {
                error = unzReadCurrentFile(zipfile, readBuffer, BUFFER_SIZE);
                if (error < 0)
                {
                    CCLOG("can not read zip file %s, error code is %d", fileName, error);
                    unzCloseCurrentFile(zipfile);
                    unzClose(zipfile);
                    return false;
                }

                if (error > 0)
                {
                    //put in tmp folder
                    fwrite(readBuffer, error, 1, out);
                }
            } while(error > 0);
            fclose(out);
            
        }
        
        unzCloseCurrentFile(zipfile);
        
        // Goto next entry listed in the zip file.
        if ((i+1) < global_info.number_entry)
        {
            if (unzGoToNextFile(zipfile) != UNZ_OK)
            {
                CCLOG("can not read next file");
                unzClose(zipfile);
                return false;
            }
        }
    }
    CCLOG("end uncompressing");
    unzClose(zipfile);
    return true;
}

void AssetsManager::setSearchPath()
{
    return;
//    vector<string> searchPaths = FileUtils::getInstance()->getSearchPaths();
//    vector<string>::iterator iter = searchPaths.begin();
//    CCLOG("setSearchPath %s",_storagePath.c_str());
//    searchPaths.insert(iter, _storagePath);
//    FileUtils::getInstance()->setSearchPaths(searchPaths);
}

const char* AssetsManager::getPackageUrl() const
{
    return _packageUrl.c_str();
}

void AssetsManager::setPackageUrl(const char *packageUrl)
{
    _packageUrl = packageUrl;
}

const char* AssetsManager::getStoragePath() const
{
    return _storagePath.c_str();
}

void AssetsManager::setStoragePath(const char *storagePath)
{
    _storagePath = storagePath;
    checkStoragePath();
}
const char* AssetsManager::getUncompressPath() const
{
    return _uncompressPath.c_str();
}

void AssetsManager::setUncompressPath(const char *uncompressPath)
{
    _uncompressPath = uncompressPath;
    checkUncompressPath();
}
const char* AssetsManager::getVersionFileUrl() const
{
    return _versionFileUrl.c_str();
}

void AssetsManager::setVersionFileUrl(const char *versionFileUrl)
{
    _versionFileUrl = versionFileUrl;
}

string AssetsManager::getVersion()
{
    return UserDefault::getInstance()->getStringForKey(keyOfVersion().c_str());
}

void AssetsManager::deleteVersion()
{
    UserDefault::getInstance()->setStringForKey(keyOfVersion().c_str(), "");
}

void AssetsManager::setDelegate(AssetsManagerDelegateProtocol *delegate)
{
    _delegate = delegate;
}

void AssetsManager::setConnectionTimeout(unsigned int timeout)
{
    _connectionTimeout = timeout;
}

unsigned int AssetsManager::getConnectionTimeout()
{
    return _connectionTimeout;
}

std::vector<std::string> AssetsManager::split(std::string str, std::string limit){
    std::vector<std::string> res;
    if("" == str) return  res;
    
    string strs = str + limit;
    size_t pos;
    size_t size = strs.size();
 
    for (int i = 0; i < size; ++i) {
        pos = strs.find(limit, i);
        if( pos < size) {
            string s = strs.substr(i, pos - i);
            res.push_back(s);
            i = pos + limit.size() - 1;
        }
        
    }
    return res;
}

AssetsManager* AssetsManager::create(const char* packageUrl, const char* versionFileUrl, const char* storagePath, ErrorCallback errorCallback, ProgressCallback progressCallback, SuccessCallback successCallback )
{
    class DelegateProtocolImpl : public AssetsManagerDelegateProtocol 
    {
    public :
        DelegateProtocolImpl(ErrorCallback& aErrorCallback, ProgressCallback& aProgressCallback, SuccessCallback& aSuccessCallback)
        : errorCallback(aErrorCallback), progressCallback(aProgressCallback), successCallback(aSuccessCallback)
        {}

        virtual void onError(AssetsManager::ErrorCode errorCode) { errorCallback(int(errorCode)); }
        virtual void onProgress(int percent) { progressCallback(percent); }
        virtual void onSuccess() { successCallback(); }

    private :
        ErrorCallback errorCallback;
        ProgressCallback progressCallback;
        SuccessCallback successCallback;
    };

    auto* manager = new (std::nothrow) AssetsManager(packageUrl,versionFileUrl,storagePath);
    auto* delegate = new (std::nothrow) DelegateProtocolImpl(errorCallback,progressCallback,successCallback);
    manager->setDelegate(delegate);
    manager->_shouldDeleteDelegateWhenExit = true;
    manager->autorelease();
    return manager;
}

NS_CC_EXT_END;
