import org.apache.tools.ant.taskdefs.condition.Os
apply plugin: 'com.android.application'
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'com.google.firebase.crashlytics'
// Remove Fabric plugin
// apply plugin: 'io.fabric'
apply plugin: 'com.huawei.agconnect'
//apply plugin: 'apm-plugin'
apply plugin: 'apmplus-upload-plugin' // 火山自动上传符号表
ApmPlusUploadConfig { // 火山自动上传符号表
    api_token = "68aeb324-6511-11ef-b169-00163e03c63d" // 从平台的 全部功能->符号表管理->系统选择 Android->下面可以看到api key和api token。或者联系开发同学获取到的对应appId的一个token，私有化部署不用设置
    api_key = 2653 // 从平台的 全部功能->符号表管理->系统选择 Android->下面可以看到api key和api token。或者联系开发同学获取到的对应appId的一个key，私有化部署不用设置
    aid = 643183 // 应用性能监控全链路版上的appid
//       updateVersionCode = -1 // 默认不用写这一行， 除非您在初始化的时候手动指定了非标准版本号（或SDK版本号）; 此处一定要与初始化的版本号一致
//       mappingUrl = "https://www.xxx.com" // 默认不用写这一行，如果是私有化，这里配置私有化平台域名
}
//ApmPlugin { // 启动分析和页面体验相关功能依赖插件插桩
//    // 是否进行插桩
//    enable true
//    // 是否在Debug包插桩，默认插桩
//    enableInDebug true
//    // DEBUG("DEBUG"), INFO("INFO"), WARN("WARN"), ERROR("ERROR");
//    // INFO 级别Log会汇总所有被插桩处理的类供查看，路径 app/build/ByteX/ApmPlugin
//    logLevel "DEBUG"
//    // 监控App启动耗时，需要同时开启pageLoadSwitch
//    startSwitch = true
//    // 监控Activity的生命周期耗时
//    pageLoadSwitch = true
//    // 监控okhttp3的网络请求
//    okHttp3Switch = true
//    // 只对白名单下的包进行插桩。可以填写自己的包名。不配置则不会插桩，影响网络监控也启动监控等功能。
//    whiteList = [
//            "com.grandegames.slots.dafu.casino"
//    ]
//    // 黑名单包下类不进行插桩，可以配置包名和类名。没有可以为空
//    blackList = [
//    ]
//}

android {
    namespace "com.grandegames.slots.dafu.casino"
    compileSdk 35

    // 添加Asset Delivery每个资源包的名称
    if (project.hasProperty("includeAssets")) {
        assetPacks = [":jw_res"]
        // 启用按纹理拆分资源包
        bundle {
            texture {
                enableSplit true
            }
        }
    }



    productFlavors {
        flavorDimensions "distribution_channel"
        google {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
			manifestPlaceholders = [deeplink_scheme:"slots.games.vegas.night.casino",
                                    mainactivity_action:"android.intent.action.MAIN",
                                    mainactivity_category:"android.intent.category.LAUNCHER",
                                    splashactivity_action:"not_used",
                                    splashactivity_category:"not_used"]
        }
        official {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino.official"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
            manifestPlaceholders = [deeplink_scheme:"slots.games.vegas.night.casino",
                                    mainactivity_action:"android.intent.action.MAIN",
                                    mainactivity_category:"android.intent.category.LAUNCHER",
                                    splashactivity_action:"not_used",
                                    splashactivity_category:"not_used"]
        }

        androidpc {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
            manifestPlaceholders = [deeplink_scheme:"slots.games.vegas.night.casino",
                                    mainactivity_action:"android.intent.action.MAIN",
                                    mainactivity_category:"android.intent.category.LAUNCHER",
                                    splashactivity_action:"not_used",
                                    splashactivity_category:"not_used"]
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86'
            }
        }

        amazon {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino.amazon"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
            manifestPlaceholders = [deeplink_scheme:"com.grandegames.slots"]
        }

        huawei {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino.huawei"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
            manifestPlaceholders = [deeplink_scheme:"com.grandegames.slots"]
        }

        samsung {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino.samsung"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
            manifestPlaceholders = [deeplink_scheme:"slots.games.vegas.night.casino",
                                    mainactivity_action:"android.intent.action.MAIN",
                                    mainactivity_category:"android.intent.category.LAUNCHER",
                                    splashactivity_action:"not_used",
                                    splashactivity_category:"not_used"]
        }

        onestore {
            dimension "distribution_channel"
            applicationId "com.grandegames.slots.dafu.casino.onestore"
            targetSdkVersion PROP_TARGET_SDK_VERSION
            versionCode PROP_VERSION_CODE as int
            versionName PROP_VERSION_NAME
            manifestPlaceholders = [deeplink_scheme:"slots.games.vegas.night.casino",
                                    mainactivity_action:"android.intent.action.MAIN",
                                    mainactivity_category:"android.intent.category.LAUNCHER",
                                    splashactivity_action:"not_used",
                                    splashactivity_category:"not_used"]
        }
    }

    defaultConfig {
        applicationId "com.grandegames.slots.dafu.casino.amazon"
        minSdkVersion 24
        targetSdkVersion PROP_TARGET_SDK_VERSION
        versionCode PROP_VERSION_CODE as int
        versionName PROP_VERSION_NAME
        multiDexEnabled true

        manifestPlaceholders = [mainactivity_action:"not_used",
                                mainactivity_category:"not_used",
                                splashactivity_action:"android.intent.action.MAIN",
                                splashactivity_category:"android.intent.category.LAUNCHER"]

        ndk {
            // 设置支持的SO库架构
            abiFilters 'armeabi-v7a', 'arm64-v8a' //, 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }


    sourceSets.main {
        java.srcDir "src"
        manifest.srcFile "AndroidManifest.xml"
    }

    signingConfigs {
        release {
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                storeFile file(RELEASE_STORE_FILE)
                storePassword RELEASE_STORE_PASSWORD
                keyAlias RELEASE_KEY_ALIAS
                keyPassword RELEASE_KEY_PASSWORD
            }
        }

        debug {
            storeFile file(RELEASE_STORE_FILE)
            storePassword RELEASE_STORE_PASSWORD
            keyAlias RELEASE_KEY_ALIAS
            keyPassword RELEASE_KEY_PASSWORD         
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (project.hasProperty("RELEASE_STORE_FILE")) {
                signingConfig signingConfigs.release
            }
            //release包不需要调试代码，这里填入一个常规的权限
            //区别见下面debug的配置
            manifestPlaceholders = [debug_need_permission:"android.permission.INTERNET",
                                    debug_need_permission2:"android.permission.INTERNET"]
        }

        debug {
            // Only use this flag on builds you don't proguard or upload to beta-by-crashlytics.bing
            ext.alwaysUpdateBuildId = false
            signingConfig signingConfigs.debug
            //debug包需要往/sdcard push代码调试，需要这个权限
            //区别见上面release的配置
            manifestPlaceholders = [debug_need_permission:"android.permission.READ_EXTERNAL_STORAGE",
                                    debug_need_permission2:"android.permission.WRITE_EXTERNAL_STORAGE"]
        }
    }

    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
       exclude 'META-INF/androidx.localbroadcastmanager_localbroadcastmanager.version'
       exclude 'META-INF/androidx.legacy_legacy-support-core-utils.version'
       exclude 'META-INF/androidx.print_print.version'
       exclude 'META-INF/androidx.documentfile_documentfile.version'
       exclude 'META-INF/androidx.loader_loader.version'
       exclude 'META-INF/proguard/androidx-annotations.pro'
       pickFirst("META-INF/*.kotlin_module")
    }
}

repositories {
    maven { url("../googlenpgaccountsdk/play_services_games_v2_16.0.1-eap/") }
//    maven { url("https://android-sdk.is.com/") }
    google()
//    maven { url 'https://jitpack.io' }
    // mopub
    mavenCentral() // includes the MoPub SDK and AVID library
    mavenCentral()
//    maven { url "https://s3.amazonaws.com/moat-sdk-builds" }
    // maven { url "https://maven.google.com" } // necessary for Android API 26
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':sdkbase')
    implementation project(':libcocos2dx')
    implementation project(':comonprotocol')
    implementation project(':pluginhelpshift')
    implementation project(':pluginfacebook')
    implementation project(':pluginfirebase')

    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'com.android.installreferrer:installreferrer:2.2'
    implementation 'com.google.android.gms:play-services-analytics:17.0.1' //11.8.0
    implementation 'com.google.firebase:firebase-crashlytics:19.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.core:core:1.7.0'
    implementation "androidx.core:core-ktx:1.7.0"
    implementation("com.google.android.ump:user-messaging-platform:2.1.0")
    implementation "net.consentmanager.sdk:android:2.2.0"
    implementation 'com.helpshift:helpshift-sdkx:10.1.0'
//    implementation 'com.github.tiktok:tiktok-business-android-sdk:1.3.3'

//   googleImplementation project(':pluginGoolgeIAP')
    googleImplementation project(':ironsourcesdk')
    //googleImplementation project(':pluginmopub')
    googleImplementation project(':googlepaysdk')
	googleImplementation project(':googlenpgaccountsdk')
    googleImplementation project(':GoogleExtraLibs')
    googleImplementation project(':twittersdk')
    googleImplementation project(':sharesdk')
//    googleImplementation project(':odeeosdk')

    androidpcImplementation project(':googlenpgaccountsdk')
    androidpcImplementation project(':googlepaysdk')

    huaweiImplementation project(':pluginmopub')
    huaweiImplementation project(':hwsdk')

    samsungImplementation project(':samsungpaysdk')

    onestoreImplementation project(":onestorepaysdk")

    samsungImplementation project(':samsungaccountsdk')
    samsungImplementation project(':samsungclouddevsdk')
    samsungImplementation project(':samsungadssdk')
    samsungImplementation project(':sharesdk')

    // 火山应用性能监控
    implementation 'com.volcengine:apm_insight:1.5.6.cn'
    implementation 'com.volcengine:apm_insight_crash:1.5.15-rc.3'
//    implementation 'androidx.lifecycle:lifecycle-process:2.3.1'
//    implementation 'androidx.lifecycle:lifecycle-common-java8:2.3.1'
    implementation 'com.android.support:support-annotations:28.0.0'
    implementation 'com.android.support:appcompat-v7:28.0.0'
}

subprojects {
    project.configurations.all {
        resolutionStrategy.eachDependency { details ->
            if (details.requested.group == 'androidx.lifecycle'
                    && !details.requested.name.contains('lifecycle-viewmodel') ) {
                details.useVersion '2.1.0'
            }
            if (details.requested.group == 'androidx.versionedparcelable'
                    && !details.requested.name.contains('versionedparcelable') ) {
                details.useVersion '1.1.0'
            }
            if (details.requested.group == 'androidx.fragment'
                    && !details.requested.name.contains('fragment') ) {
                details.useVersion '1.1.0'
            }
            if (details.requested.group == 'androidx.lifecycle'
                    && !details.requested.name.contains('lifecycle-runtime') ) {
                details.useVersion '2.1.0'
            }
        }
    }
}

apply plugin: 'com.google.gms.google-services'