﻿/****************************************************************************
 *
 * CRI Middleware SDK
 *
 * Copyright (c) 2016 CRI Middleware Co., Ltd.
 *
 * Library  : CRI File System
 * Module   : Library User's Header
 * File     : crifs_web_installer.h
 *
 ****************************************************************************/
/*!
 *	\file		crifs_web_installer.h
 */

/*JP
 * \addtogroup FSLIB_CRIFS_WEB_INSTALLER
 * HTTP によるローカルストレージへのインストール機能を使用するためのAPIです。
 * @{
 */
/*EN
 * \addtogroup FSLIB_CRIFS_WEB_INSTALLER
 * @{
 */


/* 多重定義防止 */
/* Prevention of redefinition */
#ifndef CRIFS_WEB_INSTALLER_H_INCLUDED
#define CRIFS_WEB_INSTALLER_H_INCLUDED

#ifndef CRIFS_WEB_INSTALLER_PLATFORM_H_INCLUDED
#    error please include platform crifs_web_installer header
#endif


/***************************************************************************
 *      インクルードファイル
 *      Include files
 ***************************************************************************/
#include "cri_xpt.h"
#include "cri_error.h"
#include "cri_file_system.h"


/***************************************************************************
 *      定数マクロ
 *      Macro Constants
 ***************************************************************************/
/* バージョン情報 */
/* Version Number */
#define CRIFSWEBINSTALLER_VER_NAME	  "CriFsWebInstaller"
#define CRIFSWEBINSTALLER_VER_NUM	  "2.1.21"

/*JP
 * \brief 無効なHTTPステータスコード
 * \par 説明:
 * 無効なHTTPステータスコードを表わす定数です。<br>
 * HTTP以外の原因でインストールに失敗した場合にセットされます。<br>
 * この値は負値であることが保証されます。
 * \sa CriFsWebInstallerHttpStatusCode, CriFsWebInstallerStatusInfo.http_status_code
 */
#define CRIFSWEBINSTALLER_INVALID_HTTP_STATUS_CODE    -1

/*JP
 * \brief 無効なコンテンツサイズ
 * \par 説明:
 * インストール対象のサイズが取得出来ていない場合にセットされます。<br>
 * この値は負値であることが保証されます。
 * \sa CriFsWebInstallerStatusInfo.contents_size
 */
#define CRIFSWEBINSTALLER_INVALID_CONTENTS_SIZE    -1


/***************************************************************************
 *      処理マクロ
 *      Macro Functions
 ***************************************************************************/
/*JP
 * \brief デフォルトコンフィギュレーションのセット
 * \param[in]	p_config	コンフィギュレーション
 * \par 説明:
 * ::criFsWebInstaller_Initialize 関数に設定するコンフィギュレーション（ ::CriFsWebInstallerConfig ）に、デフォルトの値をセットします。<br>
 * \sa
 * criFsWebInstaller_Initialize, CriFsWebInstallerConfig
 */
#define criFsWebInstaller_SetDefaultConfig(p_config)								\
	{																				\
		(p_config)->num_installers		 = 2;										\
		(p_config)->proxy_host			 = NULL;									\
		(p_config)->proxy_port			 = 0;										\
		(p_config)->user_agent			 = NULL;									\
		(p_config)->inactive_timeout_sec = 300;										\
		(p_config)->allow_insecure_ssl	 = CRI_FALSE;								\
		(p_config)->crc_enabled			 = CRI_FALSE;								\
		criFsWebInstaller_SetPlatformDefaultConfig(&((p_config)->platform_config));	\
	}


/***************************************************************************
 *      データ型宣言
 *      Data Type Declarations
 ***************************************************************************/
/*JP
 * \brief CriFsWebInstaller コンフィギュレーション
 * \par 説明:
 * CriFsWebInstaller 動作仕様を指定するための構造体です。<br>
 * モジュール初期化時（::criFsWebInstaller_Initialize 関数）に引数として本構造体を指定します。<br>
 * \par 備考:
 * ::criFsWebInstaller_SetDefaultConfig 関数でデフォルトパラメータをセットし、 ::criFsWebInstaller_Initialize 関数に指定してください。<br>
 * \attention
 * 将来的にメンバーが増える可能性に備え、設定前に ::criFsWebInstaller_SetDefaultConfig 関数で初期化してから使用してください。<br>
 * \sa criFsWebInstaller_Initialize, criFsWebInstaller_SetDefaultConfig
 */
typedef struct CriFsWebInstallerConfigTag
{
	/*JP
	 * \brief 同時に使用するインストーラーの最大数設定
	 * \par 説明：
	 * この数を越えて CriFsWebInstaller を同時に生成することは出来ません。
	 */
	CriUint32 num_installers;

	/*JP
	 * \brief HTTP プロキシサーバーホスト名設定
	 * \par 説明：
	 * CriFsWebInstaller で使用するプロキシサーバーのホスト名を設定してください。<br>
	 * NULL が設定された場合は、プロキシサーバーは使用されません。
	 */
	const CriChar8* proxy_host;

	/*JP
	 * \brief HTTP プロキシサーバーポート設定
	 * \par 説明：
	 * CriFsWebInstaller で使用するプロキシサーバーのポートを設定してください。<br>
	 * この値は、 CriFsWebInstallerConfig.proxy_host != NULL の場合のみ効果があります。
	 */
	CriUint16 proxy_port;

	/*JP
	 * \brief User-Agent 設定
	 * \par 説明：
	 * デフォルトの User-Agent を上書きする際に設定してください。
	 * NULL が設定された場合は、デフォルトの User-Agent が使用されます。
	 */
	const CriChar8* user_agent;

	/*JP
	 * \brief タイムアウト時間設定(秒単位)
	 * \par 説明：
	 * この時間の間、受信済みのサイズが変化しない場合にタイムアウトエラー( CRIFSWEBINSTALLER_ERROR_TIMEOUT )が発生します。
	 * \sa CriFsWebInstallerStatusInfo.error, CRIFSWEBINSTALLER_ERROR_TIMEOUT
	 */
	CriUint32 inactive_timeout_sec;

	/*JP
	 * \brief 安全でない HTTPS 通信の許可設定
	 * \par 説明：
	 * CRI_TRUE の場合、安全でない HTTPS 通信を許可します。<br>
	 * アプリケーション開発時に、有効なサーバー証明書を用意出来ない場合のみ CRI_TRUE を設定してください。
	 * \attention
	 *   - Apple のプラットフォームにおいて安全でない HTTPS 通信を許可するためには、
	 *     このフラグを true にすることに加えて、 ATS(App Transport Security) を無効にするか、
	 *     例外設定を行なう必要があります。
	 *   - Android ではこのフラグを true にすることができません。
	 *     初期化時にエラーとなります。
	 */
	CriBool allow_insecure_ssl;
    
	/*JP
	 * \brief CRCの有効化
	 * \par 説明：
	 * CRI_TRUE の場合のみ、CRCの計算をします。
	 */
	CriBool crc_enabled;

	/*JP
	 * \brief プラットフォーム固有の設定
	 */
	CriFsWebInstallerPlatformConfig platform_config;
} CriFsWebInstallerConfig;

/*JP
 * \brief CriFsWebInstaller ハンドル
 */
typedef struct CriFsWebInstaller* CriFsWebInstallerHn;

/*JP
 * \brief CriFsWebInstaller ステータス
 * \par 説明：
 * インストーラーハンドルの状態を表します。<br>
 * ::criFsWebInstaller_GetStatusInfo 関数により取得できます。
 * \sa CriFsWebInstallerStatusInfo, criFsWebInstaller_GetStatusInfo
 */
typedef enum CriFsWebInstallerStatusTag
{
	CRIFSWEBINSTALLER_STATUS_STOP,      /*JP< 停止中		*/	/*EN< Stopping			*/
	CRIFSWEBINSTALLER_STATUS_BUSY,      /*JP< 処理中		*/	/*EN< Busy				*/
	CRIFSWEBINSTALLER_STATUS_COMPLETE,  /*JP< 完了		*/	/*EN< Complete			*/
	CRIFSWEBINSTALLER_STATUS_ERROR      /*JP< エラー		*/	/*EN< Error				*/
} CriFsWebInstallerStatus;

XPT_STATIC_ASSERT(sizeof(CriFsWebInstallerStatus) == 4);

/*JP
 * \brief CriFsWebInstaller のエラー種別
 * \par 説明：
 * インストーラーハンドルのエラー種別を表します。<br>
 * ::criFsWebInstaller_GetStatusInfo 関数により取得できます。
 * INTERNALエラーが返ってきた場合はデバグ実行でログの詳細を確認ください。
 * \sa CriFsWebInstallerStatusInfo, criFsWebInstaller_GetStatusInfo
 */
typedef enum CriFsWebInstallerErrorTag
{
	CRIFSWEBINSTALLER_ERROR_NONE,       /*JP< エラーなし						*/	/*EN< No error					*/
	CRIFSWEBINSTALLER_ERROR_TIMEOUT,    /*JP< タイムアウトエラー					*/	/*EN< Timeout error				*/
	CRIFSWEBINSTALLER_ERROR_MEMORY,     /*JP< メモリ確保失敗					*/	/*EN< Memory allocation error	*/
	CRIFSWEBINSTALLER_ERROR_LOCALFS,    /*JP< ローカルファイルシステムエラー			*/	/*EN< Local filesystem error	*/
	CRIFSWEBINSTALLER_ERROR_DNS,        /*JP< DNSエラー						*/	/*EN< DNS error					*/
	CRIFSWEBINSTALLER_ERROR_CONNECTION, /*JP< 接続エラー						*/	/*EN< Connection error			*/
	CRIFSWEBINSTALLER_ERROR_SSL,        /*JP< SSLエラー						*/	/*EN< SSL error					*/
	CRIFSWEBINSTALLER_ERROR_HTTP,       /*JP< HTTPエラー						*/	/*EN< HTTP error				*/

	CRIFSWEBINSTALLER_ERROR_INTERNAL,   /*JP< 内部エラー(予期せぬエラー)			*/	/*EN< Internal error			*/
} CriFsWebInstallerError;

XPT_STATIC_ASSERT(sizeof(CriFsWebInstallerError) == 4);

/*JP
 * \brief HTTPステータスコード
 * \par 説明：
 * HTTPステータスコードを表す型です。<br>
 * CRIFSWEBINSTALLER_INVALID_HTTP_STATUS_CODE は無効な値を表わします。
 * \sa CriFsWebInstallerStatusInfo, CRIFSWEBINSTALLER_INVALID_HTTP_STATUS_CODE
 */
typedef CriSint32 CriFsWebInstallerHttpStatusCode;

/*JP
 * \brief CriFsWebInstaller ステータス情報
 * \par 説明：
 * ::CriFsWebInstallerStatus を含む詳細な状態を表します。<br>
 * ::criFsWebInstaller_GetStatusInfo 関数により取得できます。
 * \sa CriFsWebInstallerStatusInfo, criFsWebInstaller_GetStatusInfo
 */
typedef struct CriFsWebInstallerStatusInfoTag
{
	/*JP
	 * \brief インストーラーハンドルの状態
	 * \sa CriFsWebInstallerStatus
	 */
	CriFsWebInstallerStatus status;

	/*JP
	 * \brief インストーラーハンドルのエラー状態
	 * \par 説明：
	 * CriFsWebInstallerStatusInfo.status == CRIFSWEBINSTALLER_STATUS_ERROR の際に、
	 * CRIFSWEBINSTALLER_ERROR_NONE 以外の値が格納されます。<br>
	 * エラー発生時には、エラー種別によって適切にエラーハンドリングを行なってください。
	 * \sa CriFsWebInstallerError
	 */
	CriFsWebInstallerError error;

	/*JP
	 * \brief HTTPステータスコード
	 * \par 説明：
	 * 以下のどちらかの場合に HTTPステータスコードが格納されます。<br>
	 *   - CriFsWebInstallerStatusInfo.status == CRIFSWEBINSTALLER_STATUS_COMPLETE <br>
	 *   - CriFsWebInstallerStatusInfo.status == CRIFSWEBINSTALLER_STATUS_ERROR かつ CriFsWebInstallerStatusInfo.error == CRIFSWEBINSTALLER_ERROR_HTTP <br>
	 *
	 * その他の場合は、負値( CRIFSWEBINSTALLER_INVALID_HTTP_STATUS_CODE )が格納されます。
	 * \sa CriFsWebInstallerHttpStatusCode, CRIFSWEBINSTALLER_INVALID_HTTP_STATUS_CODE
	 */
	CriFsWebInstallerHttpStatusCode http_status_code;

	/*JP
	 * \brief インストール対象のサイズ(byte)
	 * \par 説明：
	 * インストール対象のサイズ(byte)が格納されます。<br>
	 * インストール対象のサイズが不明な場合は負値( CRIFSWEBINSTALLER_INVALID_CONTENTS_SIZE ) が格納されます。<br>
	 * HTTP による転送が開始すると有効な値が格納されます。
	 * \sa CRIFSWEBINSTALLER_INVALID_CONTENTS_SIZE, CriFsWebInstallerStatusInfo.received_size
	 */
	CriSint64 contents_size;

	/*JP
	 * \brief 受信済みのサイズ(byte)
	 * \sa CriFsWebInstallerStatusInfo.contents_size
	 */
	CriSint64 received_size;
} CriFsWebInstallerStatusInfo;


/***************************************************************************
 *      関数宣言
 *      Prototype Functions
 ***************************************************************************/
#ifdef __cplusplus
extern "C" {
#endif


/*JP
 * \brief CriFsWebInstaller モジュールの初期化
 * \param[in]	config		コンフィギュレーション
 * \return		CriError	エラーコード
 * \par 説明:
 * CriFsWebInstaller モジュールを初期化します。<br>
 * モジュールの機能を利用するには、必ずこの関数を実行する必要があります。<br>
 * （モジュールの機能は、本関数を実行後、 ::criFsWebInstaller_Finalize 関数を実行するまでの間、利用可能です。）<br>
 * \attention
 * 本関数を実行後、必ず対になる ::criFsWebInstaller_Finalize 関数を実行してください。<br>
 * また、 ::criFsWebInstaller_Finalize 関数を実行するまでは、本関数を再度実行することはできません。<br>
 * \sa CriFsWebInstallerConfig, criFsWebInstaller_Finalize
 */
CriError CRIAPI criFsWebInstaller_Initialize(const CriFsWebInstallerConfig* config);

/*JP
 * \brief CriFsWebInstaller モジュールの終了
 * \return	CriError	エラーコード
 * \par 説明:
 * CriFsWebInstaller モジュールを終了します。<br>
 * \attention
 *   - ::criFsWebInstaller_Initialize 関数実行前に本関数を実行することはできません。<br>
 *   - 全ての ::CriFsWebInstallerHn が破棄されている必要があります。
 * \sa criFsWebInstaller_Initialize
 */
CriError CRIAPI criFsWebInstaller_Finalize(void);

/*JP
 * \brief サーバー処理の実行
 * \return	CriError	エラーコード
 * \par 説明:
 * サーバー処理を実行します。この関数は定期的（例：1Vに1回）に呼び出してください。<br>
 */
CriError CRIAPI criFsWebInstaller_ExecuteMain(void);


/*JP
 * \brief CriFsWebInstaller の作成
 * \param[out]	installer	インストーラーハンドル
 * \return		CriError	エラーコード
 * \par 説明:
 * インストーラーを作成し、インストーラーハンドルを返します。
 * \sa criFsWebInstaller_Destroy
 */
CriError CRIAPI criFsWebInstaller_Create(CriFsWebInstallerHn* installer);

/*JP
 * \brief CriFsWebInstaller の破棄
 * \param[in]	installer	インストーラーハンドル
 * \return		CriError	エラーコード
 * \par 説明:
 * インストーラーを破棄します。
 * \attention
 * インストーラーが CRIFSWEBINSTALLER_STATUS_STOP 状態である必要があります。
 * \sa criFsWebInstaller_Create
 */
CriError CRIAPI criFsWebInstaller_Destroy(CriFsWebInstallerHn installer);

/*JP
 * \brief ファイルのインストール
 * \param[in]	installer	インストーラーハンドル
 * \param[in]	url			インストール元URL
 * \param[in]	dst_path	インストール先ファイルパス名
 * \return		CriError	エラーコード
 * \par 説明:
 * ファイルのインストールを開始します。<br>
 * 本関数は即時復帰関数です。<br>
 * \attention
 *   - インストール先のファイルが存在する場合はエラー CRIFSWEBINSTALLER_ERROR_LOCALFS が発生します。
 *   - インストール先のフォルダーが存在しない場合はエラー CRIFSWEBINSTALLER_ERROR_LOCALFS が発生します。
 * \sa criFsWebInstaller_GetStatusInfo
 */
CriError CRIAPI criFsWebInstaller_Copy(CriFsWebInstallerHn installer, const CriChar8* url, const CriChar8* dst_path);

/*JP
 * \brief 処理の停止
 * \param[in]	installer	インストーラーハンドル
 * \return		CriError	エラーコード
 * \par 説明:
 * 処理を停止します。<br>
 * 本関数は即時復帰関数です。<br>
 * 停止の完了状態を取得するには ::criFsWebInstaller_GetStatusInfo 関数を使用してください。
 * \sa criFsWebInstaller_GetStatusInfo
 */
CriError CRIAPI criFsWebInstaller_Stop(CriFsWebInstallerHn installer);

/*JP
 * \brief ステータス情報の取得
 * \param[in]	installer	インストーラーハンドル
 * \param[out]	status_info	ステータス情報
 * \return		CriError	エラーコード
 * \par 説明:
 * ステータス情報を取得します。
 */
CriError CRIAPI criFsWebInstaller_GetStatusInfo(CriFsWebInstallerHn installer, CriFsWebInstallerStatusInfo* status_info);

/*JP
 * \brief CRC32計算結果の取得
 * \param[in]	installer	インストーラーハンドル
 * \param[out]	crc32	CRC結果格納用
 * \return		CriError	エラーコード
 * \par 説明:
 * Complete状態でのみ有効なチェックサムを返却します。<br>
 * Complete以外で取得した場合、CRC結果の値は0になります。<br>
 * 本関数は CriFsWebInstallerConfig.crc_enabled=CRI_TRUE の場合のみ使用可能です。
 */
CriError CRIAPI criFsWebInstaller_GetCRC32(CriFsWebInstallerHn installer, CriUint32 *crc32);

/*JP
 * \brief
 * \param[in]   field       フィールド名
 * \param[in]   value       フィールドの値
 * \return      CriError    エラーコード
 * \par 説明:
 * HTTPリクエストヘッダーの情報を変更します。<br>
 * この関数は ::criFsWebInstaller_Initialize 関数実行後に呼び出す必要があります。<br>
 * インストール実行前に本関数を実行してください。<br>
 * すでにフィールド名が登録されていた場合、フィールドの値を上書きします。<br>
 * フィールドの値として NULL が渡された場合、フィールドを削除します。<br>
 */
CriError CRIAPI criFsWebInstaller_SetRequestHeader(const CriChar8* field, const CriChar8* value);

#ifdef __cplusplus
}
#endif

#endif  /* CRIFS_WEB_INSTALLER_H_INCLUDED */


/*JP
 * @}
 */
/*EN
 * @}
 */


/* --- end of file --- */
