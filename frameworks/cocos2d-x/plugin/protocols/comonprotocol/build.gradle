apply plugin: 'com.android.library'

android {
    namespace "com.cocos2dx.commonplugin"
    compileSdk 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
//        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

   sourceSets.main {
        java.srcDir  "src/main/java"
        res.srcDir "src/main/res"
        jniLibs.srcDir "libs"
        manifest.srcFile "src/main/AndroidManifest.xml"
    }

}

repositories {
    mavenCentral()
    maven {
        url "https://maven.google.com"
    }
    flatDir {
        dirs 'libs'
    }
    mavenCentral()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar',])

    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.browser:browser:1.0.0'
    implementation 'androidx.vectordrawable:vectordrawable:1.0.0'

    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.0.0'
    implementation 'androidx.cardview:cardview:1.0.0'

    api 'com.adjust.sdk:adjust-android:5.4.1'
    implementation 'com.adjust.sdk:adjust-android-webbridge:5.4.1'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    implementation 'com.adjust.sdk:adjust-android-samsung-clouddev:5.4.1'
    implementation 'com.adjust.sdk:adjust-android-meta-referrer:5.0.0'
    implementation 'com.adjust.sdk:adjust-android-samsung-referrer:5.4.1'
}