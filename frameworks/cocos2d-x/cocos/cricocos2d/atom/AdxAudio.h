#ifndef CRICOCOS2D_MANA_AUDIO_H
#define CRICOCOS2D_MANA_AUDIO_H

/**
 * 1. LibraryContext 播放器初始化和删除
 */

#include "cricocos2d/cricocos2d.h"

namespace cricocos2d
{
	namespace atom
	{
		/**
		 * @headerfile cricocos2d/atom/AdxAudio.h
		 * 音效包装类
		 *
		 */
    class CC_DLL AdxAudio : public cocos2d::Ref
    {
    public:

        AdxAudio(int maxVoices, int standardMaxSamplingRate = 96000, int hcaMxMaxSamplingRate = 48000);
        ~AdxAudio();

        void loadAcfData(std::string acfFileName);
        
        // 流式播放时需添加awb
        void addAcbAwbData(std::string acbFileName, std::string awbFileName, std::string key);
        
        void addAcbData(std::string acbFileName, std::string key);
        
        void removeAcbData(std::string key);
        
        void allocateVoicePool(bool isHcaMx);
        
        int getNumCues(std::string key);
        
        std::string getCueInfoNameByIndex(int index, std::string key);
        
        int getCueInfoIdByIndex(int index, std::string key);
        
        // 获取音效时长
        long long getCueInfoLengthByName(std::string name, std::string key);
        
        int getPlayerStatus(std::string playerName);
        
        // 获取当前音效播放的时长 此函数返回的播放时间是从播放开始到现在经过的时间
        long long getPlayerTime(std::string playerName);
        
        // 获取播放器所占内存
        int getPlayerWorkSize();
        
        void setCueId(std::string playerName, signed int cueId, std::string key);
        
        void setCueName(std::string playerName, std::string cueName, std::string key);
        
        void setCueIndex(std::string playerName, signed int index, std::string key);
        
        // 开始播放
        // 在流式播放中，实际的音频播放会有一些延迟。这是因为音频数据的缓冲需要一些时间。
        int playerStart(std::string playerName);
        
        // 播放器更新数据,比如设置音量后
        void playerUpdate(std::string playerName, int playbackId);
        
        // 播放器更新数据,比如设置音量后
        void playerUpdateAll(std::string playerName);
        
        // 准备音效 准备音频数据的播放 （用于同时播放多个音效）
        // 跟playerStart一样，调用之前需要先设定要播放的cue
        // 此函数在暂停模式下开始声音播放
        // 当函数被调用时，为声音播放所需的资源会被分配，并开始对要流式传输的文件进行缓冲。但是，在缓冲完成后播放并不会开始,即使可以播放声音，播放也会保持暂停状态。
        // 此时需要调用 pausePlayer(true)来恢复播放；
        // 是否能够播放需要先判断状态 criAtomExPlayer_GetStatus(player) == CRIATOMEXPLAYBACK_STATUS_PLAYING
        int playerPrepare(std::string playerName);
        
        // 设置player的循环状态
        void setPlayerLoopState(std::string playerName, int count);
        
        // 设置播放器音量，在开始播放前设置
        // 默认是1.f, 0.f是静音
        void setPlayerVolume(std::string playerName, float vol);
        
        // 更改音调
        // 默认值是0.f，正数是升调，负数是降调
        void setPlayerPitch(std::string playerName, float offset);
        
        // 设置3d音源角度，正前方是0，右方是正度数，左方是负度数
        void setPlayerPan3dAngle(std::string playerName, float angle);
        
        // 设置3d音源距离
        void setplayerPan3dDistance(std::string playerName, float distance);
        
        // 设置3d类型 （在3d音源，监听者设置的时候需要先这是3d环境声明）
        // type:0, 1, 2
        void setPan3dType(std::string playerName, int type);
        
        // 创建3d音效监听者（耳朵）
        void create3dListener(std::string playerName);
        
        // 创建3d音源（包含位置信息）
        void create3dSource(std::string playerName);
        
        void set3dListenerPos(float x, float y, float z);
        
        void set3dSourcePos(float x, float y, float z);
        
        // 设置音源最小最大的距离
        void set3dSourceMinMaxDistance(float min, float max);
        
        void destroy3dListener();
        
        void destroy3dSource();
        
        // 设置双二阶滤波器
        // type 覆盖在数据中设置的值
        // CRIATOMEX_BIQUAD_FILTER_TYPE_LOWSHELF：低频搁板滤波器（公主被装在盒子里呼喊）
        // CRIATOMEX_BIQUAD_FILTER_TYPE_HIGHSHELF：高频搁板滤波器
        // CRIATOMEX_BIQUAD_FILTER_TYPE_PEAKING：峰值滤波器
        // frequency 添加到在数据中设置的值。 归一化频率是一个由对数尺度上24 Hz到24000 Hz范围的频率，范围到从0.0f到1.0f的范围的值时进行归一化
        // gain 乘以在数据中设置的值。增益必须以分贝为单位指定
        // q 添加到在数据中设置的值
        // 双二阶滤波器不适用于编码为HCA-MX的声音数据。如果您需要对声音使用双二阶滤波器，请使用其他编解码器，如ADX或HCA来编码声音
        void setPlayerBiquadFilter(std::string playerName, int type, float frequency, float gain, float q);
        
        // 带通滤波器只会允许这两个频率之间的频率通过
        // 截止频率在映射从对数尺度上的24 Hz到24000 Hz的范围到从0.0f到1.0f的范围的值时进行归一化
        // 最小0.0f， 最大 1.0f
        void setPlayerBandpassFilter(std::string playerName, float min, float max);
        
        // 清除以上两种滤波器，也会清除aisac
        void resetPlayerFilter(std::string playerName);
        
        // 控制音效由远及近(玩家在瀑布由远及近，可被criAtomExPlayer_ResetParameters清除)
        // param 0.0f ~ 1.0f
        void setPlayerAisacById(std::string playerName, int controlId, float param);
        
        void setPlayerAisacByName(std::string playerName, std::string controlName, float param);
        
        // 清除aisac设置,也需要 criAtomExPlayer_Update，criAtomExPlayer_UpdateAll 来立即生效
        void resetPlayerAisac(std::string playerName);
        
        // 初始化 fader
        void attatchPlayerFader(std::string playerName);
        
        // 音效渐入
        // time 毫秒
        void setPlayerFadeInTime(std::string playerName, float time);
        
        // 音效渐出
        // time 毫秒
        void setPlayerFadeOutTime(std::string playerName, float time);
        
        // 取消音效渐入渐出效果
        // 当此函数从播放器分离一个fader后，播放器不再进行淡入/淡出控制。
        // 备注:
        //如果从其中分离fader的播放器仍在播放声音，那么当此函数执行时，所有的声音都会停止。
        //如果你在不执行此函数的情况下销毁一个播放器，当播放器被销毁（通过执行::criAtomExPlayer_Destroy函数）时， 库内部会分离fader
        void detachPlayerFader(std::string playerName);
        
        // 开启混音（因为混音比较消耗cpu性能）
        void startDspBus(std::string busName);
        
        // 设置混音
        // busName 总线名称; level 发送级别值（0.0f至1.0f）
        void setPlayerDspBusLevel(std::string playerName, std::string busName, float level);
        
        // 关闭混音
        void stopDspBus();
        
        // 为某个音效添加分类
        void setPlayerCategoryByName(std::string playerName, std::string categoryName);
        
        // 为某个音效取消分类
        void unsetPlayerCategory(std::string playerName);
        
        // 设置分类音量(全局的)
        // vol 0.0f ~ 1.0f
        void setCategoryVolumeByName(std::string categoryName, float vol);
        
        float getCategoryVolumeByName(std::string categoryName);
        
        // 设置分类是否静音
        void setCategoryMuteByName(std::string categoryName, bool isMute);
        
        // 某分类是否静音
        bool isCategoryMuteByName(std::string categoryName);
        
        // 独奏给定分类的声音
        // isSolo 是否独奏 muteVol 独奏分类之外分类的音量
        void setCategorySoloByName(std::string categoryName, bool isSolo, float muteVol);
        
        // 某分类是否独奏
        bool isCategorySoloByName(std::string categoryName);
        
        int getPlaybackStatus(int playbackId);
        
        // 此函数返回的播放时间是自播放开始以来的时间
        long long getPlayBackTime(int playbackId);
        
        int getStandardNumUsedVoices();
        
        int getHCANumUsedVoices();
        
        void stopCueByPlaybackId(int playbackId);
        
        void stopPlayer(std::string playerName);
        
        // 是暂停还是恢复
        void pauseCueByPlaybackId(int playbackId, bool isPause);
        // 是暂停还是恢复
        void pausePlayer(std::string playerName, bool isPause);
        
        // 可恢复由 ::criAtomExPlayer_Pause 或 ::criAtomExPlayback_Pause 函数暂停的音频的播放
        void resumeCueByPlaybackId(int playbackId);
        
        void resumePlayer(std::string playerName);
        
        void startDbas();
        
        void createPlayer(std::string playerName);
        
        void destroyPlayer(std::string playerName);
        
        void destroyDbus();
        
        int getLastPlaybackId(std::string playerName);
        
        // 创建在播放速度声音池里的player
        void createSpeedPlayer(std::string playerName);
        
        // 更改声音的播放速度 speedRate : 0.25f ～ 3.0f
        void changeSpeed(std::string playerName, float speedRate);
        
        // 更改从声音中间时间段开始播放，startTime 毫秒
        void setPlayerStartTime(std::string playerName, long long startTime);
        
    protected:
        // 卸载
        void unloadDataAndFreeVoicePool();

    private:
        CriSint32 _maxVoices;
        CriSint32 _hcaMxMaxSamplingRate;
        CriSint32 _standardMaxSamplingRate;
        
        bool _isHcaMx; // 是否有hca-mx语音池
        uint _standardVPID; // 正常声音池的id
        uint _hcamxVPID;    // hca_mx声音池的id
        uint _speedVPID;    // 控制播放速度声音池的id
        
        CriAtomExPlayerConfig playerConfig;
        std::map<std::string, CriAtomExPlayerHn> atomExPlayerHnMap; // 播放器map
        std::map<std::string, CriAtomExAcbHn> atomExAcbHnMap; // 声音库map
        CriAtomExVoicePoolHn standardVoicePoolHn; // 声音池
        CriAtomExVoicePoolHn hcaMxVoicePoolHn;    // 声音池
        CriAtomExVoicePoolHn speedVoicePoolHn;    // 控制播放速度声音池
        CriAtomDbasId        dbas_id;             // 流式 我们一直在播放加载在内存中的。这个是从磁盘进行流式传输
        
        CriAtomEx3dListenerHn atomEx3dListener; // 3d的监听者
        CriAtomEx3dSourceHn   atomEx3dSource;   // 3d的音源
    };
	}
}


#endif
