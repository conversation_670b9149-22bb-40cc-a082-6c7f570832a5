/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/detail/log.h"
#include "cricocos2d/cricocos2d.h"

#include "cri_xpt.h"
#if defined(XPT_TGT_PC)
#    include <windows.h>
#elif defined(XPT_TGT_ANDROID)
#    include <android/log.h>
#endif

#include <cstdarg>
#include <cstdio>


namespace cricocos2d
{
	namespace detail
	{
		extern bool libraryContextExists;

		void printLog(LogSeverity log_severity, const char* format, ...)
		{
			char	message1[1024];

			va_list args;
			va_start(args, format);
			std::vsprintf(message1, format, args);
			va_end(args);

			char message[1024];
			switch (log_severity) {
			case LogSeverity::Error :
				std::sprintf(message, "[CRIWARE][ERROR] %s\n", message1);
				break;

			case LogSeverity::Warning :
				std::sprintf(message, "[CRIWARE][WARNING] %s\n", message1);
				break;

			case LogSeverity::Info :
				std::sprintf(message, "[CRIWARE][INFO] %s\n", message1);
				break;

			case LogSeverity::Debug :
				std::sprintf(message, "[CRIWARE][DEBUG] %s\n", message1);
				break;

			default :
				std::sprintf(message, "[CRIWARE][UNKNOWN] %s\n", message1);
			}

// 			// 传给lua
// #if CC_ENABLE_SCRIPT_BINDING
//             cocos2d::ScriptEngineManager::getInstance()->getScriptEngine()->executeGlobalFunctionWithString("printCriWareLog", message, true);
// #endif

#if defined(XPT_TGT_PC)
			OutputDebugStringA(message);
#elif defined(XPT_TGT_MACOSX) || defined(XPT_TGT_IOS)
			if (log_severity <= LogSeverity::Warning) {
				std::fputs(message, stderr);
				std::fflush(stderr);
			} else {
				std::fputs(message, stdout);
				std::fflush(stdout);
			}
//            int criIndex = cocos2d::UserDefault::getInstance()->getIntegerForKey("criIndex", 0);
//            criIndex = criIndex + 1;
//            
//            char outLog[100];
//            // std::sprintf(outLog, "criware%d", criIndex);
//            snprintf(outLog, sizeof(outLog) - 1, "criware%d", criIndex);
//            cocos2d::UserDefault::getInstance()->setStringForKey(outLog, message);
//            cocos2d::UserDefault::getInstance()->setIntegerForKey("criIndex", criIndex);
//            cocos2d::UserDefault::getInstance()->flush();
#elif defined(XPT_TGT_ANDROID)
			android_LogPriority android_log_priority;
			switch (log_severity) {
			case LogSeverity::Error :
				android_log_priority = ANDROID_LOG_ERROR;
				break;

			case LogSeverity::Warning :
				android_log_priority = ANDROID_LOG_WARN;
				break;

			case LogSeverity::Info :
				android_log_priority = ANDROID_LOG_INFO;
				break;

			case LogSeverity::Debug :
				android_log_priority = ANDROID_LOG_DEBUG;
				break;

			default :
				android_log_priority = ANDROID_LOG_UNKNOWN;
			}
			__android_log_print(android_log_priority, "CRIWARE for Cocos2d", "%s", message);
#endif
		}


		void assertLibraryContextExists()
		{
			if (!libraryContextExists) {
				printLog(LogSeverity::Error, "::cricocos2d::LibraryContext does not exist.");
				criErr_SetCallback(::cricocos2d::defaultErrorCallback);
			}
		}


	}
}
