/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/**
 * @addtogroup CRICOCOS2D
 * @{
 */

/**
 * @file cricocos2d/cricocos2d.h
 */


/* Prevention of redefinition */
#ifndef CRICOCOS2D_CRICOCOS2D_H
#define CRICOCOS2D_CRICOCOS2D_H


#include "cocos2d.h"

#include <cri_xpt.h>

#if defined(XPT_TGT_PC)
#    include <cri_file_system.h>
#    include <cri_atom_wasapi.h>
#    include <cri_mana_pc.h>
#elif defined(XPT_TGT_MACOSX)
#    include <cri_file_system.h>
#    include <cri_atom_macosx.h>
#    include <cri_mana.h>
#elif defined(XPT_TGT_IOS)
#    include <cri_file_system.h>
#    include <cri_atom_ios.h>
#    include <cri_mana_ios.h>
#elif defined(XPT_TGT_ANDROID)
#    include <cri_file_system_android.h>
#    include <cri_atom_android.h>
#    include <cri_mana_android.h>
#endif

#include <string>


/**
 * @~japanese
 * @brief cricocos2d のメジャーバージョン  的主要版本
 *
 * 後方互換性のない仕様変更があった場合に増加します。 在不兼容的情况下增加
 */
#define CRICOCOS2D_VERSION_MAJOR 3

/**
 * @~japanese
 * @brief cricocos2d のマイナーバージョン
 *
 * 後方互換性を保ったまま新機能が追加された場合に増加します。
 */
#define CRICOCOS2D_VERSION_MINOR 4

/**
 * @~japanese
 * @brief cricocos2d のパッチバージョン
 *
 * 後方互換性を保ったままバグ修正や性能改善が行われた場合に増加します。
 */
#define CRICOCOS2D_VERSION_PATCH 0

/**
 * @~japanese
 * @brief cricocos2d の名前
 */
#define CRICOCOS2D_VER_NAME    "cricocos2d"

/**
 * @~japanese
 * @brief cricocos2d のバージョン番号文字列
 */
#define CRICOCOS2D_VER_NUM    "3.4.0"

/**
 * @def CRICOCOS2D_NOEXCEPT
 * @brief noexcept specifier macro
 */
#if defined(_MSC_VER)
#    if (_MSC_VER < 1900)
#        define CRICOCOS2D_NOEXCEPT
#    else
#        define CRICOCOS2D_NOEXCEPT    noexcept
#    endif
#else
#    define CRICOCOS2D_NOEXCEPT    noexcept
#endif


/**
 * @~japanese
 * @brief Cocos2d で CRIWARE を使用するための関数、クラスを定義している名前空間
 */
namespace cricocos2d
{
	/**
	 * @headerfile cricocos2d/cricocos2d.h
	 *
	 * @~japanese
	 * @brief CRIWARE で使用するメモリアロケータ 中使用的内存定位器
	 */
	struct Allocator
	{
		/**
		 * @~japanese
		 * @brief メモリ確保コールバック関数ポインタ
		 *
		 * メモリ確保コールバック関数ポインタです。\n
		 * コールバック関数が実行される際には、 size に必要とされるメモリのサイズがセットされています。\n
		 * コールバック関数内で size 分のメモリを確保し、確保したメモリのアドレスを戻り値として返してください。\n
		 * 尚、引数の obj には、#obj に設定したオブジェクトが渡されます。
		 *
		 * @attention
		 * メモリの確保に失敗した場合、エラーコールバックが返されたり、呼び出し元の関数が
		 * 失敗する可能性がありますのでご注意ください。
		 *
		 * @param[in]	user_obj	ユーザ指定オブジェクト(#obj が渡されます)
		 * @param[in]	size		要求メモリサイズ(バイト単位)
		 * @return		確保したメモリのアドレス(失敗時はNULL)
		 */
		void* (* allocate)(void* user_obj, CriUint32 size);

		/**
		 * @~japanese
		 * @brief メモリ解放コールバック関数ポインタ
		 *
		 * メモリ解放コールバック関数ポインタです。\n
		 * コールバック関数が実行される際には、 mem に解放すべきメモリのアドレスがセットされています。\n
		 * コールバック関数内で mem の領域のメモリを解放してください。
		 * 尚、引数の obj には、#obj に設定したオブジェクトが渡されます。
		 *
		 * @param[in]	user_obj	ユーザ指定オブジェクト(#obj が渡されます)
		 * @param[in]	mem			解放するメモリアドレス
		 */
		void (* deallocate)(void* user_obj, void* mem);

		/**
		 * @~japanese
		 * @brief ユーザ指定オブジェクト
		 *
		 * #allocate, #deallocate コールバック関数が実行される際に第一引数として渡されるポインタです。\n
		 * #allocate, #deallocate コールバック関数内でメモリマネージャ等を参照する必要がある場合には、本データメンバにポインタを設定してください。
		 */
		void* obj;
	};

	/**
	 * @~japanese
	 * @brief デフォルトの Allocator を取得
	 */
	Allocator getDefaultAllocator() CRICOCOS2D_NOEXCEPT;

	/**
	 * @~japanese
	 * @brief デフォルトエラーコルバック関数
	 */
	void defaultErrorCallback(const CriChar8* errid, CriUint32 p1, CriUint32 p2, CriUint32* parray);

	/**
	 * @~japanese
	 * @brief リソースファイルパスの取得
	 *
	 * Cocos2d プロジェクトにおける Resources フォルダ下のファイルパスを取得します。\n
	 * プラットフォームに応じたパスが返されます。
	 *
	 * @param[in]	path	リースファイル名
	 * @return		リソースファイルパス
	 */
	std::string getResourcePath(std::string path);
}


#endif


/**
 * @namespace cricocos2d::detail
 * @~japanese
 * @brief 内部実装用の名前空間です。アプリケーションから直接使用しないでください。
 */


/**
 * @}
 */
