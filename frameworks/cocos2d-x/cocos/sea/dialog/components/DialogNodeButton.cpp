//
//  DialogNodeButton.cpp
//  cocos2d_libs
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/2/11.
//

#include "../Dialog.h"
#include "../DialogHelper.h"
#include "DialogNodeButton.h"

namespace sea {
DialogNodeButton::DialogNodeButton(const cocos2d::ValueMap& cfg, Dialog *dialog, const cocos2d::ValueMap& params) :
    <PERSON>leButton(),
    DialogNodeBase(cfg, dialog, this, params) {
    auto typeIter = cfg.find("type");

    if (typeIter != cfg.end()) {
        auto typeValue = typeIter->second;

        if (typeValue.isNumber()) {
            auto type = typeValue.asInt();
            switch (type) {
                case NodeType::BUTTON_NORMAL:
                case NodeType::BUTTON_NORMAL_NEW:
                case NodeType::BUTTON_TRADITION:
                case NodeType::BUTTON_TRADITION_NEW:
                    this->setSeaUserData("type", cocos2d::Value(type));
                    break;

                default:
                    this->setSeaUserData("type", cocos2d::Value(NodeType::BUTTON));
                    break;
            }
        }
    } else {
        this->setSeaUserData("type", cocos2d::Value(NodeType::BUTTON));
    }
}

DialogNodeButton::~DialogNodeButton() {
}

DialogNodeButton * DialogNodeButton::create(const cocos2d::ValueMap& cfg, Dialog *dialog, const cocos2d::ValueMap& params) {
    DialogNodeButton *ret = new (std::nothrow) DialogNodeButton(cfg, dialog, params);

    if (ret && ret->init()) {
        ret->autorelease();
    } else {
        CC_SAFE_DELETE(ret);
    }

    return ret;
}

void DialogNodeButton::onExit() {
    DialogNodeBase::doExit();
    BoleButton::onExit();
}

void DialogNodeButton::onEnter() {
    DialogNodeBase::doEnter();
    BoleButton::onEnter();
}

void DialogNodeButton::reloadNodeFromData(const cocos2d::ValueMap& cfg) {
    auto ret = this->getInfoFromDataForButton(cfg);

    cocos2d::Value normal, text, clicked, params;
    for (const auto& iter : ret) {
        auto k = iter.first;
        if (k == "normal") {
            normal = iter.second;
        }
        else if (k == "text") {
            text = iter.second;
        }
        else if (k == "clicked") {
            clicked = iter.second;
        }
        else if (k == "params") {
            params = iter.second;
        }
    }
    this->reload(normal, text, clicked, params);
}

void DialogNodeButton::changeLanguage() {
    auto cfg = this->getConfig();

    if (!DialogHelper::isValid(cfg, isVertical())) {
        return;
    }

    if (cfg.find("invalid_lang") != cfg.end()) {
        auto flag = cfg.at("invalid_lang");

        if (flag.asBool()) {
            return;
        }
    }

    this->reloadNodeFromData(cfg);
}

void DialogNodeButton::addClickEventNoMove(ADD_CLICK_EVENT_FUN fun, const cocos2d::Value& params /* = cocos2d::Value::Null*/, float autoDisable /* = -1*/) {
    auto dialog = this->getDialog();

    if (dialog) {
        dialog->addClickEventNoMove(this, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& _params) {
                if (fun) {
                    fun(this, _params);
                }

//                bool mute = sea::getBoolFromMap(_params, "mute");
                bool bright = sea::getBoolFromMap(_params, "bright");

                if (autoDisable > 0) {
                    if (bright) {
                        this->setTouchEnabled(false);
                    } else {
                        this->setTouchEnabledWithBright(false);
                    }

                    sea::createDelayAction(this, [ = ](cocos2d::Node *) {
                        if (bright) {
                            this->setTouchEnabled(true);
                        } else {
                            this->setTouchEnabledWithBright(true);
                        }

                        this->removeLoading();
                    }, autoDisable);
                }
            });
    }
}

bool DialogNodeButton::init() {
    auto cfg = this->getConfig();
    auto map = this->getInfoFromDataForButton(cfg);
    auto nodeTypeValue = this->getSeaUserData("type");

    if (nodeTypeValue) {
        auto nodeType = nodeTypeValue->asInt();
        cocos2d::Value normal = cocos2d::Value::Null, text = cocos2d::Value::Null, clicked = cocos2d::Value::Null, params = cocos2d::Value::Null;

        if (map.find("normal") != map.end()) {
            normal = map.at("normal");
        }

        if (map.find("text") != map.end()) {
            text = map.at("text");
        }

        if (map.find("clicked") != map.end()) {
            clicked = map.at("clicked");
        }

        if (map.find("params") != map.end()) {
            params = map.at("params");
        }

        if (nodeType == NodeType::BUTTON_TRADITION || nodeType == NodeType::BUTTON_TRADITION_NEW) {
            cocos2d::ValueVector vec = { normal, text, clicked };
            BoleButton::init(cocos2d::Value(vec), cocos2d::Value::Null, cocos2d::Value::Null, params);
        } else {
            BoleButton::init(normal, text, clicked, params);
        }
    }

    this->initSpecialButton();

    //可能会被修改，重新获取一次
    cfg = this->getConfig();
    FunctionList<std::pair<std::string, cocos2d::Value>> funElseParamList;

    for (const auto& iter : cfg) {
        auto k = iter.first;
        auto v = iter.second;

        if (k == "swallow") {
            this->setSwallowTouches(v.asBool());
        } else if (k == "clicked_fun") {
            bool mute = false;
            float autoDisable = -1;
            int fHandler = -1;
            auto vType = v.getType();

            if (vType != ccValueType::LUA_FUNCTION) {
                if (vType == ccValueType::MAP) {
                    auto m = v.asValueMap();

                    if (m.find("fun") != m.end()) {
                        auto f = m.at("fun");

                        if (f.getType() == ccValueType::LUA_FUNCTION) {
                            fHandler = f.asInt();

                            if (m.find("mute") != m.end()) {
                                mute = m.at("mute").asBool();
                            }

                            if (m.find("auto_disable") != m.end()) {
                                autoDisable = m.at("auto_disable").asFloat();
                            }
                        }
                    }
                }
            } else {
                fHandler = v.asInt();
            }

            if (fHandler > 0) {
                cocos2d::ValueMap p = { { "mute", cocos2d::Value(mute) } };
                this->addClickEventListenerData([ = ](BoleButton *btn, const cocos2d::Value& _m) {
                        sea::excuteLuaFunction(fHandler, 2, [ = ](cocos2d::LuaStack *stack) {
                            sea::pushValueToStack(stack, this->getDialog(), "sea.Dialog");
                            sea::pushValueToStack(stack, btn, "sea.BoleButton");
                        });
                }, cocos2d::Value(p), autoDisable);
            }
        } else {
            funElseParamList.push([=](std::pair<std::string, cocos2d::Value> item){
                this->doCommonKeyForAnyNode(item.first, item.second, this);
            }, {k, v});
        }
    }

    funElseParamList.run();
    this->doCommonForAnyNode(cfg, this);

    return true;
}

cocos2d::ValueMap DialogNodeButton::getInfoFromDataForButton(const cocos2d::ValueMap& map) {
    cocos2d::ValueMap ret;
    cocos2d::ValueVector multi, path_abs, path_abs_mv, paths, vertical, resHeadKey;
    cocos2d::ValueMap btnParams;
    IntBool activity = IntBool::None;
    std::string themeId = "";
    sea::TextureResType resType = sea::TextureResType::NONE;
    cocos2d::Value textExtraData;

    int nodeType = 0;
    auto nodeTypeValue = this->getSeaUserData("type");

    if (nodeTypeValue) {
        nodeType = nodeTypeValue->asInt();
    }

    std::string key;

    for (const auto& iter : map) {
        auto k = iter.first;
        auto v = iter.second;

        if (k == "type") {
        } else if (k == "key") {
            key = v.asString();
        } else if (k == "multi") {
            if (v.isTable()) {
                multi = sea::getVectorFromCCValue(v, 3);
            } else {
                multi = cocos2d::ValueVector({ v, v, v });
            }
        } else if (k == "path_abs") {
            auto p = this->getFunPath(v, nullptr, &map);

            if (p.isTable()) {
                path_abs = sea::getVectorFromCCValue(p, 3);
            } else {
                path_abs = cocos2d::ValueVector({ p });
            }
        } else if (k == "path_abs_mv") {
            auto p = this->getFunPath(v, nullptr, &map);

            if (p.isTable()) {
                path_abs_mv = sea::getVectorFromCCValue(p, 3);
            } else {
                path_abs_mv = cocos2d::ValueVector({ p });
            }
        } else if (k == "path") {
            auto p = this->getFunPath(v, nullptr, &map);

            if (p.isTable()) {
                paths = sea::getVectorFromCCValue(p, 3);

                if (nodeType == NodeType::BUTTON_TRADITION || nodeType == NodeType::BUTTON_TRADITION_NEW) {
                    if (paths.size() > 0) {
                        if (paths.size() < 2) {
                            paths.push_back(paths[0]);
                        }

                        if (paths.size() < 3) {
                            paths.push_back(paths[1]);
                        }
                    }
                }
            } else {
                if (nodeType == NodeType::BUTTON_TRADITION_NEW || nodeType == NodeType::BUTTON_NORMAL_NEW || nodeType == NodeType::RADIO) {
                    paths = cocos2d::ValueVector({ p, p, cocos2d::Value(p.asString() + "_clicked") });
                } else {
                    paths = cocos2d::ValueVector({ p, p, p });
                }
            }
        } else if (k == "vertical") {
            if (v.isTable()) {
                vertical = sea::getVectorFromCCValue(v, 3);
            } else {
                vertical = cocos2d::ValueVector({ v, v, v });
            }
        } else if (k == "res") {
            if (v.isTable()) {
                resHeadKey = sea::getVectorFromCCValue(v, 3);
            } else {
                resHeadKey = cocos2d::ValueVector({ v, v, v });
            }
        } else if (k == "touch") {
            if (v.getType() == ccValueType::MAP) {
                cocos2d::ValueMap touch;

                for (const auto& _iter : v.asValueMap()) {
                    auto _k = _iter.first;
                    auto _v = _iter.second;

                    if (_k == "size") {
                        cocos2d::Value size = _v;

                        if (_v.isTable()) {
                            cocos2d::ValueVector _p;// = sea::getVectorFromCCValue(_v, 2);
                            if (_v.getType() == ccValueType::MAP) {
                                _p = {_v};
                            }
                            else {
                                _p = sea::getVectorFromCCValue(_v, 2);
                            }
                            auto n = _p.size();

                            if (n > 0) {
                                if (this->isVertical()) {
                                    if (n >= 2) {
                                        size = _p[1];
                                    }
                                } else {
                                    size = _p[0];
                                }
                            }
                        }

                        touch["size"] = size;
                    } else if (_k == "off") {
                        auto off = this->getFunPosition(_v, nullptr, &map);
                        touch["off"] = cocos2d::Value({
                                { "x", cocos2d::Value(off.x) }, { "y", cocos2d::Value(off.y) },
                            });
                    } else if (_k == "debug") {
                        touch["debug"] = _v;
                    }
                }

                if (touch.size() > 0) {
                    btnParams["touch"] = touch;
                }
            } else {
                sea::showMessageBox("touch 参数值必须为table类型");
            }
        } else if (k == "auto_close") {
            btnParams["auto_close"] = v;
        } else if (k == "res_type") {
            btnParams["res_type"] = v;
            resType = sea::getTextureResTypeFromCCValue(v);
        } else if (k == "text_extra") {
            if (v.getType() == ccValueType::LUA_FUNCTION) {
                textExtraData = sea::excuteLuaFunction(v);
            } else {
                textExtraData = v;
            }
        } else if (k == "async_load") {
            btnParams["async_load"] = v;
        } else if (k == "activity") {
            activity = v.asBool() ? IntBool::True : IntBool::False;
        } else if (k == "id") {
            if (v.isNumber()) {
                themeId = std::to_string(v.asInt());
            } else {
                themeId = v.asString();
            }
        }
    }

    if (multi.size() == 0) {
        if (nodeType == NodeType::BUTTON_TRADITION || nodeType == NodeType::BUTTON_TRADITION_NEW) {
            multi = { cocos2d::Value(false), cocos2d::Value(false), cocos2d::Value(false) };
        } else {
            multi = { cocos2d::Value(false), cocos2d::Value(true), cocos2d::Value(false) };
        }
    }

    if (path_abs.size() == 0 && path_abs_mv.size() == 0 && paths.size() == 0) {
        if (nodeType == NodeType::BUTTON_TRADITION || nodeType == NodeType::BUTTON_TRADITION_NEW) {
            paths = { cocos2d::Value(key), cocos2d::Value(key), cocos2d::Value(key + "_clicked") };
        } else {
            paths = { cocos2d::Value(key + "_normal"), cocos2d::Value(key), cocos2d::Value(key + "_clicked") };
        }
    }

    cocos2d::Value pathRet[] = { cocos2d::Value::Null, cocos2d::Value::Null, cocos2d::Value::Null };
    do {
        auto pathAbsCount = path_abs.size();
        auto pathAbsMvCount = path_abs_mv.size();
        auto pathCount = paths.size();

        for (int i = 1; i <= 3; ++i) {
            int index = i - 1;

            if (pathAbsCount >= i && !path_abs[index].isNull()) {
                pathRet[index] = path_abs[index];
            } else {
                auto flag1 = pathAbsMvCount >= i && !path_abs_mv[index].isNull();
                auto flag2 = pathCount >= i && !paths[index].isNull();
                cocos2d::Value pArr;
                bool notAddHead = false;

                if (flag1) {
                    pArr = path_abs_mv[index];
                    notAddHead = true;
                } else if (flag2) {
                    pArr = paths[index];
                }

                if (!pArr.isNull()) {
                    if (pArr.getType() == ccValueType::STRING) {
                        pathRet[index] = this->getImgPath(
                            pArr.asString(),
                            sea::getBoolFromVector(multi, index, false) ? IntBool::True : IntBool::False,
                            sea::getBoolFromVector(vertical, index, false) ? IntBool::True : IntBool::False,
                            resType,
                            notAddHead,
                            sea::getStringFromVector(resHeadKey, index),
                            activity,
                            themeId
                            );
                    } else if (pArr.getType() == ccValueType::VECTOR) {
                        auto vec = pArr.asValueVector();
                        cocos2d::ValueVector arr;

                        for (int _ii = 0; _ii < vec.size(); ++_ii) {
                            IntBool _multi = IntBool::None, _vertical = IntBool::None;

                            if (multi.size() > index) {
                                auto mValue = multi[index];

                                if (mValue.isTable()) {
                                    auto mVec = sea::getVectorFromCCValue(mValue, 3);
                                    _multi = sea::getBoolFromVector(mVec, _ii) ? IntBool::True : IntBool::False;
                                } else {
                                    _multi = mValue.asBool() ? IntBool::True : IntBool::False;
                                }
                            }

                            if (vertical.size() > index) {
                                auto vValue = vertical[index];

                                if (vValue.isTable()) {
                                    auto vVec = sea::getVectorFromCCValue(vValue, 3);
                                    _vertical = sea::getBoolFromVector(vVec, _ii) ? IntBool::True : IntBool::False;
                                } else {
                                    _vertical = vValue.asBool() ? IntBool::True : IntBool::False;
                                }
                            }

                            auto pp = vec[_ii];

                            if (pp.getType() == ccValueType::STRING) {
                                auto p = this->getImgPath(pp.asString(),
                                                          _multi,
                                                          _vertical,
                                                          resType,
                                                          notAddHead,
                                                          sea::getStringFromVector(resHeadKey, index),
                                                          activity,
                                                          themeId);
                                arr.push_back(cocos2d::Value(p));
                            } else {
                                arr.push_back(pp);
                            }
                        }

                        pathRet[index] = arr;
                    } else if (pArr.getType() == ccValueType::MAP) {
                        auto _map = pArr.asValueMap();
                        
                        cocos2d::ValueMap _dataConfig;
                        
                        
                        //获取对应的配置
                        auto _getMulti = [=](int _ii){
                            IntBool _multi = IntBool::None;
                            if (multi.size() > index) {
                                auto mValue = multi[index];

                                if (mValue.isTable()) {
                                    auto mVec = sea::getVectorFromCCValue(mValue, 3);
                                    _multi = sea::getBoolFromVector(mVec, _ii) ? IntBool::True : IntBool::False;
                                } else {
                                    _multi = mValue.asBool() ? IntBool::True : IntBool::False;
                                }
                            }
                            return _multi;
                        };
                        
                        auto _getVertical = [=](int _ii) {
                            IntBool _vertical = IntBool::None;
                            if (vertical.size() > index) {
                                auto vValue = vertical[index];

                                if (vValue.isTable()) {
                                    auto vVec = sea::getVectorFromCCValue(vValue, 3);
                                    _vertical = sea::getBoolFromVector(vVec, _ii) ? IntBool::True : IntBool::False;
                                } else {
                                    _vertical = vValue.asBool() ? IntBool::True : IntBool::False;
                                }
                            }
                            return _vertical;
                        };
                        
                        cocos2d::Value _pathConfig;
                        
                        for (const auto& _iter : _map) {
                            auto _k = _iter.first;
                            auto _v = _iter.second;
                            if (_k == "path") {
                                if (_v.getType() == ccValueType::STRING) {
                                    auto p = this->getImgPath(_v.asString(),
                                                              _getMulti(1),
                                                              _getVertical(1),
                                                              resType,
                                                              notAddHead,
                                                              sea::getStringFromVector(resHeadKey, index),
                                                              activity,
                                                              themeId);
                                    _pathConfig = cocos2d::Value(p);
                                }
                                else if (_v.isTable()) {
                                    cocos2d::ValueVector _pathArr;
                                    auto pathVec = sea::getVectorFromCCValue(_v, 3);
                                    for (int iii = 1; iii <= pathVec.size(); ++iii) {
                                        auto vvv = pathVec[iii - 1];
                                        if (vvv.isNull()) {
                                            pathVec.push_back(vvv);
                                        }
                                        else if (vvv.getType() == ccValueType::STRING) {
                                            auto p = this->getImgPath(_v.asString(),
                                                                      _getMulti(iii),
                                                                      _getVertical(iii),
                                                                      resType,
                                                                      notAddHead,
                                                                      sea::getStringFromVector(resHeadKey, index),
                                                                      activity,
                                                                      themeId);
                                            pathVec.push_back(cocos2d::Value(p));
                                            
                                        }
                                        else {
#if COCOS2D_DEBUG >= 1
                                            sea::showMessageBox("按钮路径类型配置错误1");
#endif
                                        }
                                    }
                                    _pathConfig = pathVec;
                                }
                                _dataConfig[_k] = _pathConfig;
                            }
                            else if (_k == "1" || _k == "2" || _k == "3") {
                                if (_v.getType() == ccValueType::STRING) {
                                    if (_pathConfig.isNull()) {
                                        _pathConfig = cocos2d::ValueMap();
                                    }
                                    if (_pathConfig.getType() == ccValueType::MAP) {
                                        auto iii = std::stoi(_k);
                                        auto p = this->getImgPath(_v.asString(),
                                                                  _getMulti(iii),
                                                                  _getVertical(iii),
                                                                  resType,
                                                                  notAddHead,
                                                                  sea::getStringFromVector(resHeadKey, index),
                                                                  activity,
                                                                  themeId);
                                        _pathConfig.asValueMap().insert({_k, cocos2d::Value(p)});
                                        
                                        _dataConfig[_k] = _pathConfig;
                                    }
                                    else {
#if COCOS2D_DEBUG >= 1
                                        sea::showMessageBox("按钮路径类型配置错误2");
#endif
                                    }
                                }
                                else {
#if COCOS2D_DEBUG >= 1
                                    sea::showMessageBox("按钮路径类型配置错误3");
#endif
                                }
                            }
                            else {
                                _dataConfig[_k] = _v;
                            }
                        }

                        pathRet[index] = _dataConfig;
                    }
                }
            }
        }
    } while (0);

    ret["normal"] = pathRet[0];
    ret["text"] = pathRet[1];
    ret["clicked"] = pathRet[2];
    ret["params"] = btnParams;

    if (!(nodeType == NodeType::BUTTON_TRADITION || nodeType == NodeType::BUTTON_TRADITION_NEW)) {
        if (!textExtraData.isNull()) {
            auto pathText = ret["text"];

            if (textExtraData.getType() == ccValueType::MAP) {
                auto extraMap = textExtraData.asValueMap();

                if (pathText.getType() == ccValueType::MAP) {
                    auto textMap = pathText.asValueMap();

                    if (textMap.find("path") != textMap.end()) {
                        for (const auto & _iter : extraMap) {
                            textMap[_iter.first] = _iter.second;
                        }
                    } else {
                        extraMap["path"] = pathText;
                        ret["text"] = extraMap;
                    }
                } else {
                    extraMap["path"] = pathText;
                    ret["text"] = extraMap;
                }
            } else {
                ret["text"] = textExtraData;
            }
        }
    }

    return ret;
}

void DialogNodeButton::initSpecialButton() {
}
}
