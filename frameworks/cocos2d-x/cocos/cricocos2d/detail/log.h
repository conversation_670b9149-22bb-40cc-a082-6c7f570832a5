/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/* Prevention of redefinition */
#ifndef CRICOCOS2D_DETAIL_LOG_H
#define CRICOCOS2D_DETAIL_LOG_H


namespace cricocos2d
{
	namespace detail
	{
		enum class LogSeverity
		{
			Error,
			Warning,
			Info,
			Debug,
		};

		void printLog(LogSeverity log_severity, const char* format, ...);

		void assertLibraryContextExists();
	}
}


#if defined(CRICOCOS2D_DEBUG)
#    define CRICOCOS2D_DEBUG_LOG(...)    ::cricocos2d::detail::printLog(::cricocos2d::detail::LogSeverity::Debug, __VA_ARGS__)
#else
#    define CRICOCOS2D_DEBUG_LOG(...)
#endif


#define CRICOCOS2D_DEBUG_LOG_PLACE    CRICOCOS2D_DEBUG_LOG("%s: %d: %s", __FILE__, __LINE__, __FUNCTION__)


#endif
