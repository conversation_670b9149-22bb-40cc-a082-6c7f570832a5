/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/mana/SofdecPlayer.h"

#include "cricocos2d/mana/detail/RendererResourceFactorySet.h"
#include "cricocos2d/detail/log.h"


namespace cricocos2d
{
	namespace mana
	{
		namespace
		{
			const std::string scheduler_update_key = "update";
		}


        SofdecPlayer::SofdecPlayer(const CriManaPlayerConfig& config)
			: mana_player_((cricocos2d::detail::assertLibraryContextExists(), criManaPlayer_CreateWithConfig(&config, nullptr, 0)))
			, mana_status_(CRIMANAPLAYER_STATUS_STOP)
			, required_status_(SofdecPlayer::Status::Stop)
			, is_movie_info_available_(false)
			, is_frame_info_available_(false)
			, is_mana_start_invoked_(false)
		{
			CC_ASSERT(mana_player_ != nullptr);

			auto scheduler = cocos2d::Director::getInstance()->getScheduler();
			scheduler->schedule([this](float dt) { update(); }, this, 0.0f, false, scheduler_update_key);

			/* Set number of frame pools. */
			criManaPlayer_SetNumberOfFramePools(mana_player_, 3);
			criManaPlayer_SetNumberOfFramesForPrep(mana_player_, 1);
		}


        SofdecPlayer::~SofdecPlayer()
		{
			criManaPlayer_StopAndWaitCompletion(mana_player_);
			renderer_resource_.reset();
			criManaPlayer_Destroy(mana_player_);

			cocos2d::Director::getInstance()->getScheduler()->unschedule(scheduler_update_key, this);
		}


		void SofdecPlayer::update()
		{
			if (required_status_ == SofdecPlayer::Status::Stop) {
				if (mana_status_ != CRIMANAPLAYER_STATUS_STOP) {
					updateManaPlayer();
				}
				return;
			}

			switch (mana_status_) {
			case CRIMANAPLAYER_STATUS_STOP :
				// Do nothing
				break;

			case CRIMANAPLAYER_STATUS_DECHDR :
				updateManaPlayer();
				if (mana_status_ == CRIMANAPLAYER_STATUS_WAIT_PREP) {
					goto case_WAIT_PREP;
				}
				break;

			case CRIMANAPLAYER_STATUS_WAIT_PREP : case_WAIT_PREP :
				// Renderer resource dispatching
				if (!is_movie_info_available_) {
					criManaPlayer_GetMovieInfo(mana_player_, &movie_info_);
					is_movie_info_available_ = true;

					if (renderer_resource_ != nullptr) {
						detail::RendererResource* renderer_resource = static_cast<detail::RendererResource*>(renderer_resource_.get());
						if (!renderer_resource->isSuitable(movie_info_)) {
							renderer_resource_.reset();
						}
					}

					if (renderer_resource_ == nullptr) {
						renderer_resource_ = detail::RendererResourceFactorySet::getInstance()->createRenderResource(movie_info_, mana_player_);
						if (renderer_resource_ == nullptr) {
							stop();
							cricocos2d::detail::printLog(cricocos2d::detail::LogSeverity::Error, "Can't dispatch renderer resource.");
							return;
						}
					}
				}

				{
					detail::RendererResource* renderer_resource = static_cast<detail::RendererResource*>(renderer_resource_.get());
					// Check completion of preparation
					if (!renderer_resource->isPrepared()) {
						renderer_resource->continuePreparation();
						if (!renderer_resource->isPrepared()) {
							break;
						}
					}

					renderer_resource->attachToPlayer();
				}
				if (required_status_ == SofdecPlayer::Status::Ready) {
					goto case_PREP;
				}
				if (required_status_ == SofdecPlayer::Status::Playing) {
					criManaPlayer_Start(mana_player_);
					is_mana_start_invoked_ = true;
					goto case_PREP;
				}
				break;

			case CRIMANAPLAYER_STATUS_PREP : case_PREP :
				updateManaPlayer();
				if (mana_status_ == CRIMANAPLAYER_STATUS_READY) {
					goto case_READY;
				}
				if (mana_status_ == CRIMANAPLAYER_STATUS_PLAYING) {
					goto case_PLAYING;
				}
				break;

			case CRIMANAPLAYER_STATUS_READY : case_READY :
				if (required_status_ == SofdecPlayer::Status::Playing) {
					if (!is_mana_start_invoked_) {
						criManaPlayer_Start(mana_player_);
						is_mana_start_invoked_ = true;
					}
					goto case_PLAYING;
				}
				break;

			case CRIMANAPLAYER_STATUS_PLAYING : case_PLAYING :
				updateManaPlayer();
				if (mana_status_ == CRIMANAPLAYER_STATUS_PLAYING) {
					detail::RendererResource* renderer_resource = static_cast<detail::RendererResource*>(renderer_resource_.get());
					is_frame_info_available_ |= renderer_resource->updateFrame(frame_info_);
				}
				if (mana_status_ == CRIMANAPLAYER_STATUS_PLAYEND) {
					goto case_PLAYEND;
				}
				break;

			case CRIMANAPLAYER_STATUS_PLAYEND : case_PLAYEND :
				// Do nothing
				break;

			case CRIMANAPLAYER_STATUS_ERROR :
				updateManaPlayer();
				break;

			default :
				break;
			}

			if (mana_status_ == CRIMANAPLAYER_STATUS_ERROR) {
				is_movie_info_available_ = false;
				is_frame_info_available_ = false;
			}
		}


		void SofdecPlayer::updateManaPlayer() CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SyncMasterTimer(mana_player_);
			criManaPlayer_ExecuteMain(mana_player_);
			mana_status_ = criManaPlayer_GetStatus(mana_player_);
		}


        SofdecPlayer::Status SofdecPlayer::getStatus() const CRICOCOS2D_NOEXCEPT
		{
			if ((required_status_ == SofdecPlayer::Status::Stop) && (mana_status_ != CRIMANAPLAYER_STATUS_STOP)) {
				return SofdecPlayer::Status::StopProcessing;
			}

			switch (mana_status_) {
			case CRIMANAPLAYER_STATUS_STOP :
				return SofdecPlayer::Status::Stop;

			case CRIMANAPLAYER_STATUS_DECHDR :
				return SofdecPlayer::Status::DecHdr;

			case CRIMANAPLAYER_STATUS_WAIT_PREP :
				return SofdecPlayer::Status::WaitPrep;

			case CRIMANAPLAYER_STATUS_PREP :
				return SofdecPlayer::Status::Prep;

			case CRIMANAPLAYER_STATUS_READY :
			{
				detail::RendererResource* renderer_resource = static_cast<detail::RendererResource*>(renderer_resource_.get());
				return ((renderer_resource != nullptr) && (renderer_resource->isPrepared())) ?
                SofdecPlayer::Status::Ready : SofdecPlayer::Status::Prep;
			}

			case CRIMANAPLAYER_STATUS_PLAYING :
				return SofdecPlayer::Status::Playing;

			case CRIMANAPLAYER_STATUS_PLAYEND :
				return SofdecPlayer::Status::PlayEnd;

			case CRIMANAPLAYER_STATUS_ERROR :
				return SofdecPlayer::Status::Error;

			default :
				return SofdecPlayer::Status::Error;
			}
			return SofdecPlayer::Status::Error;
		}


		bool SofdecPlayer::isPaused() const CRICOCOS2D_NOEXCEPT
		{
			return criManaPlayer_IsPaused(mana_player_);
		}


		std::tuple<CriUint64, CriUint64> SofdecPlayer::getTime() const CRICOCOS2D_NOEXCEPT
		{
			CriUint64 count;
			CriUint64 unit;
			criManaPlayer_GetTime(mana_player_, &count, &unit);
			return std::make_tuple(count, unit);
		}


		const CriManaMovieInfo* SofdecPlayer::getMovieInfo() const CRICOCOS2D_NOEXCEPT
		{
			return is_movie_info_available_ ? &movie_info_ : nullptr;
		}


		const CriManaFrameInfo* SofdecPlayer::getFrameInfo() const CRICOCOS2D_NOEXCEPT
		{
			return is_frame_info_available_ ? &frame_info_ : nullptr;
		}


		void SofdecPlayer::prepare() CRICOCOS2D_NOEXCEPT
		{
			CC_ASSERT(getStatus() == SofdecPlayer::Status::Stop);
			criManaPlayer_Prepare(mana_player_);
			updateManaPlayer();
			is_movie_info_available_ = false;
			is_frame_info_available_ = false;
			is_mana_start_invoked_	 = false;
			required_status_		 = SofdecPlayer::Status::Ready;
		}


		void SofdecPlayer::start() CRICOCOS2D_NOEXCEPT
		{
			CC_ASSERT((required_status_ == SofdecPlayer::Status::Ready) || (getStatus() == SofdecPlayer::Status::Stop));
			if ((mana_status_ == CRIMANAPLAYER_STATUS_STOP) ||
				(mana_status_ == CRIMANAPLAYER_STATUS_PLAYEND)) {
				// Just 'Prepare'. Mana player automatically 'Start' ASAP.
				criManaPlayer_Prepare(mana_player_);
				updateManaPlayer();
				is_movie_info_available_ = false;
				is_frame_info_available_ = false;
				is_mana_start_invoked_	 = false;
			}
			required_status_ = SofdecPlayer::Status::Playing;
		}


		void SofdecPlayer::stop() CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_Stop(mana_player_);
			updateManaPlayer();
			is_movie_info_available_ = false;
			is_frame_info_available_ = false;
			is_mana_start_invoked_	 = false;
			required_status_		 = SofdecPlayer::Status::Stop;
		}


		void SofdecPlayer::stopAndWaitCompletion() CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_StopAndWaitCompletion(mana_player_);
			updateManaPlayer();
			is_movie_info_available_ = false;
			is_frame_info_available_ = false;
			is_mana_start_invoked_	 = false;
			required_status_		 = SofdecPlayer::Status::Stop;
		}


		void SofdecPlayer::pause(bool sw) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_Pause(mana_player_, sw);
		}


		void SofdecPlayer::loop(bool sw) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_Loop(mana_player_, sw);
		}


		void SofdecPlayer::setSeekPosition(CriSint32 seek_frame_no) CRICOCOS2D_NOEXCEPT
		{
			CC_ASSERT(getStatus() == SofdecPlayer::Status::Stop);
			criManaPlayer_SetSeekPosition(mana_player_, seek_frame_no);
		}


		void SofdecPlayer::setMetaDataWorkAllocator(const Allocator& allocator) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetMetaDataWorkAllocator(
				mana_player_,
				allocator.allocate,
				allocator.deallocate,
				allocator.obj,
				CRIMANA_META_FLAG_ALL
												  );
		}


		void SofdecPlayer::setDataRequestCallback(std::function<void(cricocos2d::mana::SofdecPlayer&)> callback_func) CRICOCOS2D_NOEXCEPT
		{
			data_request_callback_func_ = std::move(callback_func);
			if (data_request_callback_func_) {
				// Set Callback
				criManaPlayer_SetDataRequestCallback(
					mana_player_,
					[](void* obj, CriManaPlayerHn player)
				{
					static_cast<SofdecPlayer*>(obj)->data_request_callback_func_(*static_cast<SofdecPlayer*>(obj));
				},
					this);
			} else {
				// Clear Callback
				criManaPlayer_SetDataRequestCallback(mana_player_, nullptr, nullptr);
			}
		}


		void SofdecPlayer::setCuePointCallback(std::function<void(cricocos2d::mana::SofdecPlayer&, const CriManaEventPoint&)> callback_func) CRICOCOS2D_NOEXCEPT
		{
			cue_point_callback_func_ = std::move(callback_func);
			if (cue_point_callback_func_) {
				// Set Callback
				criManaPlayer_SetCuePointCallback(
					mana_player_,
					[](void* obj, CriManaPlayerHn player, CriManaEventPoint* event_info)
				{
					static_cast<SofdecPlayer*>(obj)->cue_point_callback_func_(*static_cast<SofdecPlayer*>(obj), *event_info);
				},
					this);
			} else {
				// Clear Callback
				criManaPlayer_SetDataRequestCallback(mana_player_, nullptr, nullptr);
			}
		}


		void SofdecPlayer::setData(const void* dataptr, CriSint64 datasize) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetData(mana_player_, dataptr, datasize);
		}


		void SofdecPlayer::setFile(CriFsBinderHn bndrhn, const std::string& path) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetFile(mana_player_, bndrhn, path.c_str());
		}


		void SofdecPlayer::setFileRange(const std::string& path, CriUint64 offset, CriSint64 datasize) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetFileRange(mana_player_, path.c_str(), offset, datasize);
		}


		void SofdecPlayer::setContentId(CriFsBinderHn bndrhn, CriSint32 id) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetContentId(mana_player_, bndrhn, id);
		}


		void SofdecPlayer::setPreviousDataAgain() CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetPreviousDataAgain(mana_player_);
		}


		void SofdecPlayer::setDecodeMode(CriManaDecodeMode mode) CRICOCOS2D_NOEXCEPT
		{
			CC_ASSERT((getStatus() == SofdecPlayer::Status::Stop)
					  || (getStatus() == SofdecPlayer::Status::DecHdr)
					  || (getStatus() == SofdecPlayer::Status::WaitPrep));
			criManaPlayer_SetDecodeMode(mana_player_, mode);
		}


		void SofdecPlayer::setAudioTrack(CriSint32 track) CRICOCOS2D_NOEXCEPT
		{
			CC_ASSERT(getStatus() == SofdecPlayer::Status::Stop);
			criManaPlayer_SetAudioTrack(mana_player_, track);
		}


		void SofdecPlayer::setSubAudioTrack(CriSint32 track) CRICOCOS2D_NOEXCEPT
		{
			CC_ASSERT(getStatus() == SofdecPlayer::Status::Stop);
			criManaPlayer_SetSubAudioTrack(mana_player_, track);
		}


		void SofdecPlayer::setAudioVolume(CriFloat32 volume) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetVolume(mana_player_, volume);
		}


		void SofdecPlayer::setSubAudioVolume(CriFloat32 volume) CRICOCOS2D_NOEXCEPT
		{
			criManaPlayer_SetSubAudioVolume(mana_player_, volume);
		}


		void SofdecPlayer::draw(
			const cocos2d::TrianglesCommand::Triangles& triangles,
			const cocos2d::Mat4&						mv,
			bool										additive
						 ) const
		{
			bool is_frame_available = is_frame_info_available_;
			bool do_draw			= is_frame_available
									  && (required_status_ == SofdecPlayer::Status::Playing)
									  && ((mana_status_ == CRIMANAPLAYER_STATUS_PLAYING) || (mana_status_ == CRIMANAPLAYER_STATUS_PLAYEND));
			if (do_draw) {
				detail::RendererResource* renderer_resource = static_cast<detail::RendererResource*>(renderer_resource_.get());
				const bool				  drawable			= renderer_resource->isDrawable();
				if (drawable) {
					renderer_resource->draw(triangles, mv, additive);
				}
			}
		}


	}
}
