//
//  SkeletonDataManager.h
//  Spine Json和Atlas资源存储管理类
//
//  Created by 孙云高 on 2022/4/27.
//
//

#include "spine/SkeletonDataManager.h"
#include "spine/extension.h"
#include "spine/Cocos2dAttachmentLoader.h"

using namespace cocos2d;
namespace spine {

static SkeletonDataManager* s_sharedSkeletonDataManager = nullptr;
SkeletonDataManager* SkeletonDataManager::getInstance()
{
    if (!s_sharedSkeletonDataManager)
    {
        s_sharedSkeletonDataManager = new SkeletonDataManager();
    }
    return s_sharedSkeletonDataManager;
}

void SkeletonDataManager::destroyInstance()
{
    CC_SAFE_RELEASE_NULL(s_sharedSkeletonDataManager);
}

void SkeletonDataManager::purge()
{
    // FIXME: 这个需要增加spine引用计数处理！
    // 这个地方不要调用，因为有些骨骼动画还在使用中，这里一调用的话，就导致骨骼动画的原始数据被销毁了
    // 这样再次渲染的时候就会崩溃，多发生在 spine::SkeletonRenderer::draw 中！
    // CC_SAFE_RELEASE_NULL(s_sharedSkeletonDataManager);
}

spSkeletonData* SkeletonDataManager::getSkeletonJsonData(const char* skeletonDataFile, spAtlas* atlas, float scale/* = 1*/)
{
    CCAssert(skeletonDataFile != NULL && atlas != NULL, " skeletonDataFile and atlas file nil");
    std::string key = skeletonDataFile;
    SkeletonDataMap::iterator it = m_skeletonDataCache.find(key);
    if (it != m_skeletonDataCache.end())
    {
        CCLOG("find skeletonData include atlas");
        return it->second;
    }
    
    // 如果没有的话就创建一个
    CCLOG("start read json file include atlas: %s",skeletonDataFile);
    spAttachmentLoader* _attachmentLoader = SUPER(Cocos2dAttachmentLoader_create(atlas));
    spSkeletonJson* json = spSkeletonJson_createWithLoader(_attachmentLoader);
    json->scale = scale;
    spSkeletonData* skeletonData = spSkeletonJson_readSkeletonDataFile(json, skeletonDataFile);
    CCAssert(skeletonData, json->error ? json->error : "Error reading skeleton data file.");
    spSkeletonJson_dispose(json);
    
    // 缓存起来！
    m_skeletonDataCache.emplace(key, skeletonData);
    m_atlasCache.emplace(key, atlas);
    
    return skeletonData;
}

//
spSkeletonData* SkeletonDataManager::getSkeletonJsonData(const char* skeletonDataFile, const char* atlasFile, float scale  /*= 1.0f */)
{
    CCAssert(skeletonDataFile != NULL && atlasFile != NULL, " skeletonDataFile and atlas file nil");
    std::string key = skeletonDataFile;
    SkeletonDataMap::iterator it = m_skeletonDataCache.find(key);
    if (it != m_skeletonDataCache.end())
    {
        // CCLOG("find skeletonData: %s", key.c_str());
        // 检查是否有 atlas，以及对应texture，如果没有atlas返回false，如果没有texture 创建
        if(checkAtlasByKey(skeletonDataFile, atlasFile))
        {
            return it->second;
        }
    }
    
    // 如果没有的话就创建一个 atlas
    spAtlas* atlas = spAtlas_createFromFile(atlasFile, 0);
    CCAssert(atlas, "Error reading atlas file.");
    if(!atlas)
    {
        return NULL;
    }
    
    // CCLOG("start read json file: %s",skeletonDataFile);
    spAttachmentLoader* _attachmentLoader = SUPER(Cocos2dAttachmentLoader_create(atlas));
    spSkeletonJson* json = spSkeletonJson_createWithLoader(_attachmentLoader);
    
    json->scale = scale;
    spSkeletonData* skeletonData = spSkeletonJson_readSkeletonDataFile(json, skeletonDataFile);
    CCAssert(skeletonData, json->error ? json->error : "Error reading skeleton data file.");
    spSkeletonJson_dispose(json);
    
    // 缓存起来！
    m_skeletonDataCache.emplace(key, skeletonData);
    m_atlasCache.emplace(key, atlas);
    
    return skeletonData;
}


spSkeletonData* SkeletonDataManager::getSkeletonBinaryData(const char* skeletonDataFile, spAtlas* atlas, float scale  /*= 1.0f */)
{
    CCAssert(skeletonDataFile != NULL && atlas != NULL, " skeletonDataFile and atlas file nil");
    std::string key = skeletonDataFile;
    SkeletonDataMap::iterator it = m_skeletonDataCache.find(key);
    if (it != m_skeletonDataCache.end())
    {
        CCLOG("find skeletonData include atlas");
        return it->second;
    }
    
    // 如果没有的话就创建一个
    CCLOG("start read json file include atlas: %s",skeletonDataFile);
    spAttachmentLoader* _attachmentLoader = SUPER(Cocos2dAttachmentLoader_create(atlas));
    spSkeletonBinary* binary = spSkeletonBinary_createWithLoader(_attachmentLoader);
    binary->scale = scale;
    spSkeletonData* skeletonData = spSkeletonBinary_readSkeletonDataFile(binary, skeletonDataFile);
    CCAssert(skeletonData, binary->error ? binary->error : "Error reading skeleton data file.");
    spSkeletonBinary_dispose(binary);
    
    // 缓存起来！
    m_skeletonDataCache.emplace(key, skeletonData);
    m_atlasCache.emplace(key, atlas);
    
    return skeletonData;
}

// skeletonDataFile
spSkeletonData* SkeletonDataManager::getSkeletonBinaryData(const char* skeletonDataFile, const char* atlasFile, float scale  /*= 1.0f */)
{
    CCAssert(skeletonDataFile != NULL && atlasFile != NULL, "getSkeletonBinaryData skeletonDataFile and atlas file nil");
    std::string key = skeletonDataFile;
    SkeletonDataMap::iterator it = m_skeletonDataCache.find(key);
    if (it != m_skeletonDataCache.end())
    {
        // CCLOG("find skeletonData: %s", key.c_str());
        // 检查是否有 atlas，以及对应texture，如果没有atlas返回false，如果没有texture 创建
        if(checkAtlasByKey(skeletonDataFile, atlasFile))
        {
            return it->second;
        }
    }
    
    // 如果没有的话就创建一个 atlas
    spAtlas* atlas = spAtlas_createFromFile(atlasFile, 0);
    CCAssert(atlas, "Error reading atlas file.");
    if(!atlas)
    {
        return NULL;
    }
    
    // CCLOG("start read json file: %s",skeletonDataFile);
    spAttachmentLoader* _attachmentLoader = SUPER(Cocos2dAttachmentLoader_create(atlas));
    spSkeletonBinary* binary = spSkeletonBinary_createWithLoader(_attachmentLoader);
    
    binary->scale = scale;
    spSkeletonData* skeletonData = spSkeletonBinary_readSkeletonDataFile(binary, skeletonDataFile);
    CCAssert(skeletonData, binary->error ? binary->error : "Error reading skeleton data file.");
    spSkeletonBinary_dispose(binary);
    
    // 缓存起来！
    m_skeletonDataCache.emplace(key, skeletonData);
    m_atlasCache.emplace(key, atlas);
    
    return skeletonData;
}

/**
 * 是否缓存里存在该文件
 */
bool SkeletonDataManager::isCacheSkeltonData(const char* skeletonDataFile)
{
    CCAssert(skeletonDataFile != NULL, "SkeletonDataManager::isCacheSkeltonData skeletonDataFile file null");
    std::string key = skeletonDataFile;
    SkeletonDataMap::iterator it = m_skeletonDataCache.find(key);
    if (it != m_skeletonDataCache.end())
    {
        return true;
    }
    return false;
}

// 检查是否有对应texture
bool SkeletonDataManager::checkAtlasByKey(const char* skeletonDataFile, const char* atlasFile)
{
    std::string key = skeletonDataFile;
    auto iter = m_atlasCache.find(key);
    if (iter != m_atlasCache.end())
    {
        spAtlas* atlas = iter->second;
        if (atlas->pages)
        {
            if (atlas->pages->rendererObject)
            {
                return true;
            }
            else
            {
                std::string atlasFileStr = atlasFile;
//                auto nPos = atlasFileStr.find(".");
//                if(nPos != -1)
//                {
//                    atlasFileStr = atlasFileStr.substr(0, nPos);
//                    atlasFileStr += ".png";
//                }
                Texture2D* texture = Director::getInstance()->getTextureCache()->getTextureForKey(atlasFileStr);
                if (texture)
                {
                    CCLOG("SkeletonDataManager checkAtlasByKey find texture: %s", atlasFileStr.c_str());
                    atlas->pages->rendererObject = texture;
                    return true;
                }
                else
                {
                    CCLOG("SkeletonDataManager checkAtlasByKey need create texture: %s", atlasFile);
                    _spAtlasPage_createTexture(atlas->pages, atlasFile);
                    return true;
                }
                
            }
        }
    }
    return false;
}

// 一个spine构建的时候调用
void SkeletonDataManager::retainAtlasTextureByKey(const char* atlasKey)
{
    std::string key = atlasKey;
    auto iter = m_atlasCache.find(key);
    if (iter != m_atlasCache.end())
    {
        spAtlas* atlas = iter->second;
        if (atlas->pages && atlas->pages->rendererObject)
        {
            Texture2D* texture = static_cast<Texture2D*>(atlas->pages->rendererObject);
            texture->retain();
        }
    }
}

// 一个spine析构的时候调用
void SkeletonDataManager::releaseAtlasTextureByKey(const char* atlasKey)
{
    std::string key = atlasKey;
    auto iter = m_atlasCache.find(key);
    if (iter != m_atlasCache.end())
    {
        spAtlas* atlas = iter->second;
        if (atlas->pages && atlas->pages->rendererObject)
        {
            Texture2D* texture = static_cast<Texture2D*>(atlas->pages->rendererObject);
            texture->release();
            // CCLOG("releaseAtlasTextureByKey %d", texture->getReferenceCount());
        }
    }
}

SkeletonDataManager::SkeletonDataManager()
{
    m_skeletonDataCache.clear();
    m_atlasCache.clear();
}

SkeletonDataManager::~SkeletonDataManager()
{
    disposeAllSkeletonData();
}

bool SkeletonDataManager::isSkeletonExist(const char* skeletonDataFile, const char* atlasFile)
{
    std::string key = skeletonDataFile;
    std::string atlas = atlasFile;
    SkeletonDataMap::iterator it = m_skeletonDataCache.find(key);
    if (it != m_skeletonDataCache.end())
    {
        return true;
    }
    if(!FileUtils::getInstance()->isFileExist(skeletonDataFile))
    {
        return false;
    }
    if(atlas != "" && !FileUtils::getInstance()->isFileExist(atlas))
    {
        return false;
    }
    return true;
}

const spAtlas* SkeletonDataManager::getAtlasByKey(const char* skeletonKey)
{
    std::string key = skeletonKey;
    if(m_atlasCache.find(key) != m_atlasCache.end())
    {
        return m_atlasCache[key];
    }
    else
    {
        return nullptr;
    }
}

// 只能应用在没有对应spine对象在使用时
void SkeletonDataManager::disposeAtlasTextureByKey(const char* skeletonKey)
{
    std::string key = skeletonKey;
    auto iter = m_atlasCache.find(key);
    if (iter != m_atlasCache.end())
    {
        spAtlas_dispose(iter->second);
        m_atlasCache.erase(iter);
    }
    
    auto sdIt = m_skeletonDataCache.find(key);
    if (sdIt != m_skeletonDataCache.end()) {
        spSkeletonData_dispose(sdIt->second);
        m_skeletonDataCache.erase(sdIt);
    }
}

void SkeletonDataManager::disposeAllSkeletonData()
{
    for (SkeletonDataMap::iterator it = m_skeletonDataCache.begin(); it != m_skeletonDataCache.end(); ++it) {
        spSkeletonData_dispose(it->second);
    }
    m_skeletonDataCache.clear();

    for (AtlasMap::iterator it = m_atlasCache.begin(); it != m_atlasCache.end(); ++it)
    {
        spAtlas_dispose(it->second);
    }
    m_atlasCache.clear();
}

float SkeletonDataManager::getEventTimeByKey(const std::string& skeletonKey, const std::string& aniName, const std::string& eventName)
{
    spSkeletonData* skeletonData = nullptr;
    auto iter = m_skeletonDataCache.find(skeletonKey);
    if (iter != m_skeletonDataCache.end())
    {
        skeletonData = iter->second;
    }
    if (skeletonData == nullptr)
    {
        return 0.0f;
    }
    spAnimation* animation = spSkeletonData_findAnimation(skeletonData, aniName.c_str());
    if (animation)
    {
        for (int i = 0; i < animation->timelinesCount; ++i)
        {
            // CCASSERT(animation->timelines[i], "");
            if (animation->timelines[i]) {
                spTimeline* timeline = animation->timelines[i];
                if (timeline->type != SP_TIMELINE_EVENT)
                {
                    continue;
                }
                spEventTimeline* eventTimeLine = SUB_CAST(spEventTimeline, timeline);
                if (!eventTimeLine || !eventTimeLine->events)
                {
                    continue;
                }
                for (int j = 0; j < eventTimeLine->framesCount; ++j)
                {
                    if (eventTimeLine->events[j])
                    {
                        spEvent* event = eventTimeLine->events[j];
                        std::string curEventName = event->data->name;
                        if (curEventName.compare(eventName) == 0)
                        {
                            return event->time;
                        }
                    }
                }
            }
        }
    }
    return 0.0f;
}

}
