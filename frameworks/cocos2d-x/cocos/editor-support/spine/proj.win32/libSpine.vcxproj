﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Animation.c" />
    <ClCompile Include="..\AnimationState.c" />
    <ClCompile Include="..\AnimationStateData.c" />
    <ClCompile Include="..\Atlas.c" />
    <ClCompile Include="..\AtlasAttachmentLoader.c" />
    <ClCompile Include="..\Attachment.c" />
    <ClCompile Include="..\AttachmentLoader.c" />
    <ClCompile Include="..\AttachmentVertices.cpp" />
    <ClCompile Include="..\Bone.c" />
    <ClCompile Include="..\BoneData.c" />
    <ClCompile Include="..\BoundingBoxAttachment.c" />
    <ClCompile Include="..\Cocos2dAttachmentLoader.cpp" />
    <ClCompile Include="..\Event.c" />
    <ClCompile Include="..\EventData.c" />
    <ClCompile Include="..\extension.c" />
    <ClCompile Include="..\IkConstraint.c" />
    <ClCompile Include="..\IkConstraintData.c" />
    <ClCompile Include="..\Json.c" />
    <ClCompile Include="..\MeshAttachment.c" />
    <ClCompile Include="..\PathAttachment.c" />
    <ClCompile Include="..\PathConstraint.c" />
    <ClCompile Include="..\PathConstraintData.c" />
    <ClCompile Include="..\RegionAttachment.c" />
    <ClCompile Include="..\Skeleton.c" />
    <ClCompile Include="..\SkeletonAnimation.cpp" />
    <ClCompile Include="..\SkeletonBatch.cpp" />
    <ClCompile Include="..\SkeletonBinary.c" />
    <ClCompile Include="..\SkeletonBounds.c" />
    <ClCompile Include="..\SkeletonData.c" />
    <ClCompile Include="..\SkeletonJson.c" />
    <ClCompile Include="..\SkeletonRenderer.cpp" />
    <ClCompile Include="..\Skin.c" />
    <ClCompile Include="..\Slot.c" />
    <ClCompile Include="..\SlotData.c" />
    <ClCompile Include="..\spine-cocos2dx.cpp" />
    <ClCompile Include="..\TransformConstraint.c" />
    <ClCompile Include="..\TransformConstraintData.c" />
    <ClCompile Include="..\VertexAttachment.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\Animation.h" />
    <ClInclude Include="..\AnimationState.h" />
    <ClInclude Include="..\AnimationStateData.h" />
    <ClInclude Include="..\Atlas.h" />
    <ClInclude Include="..\AtlasAttachmentLoader.h" />
    <ClInclude Include="..\Attachment.h" />
    <ClInclude Include="..\AttachmentLoader.h" />
    <ClInclude Include="..\AttachmentVertices.h" />
    <ClInclude Include="..\Bone.h" />
    <ClInclude Include="..\BoneData.h" />
    <ClInclude Include="..\BoundingBoxAttachment.h" />
    <ClInclude Include="..\Cocos2dAttachmentLoader.h" />
    <ClInclude Include="..\Event.h" />
    <ClInclude Include="..\EventData.h" />
    <ClInclude Include="..\extension.h" />
    <ClInclude Include="..\IkConstraint.h" />
    <ClInclude Include="..\IkConstraintData.h" />
    <ClInclude Include="..\Json.h" />
    <ClInclude Include="..\MeshAttachment.h" />
    <ClInclude Include="..\PathAttachment.h" />
    <ClInclude Include="..\PathConstraint.h" />
    <ClInclude Include="..\PathConstraintData.h" />
    <ClInclude Include="..\RegionAttachment.h" />
    <ClInclude Include="..\Skeleton.h" />
    <ClInclude Include="..\SkeletonAnimation.h" />
    <ClInclude Include="..\SkeletonBatch.h" />
    <ClInclude Include="..\SkeletonBinary.h" />
    <ClInclude Include="..\SkeletonBounds.h" />
    <ClInclude Include="..\SkeletonData.h" />
    <ClInclude Include="..\SkeletonJson.h" />
    <ClInclude Include="..\SkeletonRenderer.h" />
    <ClInclude Include="..\Skin.h" />
    <ClInclude Include="..\Slot.h" />
    <ClInclude Include="..\SlotData.h" />
    <ClInclude Include="..\spine-cocos2dx.h" />
    <ClInclude Include="..\spine.h" />
    <ClInclude Include="..\TransformConstraint.h" />
    <ClInclude Include="..\TransformConstraintData.h" />
    <ClInclude Include="..\VertexAttachment.h" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B7C2A162-DEC9-4418-972E-240AB3CBFCAE}</ProjectGuid>
    <RootNamespace>libSpine</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '12.0'">v120</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '12.0' and exists('$(MSBuildProgramFiles32)\Microsoft SDKs\Windows\v7.1A')">v120_xp</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '14.0'">v140</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '14.0' and exists('$(MSBuildProgramFiles32)\Microsoft SDKs\Windows\v7.1A')">v140_xp</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '12.0'">v120</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '12.0' and exists('$(MSBuildProgramFiles32)\Microsoft SDKs\Windows\v7.1A')">v120_xp</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '14.0'">v140</PlatformToolset>
    <PlatformToolset Condition="'$(VisualStudioVersion)' == '14.0' and exists('$(MSBuildProgramFiles32)\Microsoft SDKs\Windows\v7.1A')">v140_xp</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\..\2d\cocos2d_headers.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\..\2d\cocos2d_headers.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)$(Configuration).win32\</OutDir>
    <IntDir>$(Configuration).win32\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)$(Configuration).win32\</OutDir>
    <IntDir>$(Configuration).win32\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>
      </SDLCheck>
      <AdditionalIncludeDirectories>$(EngineRoot);$(EngineRoot)cocos\editor-support;$(EngineRoot)extensions;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_LIB;COCOS2DXWIN32_EXPORTS;GL_GLEXT_PROTOTYPES;COCOS2D_DEBUG=1;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <DisableSpecificWarnings>4267;4251;4244;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <MinimalRebuild>false</MinimalRebuild>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MinSpace</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>
      </SDLCheck>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_LIB;COCOS2DXWIN32_EXPORTS;GL_GLEXT_PROTOTYPES;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(EngineRoot);$(EngineRoot)cocos\editor-support;$(EngineRoot)extensions;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>None</DebugInformationFormat>
      <WholeProgramOptimization>false</WholeProgramOptimization>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
