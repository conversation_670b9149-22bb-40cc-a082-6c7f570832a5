//
//  DialogBase.cpp
//  Slots
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/12/9.
//

#include "components/DialogComponentsHeader.h"
#include "Dialog.h"
#include "DialogHelper.h"
#include "scripting/lua-bindings/manual/CCLuaEngine.h"
#include "scripting/lua-bindings/manual/LuaBasicConversions.h"
#include "sea/common/SeaCommon.h"

namespace sea {
std::unordered_map<int, std::function<cocos2d::Node *(cocos2d::ValueMap, Dialog *, cocos2d::ValueMap)> > Dialog::sCreateFunMap;
std::unordered_map<int, cocos2d::Value> Dialog::sCreateHandlerMap;
bool Dialog::sIsInitStatic = false;

Dialog::Dialog() :
    _bIsVertical(false),
    _pRootNode(nullptr),
    _bIsDefaultVertical(false),
    _bIsInRoot(false) {
    initStatic();
}

Dialog::~Dialog() {
    clearDialog();
    CC_SAFE_RELEASE_NULL(_pRootNode);
}

//销毁时由lua调用
void Dialog::clearDialog() {
    _mButtonHandlerData.clear();
}

void Dialog::initStatic() {
    if (sIsInitStatic) {
        return;
    }

    sIsInitStatic = true;

    if (sCreateFunMap.find(NodeType::SPRITE) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::SPRITE] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeSprite::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::SPRITE9) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::SPRITE9] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeSprite9::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::ANI) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::ANI] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeAnimation::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::ANI_NEW) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::ANI_NEW] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeAnimationNew::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::LABEL) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::LABEL] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeLabel::create(cfg, dialog, params);
            };
    }
    
    if (sCreateFunMap.find(NodeType::FNT) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::FNT] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeFnt::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::BUTTON_NORMAL) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_NORMAL] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButton::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::BUTTON_NORMAL_NEW) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_NORMAL_NEW] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButton::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::BUTTON_TRADITION) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_TRADITION] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButton::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::BUTTON_TRADITION_NEW) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_TRADITION_NEW] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButton::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::RADIO) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::RADIO] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButtonRadio::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::BUTTON_EMPTY) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_EMPTY] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButtonEmpty::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::BUTTON_MASK) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_MASK] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButtonMask::create(cfg, dialog, params);
            };
    }
    
    if (sCreateFunMap.find(NodeType::BUTTON_PAY) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::BUTTON_PAY] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeButtonPay::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::LIST) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::LIST] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeList::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::PROGRESS) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::PROGRESS] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeProgress::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::PROGRESS_TIMER) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::PROGRESS_TIMER] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeProgressTimer::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::EDIT) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::EDIT] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeEdit::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::SCROLL_VIEW) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::SCROLL_VIEW] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeScrollView::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::CLIPPING) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::CLIPPING] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeClipping::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::SLIDER) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::SLIDER] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeSlider::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::SWITCH) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::SWITCH] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeSwitch::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::ORDER) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::ORDER] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeOrder::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::PAGE_VIEW) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::PAGE_VIEW] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodePageView::create(cfg, dialog, params);
            };
    }
    
    if (sCreateFunMap.find(NodeType::RED) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::RED] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeRedDot::create(cfg, dialog, params);
            };
    }

    if (sCreateFunMap.find(NodeType::DIY) == sCreateFunMap.end()) {
        sCreateFunMap[NodeType::DIY] = [](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
                return DialogNodeDiy::create(cfg, dialog, params);
            };
    }
}

void Dialog::registerLuaFunctionVersion(int type, int handler) {
    sCreateHandlerMap[type] = cocos2d::Value(cocos2d::ValueLuaFunction(handler));
    sCreateFunMap[type] = [ = ](cocos2d::ValueMap cfg, Dialog *dialog, cocos2d::ValueMap params) -> cocos2d::Node * {
            cocos2d::Value luaFun;

            return sea::excuteLuaFunctionToNode(sCreateHandlerMap[type], 3, [&](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, cfg);
                sea::pushValueToStack(stack, dialog, "sea.Dialog");
                sea::pushValueToStack(stack, params);
            });
        };
}

void Dialog::onEnter() {
    cocos2d::Node::onEnter();
}

void Dialog::onExit() {
    for (auto& pair : _vExitCallbackList) {
        pair(this);
    }

    _vExitCallbackList.clear();

    cocos2d::Node::onExit();
}

Dialog * Dialog::create() {
    Dialog *dialog = new (std::nothrow) Dialog();

    if (dialog && dialog->init()) {
        dialog->autorelease();
        return dialog;
    }

    CC_SAFE_DELETE(dialog);
    return nullptr;
}

bool Dialog::init() {
    _pRootNode = cocos2d::Node::create();

    if (_pRootNode) {
        addChild(_pRootNode);
        CC_SAFE_RETAIN(_pRootNode);
    }

    auto visibleSize = cocos2d::Director::getInstance()->getVisibleSize();
    this->setPosition(visibleSize.width * 0.5, visibleSize.height * 0.5);

    //临时测试加关闭
//    runAction(cocos2d::Sequence::create(
//                  cocos2d::DelayTime::create(10),
//                  cocos2d::RemoveSelf::create(),
//                  nullptr
//                  ));

    return true;
}

bool Dialog::initWithData(const cocos2d::ValueMap &params) {
    cocos2d::ValueMap pathConfig;

    for (const auto& pair : params) {
        auto key = pair.first;
        auto val = pair.second;

        if (key == "vertical") {
            _bIsVertical = val.asBool();
        } else if (key == "is_in_root") {
            _bIsInRoot = val.asBool();
        } else if (key == "params") {
            if (val.getType() != ccValueType::MAP) {
                continue;
            }
            auto p = val.asValueMap();

            for (const auto& pair2 : p) {
                auto k = pair2.first;
                auto v = pair2.second;

                if (k == "img") {
                    pathConfig["img"] = v;
                } else if (k == "ani") {
                    pathConfig["ani"] = v;
                } else if (k == "root") {
                    pathConfig["root"] = v;
                } else if (k == "fnt") {
                    pathConfig["fnt"] = v;
                } else if (k == "is_node") {
                    setIsNode();
                } else if (k == "vertical") {
                    _bIsDefaultVertical = v.asBool();
                } else if (k == "res_check_fun") {
                } else if (k == "res_check") {
                }
            }
        }
    }

    pushResPathConfig(pathConfig);
    return true;
}

void Dialog::setIsNode() {
    if (_pRootNode) {
        _pRootNode->setScale(1);
        setPosition(0, 0);
    }
}

void Dialog::pushResPathConfig(const cocos2d::ValueMap& params, bool force/* = false*/) {
    auto push = [](cocos2d::Value& val, std::string& _default, std::unordered_map<std::string, std::string>& _map, const char *where, bool force) {
            auto t = val.getType();

            if (t == ccValueType::STRING) {
                auto newVal = val.asString();

                if (_default.size() > 0 && _default != newVal && !force) {
#if COCOS2D_DEBUG >= 1
                    std::ostringstream oss;
                    oss << "Set path value ERROR. where=" << where << ", old-key=" << _default << ", new-key=" << newVal;
                    sea::showMessageBox(oss.str());
#endif
                } else {
                    _default = newVal;
                }
            }
            else if (t == ccValueType::VECTOR) {
                auto vec = val.asValueVector();
#if COCOS2D_DEBUG >= 1
                if (vec.size() != 1 && !force) {
                    std::ostringstream oss;
                    oss << "Set path value ERROR (default-count must be 1). where=" << where << ", old-key=" << _default << ", new-key=" << "1";
                    sea::showMessageBox(oss.str());
                }
#endif
                if (vec.size() >= 1) {
                    _default = vec[0].asString();
                }
            }
            else if (t == ccValueType::MAP) {
                auto p = val.asValueMap();

                for (const auto & pair : p) {
                    auto k = pair.first;
                    auto v = pair.second;

                    if (k == "1" || k == "default") {
                        auto newVal = v.asString();

                        if (_default.size() > 0 && _default != newVal && !force) {
#if COCOS2D_DEBUG >= 1
                            std::ostringstream oss;
                            oss << "Set path value ERROR. where=" << where << ", old-key=" << _default << ", new-key=" << newVal;
                            sea::showMessageBox(oss.str());
#endif
                        } else {
                            _default = newVal;
                        }
                    } else {
                        auto newVal = v.asString();

                        if (_map.find(k) != _map.end() && _map[k] != newVal && !force) {
#if COCOS2D_DEBUG >= 1
                            std::ostringstream oss;
                            oss << "Set path value ERROR. where=" << where << ", old-key=" << _default << ", new-key=" << newVal;
                            sea::showMessageBox(oss.str());
#endif
                        } else {
                            _map[k] = v.asString();
                        }
                    }
                }
            }
        };

    for (const auto& pair : params) {
        auto key = pair.first;
        auto val = pair.second;

        if (key == "img") {
            if (val.getType() != ccValueType::MAP) {
                continue;
            }
            auto p2 = val.asValueMap();

            for (const auto& pair2 : p2) {
                auto key2 = pair2.first;
                auto val2 = pair2.second;

                if (key2 == "common") {
                    push(val2, _sPathImageCommonDefault, _mPathImageCommonMap, "img_common", force);
                } else if (key2 == "multi") {
                    push(val2, _sPathImageMultiDefault, _mPathImageMultiMap, "img_multi", force);
                }
            }
        } else if (key == "ani") {
            if (val.getType() != ccValueType::MAP) {
                continue;
            }
            auto p2 = val.asValueMap();

            for (const auto& pair2 : p2) {
                auto key2 = pair2.first;
                auto val2 = pair2.second;

                if (key2 == "common") {
                    push(val2, _sPathAniCommonDefault, _mPathAniCommonMap, "ani_common", force);
                } else if (key2 == "multi") {
                    push(val2, _sPathAniMultiDefault, _mPathAniMultiMap, "ani_multi", force);
                }
            }
        } else if (key == "fnt") {
            push(val, _sPathFntDefault, _mPathFntMap, "fnt", force);
        } else if (key == "root") {
            push(val, _sPathRootDefault, _mPathRootMap, "root", force);
        }
    }
}

void Dialog::updateResPathConfig(const cocos2d::ValueMap& params, bool force/* = false*/) {
    pushResPathConfig(params, force);
}

void Dialog::pushExitCallback(const seaCallback1& callback) {
    _vExitCallbackList.push_back(callback);
}

void Dialog::checkOnlyHorizontal(bool isNew /* = false*/) {
    //Angus TODO
}

const cocos2d::Value Dialog::getVertical(const cocos2d::Value& value /* = cocos2d::Value::Null*/, bool force/* = false*/) {
    return DialogHelper::getVertical(value, _bIsVertical, force);
}

spine::SkeletonAnimation * Dialog::getAniPlus(const std::string& key, const cocos2d::ValueMap& params) {
    auto pathVec = getAniPathPlus(key, params);

    if (pathVec.size() >= 2) {
        return spine::SkeletonAnimation::createWithBinaryFile(pathVec[0], pathVec[1]);
    }

    return nullptr;
}

spine::SkeletonAnimation * Dialog::getAni(const std::string& key, const sea::IntBool& isLang /* = sea::IntBool::None*/, const sea::IntBool& isVertical /* = sea::IntBool::None*/, const bool& notAddHead /* = false*/, const std::string& resHeadKey /* = ""*/, const sea::IntBool& isActivity /* = sea::IntBool::None*/, const std::string& themeId /* = ""*/) {
    auto pathVec = getAniPath(key, isLang, isVertical, notAddHead, resHeadKey, isActivity, themeId);

    if (pathVec.size() >= 2) {
        return spine::SkeletonAnimation::createWithBinaryFile(pathVec[0], pathVec[1]);
    }

    return nullptr;
}

std::vector<std::string> Dialog::getAniPathPlus(const std::string& key, const cocos2d::ValueMap& params) {
    std::string res = "", id = "";
    sea::IntBool multi = sea::IntBool::None, isVertical = sea::IntBool::None, activity = sea::IntBool::None;
    bool noHead = false;


    for (const auto& pair : params) {
        auto k = pair.first;
        auto v = pair.second;

        if (k == "lang" || k == "multi") {
            multi = sea::getIntBoolFromCCValue(v);
        } else if (k == "vertical") {
            isVertical = sea::getIntBoolFromCCValue(v);
        } else if (k == "no_head") {
            noHead = v.asBool() ? true : false;
        } else if (k == "res" || k == "res_head") {
            res = v.asString();
        } else if (k == "activity") {
            activity = sea::getIntBoolFromCCValue(v);
        } else if (k == "id") {
            id = v.asString();
        }
    }

    return getAniPath(key, multi, isVertical, noHead, res, activity, id);
}

std::vector<std::string> Dialog::getAniPath(const std::string& key, const sea::IntBool& isLang /* = sea::IntBool::None*/, const sea::IntBool& isVertical /* = sea::IntBool::None*/, const bool& notAddHead /* = false*/, const std::string& resHeadKey /* = ""*/, const sea::IntBool& isActivity /* = sea::IntBool::None*/, const std::string& themeId /* = ""*/) {
    if (key == "") {
#if COCOS2D_DEBUG >= 1
        sea::showMessageBox("key不能为空字符串[Dialog::getAniPath]");
#endif
        std::vector<std::string> ret;
        return ret;
    }

    std::string retPath = key;

    if (isVertical == sea::IntBool::True && _bIsVertical) {
        retPath += "_v";
    }

    //加头
    std::string head = "";

    if (!notAddHead) {
        if (isLang == sea::IntBool::True) {
            if (!resHeadKey.empty()) {
                auto iter = _mPathAniMultiMap.find(resHeadKey);

                if (iter != _mPathAniMultiMap.end()) {
                    head = iter->second;
                } else {
                    head = _sPathAniMultiDefault;
#if COCOS2D_DEBUG >= 1
                    std::ostringstream oss;
                    oss << "找不到配置为" << resHeadKey << "的<多语动效>路径配置，已设置为默认路径" << _sPathAniMultiDefault << "，请修改！[Dialog::getAniPath]";
                    sea::showMessageBox(oss.str());
#endif
                }
            } else {
                head = _sPathAniMultiDefault;
            }
        } else {
            if (!resHeadKey.empty()) {
                auto iter = _mPathAniCommonMap.find(resHeadKey);

                if (iter != _mPathAniCommonMap.end()) {
                    head = iter->second;
                } else {
                    head = _sPathAniCommonDefault;
#if COCOS2D_DEBUG >= 1
                    std::ostringstream oss;
                    oss << "找不到配置为" << resHeadKey << "的<普通动效>路径配置，已设置为默认路径" << _sPathAniCommonDefault << "，请修改！[Dialog::getAniPath]";
                    sea::showMessageBox(oss.str());
#endif
                }
            } else {
                head = _sPathAniCommonDefault;
            }
        }
    }

    cocos2d::ValueMap params;

    if (isLang == sea::IntBool::True) {
        if (isActivity == sea::IntBool::None) {
            cocos2d::ValueMap multiVal;

            if (!themeId.empty()) {
                multiVal["id"] = themeId;
            }

            if (!_bIsInRoot) {
                multiVal["activity"] = true;
            }

            params["multi"] = multiVal;
        } else {
            cocos2d::ValueMap multiVal;

            if (!themeId.empty()) {
                multiVal["id"] = themeId;
            }

            multiVal["activity"] = (isActivity == sea::IntBool::True ? true : false);
            params["multi"] = multiVal;
        }
    }

    return sea::Global::getAnimationPath(head + retPath, params);
}

std::string Dialog::getPath(const std::string& key, const std::string& resHeadKey) {
    if (!resHeadKey.empty()) {
        auto iter = _mPathRootMap.find(resHeadKey);

        if (iter == _mPathRootMap.end()) {
#if COCOS2D_DEBUG >= 1
            std::ostringstream oss;
            oss << "找不到配置为" << resHeadKey << "的<根路径>路径配置，已设置为默认路径" << _sPathRootDefault << "，请修改！[Dialog::getPath]";
            sea::showMessageBox(oss.str());
#endif
            return _sPathRootDefault + key;
        } else {
            return iter->second + key;
        }
    }

    return _sPathRootDefault + key;
}

std::string Dialog::getPath(const std::string& key) {
    return _sPathRootDefault + key;
}

std::string Dialog::getFntPath(const std::string& key, const std::string& resHeadKey) {
    if (!resHeadKey.empty()) {
        auto iter = _mPathFntMap.find(resHeadKey);

        if (iter == _mPathFntMap.end()) {
#if COCOS2D_DEBUG >= 1
            std::ostringstream oss;
            oss << "找不到配置为" << resHeadKey << "的<fnt路径>路径配置，已设置为默认路径" << _sPathFntDefault << "，请修改！[Dialog::getFntPath]";
            sea::showMessageBox(oss.str());
#endif
            return _sPathFntDefault + key + ".fnt";
        } else {
            return iter->second + key + ".fnt";
        }
    }

    return _sPathFntDefault + key + ".fnt";
}

std::string Dialog::getImgPathPlus(const std::string& key, const cocos2d::ValueMap& params) {
    std::string res = "", id = "";
    sea::IntBool isLang = sea::IntBool::None, isVertical = sea::IntBool::None, activity = sea::IntBool::None;
    bool noHead = false;
    sea::TextureResType resType = sea::TextureResType::LOCAL;


    for (const auto& pair : params) {
        auto k = pair.first;
        auto v = pair.second;

        if (k == "lang" || k == "multi") {
            isLang = sea::getIntBoolFromCCValue(v);
        } else if (k == "vertical") {
            isVertical = sea::getIntBoolFromCCValue(v);
        } else if (k == "res_type" || k == "type") {
            resType = sea::getTextureResTypeFromCCValue(v);
        } else if (k == "no_head") {
            noHead = v.asBool() ? true : false;
        } else if (k == "res" || k == "res_head") {
            res = v.asString();
        } else if (k == "activity") {
            activity = sea::getIntBoolFromCCValue(v);
        } else if (k == "id") {
            id = v.asString();
        }
    }

    return getImgPath(key, isLang, isVertical, resType, noHead, res, activity, id);
}

std::string Dialog::getImagePathPlus(const std::string& key, const cocos2d::ValueMap& params) {
    return getImgPathPlus(key, params);
}

std::string Dialog::getImgPath(const std::string& key, const sea::IntBool& isLang /* = sea::IntBool::None*/, const sea::IntBool& isVertical /* = sea::IntBool::None*/, const sea::TextureResType& resType /* = sea::TextureResType::NONE*/, const bool& notAddHead /* = false*/, const std::string& resHeadKey /* = ""*/, const sea::IntBool& isActivity /* = sea::IntBool::None*/, const std::string& themeId /* = ""*/) {
    std::string retPath = key;

    if (isVertical == sea::IntBool::True && _bIsVertical) {
        retPath += "_v";
    }

    sea::IntBool activity = isActivity;

    if (resType == sea::TextureResType::NONE || resType == sea::TextureResType::LOCAL) {
        if (isLang == sea::IntBool::True) {
            if (!_bIsInRoot) {
                if (activity == sea::IntBool::None) {
                    activity = sea::IntBool::True;
                }
            }

            if (notAddHead) {
                return sea::Global::translateImage(retPath, themeId, activity);
            } else {
                std::string pathHead = "";

                if (resHeadKey.empty()) {
                    pathHead = _sPathImageMultiDefault;
                } else {
                    auto iter = _mPathImageMultiMap.find(resHeadKey);

                    if (iter != _mPathImageMultiMap.end()) {
                        pathHead = iter->second;
                    } else {
                        pathHead = _sPathImageMultiDefault;
#if COCOS2D_DEBUG >= 1
                        std::ostringstream oss;
                        oss << "找不到配置为" << resHeadKey << "的<多语切图>路径配置，已设置为默认路径" << _sPathImageMultiDefault << "，请修改！[Dialog::getImgPath]";
                        sea::showMessageBox(oss.str());
#endif
                    }
                }

                return sea::Global::translateImage(pathHead + retPath, themeId, activity);
            }
        } else {
            if (notAddHead) {
                return retPath + ".png";
            } else {
                std::string pathHead = "";

                if (resHeadKey.empty()) {
                    pathHead = _sPathImageCommonDefault;
                } else {
                    auto iter = _mPathImageCommonMap.find(resHeadKey);

                    if (iter != _mPathImageCommonMap.end()) {
                        pathHead = iter->second;
                    } else {
                        pathHead = _sPathImageCommonDefault;
#if COCOS2D_DEBUG >= 1
                        std::ostringstream oss;
                        oss << "找不到配置为" << resHeadKey << "的<普通切图>路径配置，已设置为默认路径" << _sPathImageCommonDefault << "，请修改！[Dialog::getImgPath]";
                        sea::showMessageBox(oss.str());
#endif
                    }
                }

                return pathHead + retPath + ".png";
            }
        }
    } else {
        if (isLang == sea::IntBool::True) {
            if (notAddHead) {
                return sea::Global::translateSpriteFrame(retPath);
            } else {
                if (!_bIsInRoot) {
                    if (activity == sea::IntBool::None) {
                        activity = sea::IntBool::True;
                    }
                }

                std::string pathHead = "";

                if (resHeadKey.empty()) {
                    pathHead = _sPathImageMultiDefault;
                } else {
                    auto iter = _mPathImageMultiMap.find(resHeadKey);

                    if (iter != _mPathImageMultiMap.end()) {
                        pathHead = iter->second;
                    } else {
                        pathHead = _sPathImageMultiDefault;
#if COCOS2D_DEBUG >= 1
                        std::ostringstream oss;
                        oss << "找不到配置为" << resHeadKey << "的<多语切图>路径配置，已设置为默认路径" << _sPathImageMultiDefault << "，请修改！[Dialog::getImgPath]";
                        sea::showMessageBox(oss.str());
#endif
                    }
                }

                return sea::Global::translateImage(pathHead + retPath, themeId, activity);
            }
        } else {
            if (notAddHead) {
                return retPath + ".png";
            } else {
                std::string pathHead = "";

                if (resHeadKey.empty()) {
                    pathHead = _sPathImageCommonDefault;
                } else {
                    auto iter = _mPathImageCommonMap.find(resHeadKey);

                    if (iter != _mPathImageCommonMap.end()) {
                        pathHead = iter->second;
                    } else {
                        pathHead = _sPathImageCommonDefault;
#if COCOS2D_DEBUG >= 1
                        std::ostringstream oss;
                        oss << "找不到配置为" << resHeadKey << "的<普通切图>路径配置，已设置为默认路径" << _sPathImageCommonDefault << "，请修改！[Dialog::getImgPath]";
                        sea::showMessageBox(oss.str());
#endif
                    }
                }

                return pathHead + retPath + ".png";
            }
        }
    }
}

cocos2d::Sprite * Dialog::getImgNodePlus(const std::string& key, cocos2d::ValueMap& params) {
    auto path = getImgPathPlus(key, params);
    sea::TextureResType resType = sea::TextureResType::LOCAL;

    if (params.find("type") != params.end()) {
        resType = sea::getTextureResTypeFromCCValue(params["type"]);
    }

    if (resType == sea::TextureResType::PLIST) {
        return cocos2d::Sprite::createWithSpriteFrameName(path);
    } else {
        sea::checkResTypeName(&path);
        return cocos2d::Sprite::create(path);
    }
}

cocos2d::Sprite * Dialog::getImgNode(const std::string& key, const sea::IntBool& isLang /* = sea::IntBool::None*/, const sea::IntBool& isVertical /* = sea::IntBool::None*/, const sea::TextureResType& resType /* = sea::TextureResType::NONE*/, const bool& notAddHead /* = false*/, const std::string& resHeadKey /* = ""*/, const sea::IntBool& isActivity /* = sea::IntBool::None*/, const std::string& themeId /* = ""*/) {
    auto path = getImgPath(key, isLang, isVertical, resType, notAddHead, resHeadKey, isActivity, themeId);

    if (path.empty()) {
        return cocos2d::Sprite::create();
    }
    else if (resType == sea::TextureResType::PLIST) {
        return cocos2d::Sprite::createWithSpriteFrameName(path);
    } else {
        sea::checkResTypeName(&path);
        return cocos2d::Sprite::create(path);
    }
}

cocos2d::ValueMap Dialog::getLangImgInfoPlus(const std::string& key, const cocos2d::ValueMap& params) {
    std::string res = "", id = "";
    sea::IntBool isLang = sea::IntBool::None, isVertical = sea::IntBool::None, activity = sea::IntBool::None, noHead = sea::IntBool::None;
    sea::TextureResType resType = sea::TextureResType::LOCAL;


    for (const auto& pair : params) {
        auto k = pair.first;
        auto v = pair.second;

        if (k == "lang" || k == "multi") {
            isLang = sea::getIntBoolFromCCValue(v);
        } else if (k == "vertical") {
            isVertical = sea::getIntBoolFromCCValue(v);
        } else if (k == "res_type" || k == "type") {
            resType = sea::getTextureResTypeFromCCValue(v);
        } else if (k == "no_head") {
            noHead = sea::getIntBoolFromCCValue(v);
        } else if (k == "res" || k == "res_head") {
            res = v.asString();
        } else if (k == "activity") {
            activity = sea::getIntBoolFromCCValue(v);
        } else if (k == "id") {
            id = v.asString();
        }
    }

    return getLangImgInfo(key, isVertical, resType, noHead, res, activity, id);
}

cocos2d::ValueMap Dialog::getLangImgInfo(const std::string& key, const sea::IntBool& isVertical /* = sea::IntBool::None*/, const sea::TextureResType& resType /* = sea::TextureResType::NONE*/, const sea::IntBool& notAddHead /* = sea::IntBool::None*/, const std::string& resHeadKey /* = ""*/, const sea::IntBool& isActivity /* = sea::IntBool::None*/, const std::string& themeId /* = ""*/) {
    std::string retPath = key;

    if (isVertical == sea::IntBool::True && _bIsVertical) {
        retPath += "_v";
    }

    sea::IntBool activity = isActivity;

    if (resType == sea::TextureResType::NONE || resType == sea::TextureResType::LOCAL) {
        if (!_bIsInRoot) {
            if (activity == sea::IntBool::None) {
                activity = sea::IntBool::True;
            }
        }

        if (notAddHead == sea::IntBool::True) {
            return sea::Global::translateImagePlus(retPath, themeId, activity);
        } else {
            std::string pathHead = "";

            if (resHeadKey.empty()) {
                pathHead = _sPathImageMultiDefault;
            } else {
                auto iter = _mPathImageMultiMap.find(resHeadKey);

                if (iter != _mPathImageMultiMap.end()) {
                    pathHead = iter->second;
                } else {
                    pathHead = _sPathImageMultiDefault;
#if COCOS2D_DEBUG >= 1
                    std::ostringstream oss;
                    oss << "找不到配置为" << resHeadKey << "的<多语切图>路径配置，已设置为默认路径" << _sPathImageMultiDefault << "，请修改！[Dialog::getImgPath]";
                    sea::showMessageBox(oss.str());
#endif
                }
            }

            return sea::Global::translateImagePlus(pathHead + retPath, themeId, activity);
        }
    } else {
        return getLangImgInfo(retPath, sea::IntBool::False, sea::TextureResType::LOCAL, notAddHead, resHeadKey, activity, themeId);
    }
}

void Dialog::callNodeChild(const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, const std::vector<std::string>& keys, cocos2d::Node *node /* = nullptr*/, bool isPlusChild /* = false*/) {
    if (!node) {
        node = _pRootNode;
    }

    if (!node) {
        return;
    }

    int size = (int)keys.size();
    int count = 0;

    for (auto & iter : keys) {
        ++count;

        if (isPlusChild) {
            node = node->getSeaDialogChildNodeByName(iter);
        } else {
            node = node->getChildByName(iter);
        }

        if (!node) {
            break;
        }

        if (count == size) {
            auto data = node->getSeaUserData("data");

            if (data) {
                callback(node, *data);
            } else {
                callback(node, cocos2d::Value::Null);
            }
        }
    }
}

void Dialog::callNodeChild(const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, const cocos2d::ValueVector& keys, cocos2d::Node *node /* = nullptr*/, bool isPlusChild /* = false*/) {
    if (!node) {
        node = _pRootNode;
    }

    if (!node) {
        return;
    }

    int size = (int)keys.size();
    int count = 0;

    for (auto & iter : keys) {
        ++count;

        if (isPlusChild) {
            node = node->getSeaDialogChildNodeByName(iter.asString());
        } else {
            node = node->getChildByName(iter.asString());
        }

        if (!node) {
            break;
        }

        if (count == size) {
            auto data = node->getSeaUserData("data");

            if (data) {
                callback(node, *data);
            } else {
                callback(node, cocos2d::Value::Null);
            }
        }
    }
}

void Dialog::callNodeChild(const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, const std::string& key, cocos2d::Node *node /* = nullptr*/, bool isPlusChild /* = false*/) {
    if (!node) {
        node = _pRootNode;
    }

    if (!node) {
        return;
    }

    if (isPlusChild) {
        node = node->getSeaDialogChildNodeByName(key);
    } else {
        node = node->getChildByName(key);
    }

    if (node) {
        auto data = node->getSeaUserData("data");

        if (data) {
            callback(node, *data);
        } else {
            callback(node, cocos2d::Value::Null);
        }
    }
}

cocos2d::Vector<cocos2d::Node *> Dialog::callChildrenByGroup(const std::string& group, const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, cocos2d::Node *parent, const cocos2d::ValueMap& params) {
    cocos2d::Vector<cocos2d::Node *> ret;

    if (!parent) {
        parent = _pRootNode;
    }

    int loop = 9999;
    int count = 100;

    for (const auto& pair : params) {
        auto k = pair.first;
        auto v = pair.second;

        if (k == "loop") {
            if (v.getType() == ccValueType::BOOLEAN) {
                if (v.asBool() == false) {
                    loop = 0;
                }
            } else if (v.isNumber()) {
                loop = v.asInt();
            }
        } else if (k == "count") {
            count = v.asInt();
        }
    }

    std::function<void (cocos2d::Node *, int)> call;
    int checkCount = 0;
    call = [&](cocos2d::Node *node, int loop) {
            for (auto& child : node->getChildren()) {
                auto groupValue = child->getSeaUserData("group");

                bool flag = false;

                if (groupValue) {
                    auto type = groupValue->getType();
                    switch (type) {
                        case ccValueType::VECTOR:
                            do{
                                auto vec = groupValue->asValueVector();

                                for (const auto & iter : vec) {
                                    if (iter.getType() == ccValueType::STRING && iter.asString() == group) {
                                        flag = true;
                                        break;
                                    }
                                }
                            }while(0);
                            break;

                        case ccValueType::MAP:
                            do{
                                auto map = groupValue->asValueMap();

                                if (map.find(group) != map.end()) {
                                    flag = true;
                                }
                            }while(0);
                            break;

                        case ccValueType::STRING:

                            if (groupValue->asString() == group) {
                                flag = true;
                            }

                            break;

                        default:
                            break;
                    }
                }while(0);

                if (flag) {
                    if (callback) {
                        auto data = child->getSeaUserData("data");
                        
                        if (data) {
                            callback(child, *data);
                        } else {
                            callback(child, cocos2d::Value::Null);
                        }
                    }
                    ret.pushBack(child);
                }

                ++checkCount;
                

                if (loop) {
                    call(child, loop - 1);
                }
            }
        };
    call(parent, loop);

#if COCOS2D_DEBUG >= 1

    if (checkCount >= count) {
        sea::showMessageBox("使用callChildrenByGroup 查询子节点已超过警戒值，请注意性能开销，检查逻辑是否有进一步优化空间");
    }

#endif

    return ret;
}

cocos2d::Node * Dialog::getRootChildByName(const std::vector<std::string>& keys) {
    return getNodeChildByName(_pRootNode, keys);
}

cocos2d::Node * Dialog::getNodeChildByName(cocos2d::Node *node, const std::vector<std::string>& keys) {
    if (!node) {
        return nullptr;
    }

    for (auto & iter : keys) {
        node = node->getChildByName(iter);

        if (!node) {
            break;
        }
    }

    return node;
}

cocos2d::Node * Dialog::getRootPlusChildByName(const std::vector<std::string>& keys) {
    return getNodePlusChildByName(_pRootNode, keys);
}

cocos2d::Node * Dialog::getNodePlusChildByName(cocos2d::Node *node, const std::vector<std::string>& keys) {
    if (!node) {
        return nullptr;
    }

    for (auto & iter : keys) {
        node = node->getSeaDialogChildNodeByName(iter);

        if (!node) {
            break;
        }
    }

    return node;
}

void Dialog::setRadioButtonCallback(const std::string& group, const int handler) {
    _mRadioGroupCallback[group] = handler;
}

void Dialog::excuteRadioButtonCall(cocos2d::Node *btn, const std::string& group, const cocos2d::Value& data) {
    if (_mRadioGroupCallback.find(group) == _mRadioGroupCallback.end()) {
        return;
    }

    int handler = _mRadioGroupCallback[group];
    sea::excuteLuaFunction(handler, 3, [ = ](cocos2d::LuaStack *stack) {
            sea::pushValueToStack(stack, btn, "cc.Node");
            stack->pushString(group.c_str());
            sea::pushValueToStack(stack, data);
        });
}

void Dialog::addChildrenByConfig(const cocos2d::ValueVector& config, cocos2d::Node *parent) {
    if (!parent) {
        parent = _pRootNode;
    }

    for (auto& iCfg : config) {
        if (iCfg.getType() != ccValueType::MAP) {
#if COCOS2D_DEBUG
            sea::showMessageBox("addChildrenByConfig 配置错误");
#endif
            continue;
        }
        auto node = createNodeWithConfig(iCfg.asValueMap());

        if (node) {
            parent->addChild(node);
        }
    }
}

cocos2d::Node * Dialog::createNodeWithConfig(const cocos2d::ValueMap& config) {
    cocos2d::ValueMap cfg;

    if (config.find("config") != config.end()) {
        cocos2d::ValueMap configValue;

        for (auto& pair : config) {
            auto key = pair.first;
            auto val = pair.second;

            if (key == "config") {
                if (val.getType() == ccValueType::MAP) {
                    configValue = val.asValueMap();
                }

#if COCOS2D_DEBUG >= 1
                else {
                    sea::showMessageBox("config值不合法");
                }
#endif
            } else {
                cfg[key] = val;
            }
        }

        for (const auto& pair : configValue) {
            auto key = pair.first;
            auto val = pair.second;

            if (cfg.find(key) == cfg.end()) {
                cfg[key] = val;
            }
        }
    } else {
        cfg = config;
    }

    //可用性判断
    if (!DialogHelper::isValid(cfg, _bIsVertical)) {
        return nullptr;
    }

    int nodeType = NodeType::DIY;

    if (cfg.find("type") != cfg.end()) {
        nodeType = cfg["type"].asInt();
    }

    cocos2d::Node *node = nullptr;
    cocos2d::ValueMap params;
    params["vertical"] = _bIsVertical;

    auto funIter = sCreateFunMap.find(nodeType);

    if (funIter != sCreateFunMap.end()) {
        auto fun = funIter->second;
        if (cfg.find("vertical") == cfg.end() && _bIsDefaultVertical) {
            cfg["vertical"] = cocos2d::Value(true);
        }
        node = fun(cfg, this, params);
#if COCOS2D_DEBUG >= 1
        if (node && sea::Global::getLogAble()) {
            CCLOG("创建的节点名：%s",node->getName().c_str());
        }
#endif
    }

    return node;
}

Dialog::BtnHandlerData* Dialog::getBtnHandlerData(cocos2d::ui::Widget* button) {
    for (auto iter = _mButtonHandlerData.begin(); iter != _mButtonHandlerData.end(); ++iter) {
        if ((*iter)->getButton() == button) {
            return (*iter);
        }
    }
    return nullptr;
}

void Dialog::_addClickEvent(cocos2d::ui::Widget *btn, sea::seaCallback2 handler, const cocos2d::Value& _params, bool mute /* = false*/, bool noMove /* = false*/) {
    if (!btn) {
        return;
    }
    
    _mButtonHandlerData.pushBack(BtnHandlerData::create(btn, _params));
    btn->addTouchEventListener([ = ](cocos2d::Ref *obj, cocos2d::ui::Widget::TouchEventType type) {
        auto btn = dynamic_cast<cocos2d::ui::Widget *>(obj);

        if (!btn) {
            return;
        }

        switch (type) {
            case cocos2d::ui::Widget::TouchEventType::BEGAN:

                if (_sBtnClickData.pClickedBtn && !_sBtnClickData.pClickedBtn->isTouchEnabled()) {
                    _sBtnClickData.pClickedBtn->release();
                    _sBtnClickData.pClickedBtn = nullptr;
                }

                if (!_sBtnClickData.pClickedBtn) {
                    _sBtnClickData.pClickedBtn = btn;
                    _sBtnClickData.pClickedBtn->retain();
                }

                if (noMove) {
                    _sBtnClickData.vTouchBeginPoint = btn->convertToWorldSpace(btn->getPosition());
                    _sBtnClickData.bIsBeginPoint = true;
                }

                break;

            case cocos2d::ui::Widget::TouchEventType::MOVED:

                if (_sBtnClickData.pClickedBtn == btn) {
                    if (noMove) {
                        if (_sBtnClickData.bIsBeginPoint) {
                            auto endTouchPoint = btn->convertToWorldSpace(btn->getPosition());
                            auto distance = _sBtnClickData.vTouchBeginPoint.distance(endTouchPoint);

                            if (distance > sea::Global::getTouchDistance() ) {
                                _sBtnClickData.bIsBeginPoint = false;
                            }
                        }
                    }
                }

                break;

            case cocos2d::ui::Widget::TouchEventType::ENDED:

                if (_sBtnClickData.pClickedBtn == btn) {
                    cocos2d::Value params = cocos2d::Value::Null;
                    auto handlerData = getBtnHandlerData(btn);
                    if (handlerData) {
                        params = handlerData->getParams();
                    }
                    
                    if (noMove) {
                        if (_sBtnClickData.bIsBeginPoint) {
                            if (!mute) {
                                sea::Global::playBtnClickSound();
                            }

                            //回调lua函数
                            if (handler) {
                                handler(btn, params);
                            }
                        }
                    } else {
                        if (!mute) {
                            sea::Global::playBtnClickSound();
                        }

                        //回调lua函数
                        if (handler) {
                            handler(btn, params);
                        }
                    }
                }

                _sBtnClickData.clear();
                break;

            case cocos2d::ui::Widget::TouchEventType::CANCELED:
                _sBtnClickData.clear();
                break;

            default:
                break;
        }
    });
}

void Dialog::addClickEvent(cocos2d::ui::Widget *btn, sea::seaCallback2 handler, const cocos2d::Value& params) {
    _addClickEvent(btn, handler, params, false, false);
}

void Dialog::addClickEventMute(cocos2d::ui::Widget *btn, sea::seaCallback2 handler, const cocos2d::Value& params) {
    _addClickEvent(btn, handler, params, true, false);
}

void Dialog::addClickEventNoMove(cocos2d::ui::Widget *btn, sea::seaCallback2 handler, const cocos2d::Value& params) {
    _addClickEvent(btn, handler, params, true, true);
}

cocos2d::Node * Dialog::getRootNode() {
    return _pRootNode;
}
}
