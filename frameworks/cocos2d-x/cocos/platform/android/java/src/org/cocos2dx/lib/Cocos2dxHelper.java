/****************************************************************************
Copyright (c) 2010-2012 cocos2d-x.org
Copyright (c) 2013-2017 Chukong Technologies Inc.

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
 ****************************************************************************/
package org.cocos2dx.lib;

import android.Manifest;
import android.app.AlertDialog;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.AudioManager;
import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.res.AssetFileDescriptor;
import android.content.res.AssetManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.IBinder;
import android.os.Looper;
import android.os.MessageQueue;
import android.os.ParcelFileDescriptor;
import android.os.Process;
import android.os.StatFs;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.preference.PreferenceManager.OnActivityResultListener;
import android.provider.MediaStore;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Pair;
import android.view.Display;
import android.view.HapticFeedbackConstants;
import android.view.View;
import android.view.WindowManager;
import android.widget.Toast;

import com.android.vending.expansion.zipfile.ZipResourceFile;
import com.enhance.gameservice.IGameTuningService;
import com.enhance.gameservice.GameTuningServiceAdapter;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.File;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class Cocos2dxHelper {
    // ===========================================================
    // Constants
    // ===========================================================
    public static final String PREFS_NAME = "Cocos2dxPrefsFile";
    private static final int RUNNABLES_PER_FRAME = 5;
    private static final String TAG = Cocos2dxHelper.class.getSimpleName();

    // ===========================================================
    // Fields
    // ===========================================================

    private static Cocos2dxMusic sCocos2dMusic;
    private static Cocos2dxSound sCocos2dSound;
    private static AssetManager sAssetManager;
//    private static Cocos2dxAccelerometer sCocos2dxAccelerometer;
    private static boolean sAccelerometerEnabled;
    private static boolean sCompassEnabled;
    private static boolean sActivityVisible;
    private static String sPackageName;
    private static String sFileDirectory;
    private static Activity sActivity = null;
    private static Cocos2dxHelperListener sCocos2dxHelperListener;
    private static Set<OnActivityResultListener> onActivityResultListeners = new LinkedHashSet<OnActivityResultListener>();
    private static Vibrator sVibrateService = null;
    //Enhance API modification begin
    private static IGameTuningService mGameServiceBinder = null;
    private static final int BOOST_TIME = 7;
    //Enhance API modification end

    // The absolute path to the OBB if it exists, else the absolute path to the APK.
    private static String sAssetsPath = "";
    
    // The OBB file
    private static ZipResourceFile sOBBFile = null;

    // 是否libcocos2dlua.so 库加载上了
    private static boolean mIsLibLoadSuccess = false;

    // 是否SDCard存储足够
    private static boolean mIsSDCardCanUse = true;

    public static boolean getIsInited() {
        return sInited;
    }

    private static boolean loadSyncThreadFinished = false;
    public static boolean getLoadSyncThreadFinished() {
        return loadSyncThreadFinished;
    }
    public static void setLoadSyncThreadFinished(boolean isFinished) {
        loadSyncThreadFinished = isFinished;
    }

    // 是否libcocos2dlua.so 库加载上了
    public static boolean getIsLibLoadSuccess() {
        return mIsLibLoadSuccess;
    }

    // 是否SDCard存储足够
    public static boolean getIsSDCardCanUse() {
        return mIsSDCardCanUse;
    }

    public static void setIsLibLoadSuccess(boolean isSet) {
        mIsLibLoadSuccess = isSet;
    }

    public static void setIsSDCardCanUse(boolean isSet) {
        mIsSDCardCanUse = isSet;
    }

    // 游戏启动流程是否会继续下去
    public static boolean isOnCreateCanGo() {
        return mIsLibLoadSuccess && mIsSDCardCanUse;
    }

    private static Cocos2dxEventListener thisListener;

    // 往Slots模块中调用函数
    public static void setListener(final Cocos2dxEventListener listener) {
        if (thisListener == null) {
            thisListener = new Cocos2dxEventListener() {
                @Override
                public void addAPMTags(String key, String value) {
                    listener.addAPMTags(key, value);
                }
                @Override
                public void loadSyncThreadAtResetFinished() {
                    listener.loadSyncThreadAtResetFinished();
                }
            };
        }
    }

    // 火山监控打点
    public static void addAPMTags(String key, String value) {
        if (thisListener != null) {
            thisListener.addAPMTags(key, value);
        }
//        Log.d(key, value);
    }

    private static Map<String, Boolean> loadSyncThreadAtReset = new HashMap<>();

    public static void loadSyncThreadAtStartApp(String key) {
        // 累加到一定值后执行
        // so;
        // MusicAndSound;
        // GooglePayHelper;
        // accountSDK;
        // FCMRegistrar;
        // FacebookWrapper;
        // notificationChannel;
        loadSyncThreadAtReset.put(key, true);
        if (loadSyncThreadAtReset.size() < 7) {
            return;
        }

        if (thisListener != null) {
            thisListener.loadSyncThreadAtResetFinished();
            loadSyncThreadAtReset.remove("so");
            // loadSyncThreadAtReset.remove("MusicAndSound");
            loadSyncThreadAtReset.remove("GooglePayHelper");
            loadSyncThreadAtReset.remove("accountSDK");
            loadSyncThreadAtReset.remove("FCMRegistrar");
            loadSyncThreadAtReset.remove("FacebookWrapper");
            loadSyncThreadAtReset.remove("notificationChannel");
        }
    }

    public static void hideVirtualButton() {

        // if (Build.VERSION.SDK_INT >= 19)
        {
            // use reflection to remove dependence of API level

            Class viewClass = View.class;

            try {
                final int SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION");
                final int SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN");
                final int SYSTEM_UI_FLAG_HIDE_NAVIGATION = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_HIDE_NAVIGATION");
                final int SYSTEM_UI_FLAG_FULLSCREEN = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_FULLSCREEN");
                final int SYSTEM_UI_FLAG_IMMERSIVE_STICKY = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_IMMERSIVE_STICKY");
                final int SYSTEM_UI_FLAG_LAYOUT_STABLE = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_LAYOUT_STABLE");

                // getWindow().getDecorView().setSystemUiVisibility();
                final Object[] parameters = new Object[]{SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                        | SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                        | SYSTEM_UI_FLAG_IMMERSIVE_STICKY};

//                 Cocos2dxHelper.addAPMTags("scene", "cocosActivity hideVirtualButton 1");
                Cocos2dxReflectionHelper.<Void>invokeInstanceMethod(sActivity.getWindow().getDecorView(),
                        "setSystemUiVisibility",
                        new Class[]{Integer.TYPE},
                        parameters);  // MARK: 77 ms
//                 Cocos2dxHelper.addAPMTags("scene", "cocosActivity hideVirtualButton 2");
            } catch (NullPointerException e) {
                Log.e(TAG, "hideVirtualButton", e);
            }
        }
    }

    // 是否处于低存储状态
    public static boolean isLowSDStorage() {
        File path = Environment.getDataDirectory();
        StatFs stat = new StatFs(path.getPath());
        long blockSize = stat.getBlockSize();
        long freeBlocks = stat.getAvailableBlocks();
        long freeStorage = freeBlocks*blockSize / 1024 / 1024;
        Log.d(TAG, "isLowSDStorage:" + freeStorage);
        mIsSDCardCanUse = freeStorage >= 10; // MB
        return !mIsSDCardCanUse;
    }

    // 某几种类型的弹窗提示
    // 1:sd存储不足
    // 2:libcocoslua.so库加载失败
    public static void showAlertDialog(Activity activity, int type) {
        String language = java.util.Locale.getDefault().getLanguage();

        String titleStr = "Alert";
        if (language.equals("zh")){
            titleStr = "警告";
        }

        String messageStr = "";
        if (1 == type){
            if (language.equals("zh")){
                messageStr = "存储空间不足，请清理存储";
            }else{
                messageStr = "Insufficient storage space, please clear storage.";
            }
        } else if (2 == type){
            if (language.equals("zh")){
                messageStr = "安装包文件损坏，请重新安装";
            }else{
                messageStr = "The installation package file is damaged, please reinstall it.";
            }
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle(titleStr);
        builder.setMessage(messageStr);
        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int id) {
                // User clicked OK button
                killAppProcess(activity);
            }
        });
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    // 杀掉进程
    public static void killAppProcess(final Activity activity)
    {
        Log.d(TAG, "killAppProcess");
        // 先关掉Activity
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            activity.finishAndRemoveTask();
        }else{
            activity.finishAffinity();
        }

        // 再杀掉进程，否则会不断重启
        Process.killProcess(Process.myPid());
        System.exit(0);
    }

    // glView 消失的时候如果有任务，先缓存到这 [2023/07/27 by sunyungao]
    private static final ArrayList<Pair<String, Runnable>> sGLViewRunnableList = new ArrayList<>();

    // 添加一个Runnable(线程安全)
    public static void addGLViewRunnableList(final String msg, final Runnable r) {
        synchronized (sGLViewRunnableList) {
            sGLViewRunnableList.add(new Pair<String, Runnable>(msg, r));
        }
    }

    // 执行恢复Runnable
    public static void resumeGLViewRunnableList() {
        synchronized (sGLViewRunnableList) {
            for (int i = 0; i < sGLViewRunnableList.size(); i++) {
                final Pair<String, Runnable> runnablePair = sGLViewRunnableList.get(i);
                if (runnablePair != null) {
                    runOnGLThread(runnablePair.second);
                    runOnGLThread(new Runnable() {
                        @Override
                        public void run() {
                            Cocos2dxLuaJavaBridge.callLuaGlobalFunctionWithString("confirmJavaLog", runnablePair.first);
                        }
                    });
                }
            }
            sGLViewRunnableList.clear();
        }
    }

    // gl线程消息缓存起来，在每帧结束时批量发送 [2023/08/17 by sunyungao]
    private static ArrayList<Runnable> mPrepareGLRunnableList = new ArrayList<>();

    // 每一帧最多执行Runnable的上限，lua可以修改此值，如果是0的话，就不在每一帧里执行，而是原来默认的直接给GLThread的数组； [2023/08/17 by sunyungao]
    private static int mGLRunnableCountPerFrame = 20;

    public static void setGLRunnableCountPerFrame(int count){
        mGLRunnableCountPerFrame = count;
    }

    public static int getGLRunnableCountPerFrame(){
        return mGLRunnableCountPerFrame;
    }

    public static void addPrepareGLRunnableList(Runnable ra){
        synchronized (mPrepareGLRunnableList){
            mPrepareGLRunnableList.add(ra);
        }
    }

    // onDrawFrame的最后调用
    public static void consumePrepareGLRunnableList(){
        synchronized (mPrepareGLRunnableList){
            int index = 0;
            while (index < mGLRunnableCountPerFrame){
                if (mPrepareGLRunnableList.isEmpty()){
                    break;
                }
                final Runnable runnable = mPrepareGLRunnableList.remove(0);
                if (runnable != null) {
                    ((Cocos2dxActivity) sActivity).getGLSurfaceView().queueEvent(runnable);
                }
                index++;
            }
        }
    }

    // ===========================================================
    // Constructors
    // ===========================================================

    public static void runOnGLThread(final Runnable r) {
        ((Cocos2dxActivity)sActivity).runOnGLThread(r);
    }

    private static boolean sInited = false;
    public static void init(final Activity activity) {
        sActivity = activity;
        Cocos2dxHelper.sCocos2dxHelperListener = (Cocos2dxHelperListener)activity;
        if (!sInited) {
            Cocos2dxHelper.addAPMTags("scene", "Cocos2dxHelper init h_0_2");
            final ApplicationInfo applicationInfo = activity.getApplicationInfo();
            Cocos2dxHelper.sPackageName = applicationInfo.packageName;
            Cocos2dxHelper.sFileDirectory = activity.getFilesDir().getAbsolutePath();
//            Cocos2dxHelper.nativeSetApkPath(Cocos2dxHelper.getAssetsPath());
//            Cocos2dxHelper.sCocos2dxAccelerometer = new Cocos2dxAccelerometer(activity); // MARK: 13
            initCocos2dxMusicAndSound();
            Cocos2dxHelper.sAssetManager = activity.getAssets();
//            Cocos2dxHelper.nativeSetContext((Context)activity, Cocos2dxHelper.sAssetManager);
            Cocos2dxBitmap.setContext(activity);
            Cocos2dxHelper.sVibrateService = (Vibrator)activity.getSystemService(Context.VIBRATOR_SERVICE);
            Cocos2dxHelper.addAPMTags("scene", "Cocos2dxHelper init h_0_3");

            //Enhance API modification begin
            GameTuningServiceAdapter adapter = new GameTuningServiceAdapter(activity.getApplicationContext());
            boolean suc = adapter.bindService();
            if (suc) {
                mGameServiceBinder = adapter;
                fastLoading(BOOST_TIME);
            }
            //Enhance API modification end
            Cocos2dxHelper.addAPMTags("scene", "Cocos2dxHelper init h_0_4");

//            int versionCode = 1;
//            try {
//                versionCode = Cocos2dxActivity.getContext().getPackageManager().getPackageInfo(Cocos2dxHelper.getCocos2dxPackageName(), 0).versionCode;
//            } catch (NameNotFoundException e) {
//                e.printStackTrace();
//            }
//            try {
//                Cocos2dxHelper.sOBBFile = APKExpansionSupport.getAPKExpansionZipFile(Cocos2dxActivity.getContext(), versionCode, 0);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            Cocos2dxHelper.addAPMTags("scene", "Cocos2dxHelper init h_1"); // MARK:17 以上可以删除
            // 初始化MMKV
//            Cocos2dxMMKVHelper.initMMKV(sActivity);
            sInited = true;
        }
    }

    private static void initCocos2dxMusicAndSound() {
        Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                Cocos2dxHelper.sCocos2dMusic = new Cocos2dxMusic(sActivity);
                Cocos2dxHelper.sCocos2dSound = new Cocos2dxSound(sActivity); // MARK: 12
                Cocos2dxHelper.loadSyncThreadAtStartApp("MusicAndSound");
                return false;
            }
        });
    }

    public static void initNativeMethods() {
        PackageManager pm = sActivity.getPackageManager();
        // 检测当前设备是否支持低延迟音频功能‌
        boolean isSupportLowLatency = pm.hasSystemFeature(PackageManager.FEATURE_AUDIO_LOW_LATENCY);

        Log.d(TAG, "isSupportLowLatency:" + isSupportLowLatency);

        int sampleRate = 44100;
        int bufferSizeInFrames = 192;

        if (Build.VERSION.SDK_INT >= 17) {
            AudioManager am = (AudioManager) sActivity.getSystemService(Context.AUDIO_SERVICE);
            // use reflection to remove dependence of API 17 when compiling

            // AudioManager.getProperty(AudioManager.PROPERTY_OUTPUT_SAMPLE_RATE);
            final Class audioManagerClass = AudioManager.class;
            Object[] parameters = new Object[]{Cocos2dxReflectionHelper.<String>getConstantValue(audioManagerClass, "PROPERTY_OUTPUT_SAMPLE_RATE")};
            final String strSampleRate = Cocos2dxReflectionHelper.<String>invokeInstanceMethod(am, "getProperty", new Class[]{String.class}, parameters);

            // AudioManager.getProperty(AudioManager.PROPERTY_OUTPUT_FRAMES_PER_BUFFER);
            parameters = new Object[]{Cocos2dxReflectionHelper.<String>getConstantValue(audioManagerClass, "PROPERTY_OUTPUT_FRAMES_PER_BUFFER")};
            final String strBufferSizeInFrames = Cocos2dxReflectionHelper.<String>invokeInstanceMethod(am, "getProperty", new Class[]{String.class}, parameters);

            try {
                sampleRate = Integer.parseInt(strSampleRate);
                bufferSizeInFrames = Integer.parseInt(strBufferSizeInFrames);
            } catch (NumberFormatException e) {
                Log.e(TAG, "parseInt failed", e);
            }
            Log.d(TAG, "sampleRate: " + sampleRate + ", framesPerBuffer: " + bufferSizeInFrames);
        } else {
            Log.d(TAG, "android version is lower than 17");
        }

        nativeSetAudioDeviceInfo(isSupportLowLatency, sampleRate, bufferSizeInFrames);
        Cocos2dxHelper.nativeSetApkPath(Cocos2dxHelper.getAssetsPath());
        Cocos2dxHelper.nativeSetContext((Context)sActivity, Cocos2dxHelper.sAssetManager);
    }
    
    // It returns the absolute path to the APK only
    // because READ_EXTERNAL_STORAGE&WRITE_EXTERNAL_STORAGE are cancelled.
    public static String getAssetsPath()
    {
        if (Cocos2dxHelper.sAssetsPath == "") {
            Cocos2dxHelper.sAssetsPath = Cocos2dxHelper.sActivity.getApplicationInfo().sourceDir;
            // /data/app/~~GLbWDKscJNXtI0S07r60Eg==/com.grandegames.slots.dafu.casino-1E-_DJlKGJcfZqRqXR95qg==/base.apk
        }
        
        return Cocos2dxHelper.sAssetsPath;
    }
    
    public static ZipResourceFile getObbFile()
    {
        return Cocos2dxHelper.sOBBFile;
    }
    
    //Enhance API modification begin
    // ServiceConnection replaced with Messenger-based communication
    //Enhance API modification end
    
    public static Activity getActivity() {
        return sActivity;
    }
    
    public static void addOnActivityResultListener(OnActivityResultListener listener) {
        onActivityResultListeners.add(listener);
    }
    
    public static Set<OnActivityResultListener> getOnActivityResultListeners() {
        return onActivityResultListeners;
    }
    
    public static boolean isActivityVisible(){
        return sActivityVisible;
    }

    // ===========================================================
    // Getter & Setter
    // ===========================================================

    // ===========================================================
    // Methods for/from SuperClass/Interfaces
    // ===========================================================

    // ===========================================================
    // Methods
    // ===========================================================

    private static native void nativeSetApkPath(final String pApkPath);

    private static native void nativeSetEditTextDialogResult(final byte[] pBytes);

    private static native void nativeSetContext(final Context pContext, final AssetManager pAssetManager);

    private static native void nativeSetAudioDeviceInfo(boolean isSupportLowLatency, int deviceSampleRate, int audioBufferSizeInFames);

    // 检测ANR信号SIGQUIT
    public static native boolean startWatchANR();

    // 停止监听信号 SIGQUIT
    public static native boolean stopWatchANR();

    public static native void setUserDefaultString(final String key, final String value);

    // c++捕捉到ANR信号返回
    public static void anrDumpCallback() {
        ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).confirmANR();
    }

    public static String getCocos2dxPackageName() {
        return Cocos2dxHelper.sPackageName;
    }
    public static String getCocos2dxWritablePath() {
        return Cocos2dxHelper.sFileDirectory;
    }

    public static String getCurrentLanguage() {
        return Locale.getDefault().getLanguage();
    }

    public static String getDeviceModel(){
        return Build.MODEL;
    }

    public static AssetManager getAssetManager() {
        return Cocos2dxHelper.sAssetManager;
    }

    // 加速器，VR相关
    public static void enableAccelerometer() {
//        Cocos2dxHelper.sAccelerometerEnabled = true;
//        Cocos2dxHelper.sCocos2dxAccelerometer.enableAccel();
    }

    // 陀螺仪，VR相关
    public static void enableCompass() {
//        Cocos2dxHelper.sCompassEnabled = true;
//        Cocos2dxHelper.sCocos2dxAccelerometer.enableCompass();
    }

    public static void setAccelerometerInterval(float interval) {
//        Cocos2dxHelper.sCocos2dxAccelerometer.setInterval(interval);
    }

    public static void disableAccelerometer() {
//        Cocos2dxHelper.sAccelerometerEnabled = false;
//        Cocos2dxHelper.sCocos2dxAccelerometer.disable();
    }

    public static void setKeepScreenOn(boolean value) {
        ((Cocos2dxActivity)sActivity).setKeepScreenOn(value);
    }

    public static void vibrate(float duration) {
        sVibrateService.vibrate((long)(duration * 1000));
    }

    public static void performHaptic(int type) {
        ((Cocos2dxActivity)sActivity).getGLSurfaceView().performHapticFeedback(type);
    }

    public static void performHaptic2(int type) {
        if (1 == type && Build.VERSION.SDK_INT >= Build.VERSION_CODES.R){
            ((Cocos2dxActivity) sActivity).getGLSurfaceView().performHapticFeedback(HapticFeedbackConstants.CONFIRM);
        } else if (2 == type && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ((Cocos2dxActivity)sActivity).getGLSurfaceView().performHapticFeedback(HapticFeedbackConstants.CLOCK_TICK);
        } else if (3 == type && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ((Cocos2dxActivity)sActivity).getGLSurfaceView().performHapticFeedback(HapticFeedbackConstants.CONTEXT_CLICK);
        } else {
            ((Cocos2dxActivity) sActivity).getGLSurfaceView().performHapticFeedback(HapticFeedbackConstants.KEYBOARD_TAP);
        }
    }

    public static void performShortVibrator(int type) {
        Vibrator v = (Vibrator)sActivity.getSystemService(Context.VIBRATOR_SERVICE);
        if (v.hasVibrator()) {
            VibrationEffect effect = null;
            if (1 == type && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
                effect = VibrationEffect.createPredefined(VibrationEffect.EFFECT_CLICK);
                v.vibrate(effect);
            } else if (2 == type && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
                effect = VibrationEffect.createPredefined(VibrationEffect.EFFECT_TICK);
                v.vibrate(effect);
            } else if (3 == type && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q){
                effect = VibrationEffect.createPredefined(VibrationEffect.EFFECT_HEAVY_CLICK);
                v.vibrate(effect);
            } else {
                v.vibrate(60);
            }
        }
    }

 	public static String getVersion() {
 		try {
 			String version = Cocos2dxActivity.getContext().getPackageManager().getPackageInfo(Cocos2dxActivity.getContext().getPackageName(), 0).versionName;
 			return version;
 		} catch(Exception e) {
 			return "";
 		}
 	}

    public static boolean openURL(String url) { 
        boolean ret = false;
        try {
            Intent i = new Intent(Intent.ACTION_VIEW);
            i.setData(Uri.parse(url));
            sActivity.startActivity(i);
            ret = true;
        } catch (Exception e) {
        }
        return ret;
    }

    // OBB相关，无相关使用
    public static long[] getObbAssetFileDescriptor(final String path) {
        long[] array = new long[3];
        if (Cocos2dxHelper.sOBBFile != null) {
            AssetFileDescriptor descriptor = Cocos2dxHelper.sOBBFile.getAssetFileDescriptor(path);
            if (descriptor != null) {
                try {
                    ParcelFileDescriptor parcel = descriptor.getParcelFileDescriptor();
                    Method method = parcel.getClass().getMethod("getFd", new Class[] {});
                    array[0] = (Integer)method.invoke(parcel);
                    array[1] = descriptor.getStartOffset();
                    array[2] = descriptor.getLength();
                } catch (NoSuchMethodException e) {
                    Log.e(Cocos2dxHelper.TAG, "Accessing file descriptor directly from the OBB is only supported from Android 3.1 (API level 12) and above.");
                } catch (IllegalAccessException e) {
                    Log.e(Cocos2dxHelper.TAG, e.toString());
                } catch (InvocationTargetException e) {
                    Log.e(Cocos2dxHelper.TAG, e.toString());
                }
            }
        }
        return array;
    }

    public static void preloadBackgroundMusic(final String pPath) {
        Cocos2dxHelper.sCocos2dMusic.preloadBackgroundMusic(pPath);
    }

    public static void playBackgroundMusic(final String pPath, final boolean isLoop) {
        Cocos2dxHelper.sCocos2dMusic.playBackgroundMusic(pPath, isLoop);
    }

    public static void resumeBackgroundMusic() {
        Cocos2dxHelper.sCocos2dMusic.resumeBackgroundMusic();
    }

    public static void pauseBackgroundMusic() {
        Cocos2dxHelper.sCocos2dMusic.pauseBackgroundMusic();
    }

    public static void stopBackgroundMusic() {
        Cocos2dxHelper.sCocos2dMusic.stopBackgroundMusic();
    }

    public static void rewindBackgroundMusic() {
        Cocos2dxHelper.sCocos2dMusic.rewindBackgroundMusic();
    }

    public static boolean willPlayBackgroundMusic() {
        return Cocos2dxHelper.sCocos2dMusic.willPlayBackgroundMusic();
    }

    public static boolean isBackgroundMusicPlaying() {
        return Cocos2dxHelper.sCocos2dMusic.isBackgroundMusicPlaying();
    }

    public static float getBackgroundMusicVolume() {
        return Cocos2dxHelper.sCocos2dMusic.getBackgroundVolume();
    }

    public static void setBackgroundMusicVolume(final float volume) {
        Cocos2dxHelper.sCocos2dMusic.setBackgroundVolume(volume);
    }

    public static void preloadEffect(final String path) {
        Cocos2dxHelper.sCocos2dSound.preloadEffect(path);
    }

    public static int playEffect(final String path, final boolean isLoop, final float pitch, final float pan, final float gain) {
        return Cocos2dxHelper.sCocos2dSound.playEffect(path, isLoop, pitch, pan, gain);
    }

    public static void resumeEffect(final int soundId) {
        Cocos2dxHelper.sCocos2dSound.resumeEffect(soundId);
    }

    public static void pauseEffect(final int soundId) {
        Cocos2dxHelper.sCocos2dSound.pauseEffect(soundId);
    }

    public static void stopEffect(final int soundId) {
        Cocos2dxHelper.sCocos2dSound.stopEffect(soundId);
    }

    public static float getEffectsVolume() {
        return Cocos2dxHelper.sCocos2dSound.getEffectsVolume();
    }

    public static void setEffectsVolume(final float volume) {
        Cocos2dxHelper.sCocos2dSound.setEffectsVolume(volume);
    }

    public static void unloadEffect(final String path) {
        Cocos2dxHelper.sCocos2dSound.unloadEffect(path);
    }

    public static void pauseAllEffects() {
        Cocos2dxHelper.sCocos2dSound.pauseAllEffects();
    }

    public static void resumeAllEffects() {
        Cocos2dxHelper.sCocos2dSound.resumeAllEffects();
    }

    public static void stopAllEffects() {
        Cocos2dxHelper.sCocos2dSound.stopAllEffects();
    }

    public static void end() {
        Cocos2dxHelper.sCocos2dMusic.end();
        Cocos2dxHelper.sCocos2dSound.end();
    }

    public static void onResume() {
        sActivityVisible = true;
//        if (Cocos2dxHelper.sAccelerometerEnabled) {
//            Cocos2dxHelper.sCocos2dxAccelerometer.enableAccel();
//        }
//        if (Cocos2dxHelper.sCompassEnabled) {
//            Cocos2dxHelper.sCocos2dxAccelerometer.enableCompass();
//        }
    }

    public static void onPause() {
        sActivityVisible = false;
//        if (Cocos2dxHelper.sAccelerometerEnabled) {
//            Cocos2dxHelper.sCocos2dxAccelerometer.disable();
//        }
    }

    public static void onEnterBackground() {
        sCocos2dSound.onEnterBackground();
        sCocos2dMusic.onEnterBackground();
    }
    
    public static void onEnterForeground() {
        sCocos2dSound.onEnterForeground();
        sCocos2dMusic.onEnterForeground();
    }
    
    public static void terminateProcess() {
        android.os.Process.killProcess(android.os.Process.myPid());
    }

    private static void showDialog(final String pTitle, final String pMessage) {
        Cocos2dxHelper.sCocos2dxHelperListener.showDialog(pTitle, pMessage);
    }


    public static void setEditTextDialogResult(final String pResult) {
        try {
            final byte[] bytesUTF8 = pResult.getBytes("UTF8");

            Cocos2dxHelper.sCocos2dxHelperListener.runOnGLThread(new Runnable() {
                @Override
                public void run() {
                    Cocos2dxHelper.nativeSetEditTextDialogResult(bytesUTF8);
                }
            });
        } catch (UnsupportedEncodingException pUnsupportedEncodingException) {
            /* Nothing. */
        }
    }

    public static int getDPI()
    {
        if (sActivity != null)
        {
            DisplayMetrics metrics = new DisplayMetrics();
            WindowManager wm = sActivity.getWindowManager();
            if (wm != null)
            {
                Display d = wm.getDefaultDisplay();
                if (d != null)
                {
                    d.getMetrics(metrics);
                    return (int)(metrics.density*160.0f);
                }
            }
        }
        return -1;
    }

    // 是否能从sp迁移到MMKV
//    public static boolean isSPCanTransMMKV() {
//        return Cocos2dxMMKVHelper.isSPCanTransMMKV();
//    }
//
//    public static void transSPToMMKV() {
//        Cocos2dxMMKVHelper.transSPToMMKV();
//    }
//
//    // MMKV有问题的话，是否使用原先的sp
//    public static void setUseSP(boolean isUseSp) {
//        Cocos2dxMMKVHelper.setUseSP(isUseSp);
//    }
//
//    public static boolean getMMKVBoolForKey(String key, boolean defaultValue) {
//        return Cocos2dxMMKVHelper.getMMKVBoolForKey(key, defaultValue);
//    }
//
//    public static int getMMKVIntegerForKey(String key, int defaultValue) {
//        return Cocos2dxMMKVHelper.getMMKVIntegerForKey(key, defaultValue);
//    }
//
//    public static float getMMKVFloatForKey(String key, float defaultValue) {
//        return Cocos2dxMMKVHelper.getMMKVFloatForKey(key, defaultValue);
//    }
//
//    public static double getMMKVDoubleForKey(String key, double defaultValue) {
//        return Cocos2dxMMKVHelper.getMMKVDoubleForKey(key, defaultValue);
//    }
//
//    public static String getMMKVStringForKey(String key, String defaultValue) {
//        return Cocos2dxMMKVHelper.getMMKVStringForKey(key, defaultValue);
//    }
//
//    public static void setMMKVBoolForKey(String key, boolean value) {
//        Cocos2dxMMKVHelper.setMMKVBoolForKey(key, value);
//    }
//
//    public static void setMMKVIntegerForKey(String key, int value) {
//        Cocos2dxMMKVHelper.setMMKVIntegerForKey(key, value);
//    }
//
//    public static void setMMKVFloatForKey(String key, float value) {
//        Cocos2dxMMKVHelper.setMMKVFloatForKey(key, value);
//    }
//
//    public static void setMMKVDoubleForKey(String key, double value) {
//        Cocos2dxMMKVHelper.setMMKVDoubleForKey(key, value);
//    }
//
//    public static void setMMKVStringForKey(String key, String value) {
//        Cocos2dxMMKVHelper.setMMKVStringForKey(key, value);
//    }
//
//    public static void deleteMMKVValueForKey(String key) {
//        Cocos2dxMMKVHelper.deleteMMKVValueForKey(key);
//    }

    // ===========================================================
    // Functions for CCUserDefault
    // ===========================================================
    
    public static boolean getBoolForKey(String key, boolean defaultValue) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        try {
            return settings.getBoolean(key, defaultValue);
        }
        catch (Exception ex) {
            ex.printStackTrace();

            Map allValues = settings.getAll();
            Object value = allValues.get(key);
            if ( value instanceof String)
            {
                return  Boolean.parseBoolean(value.toString());
            }
            else if (value instanceof Integer)
            {
                int intValue = ((Integer) value).intValue();
                return (intValue !=  0) ;
            }
            else if (value instanceof Float)
            {
                float floatValue = ((Float) value).floatValue();
                return (floatValue != 0.0f);
            }
        }

        return defaultValue;
    }
    
    public static int getIntegerForKey(String key, int defaultValue) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        try {
            return settings.getInt(key, defaultValue);
        }
        catch (Exception ex) {
            ex.printStackTrace();

            Map allValues = settings.getAll();
            Object value = allValues.get(key);
            if ( value instanceof String) {
                return  Integer.parseInt(value.toString());
            }
            else if (value instanceof Float)
            {
                return ((Float) value).intValue();
            }
            else if (value instanceof Boolean)
            {
                boolean booleanValue = ((Boolean) value).booleanValue();
                if (booleanValue)
                    return 1;
            }
        }

        return defaultValue;
    }
    
    public static float getFloatForKey(String key, float defaultValue) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        try {
            return settings.getFloat(key, defaultValue);
        }
        catch (Exception ex) {
            ex.printStackTrace();

            Map allValues = settings.getAll();
            Object value = allValues.get(key);
            if ( value instanceof String) {
                return  Float.parseFloat(value.toString());
            }
            else if (value instanceof Integer)
            {
                return ((Integer) value).floatValue();
            }
            else if (value instanceof Boolean)
            {
                boolean booleanValue = ((Boolean) value).booleanValue();
                if (booleanValue)
                    return 1.0f;
            }
        }

        return defaultValue;
    }
    
    public static double getDoubleForKey(String key, double defaultValue) {
        // SharedPreferences doesn't support saving double value
        return getFloatForKey(key, (float) defaultValue);
    }
    
    public static String getStringForKey(String key, String defaultValue) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        try {
            return settings.getString(key, defaultValue);
        }
        catch (Exception ex) {
            ex.printStackTrace();
            
            return settings.getAll().get(key).toString();
        }
    }
    
    public static void setBoolForKey(String key, boolean value) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putBoolean(key, value);
        editor.apply();
    }
    
    public static void setIntegerForKey(String key, int value) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putInt(key, value);
        editor.apply();
    }
    
    public static void setFloatForKey(String key, float value) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putFloat(key, value);
        editor.apply();
    }
    
    public static void setDoubleForKey(String key, double value) {
        // SharedPreferences doesn't support recording double value
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putFloat(key, (float)value);
        editor.apply();
    }
    
    public static void setStringForKey(String key, String value) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.putString(key, value);
        editor.apply();
    }
    
    public static void deleteValueForKey(String key) {
        SharedPreferences settings = sActivity.getSharedPreferences(Cocos2dxHelper.PREFS_NAME, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.remove(key);
        editor.apply();
    }

    public static byte[] conversionEncoding(byte[] text, String fromCharset,String newCharset)
    {
        try {
            String str = new String(text,fromCharset);
            return str.getBytes(newCharset);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return null;
    }

    // ===========================================================
    // Inner and Anonymous Classes
    // ===========================================================

    public static interface Cocos2dxHelperListener {
        public void showDialog(final String pTitle, final String pMessage);

        public void runOnGLThread(final Runnable pRunnable);
    }

    //Enhance API modification begin
    public static int setResolutionPercent(int per) {
        try {
            if (mGameServiceBinder != null) {
                return mGameServiceBinder.setPreferredResolution(per);
            }
            return -1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static int setFPS(int fps) {
        try {
            if (mGameServiceBinder != null) {
                return mGameServiceBinder.setFramePerSecond(fps);
            }
            return -1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static int fastLoading(int sec) {
        try {
            if (mGameServiceBinder != null) {
                return mGameServiceBinder.boostUp(sec);
            }
            return -1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static int getTemperature() {
        try {
            if (mGameServiceBinder != null) {
                return mGameServiceBinder.getAbstractTemperature();
            }
            return -1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static int setLowPowerMode(boolean enable) {
        try {
            if (mGameServiceBinder != null) {
                return mGameServiceBinder.setGamePowerSaving(enable);
            }
            return -1;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    //Enhance API modification end     
    public static float[] getAccelValue() {
//        return Cocos2dxHelper.sCocos2dxAccelerometer.accelerometerValues;
        return new float[3];
    }

    public static float[] getCompassValue() {
//        return Cocos2dxHelper.sCocos2dxAccelerometer.compassFieldValues;
        return new float[3];
    }

    public static int getSDKVersion() {
        return Build.VERSION.SDK_INT;
    }

    // 保存内部存储的图片到相册中 【sunyungao 23/08/30】
    public static String saveImageToGalleryPath;
    public static void saveImageToGallery(String path)
    {
        boolean isCanPerform = false;
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) { // 29 android 10
            if (Cocos2dxHelper.getActivity().checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED) {
                // 已经拥有WRITE_EXTERNAL_STORAGE权限，可以进行文件操作
                isCanPerform = true;
            } else {
                saveImageToGalleryPath = path;
                // 请求WRITE_EXTERNAL_STORAGE权限
                Cocos2dxHelper.getActivity().requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, 1);
            }
        } else {
            // 大于29的如果只需要保存媒体文件到媒体库，不需要申请权限
            isCanPerform = true;
        }

        if (!isCanPerform){
            return;
        }

        final String[] tPath = {path};
        new Thread(new Runnable() {
            @Override
            public void run() {
                tPath[0] = getCocos2dxWritablePath() + "/" + tPath[0];
                Bitmap bitmap = BitmapFactory.decodeFile(tPath[0]);
                if (bitmap == null) {
                    Cocos2dxHelper.showDialog("Tips", "saved image fail......");
                    return;
                }

                // 创建要保存的图像文件
                String fileName = "Image_" + System.currentTimeMillis() + ".png";
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    final ContentValues values = new ContentValues();
                    values.put(MediaStore.Images.Media.TITLE, fileName);
                    values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + File.separator + "boleDafu"); //Environment.DIRECTORY_SCREENSHOTS:截图,图库中显示的文件夹名。
                    values.put(MediaStore.MediaColumns.DISPLAY_NAME, fileName);
                    values.put(MediaStore.MediaColumns.MIME_TYPE, "image/png");
                    values.put(MediaStore.MediaColumns.DATE_ADDED, System.currentTimeMillis() / 1000);
                    values.put(MediaStore.MediaColumns.DATE_MODIFIED, System.currentTimeMillis() / 1000);
                    values.put(MediaStore.MediaColumns.DATE_EXPIRES, System.currentTimeMillis() / 1000);
                    // 文件保存成功后，将图像插入到相册中
                    values.put(MediaStore.MediaColumns.IS_PENDING, 1);
                    ContentResolver resolver = sActivity.getContentResolver();
                    Uri uri = resolver.insert(MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY), values);
                    try {
                        // First, write the actual data for our screenshot
                        assert uri != null;
                        try (OutputStream out = resolver.openOutputStream(uri)) {
                            assert out != null;
                            if (!bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)) {
                                Cocos2dxHelper.showDialog("Tips", "saved image fail...");
                                return;
                            }
                        }
                        // Everything went well above, publish it!
                        values.clear();
                        values.put(MediaStore.MediaColumns.IS_PENDING, 0);
                        values.putNull(MediaStore.MediaColumns.DATE_EXPIRES);
                        resolver.update(uri, values, null, null);
                    } catch (IOException e) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R){
                            resolver.delete(uri, null);
                        }
                    }

                    Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(sActivity.getApplicationContext(), "saved image success", Toast.LENGTH_SHORT).show();
                        }
                    });
                } else {
                    // Android Q 以下版本，使用 MediaStore.Images.Media.insertImage()
                    // values.put(MediaStore.Images.Media.DATA, imageFile.getAbsolutePath());
//                    Uri uri = sActivity.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);

                    // 首先保存图片
                    File appDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "boleDafu");
                    if (!appDir.exists() && !appDir.mkdirs()) {
                        Log.e("TAG", "目录创建失败");
                        Cocos2dxHelper.showDialog("Tips", "saved image fail...");
                        return;
                    }

                    File file = new File(appDir, fileName);
                    try {
                        FileOutputStream fos = new FileOutputStream(file);
                        // 通过io流的方式来压缩保存图片
                        boolean isSuccess = bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos);
                        fos.flush();
                        fos.close();

                        // 保存图片后发送广播通知更新数据库
                        Uri uri = Uri.fromFile(file);
                        sActivity.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri));
                        if (isSuccess) {
                            Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    Toast.makeText(sActivity.getApplicationContext(), "saved image success", Toast.LENGTH_SHORT).show();
                                }
                            });
                        } else {
                            Cocos2dxHelper.showDialog("Tips", "saved image fail....");
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }).start();
    }

    // 异步删除可读写目录下：文件夹及文件夹下所有内容
    public static void deleteWritableFileDirAsync(final String deleteFileDir) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Thread mCheckTimeThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Path path = Paths.get(Cocos2dxHelper.getCocos2dxWritablePath() + "/" + deleteFileDir);
                        Files.walkFileTree(path,
                                new SimpleFileVisitor<Path>() {
                                    // 先去遍历删除文件
                                    @Override
                                    public FileVisitResult visitFile(Path file,
                                                                     BasicFileAttributes attrs) throws IOException {
                                        Files.delete(file);
                                        //Log.d(TAG, "删除文件: " + file);
                                        return FileVisitResult.CONTINUE;
                                    }
                                    // 再去遍历删除目录
                                    @Override
                                    public FileVisitResult postVisitDirectory(Path dir,
                                                                              IOException exc) throws IOException {
                                        Files.delete(dir);
                                        //Log.d(TAG, "删除目录: " + dir);
                                        return FileVisitResult.CONTINUE;
                                    }

                                }
                        );
                        //Log.d(TAG, "删除完成");
                        Cocos2dxHelper.callLuaGlobalFunctionWithStringOnGL("deleteWritableFileDirAsync", "");
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            });
            mCheckTimeThread.start();
            mCheckTimeThread.setName("deleteWritableFileDir");
        } else
        {
            Thread mCheckTimeThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    deleteDirectory(Cocos2dxHelper.getCocos2dxWritablePath() + "/" + deleteFileDir);
                    //Log.d(TAG, "删除完成");
                    Cocos2dxHelper.callLuaGlobalFunctionWithStringOnGL("deleteWritableFileDirAsync", "");
                }
            });
            mCheckTimeThread.start();
            mCheckTimeThread.setName("deleteWritableFileDir");
        }
    }

    /**
     * 删除单个文件
     *
     * @param fileName
     *            被删除文件的文件名
     * @return 单个文件删除成功返回true,否则返回false
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.isFile() && file.exists()) {
            file.delete();
            //Log.d(TAG, "删除文件: " + fileName + "成功！");
            return true;
        } else {
            Log.e(TAG, "删除文件: " + fileName + "失败！");
            return false;
        }
    }

    /**
     * 删除目录（文件夹）以及目录下的文件
     *
     * @param dir
     *            被删除目录的文件路径
     * @return 目录删除成功返回true,否则返回false
     */
    public static boolean deleteDirectory(String dir) {
        // 如果dir不以文件分隔符结尾，自动添加文件分隔符
        if (!dir.endsWith(File.separator)) {
            dir = dir + File.separator;
        }
        File dirFile = new File(dir);
        // 如果dir对应的文件不存在，或者不是一个目录，则退出
        if (!dirFile.exists() || !dirFile.isDirectory()) {
            Log.d(TAG, "删除目录失败: " + dir + "目录不存在！");
            return false;
        }
        boolean flag = true;
        // 删除文件夹下的所有文件(包括子目录)
        File[] files = dirFile.listFiles();
        for (int i = 0; i < Objects.requireNonNull(files).length; i++) {
            // 删除子文件
            if (files[i].isFile()) {
                flag = deleteFile(files[i].getAbsolutePath());
                if (!flag) {
                    break;
                }
            }
            // 删除子目录
            else {
                flag = deleteDirectory(files[i].getAbsolutePath());
                if (!flag) {
                    break;
                }
            }
        }

        if (!flag) {
            Log.e(TAG, "删除目录失败");
            return false;
        }

        // 删除当前目录
        if (dirFile.delete()) {
            //Log.d(TAG, "删除目录: " + dir + "成功！");
            return true;
        } else {
            Log.e(TAG, "删除目录: " + dir + "失败！");
            return false;
        }
    }

    public static void showToast(final String content){
        Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(sActivity.getApplicationContext(), content, Toast.LENGTH_SHORT).show();
            }
        });
    }

    public static void callLuaGlobalFunctionWithStringOnGL(final String funcName, final String value){
        if (Cocos2dxHelper.getActivity() == null || ((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView() == null) {
            return;
        }
        if (((Cocos2dxActivity)Cocos2dxHelper.getActivity()).getGLSurfaceView().getCocos2dxRenderNativeInitCompleted()) {
            Cocos2dxHelper.runOnGLThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Cocos2dxLuaJavaBridge.callLuaGlobalFunctionWithString(funcName, value);
                    } catch (Exception e) {
                        Log.e(TAG, e.toString());
                        e.printStackTrace();
                    }
                }
            });
        }
    }
}
