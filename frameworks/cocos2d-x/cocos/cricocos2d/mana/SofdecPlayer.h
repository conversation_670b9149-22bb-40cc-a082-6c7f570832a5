/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/**
 * @addtogroup CRICOCOS2D
 * @{
 */

/**
 * @file cricocos2d/mana/Player.h
 */


/* Prevention of redefinition */
#ifndef CRICOCOS2D_MANA_PLAYER_H
#define CRICOCOS2D_MANA_PLAYER_H


#include "cricocos2d/cricocos2d.h"

#include <memory>
#include <functional>
#include <tuple>


namespace cricocos2d
{
	namespace mana
	{
        /**
         * @headerfile cricocos2d/mana/Player.h
         *
         * @~chinese
         * @brief 协调 CriManaPlayer 和渲染资源的类
         *
         * 这是一个用于协调 CriManaPlayer 和渲染资源（如着色器、纹理）的类。\n
         * 它内部持有一个 CriManaPlayer，根据需要生成渲染资源并进行视频帧传输。\n
         *
         * @remarks
         * 如果要直接使用其内部持有的 CriManaPlayer 的 API，
         * 请使用 #nativePlayer。
         *
         *
         */
		class SofdecPlayer : public cocos2d::Ref
		{
		public:
            /**
             * @~chinese
             * @brief 获取 CriManaPlayerConfig 的默认值
             */
			inline static CriManaPlayerConfig getDefaultConfig() CRICOCOS2D_NOEXCEPT
			{
				CriManaPlayerConfig config;
				criManaPlayer_SetDefaultHandleConfig(&config);
				return config;
			}

            /**
             * @~chinese
             * @brief 播放器状态
             *
             * 表示 Player 的状态的值。\n
             * 初始状态是 Status::Stop。
             */
            enum class Status
            {
                Stop,            /**< @~chinese 停止中                        */
                DecHdr,          /**< @~chinese 头部解析中                    */
                WaitPrep,        /**< @~chinese 缓冲开始停止中                */
                Prep,            /**< @~chinese 准备播放中                    */
                Ready,           /**< @~chinese 准备播放完成                */
                Playing,         /**< @~chinese 正在播放中                    */
                PlayEnd,         /**< @~chinese 播放结束                    */
                Error,           /**< @~chinese 错误                        */
                StopProcessing,  /**< @~chinese 正在处理停止                */
            };


            SofdecPlayer(const SofdecPlayer&)			   = delete;
            SofdecPlayer& operator = (const SofdecPlayer&) = delete;

            /**
             * @~chinese
             * @brief 构造函数
             *
             * 这是构造函数。\n
             * config 是用于创建内部生成的 CriManaPlayer 的配置。
             *
             * @param[in]    config    用于创建 CriManaPlayer 的配置结构体
             */
			explicit SofdecPlayer(const CriManaPlayerConfig& config);

			/**
			 * @~japanese
			 * @brief デストラクタ
			 *
			 * デストラクタです。\n
			 * 内部で生成したリソースを開放します。
			 */
			~SofdecPlayer();

            /**
             * @~chinese
             * @brief 获取内部持有的 CriManaPlayer 句柄
             *
             * 获取内部所持有的 CriManaPlayer 句柄。
             *
             * @attention
             * 为了本类能够正常工作，需要通过本类对内部持有的 CriManaPlayer 进行播放控制。\n
             * 如果使用此成员函数获取的 CriManaPlayerHn 直接调用 CriManaPlayer 的 API，
             * 请确保不进行播放控制。
             *
             * @return 内部持有的 CriManaPlayer 句柄
             */
			CriManaPlayerHn nativePlayer() const CRICOCOS2D_NOEXCEPT
			{
				return mana_player_;
			}


			/**
			 * @~japanese
			 * @brief 状態取得
			 *
			 * @return	Player の状態
			 */
			Status getStatus() const CRICOCOS2D_NOEXCEPT;

			/**
			 * @~japanese
			 * @brief ポーズ状態を取得
			 *
			 * @return	ポーズ状態
			 */
			bool isPaused() const CRICOCOS2D_NOEXCEPT;

			/**             
             * @brief 获取播放时间
             * 获取播放时间。\n
             * 在开始播放之前和停止播放后，返回时间 0 (定时器计数为0)。\n
             * 该成员函数仅返回由主定时器指定的定时器时间，
             * 并非返回视频帧的时间。
             * 视频帧的原始显示时间可以通过 #getFrameInfo 获取。
             * @remarks
             * 可以通过以下处理来计算秒数：
             * @code
             * auto playbackTime = player.getTime();
             * double playbackTimeFloat = std::get<0>(playbackTime) / static_cast<double>(std::get<1>(playbackTime));
             * @endcode
             * @return 播放时间(第1个元素是定时器计数，第2个元素是每秒的定时器计数值)
			 */
			std::tuple<CriUint64, CriUint64> getTime() const CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 获取电影信息
             *
             * 获取电影信息。\n
             * 在 Status::WaitPrep 到 Status::PlayEnd 之间的状态，会返回一个有效的指针。\n
             * 在其他状态下，返回 nullptr。
             *
             * @return    电影信息（如果无法获取电影信息，则为 nullptr）
             */
			const CriManaMovieInfo* getMovieInfo() const CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 获取视频帧信息
             *
             * 获取当前可以绘制的视频帧信息。\n
             * 从第一个视频帧可以绘制的时刻开始，
             * 到 #stop 被调用为止，会返回一个有效的指针。\n
             * 在其他状态下，返回 nullptr。\n
             * 但是，即使没有调用 #stop，如果状态变为 Status::Error，也会返回 nullptr。
             *
             * @return    帧信息（如果无法获取帧信息，则为 nullptr）
             */
			const CriManaFrameInfo* getFrameInfo() const CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 请求准备播放
             * 发出请求准备播放电影。\n
             * 该成员函数用于只进行头部解析和准备播放，而不开始播放电影，进入等待状态。\n
             * 通过使用此成员函数提前完成播放准备，可以精细控制电影播放开始的时间点。\n
             * 调用此成员函数并完成播放准备后，状态将变为 Status::Ready 。\n
             * 在状态为 Status::Ready 的时候，通过调用 #start 可以开始播放。<br>
             * @pre
             * 必须在 Status::Stop 状态下。
             * 请在执行本成员函数之前指定播放文件或播放数据。\n
             * 但是，如果已经注册了数据请求回调函数，则可以省略预先设置电影数据。
             * @remarks
             * 如果未调用此成员函数就直接调用 #start ，则实际播放开始前会有时间延迟。
             */
			void prepare() CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 请求开始播放
             * 发出请求开始播放电影的指令。
             * @pre
             * 如果在没有调用 #prepare 的情况下直接调用这个成员函数，状态必须是
             * Status::Stop 。
             * 请在执行本成员函数之前指定播放文件或播放数据。\n
             * 但是，如果已经注册了数据请求回调函数，那么可以省略预先设置电影数据的步骤。
             * @remarks
             * 如果在未调用 #prepare 的情况下直接调用此成员函数，由于需要进行电影的解析和播放准备，
             * 所以在实际开始播放电影之前会有时间延迟。\n
             * 如果先调用了 #prepare 并且状态变为了 Status::Ready ，
             * 那么调用这个成员函数后，播放将立即开始。
             */
			void start() CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 请求立即停止播放
             * 发出请求立即停止播放电影的指令。这是一个即时返回函数。\n
             * 这个成员函数并不会执行所有的停止处理。\n
             * 在执行此成员函数后，如果状态变为 Status::StopProcessing ，
             * 那么在主循环中，请确认状态已自动转变为 Status::Stop 后，再进行下一步的播放控制。
             * 另外，如果状态变为 Status::Error ，则请调用此成员函数并等待状态转变为 Status::Stop 。
             * @post
             * 状态将变为 Status::StopProcessing 或 Status::Stop 。
             * 如果状态变为 Status::StopProcessing ，那么当停止处理完成后，状态将自动变为 Status::Stop 。
             * 如果之前的状态是 Status::PlayEnd ，那么状态将变为 Status::Stop (停止处理将立即完成)。
             * @remarks
             * 如果你希望阻塞主处理以完成停止处理，那么请使用 #stopAndWaitCompletion 。
             */
			void stop() CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 请求停止播放（完成返回）
             * 发出请求停止播放电影的指令。这是一个完成返回函数。\n
             * 当从此成员函数返回时，状态将变为 Status::Stop 。\n
             * 由于此成员函数会等待流处理停止等操作，因此可能会阻塞几十毫秒的处理。\n
             * 只有在游戏处理可以暂停的情况下，如场景切换时机等，才应执行此函数。
             * @post
             * 状态将变为 Status::Stop 。
             * @remarks
             * 如果主处理被阻塞会造成问题，那么请使用 #stop 。
            */
			void stopAndWaitCompletion() CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 切换暂停状态
             * 切换暂停状态。\n
             * 即使调用此成员函数，文件读取或解码的停止也不会立即执行。\n
             * 调用 #stop 会解除暂停状态。
             * @param[in] sw 暂停开关
             */
			void pause(bool sw) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 切换循环播放的开关
             * 切换循环播放的开关，默认为关闭循环播放。\n
             * 如果打开循环播放，即使播放到电影的结束部分，状态也不会变为 Status::PlayEnd ，而是会从电影的开始部分重复播放。\n
             * 如果关闭循环播放，当播放到当前加载电影的结束部分时，状态会转变为 Status::PlayEnd 。\n
             * 如果在循环播放过程中关闭了循环播放，根据时间点的不同，可能不会在播放结束时停止，而是会执行下一个循环播放。
             * @param[in] sw 循环开关
             */
			void loop(bool sw) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置搜索位置
             * 指定开始搜索播放的帧号。\n
             * 如果 seek_frame_no 大于电影数据的总帧数或为负数，则从电影的开头开始播放。\n
             * 即使在调用此成员函数后停止播放、开始播放，搜索位置的设置信息也会保留。
             * @pre
             * 必须处于 Status::Stop 状态。
             * 必须已使用 #setMetaDataWorkAllocator 注册元数据用内存分配器。
             * @param[in] seek_frame_no 目标搜索帧号
             */
			void setSeekPosition(CriSint32 seek_frame_no) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 注册元数据用分配器
             * 注册一个内存分配器，用于分配进行搜索和关键点功能所需的元数据。\n
             * 如果使用此成员函数注册了内存分配器，那么在进行头部解析时，库内部将使用
             * 已注册的元数据工作用分配器来分配元数据工作。\n
             * 如果不注册内存分配器，元数据将被丢弃。请注意，这种情况下无法使用搜索或
             * 关键点功能。\n
             * 如果你想重新使用 Player 播放电影，不需要重新注册分配器。
             * @param[in] allocator 分配器
             */
			void setMetaDataWorkAllocator(const Allocator& allocator) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * 这个回调是最后帧的“数据读完”的时候，会比实际播完早几帧
             * @brief 注册数据请求回调函数
             * 注册一个电影文件请求回调函数，用于进行电影的连续播放。\n
             * 此回调函数在以下时机触发：
             * 当电影文件读取完成时。
             * 当以无文件指定的方式开始播放时。
             * 在数据请求回调函数中调用 #setFile, #setFileRange, #setData, #setContentId ，
             * 可以指定连续的下一个电影文件。\n
             * 如果都没有调用，则当已加载的电影播放结束后，状态将转变为 Status::PlayEnd 。\n
             * 另外，如果要再次播放上一次指定的电影文件，应在回调函数中调用 #setPreviousDataAgain 。
             * @attention
             * 要进行连续播放的电影文件必须满足以下所有条件：
             * 视频分辨率相同
             * 视频帧率相同（只有在主定时器不处于异步模式的情况下）
             * 视频编解码器相同
             * 音频和字幕的轨道配置相同
             * @param[in] callback_func 数据请求回调对象
             */
			void setDataRequestCallback(std::function<void(SofdecPlayer&)> callback_func) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 注册关键点回调函数
             * 注册一个关键点的回调函数。\n
             * 当电影播放时间经过各个事件点指定的时间时，将触发此回调函数。\n
             * 在关键点回调发生时，回调函数的第二个参数 eventinfo 将传递事件点信息。
             * @attention
             * 在关键点回调函数中，不应调用控制电影播放的成员函数（如 #prepare, #start, #stop 等）。
             * @param[in] callback_func 关键点回调对象
             */
			void setCuePointCallback(std::function<void(SofdecPlayer&, const CriManaEventPoint&)> callback_func) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置内存播放的数据
             * 设置用于内存播放的数据。\n
             * 如果调用了 #setFile, #setFileRange, #setData, #setContentId ，
             * 则此成员函数指定的内存信息将无效。
             * 如果使用同一个 Player 反复播放相同的电影数据，可以省略调用此成员函数。
             * @pre
             * 必须处于 Status::Stop 状态。\n
             * 唯一的例外是，可以在数据请求回调中调用此函数。
             * @remarks
             * 如果指定的缓冲区大于电影数据大小，
             * 将跳过缓冲区末端的非电影数据部分。\n
             * 对于缓冲区地址，请始终指定电影数据的开始位置。
             * @param[in] dataptr 加载完整电影数据的缓冡区地址
             * @param[in] datasize 缓冲区大小
             */
			void setData(const void* dataptr, CriSint64 datasize) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置电影文件
             * 设置用于流媒体播放的文件。\n
             * 通过指定绑定了CPK文件的绑定器句柄 bndrhn ，可以从CPK文件中播放电影。\n
             * 如果要直接从文件而不是CPK进行流媒体播放，应将 bndrhn 设置为 nullptr 。
             * 如果调用了 #setFile, #setFileRange, #setData, #setContentId ，
             * 则此成员函数指定的文件信息将无效。
             * 如果使用同一个 Player 反复播放相同的电影文件，可以省略调用此成员函数。
             * @pre
             * 必须处于 Status::Stop 状态。\n
             * 唯一的例外是，可以在数据请求回调中调用此函数。
             * @param[in] bndrhn 绑定了CPK文件的绑定器句柄
             * @param[in] path CPK文件内的内容路径（如果 bndrhn 为 nullptr，则为文件路径）
             */
			void setFile(CriFsBinderHn bndrhn, const std::string& path) CRICOCOS2D_NOEXCEPT;

            /**
            * @~chinese
            * @brief 设置包文件中的电影文件（指定文件范围）
            *
            * 指定您想要进行流媒体播放的电影文件的打包文件。\n
            * 通过在参数中指定偏移位置和数据范围，您可以读取包文件中任意电影数据作为源。\n
            * 可以指定的文件路径的最大字符串长度是 CRIMANA_MAX_FILE_NAME 字节。\n
            *
            * 如果调用了 #setFile，#setFileRange，#setData，#setContentId，
            * 则此成员函数所设定的文件信息将会失效。
            *
            * 如果使用同一 Player 反复播放同一电影文件，可以省略调用此成员函数。
            *
            * @pre
            * 必须处于 Status::Stop 状态。\n
            * 但有例外，可以在数据请求回调的内部进行调用。
            *
            * @param[in]    path        包含电影文件的包文件的路径
            * @param[in]    offset        从包文件头到电影的偏移量
            * @param[in]    datasize    电影数据的大小
            */
			void setFileRange(const std::string& path, CriUint64 offset, CriSint64 datasize) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置播放电影文件（指定CPK的Content ID）
             *
             * 配置用于流媒体播放的文件。
             *
             * 如果调用了 #setFile，#setFileRange，#setData，#setContentId，
             * 则此成员函数所设定的文件信息将会失效。
             *
             * 如果使用同一 Player 反复播放同一电影文件，可以省略调用此成员函数。
             *
             * @pre
             * 必须处于 Status::Stop 状态。\n
             * 但有例外，可以在数据请求回调的内部进行调用。
             *
             * @param[in]    bndrhn    与CPK绑定的Binder句柄
             * @param[in]    id        CPK文件中的内容ID
             */
			void setContentId(CriFsBinderHn bndrhn, CriSint32 id) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 重设相同的电影数据
             *
             * 指示再次播放上次播放过的数据。
             *
             * @attention
             * 请仅在数据请求回调函数内部使用此函数。
             *
             * @remarks
             * 如果只是简单地进行循环播放，也可以用 #loop 实现。
             *
             * @~
             * @sa #setDataRequestCallback
             */
			void setPreviousDataAgain() CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置解码操作模式
             *
             * @pre
             * 状态需要在 Status::WaitPrep 或更早。\n
             * 无法更改正在播放的电影的解码操作模式。
             *
             * @remarks
             * 可指定的模式可能受到平台和编解码器的限制。
             *
             * @param[in]    mode    解码操作模式
             */
			void setDecodeMode(CriManaDecodeMode mode) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置主音频轨道号
             *
             * 设置主音频轨道号。\n
             * 默认情况下，会播放编号最小的音频轨道。\n
             * 如果指定了不存在的轨道号，音频将不会播放。
             *
             * 如果指定了 CRIMANA_AUDIO_TRACK_OFF 作为轨道号，
             * 即使电影中包含音频，也不会播放音频。
             *
             * 此外，如果您想使用默认设置（播放最小轨道号的音频），
             * 请指定 CRIMANA_AUDIO_TRACK_AUTO。
             *
             * @pre
             * - 状态必须是 Status::Stop。
             *
             * @param[in]    track    音频轨道号
             */
			void setAudioTrack(CriSint32 track) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置副音频轨道号
             *
             * 设置副音频轨道号。\n
             * 如果您指定了与主音频相同的轨道作为副音频轨道，那么将不会从副音频中播放任何内容。\n
             * 如果指定了没有数据的轨道号，音频将不会播放。
             *
             * 如果把 CRIMANA_AUDIO_TRACK_OFF 指定为轨道号，
             * 那么启用的副音频功能将被禁用。
             *
             * @pre
             * - 状态必须是 Status::Stop。
             *
             * @param[in]    track    副音频轨道号
             */

			void setSubAudioTrack(CriSint32 track) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * \brief 设置主音频音量
             *
             * 设置主音频的音量。\n
             * 音量值应在 0.0f 到 1.0f 的范围内指定。\n
             * 如果设定了超出范围的值，它们会被限制在最小值或最大值。
             * 音量值是对音频数据振幅的倍数（单位不是分贝）。\n
             * 音量的默认值是 1.0f。
             *
             * @param[in]    volume    音量
             */
			void setAudioVolume(CriFloat32 volume) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * \brief 设置副音频音量
             *
             * 设置副音频的音量。\n
             * 音量值应在 0.0f 到 1.0f 的范围内指定。\n
             * 如果设定了超出范围的值，它们会被限制在最小值或最大值。
             * 音量值是对音频数据振幅的倍数（单位不是分贝）。\n
             * 音量的默认值是 1.0f。
             *
             * @param[in]    volume    音量
             */
			void setSubAudioVolume(CriFloat32 volume) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 绘制函数
             *
             * 这是绘制函数。\n
             * 由 Cocos2d 的绘制系统调用。
             *
             * @attention
             * 请不要直接从应用程序中调用。\n
             * 唯一的例外是，如果创建像 Sprite 这样的电影绘制类，
             * 请调用此成员函数进行绘制。
             *
             * @param[in]    triangles    顶点信息
             * @param[in]    mv            模型视图转换矩阵
             * @param[in]    additive    加法合成开关
             */
			void draw(
				const cocos2d::TrianglesCommand::Triangles& triangles,
				const cocos2d::Mat4&						mv,
				bool										additive
					 ) const;


		private:
			void update();
			void updateManaPlayer() CRICOCOS2D_NOEXCEPT;


		private:
			CriManaPlayerHn										   mana_player_;
			CriManaMovieInfo									   movie_info_;
			CriManaFrameInfo									   frame_info_;
			std::shared_ptr<void>								   renderer_resource_;
			CriManaPlayerStatus									   mana_status_;
			Status												   required_status_;

			std::function<void(SofdecPlayer&)>						   data_request_callback_func_;
			std::function<void(SofdecPlayer&, const CriManaEventPoint&)> cue_point_callback_func_;

			bool												   is_loop_;
			bool												   is_movie_info_available_;
			bool												   is_frame_info_available_;
			bool												   is_mana_start_invoked_;
		};
	}
}


#endif


/**
 * @}
 */
