/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/* Prevention of redefinition */
#ifndef CRICOCOS2D_MANA_DETAIL_UTIL_H
#define CRICOCOS2D_MANA_DETAIL_UTIL_H


#include "cricocos2d/cricocos2d.h"

#include <utility>


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			namespace gl
			{
				extern const GLchar* const generic_vertex_shader;
				extern const GLchar* const generic_vertex_shader_with_alpha;

				const GLuint			   invalid_texture = 0;

				struct GenericShaderLocation
				{
					GLint position;
					GLint texcoord;
					GLint color;
					GLint mvp_matrix;
					GLint movie_uv_transform;
					GLint movie_uv_transform_alpha;
				};

				GLuint createShaderProgram(const GLchar* vs_src, const GLchar* fs_src);

				void getShaderLocation(
					GLuint shader,
					std::initializer_list<std::pair<GLint&, const char*>> attributes,
					std::initializer_list<std::pair<GLint&, const char*>> uniforms
									  );


				void bindTextureAndSetUniform(GLuint tex_unit, GLuint tex_obj, GLint uniform_loc);

				void genericDrawMovieSprite(
					const cocos2d::TrianglesCommand::Triangles& triangles,
					const cocos2d::Mat4&						mv,
					const cocos2d::Mat4&						movie_uv_transform,
					const GenericShaderLocation&				location,
					bool										alpha,
					bool										additive
										   );

				void genericDrawMovieSpriteAlpha(
					const cocos2d::TrianglesCommand::Triangles& triangles,
					const cocos2d::Mat4&						mv,
					const cocos2d::Mat4&						movie_uv_transform,
					const cocos2d::Mat4&						movie_uv_transform_alpha,
					const GenericShaderLocation&				location,
					bool										alpha,
					bool										additive
										   );
			}


			inline
			CriUint32 roundUpPowerOfTwo(CriUint32 x) CRICOCOS2D_NOEXCEPT
			{
				x  = (x > 0) ? (x - 1) : 0;
				x |= x >> 1;
				x |= x >> 2;
				x |= x >> 4;
				x |= x >> 8;
				x |= x >> 16;
				return ++x;
			}


		}
	}
}


#endif
