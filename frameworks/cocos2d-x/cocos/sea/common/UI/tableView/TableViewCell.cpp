//
//  TableViewCell.cpp
//  cocos2d_libs
//
//  Created by chen<PERSON>g<PERSON><PERSON> on 2025/4/23.
//

#include "TableViewCell.h"
#include "sea/common/SeaCommon.h"
#include "TableViewInterface.h"

namespace sea {
int TableViewCell::s_keyIndex = 0;

TableViewCell::TableViewCell():
pData(nullptr),
pItemNode(nullptr),
pInterface(nullptr),
iIndex(-1)
{
    
}
TableViewCell::~TableViewCell() {
    CC_SAFE_RELEASE_NULL(pData);
    CC_SAFE_RELEASE_NULL(pItemNode);
}

TableViewCell* TableViewCell::create() {
    auto ret = new (std::nothrow) TableViewCell();

    if (ret && ret->init()) {
        ret->autorelease();
        ret->sUniqueKey = std::to_string(TableViewCell::s_keyIndex);
        ++TableViewCell::s_keyIndex;
    } else {
        CC_SAFE_DELETE(ret);
    }

    return ret;
}

TableViewCellData* TableViewCell::getCellData() {
    return pData;
}

void TableViewCell::setCellData(TableViewCellData* data) {
    CC_SAFE_RELEASE_NULL(pData);
    if (data) {
        pData = data;
        CC_SAFE_RETAIN(pData);
    }
}

void TableViewCell::updateItemNode() {
    if (iIndex < 0 || !pData || !pItemNode || !pInterface) {
        return;
    }
    
    auto cellSize = getSize();
    do {
        float x = 0, y = 0;
        if (pInterface->getDirection() == TABLE_VIEW_DIRECTION::VERTICAL) {
            x = pInterface->getViewItemStartX();
            y = cellSize.height * 0.5 + pData->getBottomHeight(iIndex);
        }
        else {
            y = pInterface->getViewItemStartY();
            x = cellSize.width * 0.5 + pData->getBottomHeight(iIndex);
        }
        pItemNode->setPosition(x, y);
        pInterface->updateItem(pItemNode, pData, iIndex);
    } while (0);
}

void TableViewCell::put() {
    if (pInterface && pItemNode) {
        pInterface->putItem(pItemNode);
    }
    setInterface(nullptr);
}

const std::string& TableViewCell::getKey() {
    return sUniqueKey;
}

void TableViewCell::setIndex(int index) {
    iIndex = index;
}

void TableViewCell::addItemNode(cocos2d::Node* node) {
    if (pItemNode) {
        pItemNode->removeFromParent();
        CC_SAFE_RELEASE_NULL(pItemNode);
    }
    
    if (node) {
        addChild(node);
        pItemNode = node;
        CC_SAFE_RETAIN(pItemNode);
    }
}

void TableViewCell::setInterface(TableViewInterface* interface) {
    CC_SAFE_RELEASE_NULL(pInterface);
    pInterface = interface;
    CC_SAFE_RETAIN(pInterface);
}

cocos2d::Size TableViewCell::getSize() {
    if (!pData) {
        return cocos2d::Size::ZERO;
    }
    
    auto size = pData->getSize(iIndex);
    if (size.isNull()) {
        if (pInterface) {
            return pInterface->getDefaultSize();
        }
    }
    else {
        return sea::getSizeFromCCValue(size);
    }
    
    return cocos2d::Size::ZERO;
}

void TableViewCell::onExit() {
    CC_SAFE_RELEASE_NULL(pData);
    CC_SAFE_RELEASE_NULL(pInterface);
    
    cocos2d::extension::TableViewCell::onExit();
}

}
