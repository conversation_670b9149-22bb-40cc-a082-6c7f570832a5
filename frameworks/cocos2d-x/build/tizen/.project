<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>libcocos2dx</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.tizen.nativecore.apichecker.apicheckerbuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.tizen.nativecore.apichecker.core.builder</name>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
		<nature>org.tizen.nativecore.apichecker.core.tizenCppNature</nature>
		<nature>org.tizen.nativecore.apichecker.apicheckernature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>cocos</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/cocos</locationURI>
		</link>
		<link>
			<name>extensions</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/extensions</locationURI>
		</link>
		<link>
			<name>external</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/external</locationURI>
		</link>
	</linkedResources>
	<filteredResources>
		<filter>
			<id>1491030614048</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-projectRelativePath-matches-false-false-*/.tpk</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614050</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-Android.mk</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614053</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-CMakeLists.txt</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614055</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*_props$</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614057</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-libcocos2d_8_1</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614059</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*.props$</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614061</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*vcxproj.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614063</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*\.def$</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614066</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*\.mm$</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614068</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*\.m$</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614070</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-proj.win32</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614073</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-proj.wp8</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614075</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-^proj.win.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614077</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-android</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614080</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-apple</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614082</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-desktop</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614094</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-ios</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614097</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-linux</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614100</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-mac</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614102</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*win8.1.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614104</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*wp8.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614107</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*winrt.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614110</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-scripting</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614112</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-lua</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614115</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*win32.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614117</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-true-.*wp_8.*</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614119</id>
			<name></name>
			<type>26</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-android-specific</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1491030614122</id>
			<name></name>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-idl_gen_grpc.cpp</arguments>
			</matcher>
		</filter>
		<filter>
			<id>1414416409298</id>
			<name>cocos/audio</name>
			<type>10</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-true-false-openal</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
