﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tga;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="2d">
      <UniqueIdentifier>{7a1f75b9-e286-444c-9469-0cf18caf9851}</UniqueIdentifier>
    </Filter>
    <Filter Include="3d">
      <UniqueIdentifier>{0fee395c-63c7-44c6-a4f4-1f36e2c87bb8}</UniqueIdentifier>
    </Filter>
    <Filter Include="base">
      <UniqueIdentifier>{50c0af16-c916-4fa8-9fa2-b86e38449e26}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosbuilder">
      <UniqueIdentifier>{5aeb6e4f-9e25-4b88-b583-fa11189743a0}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosdenshion">
      <UniqueIdentifier>{cfeb83ba-f330-49a3-a32f-4360729feb34}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio">
      <UniqueIdentifier>{48ef00ff-acc8-4c9c-a6e4-00d72e737ad2}</UniqueIdentifier>
    </Filter>
    <Filter Include="deprecated">
      <UniqueIdentifier>{f2236b85-0526-49e2-a18c-ac4951062666}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension">
      <UniqueIdentifier>{33f548c2-d835-4404-9e44-ad1511588c8e}</UniqueIdentifier>
    </Filter>
    <Filter Include="external">
      <UniqueIdentifier>{26495053-242d-4ec8-bf5a-6ea6f5ac4000}</UniqueIdentifier>
    </Filter>
    <Filter Include="math">
      <UniqueIdentifier>{94bbb957-b493-45d2-9f5a-753ac1ab78ce}</UniqueIdentifier>
    </Filter>
    <Filter Include="network">
      <UniqueIdentifier>{a2141e87-a561-433e-905b-63ff5c4e0e6e}</UniqueIdentifier>
    </Filter>
    <Filter Include="physics">
      <UniqueIdentifier>{661c905d-7ec2-4161-8b09-1c22ddefb830}</UniqueIdentifier>
    </Filter>
    <Filter Include="platform">
      <UniqueIdentifier>{6b13d475-62b0-4d73-8cee-52f077aa3fe7}</UniqueIdentifier>
    </Filter>
    <Filter Include="renderer">
      <UniqueIdentifier>{33af945f-9ffb-4e87-9467-7774d4cbaaee}</UniqueIdentifier>
    </Filter>
    <Filter Include="storage">
      <UniqueIdentifier>{abb46246-b4f9-4ad0-8db8-11b47ce43a1f}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui">
      <UniqueIdentifier>{e779fbed-6102-4689-81bd-57b2e70b0f26}</UniqueIdentifier>
    </Filter>
    <Filter Include="base\allocator">
      <UniqueIdentifier>{769e8f33-4eef-4d34-a192-6113354d584f}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosdenshion\include">
      <UniqueIdentifier>{2775a689-9303-4568-8bdd-85793c152dc0}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosdenshion\winrt">
      <UniqueIdentifier>{d58e753e-e7d1-49ea-970a-4e980b04cffa}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\action">
      <UniqueIdentifier>{9ec27523-fcde-48f7-8f77-34e2f2c51d3d}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature">
      <UniqueIdentifier>{e7a997f1-97ef-408f-9455-132da617f439}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\components">
      <UniqueIdentifier>{b7fa88eb-4dd8-4476-89b3-665a128ecd49}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader">
      <UniqueIdentifier>{1c30324c-b033-419a-b0bc-fcd04bf5e887}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\TimelineAction">
      <UniqueIdentifier>{5f24927a-be4e-43d4-bfa6-19a11c86ba74}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\trigger">
      <UniqueIdentifier>{b4dfd33e-325a-45e5-b5c4-bc759a765944}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\animation">
      <UniqueIdentifier>{1adc7897-87b2-4fa2-9de1-a662bbc0cef2}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\datas">
      <UniqueIdentifier>{8f07e318-d877-4880-834e-0e5a36759148}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\display">
      <UniqueIdentifier>{47260f4a-f9ab-4801-8a7f-5610755592f6}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\physics">
      <UniqueIdentifier>{69e44658-eabe-4434-bc55-09ba8fc18a65}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\utils">
      <UniqueIdentifier>{0a31f1b5-366b-4b0e-b998-6eee00a5e232}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader">
      <UniqueIdentifier>{377b23e6-4800-4305-b6de-7ffd6ab75445}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ArmatureNodeReader">
      <UniqueIdentifier>{3629c3cd-b691-403d-bec6-6915a63304bd}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ButtonReader">
      <UniqueIdentifier>{46c2c273-aa0e-4bc5-8eea-3aa512372d3a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\CheckBoxReader">
      <UniqueIdentifier>{09f711eb-3ba4-447e-85a0-b2b5ebb40328}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ComAudioReader">
      <UniqueIdentifier>{ed4442ff-0420-424c-bbfa-720b8ab658d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\GameMapReader">
      <UniqueIdentifier>{e6726a08-977a-4d22-b4b4-4e8c5aa75c80}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ImageViewReader">
      <UniqueIdentifier>{33cb6b93-333c-4f05-bc0b-e17ac95abdf9}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\LayoutReader">
      <UniqueIdentifier>{e74ad41e-c739-4e66-8b54-15914b651f1b}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ListViewReader">
      <UniqueIdentifier>{e6a2d595-06dc-436f-b5d7-57e8852f800e}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\LoadingBarReader">
      <UniqueIdentifier>{5b4d4e9c-90de-496a-9897-930e48c96052}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Node3DReader">
      <UniqueIdentifier>{f711caa7-1c16-43ec-8f2e-92438bfdd92d}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\NodeReader">
      <UniqueIdentifier>{9b4e1b59-dc28-49aa-9b93-05ae39afeef4}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\PageViewReader">
      <UniqueIdentifier>{566aad08-9d43-48ff-99a0-3f4a8e6cfa23}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Particle3DReader">
      <UniqueIdentifier>{ebca298d-3724-42b8-923f-934f2df267de}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ProjectNodeReader">
      <UniqueIdentifier>{cdcb5464-cce7-4252-9fa9-0113c6f88731}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ScrollViewReader">
      <UniqueIdentifier>{6ba31aca-4ffb-4c7f-86b3-68c686e9e83a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SliderReader">
      <UniqueIdentifier>{12508884-4921-4027-9e3f-0afcc4d51b18}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Sprite3DReader">
      <UniqueIdentifier>{d28da99a-5849-465a-a1ef-a39f13b8d71b}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SpriteReader">
      <UniqueIdentifier>{5c9216f7-37c5-45a7-abd5-0f91ab126e78}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextAtlasReader">
      <UniqueIdentifier>{efebb9af-0744-4d20-947b-a118679860ba}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextBMFontReader">
      <UniqueIdentifier>{c67477ac-753e-48da-9f7c-2044d8d3561c}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextFieldReader">
      <UniqueIdentifier>{b96929a6-1068-4e34-ba12-9b1506c52029}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextReader">
      <UniqueIdentifier>{2b9df357-22d4-4fd0-b797-a9bfb25a2f49}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\UserCameraReader">
      <UniqueIdentifier>{cd711175-67df-4a15-bc83-c13422334b46}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\AssetsManager">
      <UniqueIdentifier>{2520090a-69d8-46d3-a958-de49c068035c}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\GUI">
      <UniqueIdentifier>{21031d69-8a1d-468a-98d7-2c3080705ab3}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\Particle3D">
      <UniqueIdentifier>{b666aa35-1920-44d0-8311-86e80b7a010a}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\physics_nodes">
      <UniqueIdentifier>{21d16309-93ee-4b38-a10a-de87be25dcc9}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\GUI\CCControlExtensions">
      <UniqueIdentifier>{939431be-589b-461b-a4dd-791ad66e9611}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\GUI\CCScrollView">
      <UniqueIdentifier>{00ac48e4-fa60-4020-b924-18562a81d799}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\Particle3D\PU">
      <UniqueIdentifier>{593b7ac7-693d-4a46-ab32-e0203004e865}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\ConvertUTF">
      <UniqueIdentifier>{6651f0d8-3685-457c-9674-91ba9c2bde9b}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\edtaa">
      <UniqueIdentifier>{922ba080-bc7b-4493-b12a-2ccf5ccc3d68}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\tinyxml2">
      <UniqueIdentifier>{7bfc1d7e-a562-4ef8-82f0-af7c5f9ec004}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\unzip">
      <UniqueIdentifier>{fff4ac28-7649-4202-85ee-4fc22b4de31b}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\xxhash">
      <UniqueIdentifier>{d0b5ce80-dfff-44b8-8ca0-c01ec18c3983}</UniqueIdentifier>
    </Filter>
    <Filter Include="platform\winrt">
      <UniqueIdentifier>{8d2b7057-43b4-41e4-a752-0cfc20b1c825}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\BaseClasses">
      <UniqueIdentifier>{1715f2ff-18ed-405a-a0b7-1bb7fc7968de}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\Layouts">
      <UniqueIdentifier>{7a8dbff0-49af-4a9e-af07-7ecc643cf7d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\System">
      <UniqueIdentifier>{287c8ea7-54ab-4514-94ad-bc60fca8e2f6}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\UIWidgets">
      <UniqueIdentifier>{f8e329a2-40aa-428b-b111-3b788023c5e0}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\UIWidgets\EditBox">
      <UniqueIdentifier>{0a443764-b762-4858-b578-114bfc5d7ab5}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\UIWidgets\ScrollWidget">
      <UniqueIdentifier>{6fcf3801-c3e1-4097-ba82-3da0a6599555}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\poly2tri">
      <UniqueIdentifier>{acb13cf1-4b21-4392-826a-0ca62db8afe9}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\poly2tri\common">
      <UniqueIdentifier>{92cd288c-1327-4990-8ae2-dab11fa3a69c}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\poly2tri\sweep">
      <UniqueIdentifier>{bc47263f-0690-488e-b240-0b7bf763f401}</UniqueIdentifier>
    </Filter>
    <Filter Include="audioengine">
      <UniqueIdentifier>{5937896e-882c-429a-ada7-72c799c06920}</UniqueIdentifier>
    </Filter>
    <Filter Include="physics3d">
      <UniqueIdentifier>{f68e83b4-2fa3-4f47-9864-e7dfd90c16a6}</UniqueIdentifier>
    </Filter>
    <Filter Include="navmesh">
      <UniqueIdentifier>{cc4f6ca9-4231-479d-8e19-ede4dff3382e}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\clipper">
      <UniqueIdentifier>{e3a5fe25-a92e-4c98-9026-782b2b5b1388}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SkeletonReader">
      <UniqueIdentifier>{a10cc84c-5c61-43f4-a665-5e5cbab47ee1}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\TimelineAction\Skeleton">
      <UniqueIdentifier>{438e0063-bdbc-4252-a0f5-f55f7d54ed6f}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\GameNodeDReader">
      <UniqueIdentifier>{0760e708-a93e-41f5-abaf-a7e422882920}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Light3DReader">
      <UniqueIdentifier>{549d5ddb-02ff-4b34-8e49-8fb7570ecff4}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TabControlReader">
      <UniqueIdentifier>{55b041f0-d164-40f4-8dbd-a188ed9d9f6e}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Particle3DReader\ParticleReader">
      <UniqueIdentifier>{2e9af99e-c268-459a-9615-9f3c5b58efdc}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr">
      <UniqueIdentifier>{757d4df8-a9d6-430f-9a7a-80170d9ca6fb}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\md5">
      <UniqueIdentifier>{9ed292ed-61f9-4197-b99d-d6d6dbf63235}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\json">
      <UniqueIdentifier>{18477c41-d2ad-4050-b23e-06613d1265b4}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\flatbuffers">
      <UniqueIdentifier>{d5ca0311-8832-4f39-a84c-986edf7b7db0}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\json\error">
      <UniqueIdentifier>{96ca83bc-ffd4-4d74-890d-f7e89df4a44e}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\json\internal">
      <UniqueIdentifier>{68ac465b-56b4-4803-806f-00d23cb49142}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\json\msinttypes">
      <UniqueIdentifier>{079e35c5-f476-44f8-a3f0-04cf205f7260}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\cocos2d.cpp" />
    <ClCompile Include="..\..\platform\winrt\pch.cpp" />
    <ClCompile Include="..\CCAction.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionCamera.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionCatmullRom.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionEase.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionGrid3D.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionInstant.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionInterval.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionManager.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionPageTurn3D.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionProgressTimer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionTiledGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCActionTween.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCAnimation.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCAnimationCache.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCAtlasNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCCamera.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCClippingNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCClippingRectangleNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCComponent.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCComponentContainer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCDrawingPrimitives.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCDrawNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFastTMXLayer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFastTMXTiledMap.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFont.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFontAtlas.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFontAtlasCache.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFontCharMap.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFontFNT.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCFontFreeType.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCGLBufferedNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCGrabber.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLabel.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLabelAtlas.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLabelBMFont.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLabelTextFormatter.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLabelTTF.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLayer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCLight.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCMenu.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCMenuItem.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCMotionStreak.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCNodeGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCParallaxNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCParticleBatchNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCParticleExamples.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCParticleSystem.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCParticleSystemQuad.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCProgressTimer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCProtectedNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCRenderTexture.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCScene.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCSprite.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCSpriteBatchNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCSpriteFrame.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCSpriteFrameCache.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTextFieldTTF.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTileMapAtlas.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTMXLayer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTMXObjectGroup.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTMXTiledMap.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTMXXMLParser.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTransition.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTransitionPageTurn.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTransitionProgress.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\CCTweenFunction.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\atitc.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\base64.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCAsyncTaskPool.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCAutoreleasePool.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ccCArray.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCConfiguration.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCConsole.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCController.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCController-android.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCData.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCDataVisitor.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCDirector.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEvent.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventAcceleration.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventController.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventCustom.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventDispatcher.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventFocus.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventKeyboard.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListener.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerAcceleration.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerController.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerCustom.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerFocus.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerKeyboard.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerMouse.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventListenerTouch.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventMouse.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCEventTouch.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ccFPSImages.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCIMEDispatcher.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCNS.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCProfiling.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ccRandom.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCRef.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCScheduler.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCScriptSupport.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCTouch.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ccTypes.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCUserDefault-android.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ccUTF8.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ccUtils.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCValue.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\etc1.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ObjectFactory.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\pvr.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\s3tc.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\TGAlib.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\ZipUtils.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\allocator\CCAllocatorDiagnostics.cpp">
      <Filter>base\allocator</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\allocator\CCAllocatorGlobal.cpp">
      <Filter>base\allocator</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\allocator\CCAllocatorGlobalNewDelete.cpp">
      <Filter>base\allocator</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCBAnimationManager.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCBFileLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCBKeyframe.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCBReader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCBSequence.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCBSequenceProperty.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCControlButtonLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCControlLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCLabelBMFontLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCLabelTTFLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCLayerColorLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCLayerGradientLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCLayerLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCMenuItemImageLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCMenuItemLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCNode+CCBRelativePositioning.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCNodeLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCNodeLoaderLibrary.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCParticleSystemQuadLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCScale9SpriteLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCScrollViewLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocosbuilder\CCSpriteLoader.cpp">
      <Filter>cocosbuilder</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\winrt\Audio.cpp">
      <Filter>cocosdenshion\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\winrt\MediaStreamer.cpp">
      <Filter>cocosdenshion\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\winrt\SimpleAudioEngine.cpp">
      <Filter>cocosdenshion\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCActionFrame.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCActionFrameEasing.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCActionManagerEx.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCActionNode.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCActionObject.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCArmature.cpp">
      <Filter>cocostudio\armature</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCBone.cpp">
      <Filter>cocostudio\armature</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCArmatureAnimation.cpp">
      <Filter>cocostudio\armature\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCProcessBase.cpp">
      <Filter>cocostudio\armature\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCTween.cpp">
      <Filter>cocostudio\armature\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCDatas.cpp">
      <Filter>cocostudio\armature\datas</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCBatchNode.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCDecorativeDisplay.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCDisplayFactory.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCDisplayManager.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCSkin.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCColliderDetector.cpp">
      <Filter>cocostudio\armature\physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCArmatureDataManager.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCArmatureDefine.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCDataReaderHelper.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCSpriteFrameCacheHelper.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCTransformHelp.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCUtilMath.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCComAttribute.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCComAudio.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCComController.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCComRender.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCInputDelegate.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\TriggerBase.cpp">
      <Filter>cocostudio\trigger</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\TriggerMng.cpp">
      <Filter>cocostudio\trigger</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\TriggerObj.cpp">
      <Filter>cocostudio\trigger</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCActionTimeline.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCActionTimelineCache.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCActionTimelineNode.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCFrame.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCTimeLine.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CSLoader.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\NodeReaderDefine.cpp">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\NodeReaderProtocol.cpp">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\WidgetReader.cpp">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ArmatureNodeReader\ArmatureNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ArmatureNodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ButtonReader\ButtonReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ButtonReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\CheckBoxReader\CheckBoxReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\CheckBoxReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ComAudioReader\ComAudioReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ComAudioReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\GameMapReader\GameMapReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\GameMapReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ImageViewReader\ImageViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ImageViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\LayoutReader\LayoutReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\LayoutReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ListViewReader\ListViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ListViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\LoadingBarReader\LoadingBarReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\LoadingBarReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\Node3DReader\Node3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Node3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\NodeReader\NodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\NodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\PageViewReader\PageViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\PageViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\Particle3DReader\Particle3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Particle3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ParticleReader\ParticleReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Particle3DReader\ParticleReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ProjectNodeReader\ProjectNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ProjectNodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\ScrollViewReader\ScrollViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ScrollViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\SingleNodeReader\SingleNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SliderReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\SliderReader\SliderReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SliderReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\Sprite3DReader\Sprite3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Sprite3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\SpriteReader\SpriteReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SpriteReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\TextAtlasReader\TextAtlasReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextAtlasReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\TextBMFontReader\TextBMFontReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextBMFontReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\TextFieldReader\TextFieldReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextFieldReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\TextReader\TextReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\UserCameraReader\UserCameraReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\UserCameraReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\deprecated\CCArray.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\..\deprecated\CCDeprecated.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\..\deprecated\CCDictionary.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\..\deprecated\CCNotificationCenter.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\..\deprecated\CCSet.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\..\deprecated\CCString.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControl.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlButton.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlColourPicker.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlHuePicker.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlPotentiometer.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlSaturationBrightnessPicker.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlSlider.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlStepper.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlSwitch.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCControlUtils.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCControlExtension\CCInvocation.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCScrollView\CCScrollView.cpp">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCScrollView\CCTableView.cpp">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\GUI\CCScrollView\CCTableViewCell.cpp">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\CCParticle3DAffector.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\CCParticle3DEmitter.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\CCParticle3DRender.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\CCParticleSystem3D.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUAffectorManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUAlignAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUAlignAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBaseCollider.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBaseColliderTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBaseForceAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBaseForceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBeamRender.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBehaviour.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBehaviourManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBehaviourTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBillboardChain.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBoxCollider.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBoxColliderTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBoxEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUBoxEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUCircleEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUCircleEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUColorAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUColorAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDynamicAttribute.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUDynamicAttributeTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUEmitterManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUEventHandler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUEventHandlerManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUForceField.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUForceFieldAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUForceFieldAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUGeometryRotator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUGeometryRotatorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUGravityAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUGravityAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUInterParticleCollider.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUInterParticleColliderTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUJetAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUJetAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPULineAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPULineAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPULinearForceAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPULinearForceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPULineEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPULineEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUListener.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUMaterialManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUMaterialTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUNoise.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUObserverManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnClearObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnClearObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnCollisionObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnCollisionObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnCountObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnCountObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnEmissionObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnEmissionObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnExpireObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnExpireObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnPositionObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnPositionObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnQuotaObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnQuotaObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnRandomObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnRandomObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnTimeObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnTimeObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnVelocityObserver.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUOnVelocityObserverTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUParticleFollower.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUParticleFollowerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUParticleSystem3D.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUParticleSystem3DTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPathFollower.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPathFollowerTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPlane.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPlaneCollider.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPlaneColliderTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPointEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPointEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPositionEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUPositionEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPURandomiser.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPURandomiserTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPURender.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPURendererTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPURibbonTrail.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPURibbonTrailRender.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScaleAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScaleAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScriptCompiler.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScriptLexer.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScriptParser.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUScriptTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSimpleSpline.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSineForceAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSineForceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveBehaviour.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveBehaviourTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSphere.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSphereCollider.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSphereColliderTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitterTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUTechniqueTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUTextureAnimator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUTextureAnimatorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUTextureRotator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUTextureRotatorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUTranslateManager.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUUtil.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUVertexEmitter.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUVortexAffector.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\Particle3D\PU\CCPUVortexAffectorTranslator.cpp">
      <Filter>extension\Particle3D\PU</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\physics-nodes\CCPhysicsDebugNode.cpp">
      <Filter>extension\physics_nodes</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\physics-nodes\CCPhysicsSprite.cpp">
      <Filter>extension\physics_nodes</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\ExtensionDeprecated.cpp">
      <Filter>extension</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\ConvertUTF\ConvertUTF.c">
      <Filter>external\ConvertUTF</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\ConvertUTF\ConvertUTFWrapper.cpp">
      <Filter>external\ConvertUTF</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\edtaa3func\edtaa3func.cpp">
      <Filter>external\edtaa</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\xxhash\xxhash.c">
      <Filter>external\xxhash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\unzip\ioapi.cpp">
      <Filter>external\unzip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\unzip\ioapi_mem.cpp">
      <Filter>external\unzip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\unzip\unzip.cpp">
      <Filter>external\unzip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\tinyxml2\tinyxml2.cpp">
      <Filter>external\tinyxml2</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\CCAffineTransform.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\CCGeometry.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\CCVertex.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\Mat4.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\MathUtil.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\Quaternion.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\TransformUtils.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\Vec2.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\Vec3.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\math\Vec4.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\HttpCookie.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\SocketIO.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\Uri.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\WebSocket.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\CCFileUtils.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\CCGLView.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\CCImage.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\CCSAXParser.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\CCThread.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCApplication.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCCommon.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCDevice.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCFileUtilsWinRT.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCFreeTypeFont.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCGLViewImpl-winrt.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCPrecompiledShaders.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCPThreadWinRT.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCStdC.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\CCWinRTUtils.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\InputEvent.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\Keyboard-winrt.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\sha1.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCBatchCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCCustomCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCGLProgram.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCGLProgramCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCGLProgramState.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCGLProgramStateCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\ccGLStateCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCGroupCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCMeshCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCPrimitive.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCPrimitiveCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCQuadCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCRenderCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCRenderer.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\ccShaders.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCTexture2D.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCTextureAtlas.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCTextureCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCTrianglesCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCVertexIndexBuffer.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCVertexIndexData.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\storage\local-storage\LocalStorage.cpp">
      <Filter>storage</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIScale9Sprite.cpp">
      <Filter>ui\BaseClasses</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIWidget.cpp">
      <Filter>ui\BaseClasses</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIHBox.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UILayout.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UILayoutComponent.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UILayoutManager.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UILayoutParameter.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIRelativeBox.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIVBox.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\CocosGUI.cpp">
      <Filter>ui\System</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIDeprecated.cpp">
      <Filter>ui\System</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIHelper.cpp">
      <Filter>ui\System</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIEditBox\UIEditBox.cpp">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIEditBox\UIEditBoxImpl-winrt.cpp">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIListView.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIPageView.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIPageViewIndicator.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIScrollView.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIScrollViewBar.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIButton.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIAbstractCheckButton.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UICheckBox.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIRadioButton.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIImageView.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UILoadingBar.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIRichText.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UISlider.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIText.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UITextAtlas.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UITextBMFont.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UITextField.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCSGUIReader.cpp">
      <Filter>cocostudio\reader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCSSceneReader.cpp">
      <Filter>cocostudio\reader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\platform\winrt\WICImageLoader-winrt.cpp">
      <Filter>platform\winrt</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\poly2tri\common\shapes.cc">
      <Filter>external\poly2tri\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\poly2tri\sweep\advancing_front.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\poly2tri\sweep\cdt.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\poly2tri\sweep\sweep.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\poly2tri\sweep\sweep_context.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\HttpClient-winrt.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\HttpConnection-winrt.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCNinePatchImageParser.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCStencilStateManager.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCMaterial.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCPass.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCRenderState.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCTechnique.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCVertexAttribBinding.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCUserDefault-winrt.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\CCProperties.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\AudioEngine.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\winrt\AudioCachePlayer.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\winrt\AudioEngine-winrt.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\audio\winrt\AudioSourceReader.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCFrameBuffer.cpp" />
    <ClCompile Include="..\..\navmesh\CCNavMesh.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\..\navmesh\CCNavMeshAgent.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\..\navmesh\CCNavMeshDebugDraw.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\..\navmesh\CCNavMeshObstacle.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\..\navmesh\CCNavMeshUtils.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\CCAutoPolygon.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\clipper\clipper.cpp">
      <Filter>external\clipper</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CCComExtensionData.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\SkeletonReader\BoneNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\SkeletonReader\SkeletonNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCBoneNode.cpp">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCSkeletonNode.cpp">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\ActionTimeline\CCSkinNode.cpp">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\GameNode3DReader\GameNode3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\GameNodeDReader</Filter>
    </ClCompile>
    <ClCompile Include="..\CCCameraBackgroundBrush.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\renderer\CCTextureCube.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics\CCPhysicsBody.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics\CCPhysicsContact.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics\CCPhysicsJoint.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics\CCPhysicsShape.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics\CCPhysicsWorld.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3D.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3DComponent.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3DConstraint.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3DDebugDrawer.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3DObject.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3DShape.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysics3DWorld.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\physics3d\CCPhysicsSprite3D.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCAABB.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCAnimate3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCAnimation3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCAttachNode.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCBillBoard.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCBundle3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCBundleReader.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCFrustum.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCMesh.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCMeshSkin.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCMeshVertexIndexData.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCMotionStreak3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCOBB.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCObjLoader.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCPlane.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCRay.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCSkeleton3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCSkybox.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCSprite3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCSprite3DMaterial.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\3d\CCTerrain.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\assets-manager\AssetsManager.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\assets-manager\AssetsManagerEx.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\assets-manager\CCEventAssetsManagerEx.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\assets-manager\CCEventListenerAssetsManagerEx.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\extensions\assets-manager\Manifest.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\CCDownloader.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\network\CCDownloader-curl.cpp">
      <Filter>network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\Light3DReader\Light3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Light3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\LocalizationManager.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetReader\TabControlReader\TabControlReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TabControlReader</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UITabControl.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ui\UIEditBox\UIEditBoxImpl-common.cpp">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClCompile>
    <ClCompile Include="..\..\vr\CCVRDistortion.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\..\vr\CCVRDistortionMesh.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\..\vr\CCVRGenericHeadTracker.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\..\vr\CCVRGenericRenderer.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\md5\md5.c">
      <Filter>external\md5</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CocoLoader.cpp">
      <Filter>cocostudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\CocoStudio.cpp">
      <Filter>cocostudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\DictionaryHelper.cpp">
      <Filter>cocostudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\FlatBuffersSerialize.cpp">
      <Filter>cocostudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\editor-support\cocostudio\WidgetCallBackHandlerProtocol.cpp">
      <Filter>cocostudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_cpp.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_fbs.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_general.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_go.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_js.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_php.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_python.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_gen_text.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\idl_parser.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\reflection.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\external\flatbuffers\util.cpp">
      <Filter>external\flatbuffers</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\cocos2d.h" />
    <ClInclude Include="..\..\platform\winrt\pch.h" />
    <ClInclude Include="..\CCAction.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionCamera.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionCatmullRom.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionEase.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionGrid3D.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionInstant.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionInterval.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionManager.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionPageTurn3D.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionProgressTimer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionTiledGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCActionTween.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCAnimation.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCAnimationCache.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCAtlasNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCCamera.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCClippingNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCClippingRectangleNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCComponent.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCComponentContainer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCDrawingPrimitives.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCDrawNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFastTMXLayer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFastTMXTiledMap.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFont.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFontAtlas.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFontAtlasCache.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFontCharMap.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFontFNT.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCFontFreeType.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCGLBufferedNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCGrabber.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLabel.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLabelAtlas.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLabelBMFont.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLabelTextFormatter.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLabelTTF.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLayer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCLight.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCMenu.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCMenuItem.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCMotionStreak.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCNodeGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCParallaxNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCParticleBatchNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCParticleExamples.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCParticleSystem.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCParticleSystemQuad.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCProgressTimer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCProtectedNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCRenderTexture.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCScene.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCSprite.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCSpriteBatchNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCSpriteFrame.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCSpriteFrameCache.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTextFieldTTF.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTileMapAtlas.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTMXLayer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTMXObjectGroup.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTMXTiledMap.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTMXXMLParser.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTransition.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTransitionPageTurn.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTransitionProgress.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\CCTweenFunction.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\atitc.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\base64.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCAsyncTaskPool.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCAutoreleasePool.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccCArray.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccConfig.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCConfiguration.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCConsole.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCController.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCData.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCDataVisitor.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCDirector.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEvent.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventAcceleration.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventController.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventCustom.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventDispatcher.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventFocus.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventKeyboard.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListener.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerAcceleration.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerController.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerCustom.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerFocus.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerKeyboard.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerMouse.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventListenerTouch.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventMouse.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventTouch.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCEventType.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccFPSImages.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCGameController.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCIMEDelegate.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCIMEDispatcher.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccMacros.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCMap.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCNS.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCProfiling.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCProtocols.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccRandom.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCRef.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCRefPtr.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCScheduler.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCScriptSupport.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCTouch.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccTypes.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCUserDefault.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccUTF8.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ccUtils.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCValue.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCVector.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\etc1.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\firePngData.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ObjectFactory.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\pvr.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\s3tc.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\TGAlib.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\uthash.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\utlist.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\ZipUtils.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorBase.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorDiagnostics.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorGlobal.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorMacros.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorMutex.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorStrategyDefault.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorStrategyFixedBlock.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorStrategyGlobalSmallBlock.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\allocator\CCAllocatorStrategyPool.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBAnimationManager.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBFileLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBKeyframe.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBMemberVariableAssigner.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBReader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBSelectorResolver.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBSequence.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCBSequenceProperty.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCControlButtonLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCControlLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCLabelBMFontLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCLabelTTFLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCLayerColorLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCLayerGradientLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCLayerLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCMenuItemImageLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCMenuItemLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCMenuLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCNode+CCBRelativePositioning.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCNodeLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCNodeLoaderLibrary.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCNodeLoaderListener.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCParticleSystemQuadLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCScale9SpriteLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCScrollViewLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CCSpriteLoader.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocosbuilder\CocosBuilder.h">
      <Filter>cocosbuilder</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\include\AudioEngine.h">
      <Filter>cocosdenshion\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\include\Export.h">
      <Filter>cocosdenshion\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\include\SimpleAudioEngine.h">
      <Filter>cocosdenshion\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\winrt\Audio.h">
      <Filter>cocosdenshion\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\winrt\MediaStreamer.h">
      <Filter>cocosdenshion\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CocosStudioExport.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCActionFrame.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCActionFrameEasing.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCActionManagerEx.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCActionNode.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCActionObject.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCArmature.h">
      <Filter>cocostudio\armature</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCBone.h">
      <Filter>cocostudio\armature</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCArmatureAnimation.h">
      <Filter>cocostudio\armature\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCProcessBase.h">
      <Filter>cocostudio\armature\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCTween.h">
      <Filter>cocostudio\armature\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCDatas.h">
      <Filter>cocostudio\armature\datas</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCBatchNode.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCDecorativeDisplay.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCDisplayFactory.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCDisplayManager.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCSkin.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCColliderDetector.h">
      <Filter>cocostudio\armature\physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCArmatureDataManager.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCArmatureDefine.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCDataReaderHelper.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCSpriteFrameCacheHelper.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCTransformHelp.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCUtilMath.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCComAttribute.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCComAudio.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCComBase.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCComController.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCComRender.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCInputDelegate.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\TriggerBase.h">
      <Filter>cocostudio\trigger</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\TriggerMng.h">
      <Filter>cocostudio\trigger</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\TriggerObj.h">
      <Filter>cocostudio\trigger</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCActionTimeline.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCActionTimelineCache.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCActionTimelineNode.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCFrame.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCTimeLine.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCTimelineMacro.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CSLoader.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\NodeReaderDefine.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\NodeReaderProtocol.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\WidgetReader.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\WidgetReaderProtocol.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ArmatureNodeReader\ArmatureNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\ArmatureNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ArmatureNodeReader\CSArmatureNode_generated.h">
      <Filter>cocostudio\reader\WidgetReader\ArmatureNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ButtonReader\ButtonReader.h">
      <Filter>cocostudio\reader\WidgetReader\ButtonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\CheckBoxReader\CheckBoxReader.h">
      <Filter>cocostudio\reader\WidgetReader\CheckBoxReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ComAudioReader\ComAudioReader.h">
      <Filter>cocostudio\reader\WidgetReader\ComAudioReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\GameMapReader\GameMapReader.h">
      <Filter>cocostudio\reader\WidgetReader\GameMapReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ImageViewReader\ImageViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\ImageViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\LayoutReader\LayoutReader.h">
      <Filter>cocostudio\reader\WidgetReader\LayoutReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ListViewReader\ListViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\ListViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\LoadingBarReader\LoadingBarReader.h">
      <Filter>cocostudio\reader\WidgetReader\LoadingBarReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\Node3DReader\Node3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Node3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\NodeReader\NodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\NodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\PageViewReader\PageViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\PageViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\Particle3DReader\Particle3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Particle3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ParticleReader\ParticleReader.h">
      <Filter>cocostudio\reader\WidgetReader\Particle3DReader\ParticleReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ProjectNodeReader\ProjectNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\ProjectNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\ScrollViewReader\ScrollViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\ScrollViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\SingleNodeReader\SingleNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\SliderReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\SliderReader\SliderReader.h">
      <Filter>cocostudio\reader\WidgetReader\SliderReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\Sprite3DReader\Sprite3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Sprite3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\SpriteReader\SpriteReader.h">
      <Filter>cocostudio\reader\WidgetReader\SpriteReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\TextAtlasReader\TextAtlasReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextAtlasReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\TextBMFontReader\TextBMFontReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextBMFontReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\TextFieldReader\TextFieldReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextFieldReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\TextReader\TextReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\UserCameraReader\UserCameraReader.h">
      <Filter>cocostudio\reader\WidgetReader\UserCameraReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCArray.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCBool.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCDeprecated.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCDictionary.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCDouble.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCFloat.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCInteger.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCNotificationCenter.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCSet.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\deprecated\CCString.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControl.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlButton.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlColourPicker.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlExtensions.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlHuePicker.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlPotentiometer.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlSaturationBrightnessPicker.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlSlider.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlStepper.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlSwitch.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCControlUtils.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCControlExtension\CCInvocation.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCScrollView\CCScrollView.h">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCScrollView\CCTableView.h">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\GUI\CCScrollView\CCTableViewCell.h">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\CCParticle3DAffector.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\CCParticle3DEmitter.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\CCParticle3DRender.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\CCParticleSystem3D.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUAffectorManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUAlignAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUAlignAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBaseCollider.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBaseColliderTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBaseForceAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBaseForceAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBeamRender.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBehaviour.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBehaviourManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBehaviourTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBillboardChain.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBoxCollider.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBoxColliderTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBoxEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUBoxEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUCircleEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUCircleEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUColorAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUColorAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDynamicAttribute.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUDynamicAttributeTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUEmitterManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUEventHandler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUEventHandlerManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUEventHandlerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUForceField.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUForceFieldAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUForceFieldAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUGeometryRotator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUGeometryRotatorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUGravityAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUGravityAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUInterParticleCollider.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUInterParticleColliderTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUJetAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUJetAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPULineAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPULineAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPULinearForceAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPULinearForceAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPULineEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPULineEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUListener.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUMaterialManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUMaterialTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUNoise.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUObserverManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnClearObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnClearObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnCollisionObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnCollisionObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnCountObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnCountObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnEmissionObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnEmissionObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnExpireObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnExpireObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnPositionObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnPositionObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnQuotaObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnQuotaObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnRandomObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnRandomObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnTimeObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnTimeObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnVelocityObserver.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUOnVelocityObserverTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUParticleFollower.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUParticleFollowerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUParticleSystem3D.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUParticleSystem3DTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPathFollower.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPathFollowerTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPlane.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPlaneCollider.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPlaneColliderTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPointEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPointEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPositionEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUPositionEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPURandomiser.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPURandomiserTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPURender.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPURendererTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPURibbonTrail.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPURibbonTrailRender.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScaleAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScaleAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScriptCompiler.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScriptLexer.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScriptParser.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUScriptTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSimpleSpline.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSineForceAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSineForceAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveBehaviour.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveBehaviourTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSlaveEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSphere.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSphereCollider.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSphereColliderTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitterTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUTechniqueTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUTextureAnimator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUTextureAnimatorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUTextureRotator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUTextureRotatorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUTranslateManager.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUUtil.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUVertexEmitter.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUVortexAffector.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\Particle3D\PU\CCPUVortexAffectorTranslator.h">
      <Filter>extension\Particle3D\PU</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\physics-nodes\CCPhysicsDebugNode.h">
      <Filter>extension\physics_nodes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\physics-nodes\CCPhysicsSprite.h">
      <Filter>extension\physics_nodes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\cocos-ext.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\ExtensionDeprecated.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\ExtensionExport.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\ExtensionMacros.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\ConvertUTF\ConvertUTF.h">
      <Filter>external\ConvertUTF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\edtaa3func\edtaa3func.h">
      <Filter>external\edtaa</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\xxhash\xxhash.h">
      <Filter>external\xxhash</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\unzip\crypt.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\unzip\ioapi.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\unzip\ioapi_mem.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\unzip\unzip.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\tinyxml2\tinyxml2.h">
      <Filter>external\tinyxml2</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\CCAffineTransform.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\CCGeometry.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\CCMath.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\CCMathBase.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\CCVertex.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\Mat4.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\MathUtil.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\Quaternion.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\TransformUtils.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\Vec2.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\Vec3.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\math\Vec4.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\HttpClient.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\HttpCookie.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\HttpRequest.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\HttpResponse.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\SocketIO.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\Uri.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\WebSocket.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCApplication.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCApplicationProtocol.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCCommon.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCDevice.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCFileUtils.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCGL.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCGLView.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCImage.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCPlatformConfig.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCPlatformDefine.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCPlatformMacros.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCSAXParser.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCStdC.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\CCThread.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCApplication.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCFileUtilsWinRT.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCFreeTypeFont.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCGL.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCGLViewImpl-winrt.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCPlatformDefine-winrt.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCPrecompiledShaders.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCPThreadWinRT.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCStdC.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\CCWinRTUtils.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\inet_ntop_winrt.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\InputEvent.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\InputEventTypes.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\Keyboard-winrt.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\sha1.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCBatchCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCCustomCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCGLProgram.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCGLProgramCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCGLProgramState.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCGLProgramStateCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\ccGLStateCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCGroupCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCMeshCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCPrimitive.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCPrimitiveCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCQuadCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCRenderCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCRenderCommandPool.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCRenderer.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\ccShaders.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCTexture2D.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCTextureAtlas.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCTextureCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCTrianglesCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCVertexIndexBuffer.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCVertexIndexData.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\storage\local-storage\LocalStorage.h">
      <Filter>storage</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIScale9Sprite.h">
      <Filter>ui\BaseClasses</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIWidget.h">
      <Filter>ui\BaseClasses</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIHBox.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UILayout.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UILayoutComponent.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UILayoutManager.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UILayoutParameter.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIRelativeBox.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIVBox.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\CocosGUI.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\GUIExport.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIDeprecated.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIHelper.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIEditBox\UIEditBox.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIEditBox\UIEditBoxImpl.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIEditBox\UIEditBoxImpl-winrt.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIListView.h">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIPageView.h">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIScrollView.h">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIButton.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UICheckBox.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIImageView.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UILoadingBar.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIRichText.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UISlider.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIText.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UITextAtlas.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UITextBMFont.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UITextField.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCSGUIReader.h">
      <Filter>cocostudio\reader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCSSceneReader.h">
      <Filter>cocostudio\reader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\platform\winrt\WICImageLoader-winrt.h">
      <Filter>platform\winrt</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\poly2tri.h">
      <Filter>external\poly2tri</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\common\shapes.h">
      <Filter>external\poly2tri\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\common\utils.h">
      <Filter>external\poly2tri\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\sweep\advancing_front.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\sweep\cdt.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\sweep\sweep.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\poly2tri\sweep\sweep_context.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\HttpConnection-winrt.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCNinePatchImageParser.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCStencilStateManager.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCMaterial.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCPass.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCRenderState.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCTechnique.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCVertexAttribBinding.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCFrameBuffer.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\CCProperties.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\winrt\AudioCachePlayer.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\winrt\AudioEngine-winrt.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\audio\winrt\AudioSourceReader.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\navmesh\CCNavMesh.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\..\navmesh\CCNavMeshAgent.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\..\navmesh\CCNavMeshDebugDraw.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\..\navmesh\CCNavMeshObstacle.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\..\navmesh\CCNavMeshUtils.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\CCAutoPolygon.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\clipper\clipper.hpp">
      <Filter>external\clipper</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CCComExtensionData.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\SkeletonReader\BoneNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\SkeletonReader\CSBoneBinary_generated.h">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\SkeletonReader\SkeletonNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCBoneNode.h">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCSkeletonNode.h">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\ActionTimeline\CCSkinNode.h">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\GameNode3DReader\GameNode3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\GameNodeDReader</Filter>
    </ClInclude>
    <ClInclude Include="..\CCCameraBackgroundBrush.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\renderer\CCTextureCube.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics\CCPhysicsBody.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics\CCPhysicsContact.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics\CCPhysicsHelper.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics\CCPhysicsJoint.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics\CCPhysicsShape.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics\CCPhysicsWorld.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3D.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3DComponent.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3DConstraint.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3DDebugDrawer.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3DObject.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3DShape.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysics3DWorld.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\physics3d\CCPhysicsSprite3D.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCAABB.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCAnimate3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCAnimation3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCAnimationCurve.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCAttachNode.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCBillBoard.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCBundle3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCBundle3DData.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCBundleReader.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCFrustum.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCMesh.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCMeshSkin.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCMeshVertexIndexData.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCMotionStreak3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCOBB.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCObjLoader.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCPlane.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCRay.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCSkeleton3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCSkybox.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCSprite3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCSprite3DMaterial.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\CCTerrain.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\3d\cocos3d.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\assets-manager\AssetsManager.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\assets-manager\AssetsManagerEx.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\assets-manager\CCEventAssetsManagerEx.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\assets-manager\CCEventListenerAssetsManagerEx.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\extensions\assets-manager\Manifest.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\CCDownloader.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\CCDownloader-curl.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\network\CCIDownloaderImpl.h">
      <Filter>network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\Light3DReader\Light3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Light3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\LocalizationManager.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\TabControlReader\CSTabControl_generated.h">
      <Filter>cocostudio\reader\WidgetReader\TabControlReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetReader\TabControlReader\TabControlReader.h">
      <Filter>cocostudio\reader\WidgetReader\TabControlReader</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UITabControl.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ui\UIEditBox\UIEditBoxImpl-common.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\..\vr\CCVRDistortion.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\..\vr\CCVRDistortionMesh.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\..\vr\CCVRGenericHeadTracker.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\..\vr\CCVRGenericRenderer.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\md5\md5.h">
      <Filter>external\md5</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CocoLoader.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CocoStudio.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CSParse3DBinary_generated.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\CSParseBinary_generated.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\DictionaryHelper.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\FlatBuffersSerialize.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\editor-support\cocostudio\WidgetCallBackHandlerProtocol.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\code_generators.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\flatbuffers.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\grpc.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\hash.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\idl.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\reflection.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\reflection_generated.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\flatbuffers\util.h">
      <Filter>external\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\document.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\document-wrapper.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\encodedstream.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\encodings.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\filereadstream.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\filewritestream.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\fwd.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\istreamwrapper.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\memorybuffer.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\memorystream.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\ostreamwrapper.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\pointer.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\prettywriter.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\rapidjson.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\reader.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\schema.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\stream.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\stringbuffer.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\writer.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\allocators.h">
      <Filter>external\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\error\en.h">
      <Filter>external\json\error</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\error\error.h">
      <Filter>external\json\error</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\biginteger.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\diyfp.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\dtoa.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\ieee754.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\itoa.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\meta.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\pow10.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\regex.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\stack.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\strfunc.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\strtod.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\internal\swap.h">
      <Filter>external\json\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\msinttypes\inttypes.h">
      <Filter>external\json\msinttypes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\external\json\msinttypes\stdint.h">
      <Filter>external\json\msinttypes</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\base\CCController-iOS.mm">
      <Filter>base</Filter>
    </None>
    <None Include="..\..\base\CCUserDefault-apple.mm">
      <Filter>base</Filter>
    </None>
    <None Include="..\..\math\Mat4.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\MathUtil.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\MathUtilNeon.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\MathUtilNeon64.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\MathUtilSSE.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\Quaternion.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\Vec2.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\Vec3.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\math\Vec4.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Color.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_ColorNormal.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_ColorNormalTex.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_ColorTex.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Particle.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Particle.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_PositionNormalTex.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_PositionTex.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Skybox.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Skybox.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Terrain.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_3D_Terrain.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Label.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Label_df.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Label_df_glow.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Label_normal.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Label_outline.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Position_uColor.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Position_uColor.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionColor.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionColor.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionColorLengthTexture.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionColorLengthTexture.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionColorTextureAsPointsize.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTexture.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTexture.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTexture_uColor.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTexture_uColor.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureA8Color.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureA8Color.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureColor.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureColor.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureColor_noMVP.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureColor_noMVP.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionTextureColorAlphaTest.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\cocos2d.def" />
    <None Include="..\..\renderer\ccShader_CameraClear.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_CameraClear.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_Position_uColor_wp81.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_PositionColorTextureAsPointsize_wp81.vert">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\renderer\ccShader_UI_Gray.frag">
      <Filter>renderer</Filter>
    </None>
    <None Include="..\..\3d\CCAnimationCurve.inl">
      <Filter>3d</Filter>
    </None>
  </ItemGroup>
</Project>