﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="APP_DLLS">
    <AngleBinPath Condition=" '$(AngleBinPath)' == '' ">$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\angle\prebuilt\$(Platform)\</AngleBinPath>
    <CurlBinPath Condition=" '$(CurlBinPath)' == '' ">$(EngineRoot)external\curl\prebuilt\$(COCOS2D_PLATFORM)\$(Platform)\</CurlBinPath>
    <OpenSSLBinPath Condition=" '$(OpenSSLBinPath)' == '' ">$(EngineRoot)external\openssl\prebuilt\$(COCOS2D_PLATFORM)\$(Platform)\</OpenSSLBinPath>
    <ZLibBinPath Condition=" '$(ZLibBinPath)' == '' ">$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\zlib\prebuilt\$(Platform)\</ZLibBinPath>
    <WebsocketsBinPath Condition=" '$(WebsocketsBinPath)' == '' ">$(EngineRoot)external\websockets\prebuilt\$(COCOS2D_PLATFORM)\$(Platform)\</WebsocketsBinPath>
    <SQLiteBinPath Condition=" '$(SQLiteBinPath)' == '' ">$(EngineRoot)external\sqlite3\libraries\$(COCOS2D_PLATFORM)\$(Platform)\</SQLiteBinPath>
    <OggBinPath Condition=" '$(OggBinPath)' == '' ">$(EngineRoot)external\$(COCOS2D_PLATFORM)-specific\OggDecoder\prebuilt\$(Platform)\</OggBinPath>
    <WebsocketsBinPath Condition=" '$(WebsocketsBinPath)' == '' ">$(EngineRoot)external\websockets\prebuilt\$(Platform)\</WebsocketsBinPath>
  </PropertyGroup>
  <ItemGroup Label="ANGLE">
    <None Include="$(AngleBinPath)libEGL.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(AngleBinPath)libGLESv2.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(ZLibBinPath)zlib1.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(SQLiteBinPath)sqlite3.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(CurlBinPath)libcurl.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenSSLBinPath)libeay32.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OpenSSLBinPath)ssleay32.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OggBinPath)ogg.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OggBinPath)vorbis.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(OggBinPath)vorbisfile.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
    <None Include="$(WebsocketsBinPath)websockets.dll">
      <DeploymentContent>true</DeploymentContent>
    </None>
  </ItemGroup>
</Project>