//
//  DialogHelper.cpp
//  Slots
//
//  Created by chen<PERSON><PERSON><PERSON><PERSON> on 2024/12/13.
//

#include "DialogHelper.h"
#include "sea/common/SeaCommon.h"
namespace sea {

bool DialogHelper::isValid(const cocos2d::ValueMap& cfg, bool isVertical)
{
    auto invalidIter = cfg.find("invalid");
    if (invalidIter != cfg.end()) {
        auto invalidValue0 = invalidIter->second;
        auto invalidValue = DialogHelper::getVertical(invalidValue0, isVertical);
        
        if (invalidValue.getType() == ccValueType::BOOLEAN) {
            return !invalidValue.asBool();
        }
        if (!invalidValue.isNull()) {
            return false;
        }
    } else {
        auto validIter = cfg.find("valid");
        if (validIter != cfg.end()) {
            
            auto validValue0 = validIter->second;
            auto validValue = DialogHelper::getVertical(validValue0, isVertical);
            
            if (validValue.getType() == ccValueType::BOOLEAN) {
                return validValue.asBool();
            }
            if (validValue.isNull()) {
                return false;
            }
        }
    }
    
    return true;
}

cocos2d::Value DialogHelper::getVertical(const cocos2d::Value& value, bool isVertical, bool force/* = false*/) {
    if (value == cocos2d::Value::Null) {
        return cocos2d::Value(isVertical);
    }

    auto t = value.getType();

    switch (t) {
        case ccValueType::NONE:
            return cocos2d::Value(isVertical);

        case ccValueType::VECTOR:
            do{
                cocos2d::Value ret = value;
                auto vec = value.asValueVector();

                if (isVertical) {
                    if (vec.size() >= 2) {
                        auto _ret = vec.at(1);
                        if (!_ret.isNull()) {
                            return _ret;
                        }
                    }
                    if (force) {
                        return cocos2d::Value::Null;
                    }
                    if (vec.size() >= 1) {
                        auto _ret = vec.at(0);
                        if (!_ret.isNull()) {
                            return _ret;
                        }
                    }
                    else if (force) {
                        return cocos2d::Value::Null;
                    }
                    if (vec.size() >= 1) {
                        ret = vec.at(0);
                    }
                } else {
                    if (vec.size() >= 1) {
                        auto _ret = vec.at(0);
                        if (!_ret.isNull()) {
                            return _ret;
                        }
                    }
                    if (force) {
                        return cocos2d::Value::Null;
                    }
                    else if (force) {
                        return cocos2d::Value::Null;
                    }
                }

                return value;
            }while(0);
            break;
            
        case ccValueType::MAP:
            do{
                auto map = value.asValueMap();
                auto index = isVertical ? "2" : "1";
                auto v = sea::getValueFromMap(value, index);
                if (!v.isNull()) {
                    return v;
                }
                if (force) {
                    return cocos2d::Value::Null;
                }
                if (isVertical) {
                    v = sea::getValueFromMap(value, "1");
                    if (!v.isNull()) {
                        return v;
                    }
                }
            }while(0);
            return value;
            break;

        case ccValueType::LUA_FUNCTION:
        {
            auto _v = sea::excuteLuaFunction(value);
            return getVertical(_v, isVertical, force);
        }

            break;

        default:
            return value;

            break;
    }
}

}
