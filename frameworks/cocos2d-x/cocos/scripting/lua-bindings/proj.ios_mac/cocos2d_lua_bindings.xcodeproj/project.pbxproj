// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		150906F019D556C5002C4D97 /* lua_cocos2dx_audioengine_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 3E2BDB0319C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp */; };
		150906F119D556C8002C4D97 /* lua_cocos2dx_audioengine_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 3E2BDB0419C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp */; };
		150906F219D556CE002C4D97 /* lua_cocos2dx_audioengine_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 3E2BDB0819C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp */; };
		150906F319D556D1002C4D97 /* lua_cocos2dx_audioengine_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 3E2BDB0919C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h */; };
		150983D01B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983CE1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp */; };
		150983D11B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983CE1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp */; };
		150983D21B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 150983CF1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp */; };
		150983D31B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 150983CF1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp */; };
		150983D71B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983D51B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp */; };
		150983D81B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983D51B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp */; };
		150983D91B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 150983D61B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h */; };
		150983DA1B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 150983D61B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h */; };
		150983DD1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983DB1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp */; };
		150983DE1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983DB1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp */; };
		150983DF1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h in Headers */ = {isa = PBXBuildFile; fileRef = 150983DC1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h */; };
		150983E01B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h in Headers */ = {isa = PBXBuildFile; fileRef = 150983DC1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h */; };
		15415A7019A71768004F1E71 /* lua_cocos2dx_extension_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE74C18BC45C200215002 /* lua_cocos2dx_extension_auto.cpp */; };
		15415A7119A71782004F1E71 /* lua_cocos2dx_extension_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE74C18BC45C200215002 /* lua_cocos2dx_extension_auto.cpp */; };
		15415A7219A718FB004F1E71 /* lua_cocos2dx_3d_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1516227F19A0F3E3006099B8 /* lua_cocos2dx_3d_auto.cpp */; };
		15415A7319A718FB004F1E71 /* lua_cocos2dx_cocosbuilder_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCC019864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.cpp */; };
		15415A7519A718FB004F1E71 /* lua_cocos2dx_experimental_video_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 156EADF11977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.cpp */; };
		15415A7619A718FB004F1E71 /* lua_cocos2dx_ui_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2905FACE18CF12E600240AA3 /* lua_cocos2dx_ui_auto.cpp */; };
		15415A7719A718FB004F1E71 /* lua_cocos2dx_spine_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75518BC45C200215002 /* lua_cocos2dx_spine_auto.cpp */; };
		15415A7819A718FB004F1E71 /* lua_cocos2dx_studio_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75818BC45C200215002 /* lua_cocos2dx_studio_auto.cpp */; };
		15415AA519A71A53004F1E71 /* auxiliar.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8719A71A53004F1E71 /* auxiliar.c */; };
		15415AA619A71A53004F1E71 /* auxiliar.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8719A71A53004F1E71 /* auxiliar.c */; };
		15415AA719A71A53004F1E71 /* auxiliar.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8819A71A53004F1E71 /* auxiliar.h */; };
		15415AA819A71A53004F1E71 /* auxiliar.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8819A71A53004F1E71 /* auxiliar.h */; };
		15415AA919A71A53004F1E71 /* buffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8919A71A53004F1E71 /* buffer.c */; };
		15415AAA19A71A53004F1E71 /* buffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8919A71A53004F1E71 /* buffer.c */; };
		15415AAB19A71A53004F1E71 /* buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8A19A71A53004F1E71 /* buffer.h */; };
		15415AAC19A71A53004F1E71 /* buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8A19A71A53004F1E71 /* buffer.h */; };
		15415AAD19A71A53004F1E71 /* except.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8B19A71A53004F1E71 /* except.c */; };
		15415AAE19A71A53004F1E71 /* except.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8B19A71A53004F1E71 /* except.c */; };
		15415AAF19A71A53004F1E71 /* except.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8C19A71A53004F1E71 /* except.h */; };
		15415AB019A71A53004F1E71 /* except.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8C19A71A53004F1E71 /* except.h */; };
		15415AB119A71A53004F1E71 /* inet.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8D19A71A53004F1E71 /* inet.c */; };
		15415AB219A71A53004F1E71 /* inet.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8D19A71A53004F1E71 /* inet.c */; };
		15415AB319A71A53004F1E71 /* inet.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8E19A71A53004F1E71 /* inet.h */; };
		15415AB419A71A53004F1E71 /* inet.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8E19A71A53004F1E71 /* inet.h */; };
		15415AB519A71A53004F1E71 /* io.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8F19A71A53004F1E71 /* io.c */; };
		15415AB619A71A53004F1E71 /* io.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8F19A71A53004F1E71 /* io.c */; };
		15415AB719A71A53004F1E71 /* io.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9019A71A53004F1E71 /* io.h */; };
		15415AB819A71A53004F1E71 /* io.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9019A71A53004F1E71 /* io.h */; };
		15415AB919A71A53004F1E71 /* luasocket.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9119A71A53004F1E71 /* luasocket.c */; };
		15415ABA19A71A53004F1E71 /* luasocket.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9119A71A53004F1E71 /* luasocket.c */; };
		15415ABB19A71A53004F1E71 /* luasocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9219A71A53004F1E71 /* luasocket.h */; };
		15415ABC19A71A53004F1E71 /* luasocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9219A71A53004F1E71 /* luasocket.h */; };
		15415ABD19A71A53004F1E71 /* mime.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9319A71A53004F1E71 /* mime.c */; };
		15415ABE19A71A53004F1E71 /* mime.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9319A71A53004F1E71 /* mime.c */; };
		15415ABF19A71A53004F1E71 /* mime.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9419A71A53004F1E71 /* mime.h */; };
		15415AC019A71A53004F1E71 /* mime.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9419A71A53004F1E71 /* mime.h */; };
		15415AC119A71A53004F1E71 /* options.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9519A71A53004F1E71 /* options.c */; };
		15415AC219A71A53004F1E71 /* options.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9519A71A53004F1E71 /* options.c */; };
		15415AC319A71A53004F1E71 /* options.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9619A71A53004F1E71 /* options.h */; };
		15415AC419A71A53004F1E71 /* options.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9619A71A53004F1E71 /* options.h */; };
		15415AC519A71A53004F1E71 /* select.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9719A71A53004F1E71 /* select.c */; };
		15415AC619A71A53004F1E71 /* select.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9719A71A53004F1E71 /* select.c */; };
		15415AC719A71A53004F1E71 /* select.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9819A71A53004F1E71 /* select.h */; };
		15415AC819A71A53004F1E71 /* select.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9819A71A53004F1E71 /* select.h */; };
		15415AC919A71A53004F1E71 /* serial.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9919A71A53004F1E71 /* serial.c */; };
		15415ACA19A71A53004F1E71 /* serial.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9919A71A53004F1E71 /* serial.c */; };
		15415ACB19A71A53004F1E71 /* socket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9A19A71A53004F1E71 /* socket.h */; };
		15415ACC19A71A53004F1E71 /* socket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9A19A71A53004F1E71 /* socket.h */; };
		15415ACD19A71A53004F1E71 /* tcp.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9B19A71A53004F1E71 /* tcp.c */; };
		15415ACE19A71A53004F1E71 /* tcp.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9B19A71A53004F1E71 /* tcp.c */; };
		15415ACF19A71A53004F1E71 /* tcp.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9C19A71A53004F1E71 /* tcp.h */; };
		15415AD019A71A53004F1E71 /* tcp.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9C19A71A53004F1E71 /* tcp.h */; };
		15415AD119A71A53004F1E71 /* timeout.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9D19A71A53004F1E71 /* timeout.c */; };
		15415AD219A71A53004F1E71 /* timeout.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9D19A71A53004F1E71 /* timeout.c */; };
		15415AD319A71A53004F1E71 /* timeout.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9E19A71A53004F1E71 /* timeout.h */; };
		15415AD419A71A53004F1E71 /* timeout.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9E19A71A53004F1E71 /* timeout.h */; };
		15415AD519A71A53004F1E71 /* udp.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9F19A71A53004F1E71 /* udp.c */; };
		15415AD619A71A53004F1E71 /* udp.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9F19A71A53004F1E71 /* udp.c */; };
		15415AD719A71A53004F1E71 /* udp.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA019A71A53004F1E71 /* udp.h */; };
		15415AD819A71A53004F1E71 /* udp.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA019A71A53004F1E71 /* udp.h */; };
		15415AD919A71A53004F1E71 /* unix.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415AA119A71A53004F1E71 /* unix.c */; };
		15415ADA19A71A53004F1E71 /* unix.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415AA119A71A53004F1E71 /* unix.c */; };
		15415ADB19A71A53004F1E71 /* unix.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA219A71A53004F1E71 /* unix.h */; };
		15415ADC19A71A53004F1E71 /* unix.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA219A71A53004F1E71 /* unix.h */; };
		15415ADD19A71A53004F1E71 /* usocket.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415AA319A71A53004F1E71 /* usocket.c */; };
		15415ADE19A71A53004F1E71 /* usocket.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415AA319A71A53004F1E71 /* usocket.c */; };
		15415ADF19A71A53004F1E71 /* usocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA419A71A53004F1E71 /* usocket.h */; };
		15415AE019A71A53004F1E71 /* usocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA419A71A53004F1E71 /* usocket.h */; };
		155C7DEA19A71BDA00F08B25 /* lua_cocos2dx_3d_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1516227F19A0F3E3006099B8 /* lua_cocos2dx_3d_auto.cpp */; };
		155C7DEB19A71BE900F08B25 /* lua_cocos2dx_cocosbuilder_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCC019864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.cpp */; };
		155C7DEC19A71BF200F08B25 /* lua_cocos2dx_cocosdenshion_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCC219864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.cpp */; };
		155C7DED19A71BF400F08B25 /* lua_cocos2dx_cocosdenshion_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCC219864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.cpp */; };
		155C7DEE19A71BFC00F08B25 /* lua_cocos2dx_experimental_video_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 156EADF11977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.cpp */; };
		155C7DEF19A71C0900F08B25 /* lua_cocos2dx_ui_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2905FACE18CF12E600240AA3 /* lua_cocos2dx_ui_auto.cpp */; };
		155C7DF019A71C1E00F08B25 /* lua_cocos2dx_spine_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75518BC45C200215002 /* lua_cocos2dx_spine_auto.cpp */; };
		155C7DF119A71C2300F08B25 /* lua_cocos2dx_studio_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75818BC45C200215002 /* lua_cocos2dx_studio_auto.cpp */; };
		155C7DF219A71C3200F08B25 /* lua_cocos2dx_3d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 158C128519A0FA1300781A76 /* lua_cocos2dx_3d_manual.cpp */; };
		155C7DF319A71C3400F08B25 /* lua_cocos2dx_3d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 158C128519A0FA1300781A76 /* lua_cocos2dx_3d_manual.cpp */; };
		155C7DF419A71C3700F08B25 /* lua_cocos2dx_3d_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 158C128619A0FA1300781A76 /* lua_cocos2dx_3d_manual.h */; };
		155C7DF519A71C3900F08B25 /* lua_cocos2dx_3d_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 158C128619A0FA1300781A76 /* lua_cocos2dx_3d_manual.h */; };
		155C7DF619A71C3E00F08B25 /* lua_cocos2dx_cocosdenshion_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15427D42198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.cpp */; };
		155C7DF719A71C4000F08B25 /* lua_cocos2dx_cocosdenshion_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15427D42198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.cpp */; };
		155C7DF819A71C4400F08B25 /* lua_cocos2dx_cocosdenshion_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15427D43198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.h */; };
		155C7DF919A71C4500F08B25 /* lua_cocos2dx_cocosdenshion_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15427D43198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.h */; };
		155C7DFE19A71C5A00F08B25 /* CCBProxy.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCF21986526C00A46ACC /* CCBProxy.cpp */; };
		155C7DFF19A71C5C00F08B25 /* CCBProxy.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCF21986526C00A46ACC /* CCBProxy.cpp */; };
		155C7E0019A71C6000F08B25 /* CCBProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCF31986526C00A46ACC /* CCBProxy.h */; };
		155C7E0119A71C6300F08B25 /* CCBProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCF31986526C00A46ACC /* CCBProxy.h */; };
		155C7E0219A71C6700F08B25 /* lua_cocos2dx_cocosbuilder_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCF41986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.cpp */; };
		155C7E0319A71C6900F08B25 /* lua_cocos2dx_cocosbuilder_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCF41986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.cpp */; };
		155C7E0419A71C6D00F08B25 /* lua_cocos2dx_cocosbuilder_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCF51986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.h */; };
		155C7E0519A71C6F00F08B25 /* lua_cocos2dx_cocosbuilder_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCF51986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.h */; };
		155C7E0619A71C7600F08B25 /* lua_cocos2dx_extension_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCFE1986526C00A46ACC /* lua_cocos2dx_extension_manual.cpp */; };
		155C7E0719A71C7800F08B25 /* lua_cocos2dx_extension_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCFE1986526C00A46ACC /* lua_cocos2dx_extension_manual.cpp */; };
		155C7E0819A71C7B00F08B25 /* lua_cocos2dx_extension_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCFF1986526C00A46ACC /* lua_cocos2dx_extension_manual.h */; };
		155C7E0919A71C8500F08B25 /* lua_cocos2dx_extension_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCFF1986526C00A46ACC /* lua_cocos2dx_extension_manual.h */; };
		155C7E0A19A71C8B00F08B25 /* lua_cocos2dx_network_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15EFA694198B668C000C57D3 /* lua_cocos2dx_network_manual.cpp */; };
		155C7E0B19A71C8D00F08B25 /* lua_cocos2dx_network_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15EFA694198B668C000C57D3 /* lua_cocos2dx_network_manual.cpp */; };
		155C7E0C19A71C9000F08B25 /* lua_cocos2dx_network_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15EFA695198B668C000C57D3 /* lua_cocos2dx_network_manual.h */; };
		155C7E0D19A71C9300F08B25 /* lua_cocos2dx_network_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15EFA695198B668C000C57D3 /* lua_cocos2dx_network_manual.h */; };
		155C7E0E19A71C9600F08B25 /* lua_extensions.c in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD011986526C00A46ACC /* lua_extensions.c */; };
		155C7E0F19A71C9800F08B25 /* lua_extensions.c in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD011986526C00A46ACC /* lua_extensions.c */; };
		155C7E1019A71C9D00F08B25 /* lua_extensions.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD021986526C00A46ACC /* lua_extensions.h */; };
		155C7E1119A71C9F00F08B25 /* lua_extensions.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD021986526C00A46ACC /* lua_extensions.h */; };
		155C7E1219A71CA200F08B25 /* Lua_web_socket.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD031986526C00A46ACC /* Lua_web_socket.cpp */; };
		155C7E1319A71CA400F08B25 /* Lua_web_socket.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD031986526C00A46ACC /* Lua_web_socket.cpp */; };
		155C7E1419A71CA800F08B25 /* Lua_web_socket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD041986526C00A46ACC /* Lua_web_socket.h */; };
		155C7E1519A71CAA00F08B25 /* Lua_web_socket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD041986526C00A46ACC /* Lua_web_socket.h */; };
		155C7E1619A71CAD00F08B25 /* lua_xml_http_request.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD051986526C00A46ACC /* lua_xml_http_request.cpp */; };
		155C7E1719A71CAF00F08B25 /* lua_xml_http_request.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD051986526C00A46ACC /* lua_xml_http_request.cpp */; };
		155C7E1819A71CB300F08B25 /* lua_xml_http_request.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD061986526C00A46ACC /* lua_xml_http_request.h */; };
		155C7E1919A71CB500F08B25 /* lua_xml_http_request.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD061986526C00A46ACC /* lua_xml_http_request.h */; };
		155C7E1A19A71CBC00F08B25 /* lua_cocos2dx_spine_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD151986526C00A46ACC /* lua_cocos2dx_spine_manual.cpp */; };
		155C7E1B19A71CBE00F08B25 /* lua_cocos2dx_spine_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD151986526C00A46ACC /* lua_cocos2dx_spine_manual.cpp */; };
		155C7E1C19A71CC200F08B25 /* lua_cocos2dx_spine_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD161986526C00A46ACC /* lua_cocos2dx_spine_manual.hpp */; };
		155C7E1D19A71CC300F08B25 /* lua_cocos2dx_spine_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD161986526C00A46ACC /* lua_cocos2dx_spine_manual.hpp */; };
		155C7E1E19A71CC700F08B25 /* LuaSkeletonAnimation.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD171986526C00A46ACC /* LuaSkeletonAnimation.cpp */; };
		155C7E1F19A71CC800F08B25 /* LuaSkeletonAnimation.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD171986526C00A46ACC /* LuaSkeletonAnimation.cpp */; };
		155C7E2019A71CCC00F08B25 /* LuaSkeletonAnimation.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD181986526C00A46ACC /* LuaSkeletonAnimation.h */; };
		155C7E2119A71CCE00F08B25 /* LuaSkeletonAnimation.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD181986526C00A46ACC /* LuaSkeletonAnimation.h */; };
		155C7E2219A71CD300F08B25 /* lua_cocos2dx_experimental_video_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1C24B1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.cpp */; };
		155C7E2319A71CD500F08B25 /* lua_cocos2dx_experimental_video_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1C24B1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.cpp */; };
		155C7E2419A71CD800F08B25 /* lua_cocos2dx_experimental_video_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1C24C1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.hpp */; };
		155C7E2519A71CDA00F08B25 /* lua_cocos2dx_experimental_video_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1C24C1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.hpp */; };
		155C7E2619A71CDD00F08B25 /* lua_cocos2dx_ui_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD1A1986526C00A46ACC /* lua_cocos2dx_ui_manual.cpp */; };
		155C7E2719A71CDE00F08B25 /* lua_cocos2dx_ui_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD1A1986526C00A46ACC /* lua_cocos2dx_ui_manual.cpp */; };
		155C7E2819A71CE600F08B25 /* lua_cocos2dx_ui_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD1B1986526C00A46ACC /* lua_cocos2dx_ui_manual.hpp */; };
		155C7E2919A71CE800F08B25 /* lua_cocos2dx_ui_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD1B1986526C00A46ACC /* lua_cocos2dx_ui_manual.hpp */; };
		1595523A1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 159552381A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp */; };
		1595523B1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 159552381A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp */; };
		1595523C1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 159552391A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp */; };
		1595523D1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 159552391A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp */; };
		15A561E51B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15A561E31B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp */; };
		15A561E61B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15A561E31B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp */; };
		15A561E71B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15A561E41B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp */; };
		15A561E81B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15A561E41B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp */; };
		15A561EC1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15A561EA1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp */; };
		15A561ED1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15A561EA1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp */; };
		15A561EE1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15A561EB1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h */; };
		15A561EF1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15A561EB1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h */; };
		15AC69D519876E9300D17520 /* lua_cocos2dx_physics_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75218BC45C200215002 /* lua_cocos2dx_physics_auto.cpp */; };
		15AC69D619876EA200D17520 /* lua_cocos2dx_physics_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE75318BC45C200215002 /* lua_cocos2dx_physics_auto.hpp */; };
		15AC69D91987710400D17520 /* tolua_event.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1F918CD8F6E0087CE3A /* tolua_event.c */; };
		15AC69DA1987710400D17520 /* tolua_is.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FB18CD8F6E0087CE3A /* tolua_is.c */; };
		15AC69DB1987710400D17520 /* tolua_map.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FC18CD8F6E0087CE3A /* tolua_map.c */; };
		15AC69DC1987710400D17520 /* tolua_push.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FD18CD8F6E0087CE3A /* tolua_push.c */; };
		15AC69DD1987710400D17520 /* tolua_to.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FE18CD8F6E0087CE3A /* tolua_to.c */; };
		15AC69DE1987711400D17520 /* lauxlib.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E618CD8F470087CE3A /* lauxlib.h */; };
		15AC69DF1987711400D17520 /* lua.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E718CD8F470087CE3A /* lua.h */; };
		15AC69E01987711400D17520 /* luaconf.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E818CD8F470087CE3A /* luaconf.h */; };
		15AC69E11987711400D17520 /* lualib.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E918CD8F470087CE3A /* lualib.h */; };
		15AC69E21987712500D17520 /* tolua_event.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1FA18CD8F6E0087CE3A /* tolua_event.h */; };
		15AC69E31987712500D17520 /* tolua++.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1FF18CD8F6E0087CE3A /* tolua++.h */; };
		15AC69E4198771FF00D17520 /* libluajit.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1ABCA1F618CD8F5F0087CE3A /* libluajit.a */; };
		15C1C2CD1987495500A46ACC /* lua_cocos2dx_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE74918BC45C200215002 /* lua_cocos2dx_auto.cpp */; };
		15C1C2CE1987498B00A46ACC /* LuaOpengl.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1C24F198747E400A46ACC /* LuaOpengl.cpp */; };
		15C1C2CF1987498B00A46ACC /* lua_cocos2dx_deprecated.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD31986525900A46ACC /* lua_cocos2dx_deprecated.cpp */; };
		15C1C2D01987498B00A46ACC /* lua_cocos2dx_experimental_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD51986525900A46ACC /* lua_cocos2dx_experimental_manual.cpp */; };
		15C1C2D11987498B00A46ACC /* lua_cocos2dx_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD71986525900A46ACC /* lua_cocos2dx_manual.cpp */; };
		15C1C2D21987498B00A46ACC /* lua_cocos2dx_physics_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD91986525900A46ACC /* lua_cocos2dx_physics_manual.cpp */; };
		15C1C2D31987498B00A46ACC /* LuaScriptHandlerMgr.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCDB1986525900A46ACC /* LuaScriptHandlerMgr.cpp */; };
		15C1C2D41987499F00A46ACC /* lua_cocos2dx_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE74A18BC45C200215002 /* lua_cocos2dx_auto.hpp */; };
		15C1C2D5198749BC00A46ACC /* LuaOpengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1C250198747E400A46ACC /* LuaOpengl.h */; };
		15C1C2D6198749BC00A46ACC /* lua_cocos2dx_deprecated.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD41986525900A46ACC /* lua_cocos2dx_deprecated.h */; };
		15C1C2D7198749BC00A46ACC /* lua_cocos2dx_experimental_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD61986525900A46ACC /* lua_cocos2dx_experimental_manual.hpp */; };
		15C1C2D8198749BC00A46ACC /* lua_cocos2dx_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD81986525900A46ACC /* lua_cocos2dx_manual.hpp */; };
		15C1C2D9198749BC00A46ACC /* lua_cocos2dx_physics_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCDA1986525900A46ACC /* lua_cocos2dx_physics_manual.hpp */; };
		15C1C2DA198749BC00A46ACC /* LuaScriptHandlerMgr.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCDC1986525900A46ACC /* LuaScriptHandlerMgr.h */; };
		15C1C2DB19874B3D00A46ACC /* xxtea.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1540AF55193EC30500717D8E /* xxtea.cpp */; };
		15C1C2DC19874B4400A46ACC /* xxtea.h in Headers */ = {isa = PBXBuildFile; fileRef = 1540AF56193EC30500717D8E /* xxtea.h */; };
		15C1C2DD19874B8800A46ACC /* CCLuaBridge.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76018BC45C200215002 /* CCLuaBridge.cpp */; };
		15C1C2DE19874B8800A46ACC /* CCLuaEngine.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76218BC45C200215002 /* CCLuaEngine.cpp */; };
		15C1C2DF19874B8800A46ACC /* CCLuaStack.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76418BC45C200215002 /* CCLuaStack.cpp */; };
		15C1C2E019874B8800A46ACC /* CCLuaValue.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76618BC45C200215002 /* CCLuaValue.cpp */; };
		15C1C2E119874B8800A46ACC /* Cocos2dxLuaLoader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76818BC45C200215002 /* Cocos2dxLuaLoader.cpp */; };
		15C1C2E219874BA100A46ACC /* LuaBasicConversions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE77E18BC45C200215002 /* LuaBasicConversions.cpp */; };
		15C1C2E419874C7C00A46ACC /* tolua_fix.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1A262AB718BEEF5900D2DB92 /* tolua_fix.cpp */; };
		15C1C2E519874C9200A46ACC /* CCLuaObjcBridge.mm in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE78F18BC45C200215002 /* CCLuaObjcBridge.mm */; };
		15C1C2E719874CBE00A46ACC /* CCLuaBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76118BC45C200215002 /* CCLuaBridge.h */; };
		15C1C2E819874CBE00A46ACC /* CCLuaEngine.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76318BC45C200215002 /* CCLuaEngine.h */; };
		15C1C2E919874CBE00A46ACC /* CCLuaStack.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76518BC45C200215002 /* CCLuaStack.h */; };
		15C1C2EA19874CBE00A46ACC /* CCLuaValue.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76718BC45C200215002 /* CCLuaValue.h */; };
		15C1C2EB19874CBE00A46ACC /* Cocos2dxLuaLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76918BC45C200215002 /* Cocos2dxLuaLoader.h */; };
		15C1C2EC19874CBE00A46ACC /* LuaBasicConversions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE77F18BC45C200215002 /* LuaBasicConversions.h */; };
		15C1C2ED19874CBE00A46ACC /* CCLuaObjcBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE78E18BC45C200215002 /* CCLuaObjcBridge.h */; };
		15C1C2EE19874CBE00A46ACC /* tolua_fix.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE7B418BC45C200215002 /* tolua_fix.h */; };
		15C9A10E1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C9A10C1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.cpp */; };
		15C9A10F1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C9A10D1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.hpp */; };
		15C9A1121AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C9A1101AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.cpp */; };
		15C9A1131AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C9A1111AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.hpp */; };
		15EFA1F61989E528000C57D3 /* lua_cocos2dx_experimental_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15622967197780DE009C9067 /* lua_cocos2dx_experimental_auto.cpp */; };
		15EFA1F71989E582000C57D3 /* lua_cocos2dx_experimental_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15622968197780DE009C9067 /* lua_cocos2dx_experimental_auto.hpp */; };
		15EFA5D9198B2DAA000C57D3 /* libluajit.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1ABCA1F318CD8F540087CE3A /* libluajit.a */; };
		15EFA617198B2E2B000C57D3 /* lua_cocos2dx_experimental_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15622967197780DE009C9067 /* lua_cocos2dx_experimental_auto.cpp */; };
		15EFA618198B2E2B000C57D3 /* lua_cocos2dx_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE74918BC45C200215002 /* lua_cocos2dx_auto.cpp */; };
		15EFA619198B2E2B000C57D3 /* lua_cocos2dx_physics_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75218BC45C200215002 /* lua_cocos2dx_physics_auto.cpp */; };
		15EFA622198B2E74000C57D3 /* lua_cocos2dx_experimental_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15622968197780DE009C9067 /* lua_cocos2dx_experimental_auto.hpp */; };
		15EFA623198B2E74000C57D3 /* lua_cocos2dx_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE74A18BC45C200215002 /* lua_cocos2dx_auto.hpp */; };
		15EFA624198B2E74000C57D3 /* lua_cocos2dx_physics_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE75318BC45C200215002 /* lua_cocos2dx_physics_auto.hpp */; };
		15EFA625198B31FB000C57D3 /* LuaOpengl.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1C24F198747E400A46ACC /* LuaOpengl.cpp */; };
		15EFA626198B31FB000C57D3 /* lua_cocos2dx_deprecated.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD31986525900A46ACC /* lua_cocos2dx_deprecated.cpp */; };
		15EFA627198B31FB000C57D3 /* lua_cocos2dx_experimental_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD51986525900A46ACC /* lua_cocos2dx_experimental_manual.cpp */; };
		15EFA628198B31FB000C57D3 /* lua_cocos2dx_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD71986525900A46ACC /* lua_cocos2dx_manual.cpp */; };
		15EFA629198B31FB000C57D3 /* lua_cocos2dx_physics_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD91986525900A46ACC /* lua_cocos2dx_physics_manual.cpp */; };
		15EFA62A198B31FB000C57D3 /* LuaScriptHandlerMgr.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCDB1986525900A46ACC /* LuaScriptHandlerMgr.cpp */; };
		15EFA62B198B3220000C57D3 /* LuaOpengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1C250198747E400A46ACC /* LuaOpengl.h */; };
		15EFA62C198B3220000C57D3 /* lua_cocos2dx_deprecated.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD41986525900A46ACC /* lua_cocos2dx_deprecated.h */; };
		15EFA62D198B3220000C57D3 /* lua_cocos2dx_experimental_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD61986525900A46ACC /* lua_cocos2dx_experimental_manual.hpp */; };
		15EFA62E198B3220000C57D3 /* lua_cocos2dx_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD81986525900A46ACC /* lua_cocos2dx_manual.hpp */; };
		15EFA62F198B3220000C57D3 /* lua_cocos2dx_physics_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCDA1986525900A46ACC /* lua_cocos2dx_physics_manual.hpp */; };
		15EFA630198B3220000C57D3 /* LuaScriptHandlerMgr.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCDC1986525900A46ACC /* LuaScriptHandlerMgr.h */; };
		15EFA632198B328B000C57D3 /* CCLuaBridge.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76018BC45C200215002 /* CCLuaBridge.cpp */; };
		15EFA633198B328B000C57D3 /* CCLuaEngine.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76218BC45C200215002 /* CCLuaEngine.cpp */; };
		15EFA634198B328B000C57D3 /* CCLuaStack.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76418BC45C200215002 /* CCLuaStack.cpp */; };
		15EFA635198B328B000C57D3 /* CCLuaValue.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76618BC45C200215002 /* CCLuaValue.cpp */; };
		15EFA636198B328B000C57D3 /* Cocos2dxLuaLoader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76818BC45C200215002 /* Cocos2dxLuaLoader.cpp */; };
		15EFA637198B328B000C57D3 /* LuaBasicConversions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE77E18BC45C200215002 /* LuaBasicConversions.cpp */; };
		15EFA638198B328B000C57D3 /* CCLuaObjcBridge.mm in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE78F18BC45C200215002 /* CCLuaObjcBridge.mm */; };
		15EFA639198B328B000C57D3 /* tolua_fix.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1A262AB718BEEF5900D2DB92 /* tolua_fix.cpp */; };
		15EFA63B198B32BB000C57D3 /* CCLuaBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76118BC45C200215002 /* CCLuaBridge.h */; };
		15EFA63C198B32BB000C57D3 /* CCLuaEngine.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76318BC45C200215002 /* CCLuaEngine.h */; };
		15EFA63D198B32BB000C57D3 /* CCLuaStack.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76518BC45C200215002 /* CCLuaStack.h */; };
		15EFA63E198B32BB000C57D3 /* CCLuaValue.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76718BC45C200215002 /* CCLuaValue.h */; };
		15EFA63F198B32BB000C57D3 /* Cocos2dxLuaLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76918BC45C200215002 /* Cocos2dxLuaLoader.h */; };
		15EFA640198B32BB000C57D3 /* LuaBasicConversions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE77F18BC45C200215002 /* LuaBasicConversions.h */; };
		15EFA641198B32BB000C57D3 /* CCLuaObjcBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE78E18BC45C200215002 /* CCLuaObjcBridge.h */; };
		15EFA642198B32BB000C57D3 /* tolua_fix.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE7B418BC45C200215002 /* tolua_fix.h */; };
		15EFA644198B32D5000C57D3 /* xxtea.h in Headers */ = {isa = PBXBuildFile; fileRef = 1540AF56193EC30500717D8E /* xxtea.h */; };
		15EFA645198B32DB000C57D3 /* xxtea.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1540AF55193EC30500717D8E /* xxtea.cpp */; };
		15EFA646198B3311000C57D3 /* tolua_event.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1F918CD8F6E0087CE3A /* tolua_event.c */; };
		15EFA647198B3311000C57D3 /* tolua_is.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FB18CD8F6E0087CE3A /* tolua_is.c */; };
		15EFA648198B3311000C57D3 /* tolua_map.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FC18CD8F6E0087CE3A /* tolua_map.c */; };
		15EFA649198B3311000C57D3 /* tolua_push.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FD18CD8F6E0087CE3A /* tolua_push.c */; };
		15EFA64A198B3311000C57D3 /* tolua_to.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FE18CD8F6E0087CE3A /* tolua_to.c */; };
		15EFA64B198B3320000C57D3 /* tolua_event.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1FA18CD8F6E0087CE3A /* tolua_event.h */; };
		15EFA64C198B3320000C57D3 /* tolua++.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1FF18CD8F6E0087CE3A /* tolua++.h */; };
		15EFA64D198B3342000C57D3 /* lauxlib.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E618CD8F470087CE3A /* lauxlib.h */; };
		15EFA64E198B3342000C57D3 /* lua.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E718CD8F470087CE3A /* lua.h */; };
		15EFA64F198B3342000C57D3 /* luaconf.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E818CD8F470087CE3A /* luaconf.h */; };
		15EFA650198B3342000C57D3 /* lualib.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E918CD8F470087CE3A /* lualib.h */; };
		37A909BA1FDFF24700408A69 /* lua_bldownload_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A909B21FDFF1FC00408A69 /* lua_bldownload_auto.cpp */; };
		37A909BB1FDFF24700408A69 /* lua_bldownload_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A909B31FDFF1FC00408A69 /* lua_bldownload_auto.hpp */; };
		37A909BC1FDFF24700408A69 /* lua_database_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A909B51FDFF1FC00408A69 /* lua_database_auto.cpp */; };
		37A909BD1FDFF24700408A69 /* lua_database_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A909B41FDFF1FC00408A69 /* lua_database_auto.hpp */; };
		37A909BE1FDFF25600408A69 /* lua_bole_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A909811FDFEA7D00408A69 /* lua_bole_manual.cpp */; };
		37A909BF1FDFF25600408A69 /* lua_bole_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A9097E1FDFEA7D00408A69 /* lua_bole_manual.hpp */; };
		37A909C01FDFF25600408A69 /* lua_bole.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A9097F1FDFEA7D00408A69 /* lua_bole.cpp */; };
		37A909C11FDFF25600408A69 /* lua_bole.h in Headers */ = {isa = PBXBuildFile; fileRef = 37A909801FDFEA7D00408A69 /* lua_bole.h */; };
		37A90C861FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A90C821FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.hpp */; };
		37A90C871FE0DB3800408A69 /* CustomGUIReader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A90C831FE0DB3800408A69 /* CustomGUIReader.cpp */; };
		37A90C881FE0DB3800408A69 /* CustomGUIReader.h in Headers */ = {isa = PBXBuildFile; fileRef = 37A90C841FE0DB3800408A69 /* CustomGUIReader.h */; };
		37A90C891FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A90C851FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.cpp */; };
		37A90C8E1FE10C2100408A69 /* CustomGUIReader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A90C831FE0DB3800408A69 /* CustomGUIReader.cpp */; };
		37A90C8F1FE10C2100408A69 /* CustomGUIReader.h in Headers */ = {isa = PBXBuildFile; fileRef = 37A90C841FE0DB3800408A69 /* CustomGUIReader.h */; };
		37A90C901FE10C2100408A69 /* lua_cocos2dx_coco_studio_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A90C851FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.cpp */; };
		37A90C911FE10C2100408A69 /* lua_cocos2dx_coco_studio_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A90C821FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.hpp */; };
		37A90C961FE1144500408A69 /* lua_cocos2dx_csloader_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A90C921FE1144500408A69 /* lua_cocos2dx_csloader_manual.cpp */; };
		37A90C991FE1144500408A69 /* lua_cocos2dx_csloader_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A90C951FE1144500408A69 /* lua_cocos2dx_csloader_manual.hpp */; };
		37A90C9A1FE1147D00408A69 /* lua_cocos2dx_csloader_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 37A90C921FE1144500408A69 /* lua_cocos2dx_csloader_manual.cpp */; };
		37A90C9B1FE1147D00408A69 /* lua_cocos2dx_csloader_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 37A90C951FE1144500408A69 /* lua_cocos2dx_csloader_manual.hpp */; };
		3E2BDB0519C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 3E2BDB0319C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp */; };
		3E2BDB0619C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 3E2BDB0419C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp */; };
		3E2BDB0A19C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 3E2BDB0819C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp */; };
		3E2BDB0B19C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 3E2BDB0919C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h */; };
		507B42BD1C31FA0C0067B53E /* lua_module_register.cpp in Sources */ = {isa = PBXBuildFile; fileRef = ADD1C0D21C196B9500733781 /* lua_module_register.cpp */; };
		507B42BE1C31FA0C0067B53E /* select.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9719A71A53004F1E71 /* select.c */; };
		507B42BF1C31FA0C0067B53E /* lua_cocos2dx_physics3d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15A561EA1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp */; };
		507B42C01C31FA0C0067B53E /* tolua_event.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1F918CD8F6E0087CE3A /* tolua_event.c */; };
		507B42C11C31FA0C0067B53E /* lua_cocos2dx_audioengine_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 3E2BDB0819C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp */; };
		507B42C21C31FA0C0067B53E /* auxiliar.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8719A71A53004F1E71 /* auxiliar.c */; };
		507B42C41C31FA0C0067B53E /* tolua_is.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FB18CD8F6E0087CE3A /* tolua_is.c */; };
		507B42C51C31FA0C0067B53E /* tolua_map.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FC18CD8F6E0087CE3A /* tolua_map.c */; };
		507B42C61C31FA0C0067B53E /* tolua_push.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FD18CD8F6E0087CE3A /* tolua_push.c */; };
		507B42C71C31FA0C0067B53E /* tolua_to.c in Sources */ = {isa = PBXBuildFile; fileRef = 1ABCA1FE18CD8F6E0087CE3A /* tolua_to.c */; };
		507B42C81C31FA0C0067B53E /* options.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9519A71A53004F1E71 /* options.c */; };
		507B42C91C31FA0C0067B53E /* xxtea.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1540AF55193EC30500717D8E /* xxtea.cpp */; };
		507B42CA1C31FA0C0067B53E /* CCLuaBridge.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76018BC45C200215002 /* CCLuaBridge.cpp */; };
		507B42CB1C31FA0C0067B53E /* lua_cocos2dx_navmesh_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983CE1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp */; };
		507B42CC1C31FA0C0067B53E /* CCLuaEngine.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76218BC45C200215002 /* CCLuaEngine.cpp */; };
		507B42CD1C31FA0C0067B53E /* lua_cocos2dx_experimental_video_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 156EADF11977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.cpp */; };
		507B42CE1C31FA0C0067B53E /* CCLuaStack.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76418BC45C200215002 /* CCLuaStack.cpp */; };
		507B42CF1C31FA0C0067B53E /* CCLuaValue.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76618BC45C200215002 /* CCLuaValue.cpp */; };
		507B42D01C31FA0C0067B53E /* Cocos2dxLuaLoader.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE76818BC45C200215002 /* Cocos2dxLuaLoader.cpp */; };
		507B42D11C31FA0C0067B53E /* lua_cocos2dx_cocosbuilder_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCC019864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.cpp */; };
		507B42D21C31FA0C0067B53E /* lua_cocos2dx_navmesh_conversions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983DB1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp */; };
		507B42D31C31FA0C0067B53E /* LuaBasicConversions.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE77E18BC45C200215002 /* LuaBasicConversions.cpp */; };
		507B42D41C31FA0C0067B53E /* CCLuaObjcBridge.mm in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE78F18BC45C200215002 /* CCLuaObjcBridge.mm */; };
		507B42D51C31FA0C0067B53E /* lua_xml_http_request.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD051986526C00A46ACC /* lua_xml_http_request.cpp */; };
		507B42D61C31FA0C0067B53E /* tcp.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9B19A71A53004F1E71 /* tcp.c */; };
		507B42D71C31FA0C0067B53E /* timeout.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9D19A71A53004F1E71 /* timeout.c */; };
		507B42D81C31FA0C0067B53E /* luasocket_scripts.c in Sources */ = {isa = PBXBuildFile; fileRef = F4FE0D5519ECD00100B8B12B /* luasocket_scripts.c */; };
		507B42D91C31FA0C0067B53E /* tolua_fix.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1A262AB718BEEF5900D2DB92 /* tolua_fix.cpp */; };
		507B42DA1C31FA0C0067B53E /* lua_cocos2dx_physics3d_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15A561E31B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp */; };
		507B42DB1C31FA0C0067B53E /* CCBProxy.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCF21986526C00A46ACC /* CCBProxy.cpp */; };
		507B42DC1C31FA0C0067B53E /* lua_cocos2dx_csloader_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 159552381A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp */; };
		507B42DD1C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C9A1101AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.cpp */; };
		507B42DE1C31FA0C0067B53E /* lua_cocos2dx_spine_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75518BC45C200215002 /* lua_cocos2dx_spine_auto.cpp */; };
		507B42DF1C31FA0C0067B53E /* lua_cocos2dx_extension_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCFE1986526C00A46ACC /* lua_cocos2dx_extension_manual.cpp */; };
		507B42E01C31FA0C0067B53E /* LuaOpengl.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1C24F198747E400A46ACC /* LuaOpengl.cpp */; };
		507B42E11C31FA0C0067B53E /* lua_cocos2dx_deprecated.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD31986525900A46ACC /* lua_cocos2dx_deprecated.cpp */; };
		507B42E21C31FA0C0067B53E /* lua_cocos2dx_experimental_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD51986525900A46ACC /* lua_cocos2dx_experimental_manual.cpp */; };
		507B42E31C31FA0C0067B53E /* lua_cocos2dx_cocosbuilder_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCF41986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.cpp */; };
		507B42E41C31FA0C0067B53E /* mime.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9319A71A53004F1E71 /* mime.c */; };
		507B42E51C31FA0C0067B53E /* unix.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415AA119A71A53004F1E71 /* unix.c */; };
		507B42E61C31FA0C0067B53E /* lua_cocos2dx_3d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 158C128519A0FA1300781A76 /* lua_cocos2dx_3d_manual.cpp */; };
		507B42E71C31FA0C0067B53E /* usocket.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415AA319A71A53004F1E71 /* usocket.c */; };
		507B42E81C31FA0C0067B53E /* lua_cocos2dx_cocosdenshion_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCC219864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.cpp */; };
		507B42E91C31FA0C0067B53E /* lua_cocos2dx_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD71986525900A46ACC /* lua_cocos2dx_manual.cpp */; };
		507B42EA1C31FA0C0067B53E /* lua_extensions.c in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD011986526C00A46ACC /* lua_extensions.c */; };
		507B42EB1C31FA0C0067B53E /* CCComponentLua.cpp in Sources */ = {isa = PBXBuildFile; fileRef = BA0CBB5C1BB0756F00003364 /* CCComponentLua.cpp */; };
		507B42EC1C31FA0C0067B53E /* udp.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9F19A71A53004F1E71 /* udp.c */; };
		507B42ED1C31FA0C0067B53E /* luasocket.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9119A71A53004F1E71 /* luasocket.c */; };
		507B42EE1C31FA0C0067B53E /* LuaSkeletonAnimation.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD171986526C00A46ACC /* LuaSkeletonAnimation.cpp */; };
		507B42EF1C31FA0C0067B53E /* serial.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A9919A71A53004F1E71 /* serial.c */; };
		507B42F01C31FA0C0067B53E /* lua_cocos2dx_3d_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1516227F19A0F3E3006099B8 /* lua_cocos2dx_3d_auto.cpp */; };
		507B42F21C31FA0C0067B53E /* lua_cocos2dx_studio_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75818BC45C200215002 /* lua_cocos2dx_studio_auto.cpp */; };
		507B42F31C31FA0C0067B53E /* lua_cocos2dx_audioengine_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 3E2BDB0319C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp */; };
		507B42F41C31FA0C0067B53E /* lua_cocos2dx_network_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15EFA694198B668C000C57D3 /* lua_cocos2dx_network_manual.cpp */; };
		507B42F51C31FA0C0067B53E /* except.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8B19A71A53004F1E71 /* except.c */; };
		507B42F61C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C9A10C1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.cpp */; };
		507B42F71C31FA0C0067B53E /* buffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8919A71A53004F1E71 /* buffer.c */; };
		507B42F81C31FA0C0067B53E /* lua_cocos2dx_experimental_video_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1C24B1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.cpp */; };
		507B42F91C31FA0C0067B53E /* lua_cocos2dx_navmesh_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 150983D51B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp */; };
		507B42FB1C31FA0C0067B53E /* lua_cocos2dx_ui_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD1A1986526C00A46ACC /* lua_cocos2dx_ui_manual.cpp */; };
		507B42FC1C31FA0C0067B53E /* io.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8F19A71A53004F1E71 /* io.c */; };
		507B42FD1C31FA0C0067B53E /* lua_cocos2dx_cocosdenshion_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15427D42198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.cpp */; };
		507B42FE1C31FA0C0067B53E /* lua_cocos2dx_ui_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 2905FACE18CF12E600240AA3 /* lua_cocos2dx_ui_auto.cpp */; };
		507B42FF1C31FA0C0067B53E /* lua_cocos2dx_spine_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD151986526C00A46ACC /* lua_cocos2dx_spine_manual.cpp */; };
		507B43001C31FA0C0067B53E /* lua_cocos2dx_extension_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE74C18BC45C200215002 /* lua_cocos2dx_extension_auto.cpp */; };
		507B43011C31FA0C0067B53E /* lua_cocos2dx_physics_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCD91986525900A46ACC /* lua_cocos2dx_physics_manual.cpp */; };
		507B43021C31FA0C0067B53E /* LuaScriptHandlerMgr.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BCDB1986525900A46ACC /* LuaScriptHandlerMgr.cpp */; };
		507B43031C31FA0C0067B53E /* lua_cocos2dx_experimental_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15622967197780DE009C9067 /* lua_cocos2dx_experimental_auto.cpp */; };
		507B43041C31FA0C0067B53E /* lua_cocos2dx_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE74918BC45C200215002 /* lua_cocos2dx_auto.cpp */; };
		507B43051C31FA0C0067B53E /* lua_cocos2dx_physics_auto.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1AACE75218BC45C200215002 /* lua_cocos2dx_physics_auto.cpp */; };
		507B43061C31FA0C0067B53E /* inet.c in Sources */ = {isa = PBXBuildFile; fileRef = 15415A8D19A71A53004F1E71 /* inet.c */; };
		507B43081C31FA0C0067B53E /* Lua_web_socket.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 15C1BD031986526C00A46ACC /* Lua_web_socket.cpp */; };
		507B430A1C31FA0C0067B53E /* libluajit.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1ABCA1F318CD8F540087CE3A /* libluajit.a */; };
		507B430C1C31FA0C0067B53E /* lua_cocos2dx_navmesh_conversions.h in Headers */ = {isa = PBXBuildFile; fileRef = 150983DC1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h */; };
		507B430D1C31FA0C0067B53E /* lauxlib.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E618CD8F470087CE3A /* lauxlib.h */; };
		507B430E1C31FA0C0067B53E /* auxiliar.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8819A71A53004F1E71 /* auxiliar.h */; };
		507B430F1C31FA0C0067B53E /* lua_extensions.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD021986526C00A46ACC /* lua_extensions.h */; };
		507B43101C31FA0C0067B53E /* mime.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9419A71A53004F1E71 /* mime.h */; };
		507B43111C31FA0C0067B53E /* lua.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E718CD8F470087CE3A /* lua.h */; };
		507B43121C31FA0C0067B53E /* udp.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA019A71A53004F1E71 /* udp.h */; };
		507B43131C31FA0C0067B53E /* luaconf.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E818CD8F470087CE3A /* luaconf.h */; };
		507B43141C31FA0C0067B53E /* unix.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA219A71A53004F1E71 /* unix.h */; };
		507B43151C31FA0C0067B53E /* lua_cocos2dx_3d_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 158C128619A0FA1300781A76 /* lua_cocos2dx_3d_manual.h */; };
		507B43161C31FA0C0067B53E /* luasocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9219A71A53004F1E71 /* luasocket.h */; };
		507B43181C31FA0C0067B53E /* lua_cocos2dx_experimental_video_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1C24C1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.hpp */; };
		507B43191C31FA0C0067B53E /* tcp.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9C19A71A53004F1E71 /* tcp.h */; };
		507B431A1C31FA0C0067B53E /* CCComponentLua.h in Headers */ = {isa = PBXBuildFile; fileRef = BA0CBB5D1BB0756F00003364 /* CCComponentLua.h */; };
		507B431B1C31FA0C0067B53E /* buffer.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8A19A71A53004F1E71 /* buffer.h */; };
		507B431C1C31FA0C0067B53E /* lualib.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1E918CD8F470087CE3A /* lualib.h */; };
		507B431D1C31FA0C0067B53E /* Lua_web_socket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD041986526C00A46ACC /* Lua_web_socket.h */; };
		507B431E1C31FA0C0067B53E /* tolua_event.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1FA18CD8F6E0087CE3A /* tolua_event.h */; };
		507B431F1C31FA0C0067B53E /* lua_cocos2dx_physics3d_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15A561EB1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h */; };
		507B43201C31FA0C0067B53E /* tolua++.h in Headers */ = {isa = PBXBuildFile; fileRef = 1ABCA1FF18CD8F6E0087CE3A /* tolua++.h */; };
		507B43211C31FA0C0067B53E /* lua_cocos2dx_cocosdenshion_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15427D43198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.h */; };
		507B43221C31FA0C0067B53E /* except.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8C19A71A53004F1E71 /* except.h */; };
		507B43231C31FA0C0067B53E /* lua_cocos2dx_audioengine_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 3E2BDB0919C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h */; };
		507B43241C31FA0C0067B53E /* lua_cocos2dx_cocosbuilder_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCF51986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.h */; };
		507B43251C31FA0C0067B53E /* socket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9A19A71A53004F1E71 /* socket.h */; };
		507B43261C31FA0C0067B53E /* usocket.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415AA419A71A53004F1E71 /* usocket.h */; };
		507B43271C31FA0C0067B53E /* options.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9619A71A53004F1E71 /* options.h */; };
		507B43281C31FA0C0067B53E /* xxtea.h in Headers */ = {isa = PBXBuildFile; fileRef = 1540AF56193EC30500717D8E /* xxtea.h */; };
		507B43291C31FA0C0067B53E /* CCLuaBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76118BC45C200215002 /* CCLuaBridge.h */; };
		507B432A1C31FA0C0067B53E /* CCLuaEngine.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76318BC45C200215002 /* CCLuaEngine.h */; };
		507B432B1C31FA0C0067B53E /* CCLuaStack.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76518BC45C200215002 /* CCLuaStack.h */; };
		507B432D1C31FA0C0067B53E /* CCLuaValue.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76718BC45C200215002 /* CCLuaValue.h */; };
		507B432E1C31FA0C0067B53E /* lua_cocos2dx_network_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15EFA695198B668C000C57D3 /* lua_cocos2dx_network_manual.h */; };
		507B432F1C31FA0C0067B53E /* Cocos2dxLuaLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE76918BC45C200215002 /* Cocos2dxLuaLoader.h */; };
		507B43301C31FA0C0067B53E /* LuaBasicConversions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE77F18BC45C200215002 /* LuaBasicConversions.h */; };
		507B43311C31FA0C0067B53E /* lua_cocos2dx_navmesh_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 150983CF1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp */; };
		507B43321C31FA0C0067B53E /* inet.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A8E19A71A53004F1E71 /* inet.h */; };
		507B43331C31FA0C0067B53E /* CCLuaObjcBridge.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE78E18BC45C200215002 /* CCLuaObjcBridge.h */; };
		507B43341C31FA0C0067B53E /* tolua_fix.h in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE7B418BC45C200215002 /* tolua_fix.h */; };
		507B43351C31FA0C0067B53E /* LuaSkeletonAnimation.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD181986526C00A46ACC /* LuaSkeletonAnimation.h */; };
		507B43361C31FA0C0067B53E /* lua_cocos2dx_csloader_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 159552391A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp */; };
		507B43371C31FA0C0067B53E /* io.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9019A71A53004F1E71 /* io.h */; };
		507B43391C31FA0C0067B53E /* LuaOpengl.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1C250198747E400A46ACC /* LuaOpengl.h */; };
		507B433A1C31FA0C0067B53E /* timeout.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9E19A71A53004F1E71 /* timeout.h */; };
		507B433B1C31FA0C0067B53E /* lua_cocos2dx_deprecated.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD41986525900A46ACC /* lua_cocos2dx_deprecated.h */; };
		507B433C1C31FA0C0067B53E /* lua_cocos2dx_ui_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD1B1986526C00A46ACC /* lua_cocos2dx_ui_manual.hpp */; };
		507B433D1C31FA0C0067B53E /* lua_cocos2dx_experimental_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD61986525900A46ACC /* lua_cocos2dx_experimental_manual.hpp */; };
		507B433E1C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C9A1111AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.hpp */; };
		507B433F1C31FA0C0067B53E /* lua_cocos2dx_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCD81986525900A46ACC /* lua_cocos2dx_manual.hpp */; };
		507B43401C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C9A10D1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.hpp */; };
		507B43411C31FA0C0067B53E /* lua_cocos2dx_navmesh_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 150983D61B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h */; };
		507B43421C31FA0C0067B53E /* lua_cocos2dx_physics_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCDA1986525900A46ACC /* lua_cocos2dx_physics_manual.hpp */; };
		507B43431C31FA0C0067B53E /* lua_cocos2dx_extension_manual.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCFF1986526C00A46ACC /* lua_cocos2dx_extension_manual.h */; };
		507B43441C31FA0C0067B53E /* lua_xml_http_request.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD061986526C00A46ACC /* lua_xml_http_request.h */; };
		507B43461C31FA0C0067B53E /* LuaScriptHandlerMgr.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCDC1986525900A46ACC /* LuaScriptHandlerMgr.h */; };
		507B43471C31FA0C0067B53E /* lua_cocos2dx_experimental_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15622968197780DE009C9067 /* lua_cocos2dx_experimental_auto.hpp */; };
		507B43481C31FA0C0067B53E /* lua_cocos2dx_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE74A18BC45C200215002 /* lua_cocos2dx_auto.hpp */; };
		507B43491C31FA0C0067B53E /* CCBProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BCF31986526C00A46ACC /* CCBProxy.h */; };
		507B434A1C31FA0C0067B53E /* lua_cocos2dx_physics3d_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15A561E41B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp */; };
		507B434B1C31FA0C0067B53E /* select.h in Headers */ = {isa = PBXBuildFile; fileRef = 15415A9819A71A53004F1E71 /* select.h */; };
		507B434C1C31FA0C0067B53E /* lua_cocos2dx_spine_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 15C1BD161986526C00A46ACC /* lua_cocos2dx_spine_manual.hpp */; };
		507B434D1C31FA0C0067B53E /* lua_cocos2dx_physics_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 1AACE75318BC45C200215002 /* lua_cocos2dx_physics_auto.hpp */; };
		507B434E1C31FA0C0067B53E /* lua_cocos2dx_audioengine_auto.hpp in Headers */ = {isa = PBXBuildFile; fileRef = 3E2BDB0419C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp */; };
		507B434F1C31FA0C0067B53E /* luasocket_scripts.h in Headers */ = {isa = PBXBuildFile; fileRef = F4FE0D5619ECD00100B8B12B /* luasocket_scripts.h */; };
		ADAC23ED1C2044A60049A6A2 /* lua_module_register.cpp in Sources */ = {isa = PBXBuildFile; fileRef = ADD1C0D21C196B9500733781 /* lua_module_register.cpp */; };
		ADD1C0D51C196B9500733781 /* lua_module_register.cpp in Sources */ = {isa = PBXBuildFile; fileRef = ADD1C0D21C196B9500733781 /* lua_module_register.cpp */; };
		ADD1C0D61C196B9500733781 /* lua_module_register.h in Headers */ = {isa = PBXBuildFile; fileRef = ADD1C0D31C196B9500733781 /* lua_module_register.h */; };
		ADD1C0D71C196B9500733781 /* Lua-BindingsExport.h in Headers */ = {isa = PBXBuildFile; fileRef = ADD1C0D41C196B9500733781 /* Lua-BindingsExport.h */; };
		BA0CBB5E1BB0756F00003364 /* CCComponentLua.cpp in Sources */ = {isa = PBXBuildFile; fileRef = BA0CBB5C1BB0756F00003364 /* CCComponentLua.cpp */; };
		BA0CBB5F1BB0756F00003364 /* CCComponentLua.cpp in Sources */ = {isa = PBXBuildFile; fileRef = BA0CBB5C1BB0756F00003364 /* CCComponentLua.cpp */; };
		BA0CBB601BB0756F00003364 /* CCComponentLua.h in Headers */ = {isa = PBXBuildFile; fileRef = BA0CBB5D1BB0756F00003364 /* CCComponentLua.h */; };
		BA0CBB611BB0756F00003364 /* CCComponentLua.h in Headers */ = {isa = PBXBuildFile; fileRef = BA0CBB5D1BB0756F00003364 /* CCComponentLua.h */; };
		EE65609B2E4AEA410066A9AA /* fpconv.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560942E4AEA410066A9AA /* fpconv.h */; };
		EE65609C2E4AEA410066A9AA /* lua_cjson.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560962E4AEA410066A9AA /* lua_cjson.h */; };
		EE65609D2E4AEA410066A9AA /* strbuf.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560982E4AEA410066A9AA /* strbuf.h */; };
		EE65609E2E4AEA410066A9AA /* lua_cjson.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560972E4AEA410066A9AA /* lua_cjson.c */; };
		EE65609F2E4AEA410066A9AA /* fpconv.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560952E4AEA410066A9AA /* fpconv.c */; };
		EE6560A02E4AEA410066A9AA /* strbuf.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560992E4AEA410066A9AA /* strbuf.c */; };
		EE6560A12E4AEA410066A9AA /* fpconv.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560942E4AEA410066A9AA /* fpconv.h */; };
		EE6560A22E4AEA410066A9AA /* lua_cjson.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560962E4AEA410066A9AA /* lua_cjson.h */; };
		EE6560A32E4AEA410066A9AA /* strbuf.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560982E4AEA410066A9AA /* strbuf.h */; };
		EE6560A42E4AEA410066A9AA /* lua_cjson.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560972E4AEA410066A9AA /* lua_cjson.c */; };
		EE6560A52E4AEA410066A9AA /* fpconv.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560952E4AEA410066A9AA /* fpconv.c */; };
		EE6560A62E4AEA410066A9AA /* strbuf.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560992E4AEA410066A9AA /* strbuf.c */; };
		EE6560A72E4AEA410066A9AA /* fpconv.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560942E4AEA410066A9AA /* fpconv.h */; };
		EE6560A82E4AEA410066A9AA /* lua_cjson.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560962E4AEA410066A9AA /* lua_cjson.h */; };
		EE6560A92E4AEA410066A9AA /* strbuf.h in Headers */ = {isa = PBXBuildFile; fileRef = EE6560982E4AEA410066A9AA /* strbuf.h */; };
		EE6560AA2E4AEA410066A9AA /* lua_cjson.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560972E4AEA410066A9AA /* lua_cjson.c */; };
		EE6560AB2E4AEA410066A9AA /* fpconv.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560952E4AEA410066A9AA /* fpconv.c */; };
		EE6560AC2E4AEA410066A9AA /* strbuf.c in Sources */ = {isa = PBXBuildFile; fileRef = EE6560992E4AEA410066A9AA /* strbuf.c */; };
		EEE63EDE2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = EEE63EDC2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp */; };
		EEE63EDF2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = EEE63EDC2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp */; };
		EEE63EE02C1FEE5C0042066B /* lua_cricocos2d_manual.cpp in Sources */ = {isa = PBXBuildFile; fileRef = EEE63EDC2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp */; };
		EEE63EE12C1FEE5C0042066B /* lua_cricocos2d_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = EEE63EDD2C1FEE5C0042066B /* lua_cricocos2d_manual.hpp */; };
		EEE63EE22C1FEE5C0042066B /* lua_cricocos2d_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = EEE63EDD2C1FEE5C0042066B /* lua_cricocos2d_manual.hpp */; };
		EEE63EE32C1FEE5C0042066B /* lua_cricocos2d_manual.hpp in Headers */ = {isa = PBXBuildFile; fileRef = EEE63EDD2C1FEE5C0042066B /* lua_cricocos2d_manual.hpp */; };
		F4FE0D5719ECD00100B8B12B /* luasocket_scripts.c in Sources */ = {isa = PBXBuildFile; fileRef = F4FE0D5519ECD00100B8B12B /* luasocket_scripts.c */; };
		F4FE0D5819ECD00100B8B12B /* luasocket_scripts.c in Sources */ = {isa = PBXBuildFile; fileRef = F4FE0D5519ECD00100B8B12B /* luasocket_scripts.c */; };
		F4FE0D5919ECD00100B8B12B /* luasocket_scripts.h in Headers */ = {isa = PBXBuildFile; fileRef = F4FE0D5619ECD00100B8B12B /* luasocket_scripts.h */; };
		F4FE0D5A19ECD00100B8B12B /* luasocket_scripts.h in Headers */ = {isa = PBXBuildFile; fileRef = F4FE0D5619ECD00100B8B12B /* luasocket_scripts.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		150983CE1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_navmesh_auto.cpp; sourceTree = "<group>"; };
		150983CF1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_navmesh_auto.hpp; sourceTree = "<group>"; };
		150983D51B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = lua_cocos2dx_navmesh_manual.cpp; path = navmesh/lua_cocos2dx_navmesh_manual.cpp; sourceTree = "<group>"; };
		150983D61B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lua_cocos2dx_navmesh_manual.h; path = navmesh/lua_cocos2dx_navmesh_manual.h; sourceTree = "<group>"; };
		150983DB1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = lua_cocos2dx_navmesh_conversions.cpp; path = navmesh/lua_cocos2dx_navmesh_conversions.cpp; sourceTree = "<group>"; };
		150983DC1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lua_cocos2dx_navmesh_conversions.h; path = navmesh/lua_cocos2dx_navmesh_conversions.h; sourceTree = "<group>"; };
		1516227F19A0F3E3006099B8 /* lua_cocos2dx_3d_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_3d_auto.cpp; sourceTree = "<group>"; };
		1516228019A0F3E3006099B8 /* lua_cocos2dx_3d_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_3d_auto.hpp; sourceTree = "<group>"; };
		1540AF55193EC30500717D8E /* xxtea.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = xxtea.cpp; sourceTree = "<group>"; };
		1540AF56193EC30500717D8E /* xxtea.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = xxtea.h; sourceTree = "<group>"; };
		15415A8719A71A53004F1E71 /* auxiliar.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = auxiliar.c; sourceTree = "<group>"; };
		15415A8819A71A53004F1E71 /* auxiliar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = auxiliar.h; sourceTree = "<group>"; };
		15415A8919A71A53004F1E71 /* buffer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = buffer.c; sourceTree = "<group>"; };
		15415A8A19A71A53004F1E71 /* buffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = buffer.h; sourceTree = "<group>"; };
		15415A8B19A71A53004F1E71 /* except.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = except.c; sourceTree = "<group>"; };
		15415A8C19A71A53004F1E71 /* except.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = except.h; sourceTree = "<group>"; };
		15415A8D19A71A53004F1E71 /* inet.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = inet.c; sourceTree = "<group>"; };
		15415A8E19A71A53004F1E71 /* inet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = inet.h; sourceTree = "<group>"; };
		15415A8F19A71A53004F1E71 /* io.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = io.c; sourceTree = "<group>"; };
		15415A9019A71A53004F1E71 /* io.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = io.h; sourceTree = "<group>"; };
		15415A9119A71A53004F1E71 /* luasocket.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = luasocket.c; sourceTree = "<group>"; };
		15415A9219A71A53004F1E71 /* luasocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = luasocket.h; sourceTree = "<group>"; };
		15415A9319A71A53004F1E71 /* mime.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mime.c; sourceTree = "<group>"; };
		15415A9419A71A53004F1E71 /* mime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mime.h; sourceTree = "<group>"; };
		15415A9519A71A53004F1E71 /* options.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = options.c; sourceTree = "<group>"; };
		15415A9619A71A53004F1E71 /* options.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = options.h; sourceTree = "<group>"; };
		15415A9719A71A53004F1E71 /* select.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = select.c; sourceTree = "<group>"; };
		15415A9819A71A53004F1E71 /* select.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = select.h; sourceTree = "<group>"; };
		15415A9919A71A53004F1E71 /* serial.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = serial.c; sourceTree = "<group>"; };
		15415A9A19A71A53004F1E71 /* socket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = socket.h; sourceTree = "<group>"; };
		15415A9B19A71A53004F1E71 /* tcp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = tcp.c; sourceTree = "<group>"; };
		15415A9C19A71A53004F1E71 /* tcp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tcp.h; sourceTree = "<group>"; };
		15415A9D19A71A53004F1E71 /* timeout.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = timeout.c; sourceTree = "<group>"; };
		15415A9E19A71A53004F1E71 /* timeout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = timeout.h; sourceTree = "<group>"; };
		15415A9F19A71A53004F1E71 /* udp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = udp.c; sourceTree = "<group>"; };
		15415AA019A71A53004F1E71 /* udp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = udp.h; sourceTree = "<group>"; };
		15415AA119A71A53004F1E71 /* unix.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = unix.c; sourceTree = "<group>"; };
		15415AA219A71A53004F1E71 /* unix.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = unix.h; sourceTree = "<group>"; };
		15415AA319A71A53004F1E71 /* usocket.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = usocket.c; sourceTree = "<group>"; };
		15415AA419A71A53004F1E71 /* usocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = usocket.h; sourceTree = "<group>"; };
		15427D42198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_cocosdenshion_manual.cpp; sourceTree = "<group>"; };
		15427D43198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_cocosdenshion_manual.h; sourceTree = "<group>"; };
		15622967197780DE009C9067 /* lua_cocos2dx_experimental_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_experimental_auto.cpp; sourceTree = "<group>"; };
		15622968197780DE009C9067 /* lua_cocos2dx_experimental_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_experimental_auto.hpp; sourceTree = "<group>"; };
		156EADF11977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_experimental_video_auto.cpp; sourceTree = "<group>"; };
		156EADF21977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_experimental_video_auto.hpp; sourceTree = "<group>"; };
		158C128519A0FA1300781A76 /* lua_cocos2dx_3d_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_3d_manual.cpp; sourceTree = "<group>"; };
		158C128619A0FA1300781A76 /* lua_cocos2dx_3d_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_3d_manual.h; sourceTree = "<group>"; };
		159552381A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_csloader_auto.cpp; sourceTree = "<group>"; };
		159552391A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_csloader_auto.hpp; sourceTree = "<group>"; };
		15A561E31B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_physics3d_auto.cpp; sourceTree = "<group>"; };
		15A561E41B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_physics3d_auto.hpp; sourceTree = "<group>"; };
		15A561EA1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = lua_cocos2dx_physics3d_manual.cpp; path = physics3d/lua_cocos2dx_physics3d_manual.cpp; sourceTree = "<group>"; };
		15A561EB1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = lua_cocos2dx_physics3d_manual.h; path = physics3d/lua_cocos2dx_physics3d_manual.h; sourceTree = "<group>"; };
		15C1BCC019864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_cocosbuilder_auto.cpp; sourceTree = "<group>"; };
		15C1BCC119864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_cocosbuilder_auto.hpp; sourceTree = "<group>"; };
		15C1BCC219864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_cocosdenshion_auto.cpp; sourceTree = "<group>"; };
		15C1BCC319864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_cocosdenshion_auto.hpp; sourceTree = "<group>"; };
		15C1BCD31986525900A46ACC /* lua_cocos2dx_deprecated.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_deprecated.cpp; sourceTree = "<group>"; };
		15C1BCD41986525900A46ACC /* lua_cocos2dx_deprecated.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_deprecated.h; sourceTree = "<group>"; };
		15C1BCD51986525900A46ACC /* lua_cocos2dx_experimental_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_experimental_manual.cpp; sourceTree = "<group>"; };
		15C1BCD61986525900A46ACC /* lua_cocos2dx_experimental_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_experimental_manual.hpp; sourceTree = "<group>"; };
		15C1BCD71986525900A46ACC /* lua_cocos2dx_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_manual.cpp; sourceTree = "<group>"; };
		15C1BCD81986525900A46ACC /* lua_cocos2dx_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_manual.hpp; sourceTree = "<group>"; };
		15C1BCD91986525900A46ACC /* lua_cocos2dx_physics_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_physics_manual.cpp; sourceTree = "<group>"; };
		15C1BCDA1986525900A46ACC /* lua_cocos2dx_physics_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_physics_manual.hpp; sourceTree = "<group>"; };
		15C1BCDB1986525900A46ACC /* LuaScriptHandlerMgr.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = LuaScriptHandlerMgr.cpp; sourceTree = "<group>"; };
		15C1BCDC1986525900A46ACC /* LuaScriptHandlerMgr.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LuaScriptHandlerMgr.h; sourceTree = "<group>"; };
		15C1BCF21986526C00A46ACC /* CCBProxy.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CCBProxy.cpp; sourceTree = "<group>"; };
		15C1BCF31986526C00A46ACC /* CCBProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCBProxy.h; sourceTree = "<group>"; };
		15C1BCF41986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_cocosbuilder_manual.cpp; sourceTree = "<group>"; };
		15C1BCF51986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_cocosbuilder_manual.h; sourceTree = "<group>"; };
		15C1BCFE1986526C00A46ACC /* lua_cocos2dx_extension_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_extension_manual.cpp; sourceTree = "<group>"; };
		15C1BCFF1986526C00A46ACC /* lua_cocos2dx_extension_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_extension_manual.h; sourceTree = "<group>"; };
		15C1BD011986526C00A46ACC /* lua_extensions.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = lua_extensions.c; sourceTree = "<group>"; };
		15C1BD021986526C00A46ACC /* lua_extensions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_extensions.h; sourceTree = "<group>"; };
		15C1BD031986526C00A46ACC /* Lua_web_socket.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Lua_web_socket.cpp; sourceTree = "<group>"; };
		15C1BD041986526C00A46ACC /* Lua_web_socket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Lua_web_socket.h; sourceTree = "<group>"; };
		15C1BD051986526C00A46ACC /* lua_xml_http_request.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_xml_http_request.cpp; sourceTree = "<group>"; };
		15C1BD061986526C00A46ACC /* lua_xml_http_request.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_xml_http_request.h; sourceTree = "<group>"; };
		15C1BD151986526C00A46ACC /* lua_cocos2dx_spine_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_spine_manual.cpp; sourceTree = "<group>"; };
		15C1BD161986526C00A46ACC /* lua_cocos2dx_spine_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_spine_manual.hpp; sourceTree = "<group>"; };
		15C1BD171986526C00A46ACC /* LuaSkeletonAnimation.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = LuaSkeletonAnimation.cpp; sourceTree = "<group>"; };
		15C1BD181986526C00A46ACC /* LuaSkeletonAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LuaSkeletonAnimation.h; sourceTree = "<group>"; };
		15C1BD1A1986526C00A46ACC /* lua_cocos2dx_ui_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_ui_manual.cpp; sourceTree = "<group>"; };
		15C1BD1B1986526C00A46ACC /* lua_cocos2dx_ui_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_ui_manual.hpp; sourceTree = "<group>"; };
		15C1C24B1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_experimental_video_manual.cpp; sourceTree = "<group>"; };
		15C1C24C1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_experimental_video_manual.hpp; sourceTree = "<group>"; };
		15C1C24F198747E400A46ACC /* LuaOpengl.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = LuaOpengl.cpp; sourceTree = "<group>"; };
		15C1C250198747E400A46ACC /* LuaOpengl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LuaOpengl.h; sourceTree = "<group>"; };
		15C1C2CC198748D200A46ACC /* libluacocos2d Mac.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libluacocos2d Mac.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		15C9A10C1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_experimental_webview_auto.cpp; sourceTree = "<group>"; };
		15C9A10D1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_experimental_webview_auto.hpp; sourceTree = "<group>"; };
		15C9A1101AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_experimental_webview_manual.cpp; sourceTree = "<group>"; };
		15C9A1111AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_experimental_webview_manual.hpp; sourceTree = "<group>"; };
		15EFA616198B2DAA000C57D3 /* libluacocos2d iOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libluacocos2d iOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		15EFA694198B668C000C57D3 /* lua_cocos2dx_network_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_network_manual.cpp; sourceTree = "<group>"; };
		15EFA695198B668C000C57D3 /* lua_cocos2dx_network_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_network_manual.h; sourceTree = "<group>"; };
		1A262AB718BEEF5900D2DB92 /* tolua_fix.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = tolua_fix.cpp; sourceTree = "<group>"; };
		1AACE74918BC45C200215002 /* lua_cocos2dx_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; lineEnding = 0; path = lua_cocos2dx_auto.cpp; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.cpp; };
		1AACE74A18BC45C200215002 /* lua_cocos2dx_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_auto.hpp; sourceTree = "<group>"; };
		1AACE74C18BC45C200215002 /* lua_cocos2dx_extension_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_extension_auto.cpp; sourceTree = "<group>"; };
		1AACE74D18BC45C200215002 /* lua_cocos2dx_extension_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_extension_auto.hpp; sourceTree = "<group>"; };
		1AACE75218BC45C200215002 /* lua_cocos2dx_physics_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_physics_auto.cpp; sourceTree = "<group>"; };
		1AACE75318BC45C200215002 /* lua_cocos2dx_physics_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_physics_auto.hpp; sourceTree = "<group>"; };
		1AACE75518BC45C200215002 /* lua_cocos2dx_spine_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_spine_auto.cpp; sourceTree = "<group>"; };
		1AACE75618BC45C200215002 /* lua_cocos2dx_spine_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_spine_auto.hpp; sourceTree = "<group>"; };
		1AACE75818BC45C200215002 /* lua_cocos2dx_studio_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_studio_auto.cpp; sourceTree = "<group>"; };
		1AACE75918BC45C200215002 /* lua_cocos2dx_studio_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_studio_auto.hpp; sourceTree = "<group>"; };
		1AACE76018BC45C200215002 /* CCLuaBridge.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CCLuaBridge.cpp; sourceTree = "<group>"; };
		1AACE76118BC45C200215002 /* CCLuaBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCLuaBridge.h; sourceTree = "<group>"; };
		1AACE76218BC45C200215002 /* CCLuaEngine.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CCLuaEngine.cpp; sourceTree = "<group>"; };
		1AACE76318BC45C200215002 /* CCLuaEngine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCLuaEngine.h; sourceTree = "<group>"; };
		1AACE76418BC45C200215002 /* CCLuaStack.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CCLuaStack.cpp; sourceTree = "<group>"; };
		1AACE76518BC45C200215002 /* CCLuaStack.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCLuaStack.h; sourceTree = "<group>"; };
		1AACE76618BC45C200215002 /* CCLuaValue.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CCLuaValue.cpp; sourceTree = "<group>"; };
		1AACE76718BC45C200215002 /* CCLuaValue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCLuaValue.h; sourceTree = "<group>"; };
		1AACE76818BC45C200215002 /* Cocos2dxLuaLoader.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = Cocos2dxLuaLoader.cpp; sourceTree = "<group>"; };
		1AACE76918BC45C200215002 /* Cocos2dxLuaLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Cocos2dxLuaLoader.h; sourceTree = "<group>"; };
		1AACE77E18BC45C200215002 /* LuaBasicConversions.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = LuaBasicConversions.cpp; sourceTree = "<group>"; };
		1AACE77F18BC45C200215002 /* LuaBasicConversions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LuaBasicConversions.h; sourceTree = "<group>"; };
		1AACE78E18BC45C200215002 /* CCLuaObjcBridge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCLuaObjcBridge.h; sourceTree = "<group>"; };
		1AACE78F18BC45C200215002 /* CCLuaObjcBridge.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = CCLuaObjcBridge.mm; sourceTree = "<group>"; };
		1AACE7B418BC45C200215002 /* tolua_fix.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = tolua_fix.h; sourceTree = "<group>"; };
		1ABCA1E618CD8F470087CE3A /* lauxlib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lauxlib.h; sourceTree = "<group>"; };
		1ABCA1E718CD8F470087CE3A /* lua.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua.h; sourceTree = "<group>"; };
		1ABCA1E818CD8F470087CE3A /* luaconf.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = luaconf.h; sourceTree = "<group>"; };
		1ABCA1E918CD8F470087CE3A /* lualib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lualib.h; sourceTree = "<group>"; };
		1ABCA1F318CD8F540087CE3A /* libluajit.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libluajit.a; sourceTree = "<group>"; };
		1ABCA1F618CD8F5F0087CE3A /* libluajit.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libluajit.a; sourceTree = "<group>"; };
		1ABCA1F918CD8F6E0087CE3A /* tolua_event.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = tolua_event.c; path = ../../../../external/lua/tolua/tolua_event.c; sourceTree = "<group>"; };
		1ABCA1FA18CD8F6E0087CE3A /* tolua_event.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = tolua_event.h; path = ../../../../external/lua/tolua/tolua_event.h; sourceTree = "<group>"; };
		1ABCA1FB18CD8F6E0087CE3A /* tolua_is.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = tolua_is.c; path = ../../../../external/lua/tolua/tolua_is.c; sourceTree = "<group>"; };
		1ABCA1FC18CD8F6E0087CE3A /* tolua_map.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = tolua_map.c; path = ../../../../external/lua/tolua/tolua_map.c; sourceTree = "<group>"; };
		1ABCA1FD18CD8F6E0087CE3A /* tolua_push.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = tolua_push.c; path = ../../../../external/lua/tolua/tolua_push.c; sourceTree = "<group>"; };
		1ABCA1FE18CD8F6E0087CE3A /* tolua_to.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = tolua_to.c; path = ../../../../external/lua/tolua/tolua_to.c; sourceTree = "<group>"; };
		1ABCA1FF18CD8F6E0087CE3A /* tolua++.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "tolua++.h"; path = "../../../../external/lua/tolua/tolua++.h"; sourceTree = "<group>"; };
		2905FACE18CF12E600240AA3 /* lua_cocos2dx_ui_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_ui_auto.cpp; sourceTree = "<group>"; };
		2905FACF18CF12E600240AA3 /* lua_cocos2dx_ui_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_ui_auto.hpp; sourceTree = "<group>"; };
		37A9097E1FDFEA7D00408A69 /* lua_bole_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_bole_manual.hpp; sourceTree = "<group>"; };
		37A9097F1FDFEA7D00408A69 /* lua_bole.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_bole.cpp; sourceTree = "<group>"; };
		37A909801FDFEA7D00408A69 /* lua_bole.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_bole.h; sourceTree = "<group>"; };
		37A909811FDFEA7D00408A69 /* lua_bole_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_bole_manual.cpp; sourceTree = "<group>"; };
		37A909B21FDFF1FC00408A69 /* lua_bldownload_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_bldownload_auto.cpp; sourceTree = "<group>"; };
		37A909B31FDFF1FC00408A69 /* lua_bldownload_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_bldownload_auto.hpp; sourceTree = "<group>"; };
		37A909B41FDFF1FC00408A69 /* lua_database_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_database_auto.hpp; sourceTree = "<group>"; };
		37A909B51FDFF1FC00408A69 /* lua_database_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_database_auto.cpp; sourceTree = "<group>"; };
		37A90C821FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_coco_studio_manual.hpp; sourceTree = "<group>"; };
		37A90C831FE0DB3800408A69 /* CustomGUIReader.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CustomGUIReader.cpp; sourceTree = "<group>"; };
		37A90C841FE0DB3800408A69 /* CustomGUIReader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CustomGUIReader.h; sourceTree = "<group>"; };
		37A90C851FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_coco_studio_manual.cpp; sourceTree = "<group>"; };
		37A90C921FE1144500408A69 /* lua_cocos2dx_csloader_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_csloader_manual.cpp; sourceTree = "<group>"; };
		37A90C951FE1144500408A69 /* lua_cocos2dx_csloader_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_csloader_manual.hpp; sourceTree = "<group>"; };
		3E2BDB0319C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_audioengine_auto.cpp; sourceTree = "<group>"; };
		3E2BDB0419C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cocos2dx_audioengine_auto.hpp; sourceTree = "<group>"; };
		3E2BDB0819C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cocos2dx_audioengine_manual.cpp; sourceTree = "<group>"; };
		3E2BDB0919C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_cocos2dx_audioengine_manual.h; sourceTree = "<group>"; };
		507B43531C31FA0C0067B53E /* libluacocos2d tvOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libluacocos2d tvOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ADD1C0D21C196B9500733781 /* lua_module_register.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_module_register.cpp; sourceTree = "<group>"; };
		ADD1C0D31C196B9500733781 /* lua_module_register.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lua_module_register.h; sourceTree = "<group>"; };
		ADD1C0D41C196B9500733781 /* Lua-BindingsExport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "Lua-BindingsExport.h"; sourceTree = "<group>"; };
		BA0CBB5C1BB0756F00003364 /* CCComponentLua.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = CCComponentLua.cpp; sourceTree = "<group>"; };
		BA0CBB5D1BB0756F00003364 /* CCComponentLua.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CCComponentLua.h; sourceTree = "<group>"; };
		C0D9BAFA1974D30000EC35BB /* liblua.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = liblua.a; path = ../../../../external/lua/lua/prebuilt/ios/liblua.a; sourceTree = "<group>"; };
		EE6560942E4AEA410066A9AA /* fpconv.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = fpconv.h; sourceTree = "<group>"; };
		EE6560952E4AEA410066A9AA /* fpconv.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = fpconv.c; sourceTree = "<group>"; };
		EE6560962E4AEA410066A9AA /* lua_cjson.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = lua_cjson.h; sourceTree = "<group>"; };
		EE6560972E4AEA410066A9AA /* lua_cjson.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = lua_cjson.c; sourceTree = "<group>"; };
		EE6560982E4AEA410066A9AA /* strbuf.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = strbuf.h; sourceTree = "<group>"; };
		EE6560992E4AEA410066A9AA /* strbuf.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = strbuf.c; sourceTree = "<group>"; };
		EEE63EDC2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; path = lua_cricocos2d_manual.cpp; sourceTree = "<group>"; };
		EEE63EDD2C1FEE5C0042066B /* lua_cricocos2d_manual.hpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.h; path = lua_cricocos2d_manual.hpp; sourceTree = "<group>"; };
		F4FE0D5519ECD00100B8B12B /* luasocket_scripts.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = luasocket_scripts.c; sourceTree = "<group>"; };
		F4FE0D5619ECD00100B8B12B /* luasocket_scripts.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = luasocket_scripts.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		15C1C28E198748D200A46ACC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				15AC69E4198771FF00D17520 /* libluajit.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		15EFA5D7198B2DAA000C57D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				15EFA5D9198B2DAA000C57D3 /* libluajit.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		507B43091C31FA0C0067B53E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				507B430A1C31FA0C0067B53E /* libluajit.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		150983D41B1C3F2D007F3818 /* navmesh */ = {
			isa = PBXGroup;
			children = (
				150983DB1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp */,
				150983DC1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h */,
				150983D51B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp */,
				150983D61B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h */,
			);
			name = navmesh;
			sourceTree = "<group>";
		};
		1540AF54193EC30500717D8E /* xxtea */ = {
			isa = PBXGroup;
			children = (
				1540AF55193EC30500717D8E /* xxtea.cpp */,
				1540AF56193EC30500717D8E /* xxtea.h */,
			);
			name = xxtea;
			path = ../../../../external/xxtea;
			sourceTree = "<group>";
		};
		15427D41198F73F700DC375D /* cocosdenshion */ = {
			isa = PBXGroup;
			children = (
				15427D42198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.cpp */,
				15427D43198F73F700DC375D /* lua_cocos2dx_cocosdenshion_manual.h */,
			);
			path = cocosdenshion;
			sourceTree = "<group>";
		};
		1551A334158F2AB200E66CFE = {
			isa = PBXGroup;
			children = (
				C0D9BAFA1974D30000EC35BB /* liblua.a */,
				1ABCA1E218CD8F230087CE3A /* external */,
				1AACE74818BC45C200215002 /* auto */,
				1AACE75B18BC45C200215002 /* manual */,
				15C1C2CC198748D200A46ACC /* libluacocos2d Mac.a */,
				15EFA616198B2DAA000C57D3 /* libluacocos2d iOS.a */,
				507B43531C31FA0C0067B53E /* libluacocos2d tvOS.a */,
			);
			sourceTree = "<group>";
		};
		158C128419A0FA1300781A76 /* 3d */ = {
			isa = PBXGroup;
			children = (
				158C128519A0FA1300781A76 /* lua_cocos2dx_3d_manual.cpp */,
				158C128619A0FA1300781A76 /* lua_cocos2dx_3d_manual.h */,
			);
			path = 3d;
			sourceTree = "<group>";
		};
		15A561E91B00A3D0005D4720 /* physics3d */ = {
			isa = PBXGroup;
			children = (
				15A561EA1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp */,
				15A561EB1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h */,
			);
			name = physics3d;
			sourceTree = "<group>";
		};
		15C1BCD21986525900A46ACC /* cocos2d */ = {
			isa = PBXGroup;
			children = (
				15C1C24F198747E400A46ACC /* LuaOpengl.cpp */,
				15C1C250198747E400A46ACC /* LuaOpengl.h */,
				15C1BCD31986525900A46ACC /* lua_cocos2dx_deprecated.cpp */,
				15C1BCD41986525900A46ACC /* lua_cocos2dx_deprecated.h */,
				15C1BCD51986525900A46ACC /* lua_cocos2dx_experimental_manual.cpp */,
				15C1BCD61986525900A46ACC /* lua_cocos2dx_experimental_manual.hpp */,
				15C1BCD71986525900A46ACC /* lua_cocos2dx_manual.cpp */,
				15C1BCD81986525900A46ACC /* lua_cocos2dx_manual.hpp */,
				15C1BCD91986525900A46ACC /* lua_cocos2dx_physics_manual.cpp */,
				15C1BCDA1986525900A46ACC /* lua_cocos2dx_physics_manual.hpp */,
				15C1BCDB1986525900A46ACC /* LuaScriptHandlerMgr.cpp */,
				15C1BCDC1986525900A46ACC /* LuaScriptHandlerMgr.h */,
			);
			path = cocos2d;
			sourceTree = "<group>";
		};
		15C1BCF11986526C00A46ACC /* cocosbuilder */ = {
			isa = PBXGroup;
			children = (
				15C1BCF21986526C00A46ACC /* CCBProxy.cpp */,
				15C1BCF31986526C00A46ACC /* CCBProxy.h */,
				15C1BCF41986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.cpp */,
				15C1BCF51986526C00A46ACC /* lua_cocos2dx_cocosbuilder_manual.h */,
			);
			path = cocosbuilder;
			sourceTree = "<group>";
		};
		15C1BCFD1986526C00A46ACC /* extension */ = {
			isa = PBXGroup;
			children = (
				15C1BCFE1986526C00A46ACC /* lua_cocos2dx_extension_manual.cpp */,
				15C1BCFF1986526C00A46ACC /* lua_cocos2dx_extension_manual.h */,
			);
			path = extension;
			sourceTree = "<group>";
		};
		15C1BD001986526C00A46ACC /* network */ = {
			isa = PBXGroup;
			children = (
				15EFA694198B668C000C57D3 /* lua_cocos2dx_network_manual.cpp */,
				15EFA695198B668C000C57D3 /* lua_cocos2dx_network_manual.h */,
				15C1BD011986526C00A46ACC /* lua_extensions.c */,
				15C1BD021986526C00A46ACC /* lua_extensions.h */,
				15C1BD031986526C00A46ACC /* Lua_web_socket.cpp */,
				15C1BD041986526C00A46ACC /* Lua_web_socket.h */,
				15C1BD051986526C00A46ACC /* lua_xml_http_request.cpp */,
				15C1BD061986526C00A46ACC /* lua_xml_http_request.h */,
			);
			path = network;
			sourceTree = "<group>";
		};
		15C1BD141986526C00A46ACC /* spine */ = {
			isa = PBXGroup;
			children = (
				15C1BD151986526C00A46ACC /* lua_cocos2dx_spine_manual.cpp */,
				15C1BD161986526C00A46ACC /* lua_cocos2dx_spine_manual.hpp */,
				15C1BD171986526C00A46ACC /* LuaSkeletonAnimation.cpp */,
				15C1BD181986526C00A46ACC /* LuaSkeletonAnimation.h */,
			);
			path = spine;
			sourceTree = "<group>";
		};
		15C1BD191986526C00A46ACC /* ui */ = {
			isa = PBXGroup;
			children = (
				15C9A1101AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.cpp */,
				15C9A1111AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.hpp */,
				15C1C24B1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.cpp */,
				15C1C24C1987473200A46ACC /* lua_cocos2dx_experimental_video_manual.hpp */,
				15C1BD1A1986526C00A46ACC /* lua_cocos2dx_ui_manual.cpp */,
				15C1BD1B1986526C00A46ACC /* lua_cocos2dx_ui_manual.hpp */,
			);
			path = ui;
			sourceTree = "<group>";
		};
		15EFA400198B2AB2000C57D3 /* cocostudio */ = {
			isa = PBXGroup;
			children = (
				37A90C921FE1144500408A69 /* lua_cocos2dx_csloader_manual.cpp */,
				37A90C951FE1144500408A69 /* lua_cocos2dx_csloader_manual.hpp */,
				37A90C831FE0DB3800408A69 /* CustomGUIReader.cpp */,
				37A90C841FE0DB3800408A69 /* CustomGUIReader.h */,
				37A90C851FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.cpp */,
				37A90C821FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.hpp */,
			);
			path = cocostudio;
			sourceTree = "<group>";
		};
		1AACE74818BC45C200215002 /* auto */ = {
			isa = PBXGroup;
			children = (
				37A909B21FDFF1FC00408A69 /* lua_bldownload_auto.cpp */,
				37A909B31FDFF1FC00408A69 /* lua_bldownload_auto.hpp */,
				37A909B51FDFF1FC00408A69 /* lua_database_auto.cpp */,
				37A909B41FDFF1FC00408A69 /* lua_database_auto.hpp */,
				150983CE1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp */,
				150983CF1B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp */,
				15A561E31B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp */,
				15A561E41B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp */,
				15C9A10C1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.cpp */,
				15C9A10D1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.hpp */,
				159552381A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp */,
				159552391A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp */,
				3E2BDB0319C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp */,
				3E2BDB0419C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp */,
				1516227F19A0F3E3006099B8 /* lua_cocos2dx_3d_auto.cpp */,
				1516228019A0F3E3006099B8 /* lua_cocos2dx_3d_auto.hpp */,
				15C1BCC019864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.cpp */,
				15C1BCC119864D8700A46ACC /* lua_cocos2dx_cocosbuilder_auto.hpp */,
				15C1BCC219864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.cpp */,
				15C1BCC319864D8700A46ACC /* lua_cocos2dx_cocosdenshion_auto.hpp */,
				156EADF11977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.cpp */,
				156EADF21977A2BA00F53709 /* lua_cocos2dx_experimental_video_auto.hpp */,
				15622967197780DE009C9067 /* lua_cocos2dx_experimental_auto.cpp */,
				15622968197780DE009C9067 /* lua_cocos2dx_experimental_auto.hpp */,
				2905FACE18CF12E600240AA3 /* lua_cocos2dx_ui_auto.cpp */,
				2905FACF18CF12E600240AA3 /* lua_cocos2dx_ui_auto.hpp */,
				1AACE74918BC45C200215002 /* lua_cocos2dx_auto.cpp */,
				1AACE74A18BC45C200215002 /* lua_cocos2dx_auto.hpp */,
				1AACE74C18BC45C200215002 /* lua_cocos2dx_extension_auto.cpp */,
				1AACE74D18BC45C200215002 /* lua_cocos2dx_extension_auto.hpp */,
				1AACE75218BC45C200215002 /* lua_cocos2dx_physics_auto.cpp */,
				1AACE75318BC45C200215002 /* lua_cocos2dx_physics_auto.hpp */,
				1AACE75518BC45C200215002 /* lua_cocos2dx_spine_auto.cpp */,
				1AACE75618BC45C200215002 /* lua_cocos2dx_spine_auto.hpp */,
				1AACE75818BC45C200215002 /* lua_cocos2dx_studio_auto.cpp */,
				1AACE75918BC45C200215002 /* lua_cocos2dx_studio_auto.hpp */,
			);
			name = auto;
			path = ../auto;
			sourceTree = "<group>";
		};
		1AACE75B18BC45C200215002 /* manual */ = {
			isa = PBXGroup;
			children = (
				37A909811FDFEA7D00408A69 /* lua_bole_manual.cpp */,
				37A9097E1FDFEA7D00408A69 /* lua_bole_manual.hpp */,
				37A9097F1FDFEA7D00408A69 /* lua_bole.cpp */,
				37A909801FDFEA7D00408A69 /* lua_bole.h */,
				EEE63EDC2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp */,
				EEE63EDD2C1FEE5C0042066B /* lua_cricocos2d_manual.hpp */,
				150983D41B1C3F2D007F3818 /* navmesh */,
				15A561E91B00A3D0005D4720 /* physics3d */,
				3E2BDB0719C5E6100055CDCD /* audioengine */,
				158C128419A0FA1300781A76 /* 3d */,
				15427D41198F73F700DC375D /* cocosdenshion */,
				15EFA400198B2AB2000C57D3 /* cocostudio */,
				15C1BCF11986526C00A46ACC /* cocosbuilder */,
				15C1BCFD1986526C00A46ACC /* extension */,
				15C1BD001986526C00A46ACC /* network */,
				15C1BD141986526C00A46ACC /* spine */,
				15C1BD191986526C00A46ACC /* ui */,
				15C1BCD21986525900A46ACC /* cocos2d */,
				ADD1C0D21C196B9500733781 /* lua_module_register.cpp */,
				ADD1C0D31C196B9500733781 /* lua_module_register.h */,
				ADD1C0D41C196B9500733781 /* Lua-BindingsExport.h */,
				1AACE76018BC45C200215002 /* CCLuaBridge.cpp */,
				1AACE76118BC45C200215002 /* CCLuaBridge.h */,
				1AACE76218BC45C200215002 /* CCLuaEngine.cpp */,
				1AACE76318BC45C200215002 /* CCLuaEngine.h */,
				1AACE76418BC45C200215002 /* CCLuaStack.cpp */,
				1AACE76518BC45C200215002 /* CCLuaStack.h */,
				1AACE76618BC45C200215002 /* CCLuaValue.cpp */,
				1AACE76718BC45C200215002 /* CCLuaValue.h */,
				1AACE76818BC45C200215002 /* Cocos2dxLuaLoader.cpp */,
				1AACE76918BC45C200215002 /* Cocos2dxLuaLoader.h */,
				BA0CBB5C1BB0756F00003364 /* CCComponentLua.cpp */,
				BA0CBB5D1BB0756F00003364 /* CCComponentLua.h */,
				1AACE77E18BC45C200215002 /* LuaBasicConversions.cpp */,
				1AACE77F18BC45C200215002 /* LuaBasicConversions.h */,
				1AACE78618BC45C200215002 /* platform */,
				1A262AB718BEEF5900D2DB92 /* tolua_fix.cpp */,
				1AACE7B418BC45C200215002 /* tolua_fix.h */,
			);
			name = manual;
			path = ../manual;
			sourceTree = "<group>";
		};
		1AACE78618BC45C200215002 /* platform */ = {
			isa = PBXGroup;
			children = (
				1AACE78D18BC45C200215002 /* ios */,
			);
			path = platform;
			sourceTree = "<group>";
		};
		1AACE78D18BC45C200215002 /* ios */ = {
			isa = PBXGroup;
			children = (
				1AACE78E18BC45C200215002 /* CCLuaObjcBridge.h */,
				1AACE78F18BC45C200215002 /* CCLuaObjcBridge.mm */,
			);
			path = ios;
			sourceTree = "<group>";
		};
		1ABCA1E218CD8F230087CE3A /* external */ = {
			isa = PBXGroup;
			children = (
				1ABCA1E318CD8F2D0087CE3A /* luajit */,
				1ABCA20E18CD8F7D0087CE3A /* luasocket */,
				1ABCA1E418CD8F330087CE3A /* tolua */,
				1540AF54193EC30500717D8E /* xxtea */,
				EE65609A2E4AEA410066A9AA /* cjson */,
			);
			name = external;
			sourceTree = "<group>";
		};
		1ABCA1E318CD8F2D0087CE3A /* luajit */ = {
			isa = PBXGroup;
			children = (
				1ABCA1E518CD8F470087CE3A /* include */,
				1ABCA1F218CD8F540087CE3A /* ios */,
				1ABCA1F518CD8F5F0087CE3A /* mac */,
			);
			name = luajit;
			sourceTree = "<group>";
		};
		1ABCA1E418CD8F330087CE3A /* tolua */ = {
			isa = PBXGroup;
			children = (
				1ABCA1F918CD8F6E0087CE3A /* tolua_event.c */,
				1ABCA1FA18CD8F6E0087CE3A /* tolua_event.h */,
				1ABCA1FB18CD8F6E0087CE3A /* tolua_is.c */,
				1ABCA1FC18CD8F6E0087CE3A /* tolua_map.c */,
				1ABCA1FD18CD8F6E0087CE3A /* tolua_push.c */,
				1ABCA1FE18CD8F6E0087CE3A /* tolua_to.c */,
				1ABCA1FF18CD8F6E0087CE3A /* tolua++.h */,
			);
			name = tolua;
			sourceTree = "<group>";
		};
		1ABCA1E518CD8F470087CE3A /* include */ = {
			isa = PBXGroup;
			children = (
				1ABCA1E618CD8F470087CE3A /* lauxlib.h */,
				1ABCA1E718CD8F470087CE3A /* lua.h */,
				1ABCA1E818CD8F470087CE3A /* luaconf.h */,
				1ABCA1E918CD8F470087CE3A /* lualib.h */,
			);
			name = include;
			path = ../../../../external/lua/luajit/include;
			sourceTree = "<group>";
		};
		1ABCA1F218CD8F540087CE3A /* ios */ = {
			isa = PBXGroup;
			children = (
				1ABCA1F318CD8F540087CE3A /* libluajit.a */,
			);
			name = ios;
			path = ../../../../external/lua/luajit/prebuilt/ios;
			sourceTree = "<group>";
		};
		1ABCA1F518CD8F5F0087CE3A /* mac */ = {
			isa = PBXGroup;
			children = (
				1ABCA1F618CD8F5F0087CE3A /* libluajit.a */,
			);
			name = mac;
			path = ../../../../external/lua/luajit/prebuilt/mac;
			sourceTree = "<group>";
		};
		1ABCA20E18CD8F7D0087CE3A /* luasocket */ = {
			isa = PBXGroup;
			children = (
				15415A8719A71A53004F1E71 /* auxiliar.c */,
				15415A8819A71A53004F1E71 /* auxiliar.h */,
				15415A8919A71A53004F1E71 /* buffer.c */,
				15415A8A19A71A53004F1E71 /* buffer.h */,
				15415A8B19A71A53004F1E71 /* except.c */,
				15415A8C19A71A53004F1E71 /* except.h */,
				15415A8D19A71A53004F1E71 /* inet.c */,
				15415A8E19A71A53004F1E71 /* inet.h */,
				15415A8F19A71A53004F1E71 /* io.c */,
				15415A9019A71A53004F1E71 /* io.h */,
				F4FE0D5519ECD00100B8B12B /* luasocket_scripts.c */,
				F4FE0D5619ECD00100B8B12B /* luasocket_scripts.h */,
				15415A9119A71A53004F1E71 /* luasocket.c */,
				15415A9219A71A53004F1E71 /* luasocket.h */,
				15415A9319A71A53004F1E71 /* mime.c */,
				15415A9419A71A53004F1E71 /* mime.h */,
				15415A9519A71A53004F1E71 /* options.c */,
				15415A9619A71A53004F1E71 /* options.h */,
				15415A9719A71A53004F1E71 /* select.c */,
				15415A9819A71A53004F1E71 /* select.h */,
				15415A9919A71A53004F1E71 /* serial.c */,
				15415A9A19A71A53004F1E71 /* socket.h */,
				15415A9B19A71A53004F1E71 /* tcp.c */,
				15415A9C19A71A53004F1E71 /* tcp.h */,
				15415A9D19A71A53004F1E71 /* timeout.c */,
				15415A9E19A71A53004F1E71 /* timeout.h */,
				15415A9F19A71A53004F1E71 /* udp.c */,
				15415AA019A71A53004F1E71 /* udp.h */,
				15415AA119A71A53004F1E71 /* unix.c */,
				15415AA219A71A53004F1E71 /* unix.h */,
				15415AA319A71A53004F1E71 /* usocket.c */,
				15415AA419A71A53004F1E71 /* usocket.h */,
			);
			name = luasocket;
			path = ../../../../external/lua/luasocket;
			sourceTree = "<group>";
		};
		3E2BDB0719C5E6100055CDCD /* audioengine */ = {
			isa = PBXGroup;
			children = (
				3E2BDB0819C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp */,
				3E2BDB0919C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h */,
			);
			path = audioengine;
			sourceTree = "<group>";
		};
		EE65609A2E4AEA410066A9AA /* cjson */ = {
			isa = PBXGroup;
			children = (
				EE6560942E4AEA410066A9AA /* fpconv.h */,
				EE6560952E4AEA410066A9AA /* fpconv.c */,
				EE6560962E4AEA410066A9AA /* lua_cjson.h */,
				EE6560972E4AEA410066A9AA /* lua_cjson.c */,
				EE6560982E4AEA410066A9AA /* strbuf.h */,
				EE6560992E4AEA410066A9AA /* strbuf.c */,
			);
			name = cjson;
			path = ../../../../external/lua/cjson;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		15C1C290198748D200A46ACC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				15EFA1F71989E582000C57D3 /* lua_cocos2dx_experimental_auto.hpp in Headers */,
				15415AA719A71A53004F1E71 /* auxiliar.h in Headers */,
				37A90C991FE1144500408A69 /* lua_cocos2dx_csloader_manual.hpp in Headers */,
				155C7E1019A71C9D00F08B25 /* lua_extensions.h in Headers */,
				15A561E71B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp in Headers */,
				15415ABF19A71A53004F1E71 /* mime.h in Headers */,
				15AC69E21987712500D17520 /* tolua_event.h in Headers */,
				15415AD719A71A53004F1E71 /* udp.h in Headers */,
				EEE63EE12C1FEE5C0042066B /* lua_cricocos2d_manual.hpp in Headers */,
				15AC69E31987712500D17520 /* tolua++.h in Headers */,
				15415ADB19A71A53004F1E71 /* unix.h in Headers */,
				155C7DF419A71C3700F08B25 /* lua_cocos2dx_3d_manual.h in Headers */,
				15415ABB19A71A53004F1E71 /* luasocket.h in Headers */,
				155C7E2419A71CD800F08B25 /* lua_cocos2dx_experimental_video_manual.hpp in Headers */,
				15415ACF19A71A53004F1E71 /* tcp.h in Headers */,
				15415AAB19A71A53004F1E71 /* buffer.h in Headers */,
				15AC69DE1987711400D17520 /* lauxlib.h in Headers */,
				155C7E1419A71CA800F08B25 /* Lua_web_socket.h in Headers */,
				ADD1C0D61C196B9500733781 /* lua_module_register.h in Headers */,
				15AC69DF1987711400D17520 /* lua.h in Headers */,
				37A90C881FE0DB3800408A69 /* CustomGUIReader.h in Headers */,
				15AC69E01987711400D17520 /* luaconf.h in Headers */,
				155C7DF819A71C4400F08B25 /* lua_cocos2dx_cocosdenshion_manual.h in Headers */,
				15415AAF19A71A53004F1E71 /* except.h in Headers */,
				150906F319D556D1002C4D97 /* lua_cocos2dx_audioengine_manual.h in Headers */,
				155C7E0419A71C6D00F08B25 /* lua_cocos2dx_cocosbuilder_manual.h in Headers */,
				15415ACB19A71A53004F1E71 /* socket.h in Headers */,
				15415ADF19A71A53004F1E71 /* usocket.h in Headers */,
				15415AC319A71A53004F1E71 /* options.h in Headers */,
				15AC69E11987711400D17520 /* lualib.h in Headers */,
				15C1C2E719874CBE00A46ACC /* CCLuaBridge.h in Headers */,
				15C1C2E819874CBE00A46ACC /* CCLuaEngine.h in Headers */,
				15C1C2E919874CBE00A46ACC /* CCLuaStack.h in Headers */,
				15C1C2EA19874CBE00A46ACC /* CCLuaValue.h in Headers */,
				155C7E0C19A71C9000F08B25 /* lua_cocos2dx_network_manual.h in Headers */,
				15C1C2EB19874CBE00A46ACC /* Cocos2dxLuaLoader.h in Headers */,
				15C1C2EC19874CBE00A46ACC /* LuaBasicConversions.h in Headers */,
				15415AB319A71A53004F1E71 /* inet.h in Headers */,
				15C1C2ED19874CBE00A46ACC /* CCLuaObjcBridge.h in Headers */,
				15C1C2EE19874CBE00A46ACC /* tolua_fix.h in Headers */,
				155C7E2019A71CCC00F08B25 /* LuaSkeletonAnimation.h in Headers */,
				150983D91B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h in Headers */,
				1595523C1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp in Headers */,
				15415AB719A71A53004F1E71 /* io.h in Headers */,
				150983DF1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h in Headers */,
				15C1C2DC19874B4400A46ACC /* xxtea.h in Headers */,
				15415AD319A71A53004F1E71 /* timeout.h in Headers */,
				15C1C2D5198749BC00A46ACC /* LuaOpengl.h in Headers */,
				15A561EE1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h in Headers */,
				ADD1C0D71C196B9500733781 /* Lua-BindingsExport.h in Headers */,
				155C7E2819A71CE600F08B25 /* lua_cocos2dx_ui_manual.hpp in Headers */,
				15C1C2D6198749BC00A46ACC /* lua_cocos2dx_deprecated.h in Headers */,
				15C1C2D7198749BC00A46ACC /* lua_cocos2dx_experimental_manual.hpp in Headers */,
				37A90C861FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.hpp in Headers */,
				15C1C2D8198749BC00A46ACC /* lua_cocos2dx_manual.hpp in Headers */,
				155C7E0819A71C7B00F08B25 /* lua_cocos2dx_extension_manual.h in Headers */,
				155C7E1819A71CB300F08B25 /* lua_xml_http_request.h in Headers */,
				150983D21B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp in Headers */,
				15C1C2D9198749BC00A46ACC /* lua_cocos2dx_physics_manual.hpp in Headers */,
				15C1C2DA198749BC00A46ACC /* LuaScriptHandlerMgr.h in Headers */,
				15AC69D619876EA200D17520 /* lua_cocos2dx_physics_auto.hpp in Headers */,
				155C7E0019A71C6000F08B25 /* CCBProxy.h in Headers */,
				15415AC719A71A53004F1E71 /* select.h in Headers */,
				155C7E1C19A71CC200F08B25 /* lua_cocos2dx_spine_manual.hpp in Headers */,
				BA0CBB601BB0756F00003364 /* CCComponentLua.h in Headers */,
				15C1C2D41987499F00A46ACC /* lua_cocos2dx_auto.hpp in Headers */,
				EE6560A72E4AEA410066A9AA /* fpconv.h in Headers */,
				EE6560A82E4AEA410066A9AA /* lua_cjson.h in Headers */,
				EE6560A92E4AEA410066A9AA /* strbuf.h in Headers */,
				150906F119D556C8002C4D97 /* lua_cocos2dx_audioengine_auto.hpp in Headers */,
				F4FE0D5919ECD00100B8B12B /* luasocket_scripts.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		15EFA5DA198B2DAA000C57D3 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				150983E01B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.h in Headers */,
				15EFA64D198B3342000C57D3 /* lauxlib.h in Headers */,
				15415AA819A71A53004F1E71 /* auxiliar.h in Headers */,
				155C7E1119A71C9F00F08B25 /* lua_extensions.h in Headers */,
				15415AC019A71A53004F1E71 /* mime.h in Headers */,
				15EFA64E198B3342000C57D3 /* lua.h in Headers */,
				15415AD819A71A53004F1E71 /* udp.h in Headers */,
				15EFA64F198B3342000C57D3 /* luaconf.h in Headers */,
				15415ADC19A71A53004F1E71 /* unix.h in Headers */,
				155C7DF519A71C3900F08B25 /* lua_cocos2dx_3d_manual.h in Headers */,
				15415ABC19A71A53004F1E71 /* luasocket.h in Headers */,
				155C7E2519A71CDA00F08B25 /* lua_cocos2dx_experimental_video_manual.hpp in Headers */,
				15415AD019A71A53004F1E71 /* tcp.h in Headers */,
				BA0CBB611BB0756F00003364 /* CCComponentLua.h in Headers */,
				37A909BD1FDFF24700408A69 /* lua_database_auto.hpp in Headers */,
				37A909BF1FDFF25600408A69 /* lua_bole_manual.hpp in Headers */,
				15415AAC19A71A53004F1E71 /* buffer.h in Headers */,
				15EFA650198B3342000C57D3 /* lualib.h in Headers */,
				155C7E1519A71CAA00F08B25 /* Lua_web_socket.h in Headers */,
				15EFA64B198B3320000C57D3 /* tolua_event.h in Headers */,
				15A561EF1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.h in Headers */,
				15EFA64C198B3320000C57D3 /* tolua++.h in Headers */,
				155C7DF919A71C4500F08B25 /* lua_cocos2dx_cocosdenshion_manual.h in Headers */,
				15415AB019A71A53004F1E71 /* except.h in Headers */,
				3E2BDB0B19C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.h in Headers */,
				155C7E0519A71C6F00F08B25 /* lua_cocos2dx_cocosbuilder_manual.h in Headers */,
				15415ACC19A71A53004F1E71 /* socket.h in Headers */,
				15415AE019A71A53004F1E71 /* usocket.h in Headers */,
				15415AC419A71A53004F1E71 /* options.h in Headers */,
				15EFA644198B32D5000C57D3 /* xxtea.h in Headers */,
				15EFA63B198B32BB000C57D3 /* CCLuaBridge.h in Headers */,
				15EFA63C198B32BB000C57D3 /* CCLuaEngine.h in Headers */,
				15EFA63D198B32BB000C57D3 /* CCLuaStack.h in Headers */,
				15EFA63E198B32BB000C57D3 /* CCLuaValue.h in Headers */,
				155C7E0D19A71C9300F08B25 /* lua_cocos2dx_network_manual.h in Headers */,
				15EFA63F198B32BB000C57D3 /* Cocos2dxLuaLoader.h in Headers */,
				37A909BB1FDFF24700408A69 /* lua_bldownload_auto.hpp in Headers */,
				15EFA640198B32BB000C57D3 /* LuaBasicConversions.h in Headers */,
				150983D31B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.hpp in Headers */,
				15415AB419A71A53004F1E71 /* inet.h in Headers */,
				15EFA641198B32BB000C57D3 /* CCLuaObjcBridge.h in Headers */,
				37A909C11FDFF25600408A69 /* lua_bole.h in Headers */,
				15EFA642198B32BB000C57D3 /* tolua_fix.h in Headers */,
				155C7E2119A71CCE00F08B25 /* LuaSkeletonAnimation.h in Headers */,
				1595523D1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.hpp in Headers */,
				15415AB819A71A53004F1E71 /* io.h in Headers */,
				37A90C9B1FE1147D00408A69 /* lua_cocos2dx_csloader_manual.hpp in Headers */,
				15EFA62B198B3220000C57D3 /* LuaOpengl.h in Headers */,
				EEE63EE22C1FEE5C0042066B /* lua_cricocos2d_manual.hpp in Headers */,
				15415AD419A71A53004F1E71 /* timeout.h in Headers */,
				15EFA62C198B3220000C57D3 /* lua_cocos2dx_deprecated.h in Headers */,
				155C7E2919A71CE800F08B25 /* lua_cocos2dx_ui_manual.hpp in Headers */,
				37A90C911FE10C2100408A69 /* lua_cocos2dx_coco_studio_manual.hpp in Headers */,
				15EFA62D198B3220000C57D3 /* lua_cocos2dx_experimental_manual.hpp in Headers */,
				37A90C8F1FE10C2100408A69 /* CustomGUIReader.h in Headers */,
				15C9A1131AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.hpp in Headers */,
				15EFA62E198B3220000C57D3 /* lua_cocos2dx_manual.hpp in Headers */,
				15C9A10F1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.hpp in Headers */,
				150983DA1B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.h in Headers */,
				15EFA62F198B3220000C57D3 /* lua_cocos2dx_physics_manual.hpp in Headers */,
				155C7E0919A71C8500F08B25 /* lua_cocos2dx_extension_manual.h in Headers */,
				155C7E1919A71CB500F08B25 /* lua_xml_http_request.h in Headers */,
				15EFA630198B3220000C57D3 /* LuaScriptHandlerMgr.h in Headers */,
				15EFA622198B2E74000C57D3 /* lua_cocos2dx_experimental_auto.hpp in Headers */,
				15EFA623198B2E74000C57D3 /* lua_cocos2dx_auto.hpp in Headers */,
				155C7E0119A71C6300F08B25 /* CCBProxy.h in Headers */,
				15A561E81B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.hpp in Headers */,
				EE6560A12E4AEA410066A9AA /* fpconv.h in Headers */,
				EE6560A22E4AEA410066A9AA /* lua_cjson.h in Headers */,
				EE6560A32E4AEA410066A9AA /* strbuf.h in Headers */,
				15415AC819A71A53004F1E71 /* select.h in Headers */,
				155C7E1D19A71CC300F08B25 /* lua_cocos2dx_spine_manual.hpp in Headers */,
				15EFA624198B2E74000C57D3 /* lua_cocos2dx_physics_auto.hpp in Headers */,
				3E2BDB0619C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.hpp in Headers */,
				F4FE0D5A19ECD00100B8B12B /* luasocket_scripts.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		507B430B1C31FA0C0067B53E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				507B430C1C31FA0C0067B53E /* lua_cocos2dx_navmesh_conversions.h in Headers */,
				507B430D1C31FA0C0067B53E /* lauxlib.h in Headers */,
				507B430E1C31FA0C0067B53E /* auxiliar.h in Headers */,
				EE65609B2E4AEA410066A9AA /* fpconv.h in Headers */,
				EE65609C2E4AEA410066A9AA /* lua_cjson.h in Headers */,
				EE65609D2E4AEA410066A9AA /* strbuf.h in Headers */,
				507B430F1C31FA0C0067B53E /* lua_extensions.h in Headers */,
				507B43101C31FA0C0067B53E /* mime.h in Headers */,
				507B43111C31FA0C0067B53E /* lua.h in Headers */,
				507B43121C31FA0C0067B53E /* udp.h in Headers */,
				507B43131C31FA0C0067B53E /* luaconf.h in Headers */,
				507B43141C31FA0C0067B53E /* unix.h in Headers */,
				507B43151C31FA0C0067B53E /* lua_cocos2dx_3d_manual.h in Headers */,
				507B43161C31FA0C0067B53E /* luasocket.h in Headers */,
				507B43181C31FA0C0067B53E /* lua_cocos2dx_experimental_video_manual.hpp in Headers */,
				507B43191C31FA0C0067B53E /* tcp.h in Headers */,
				507B431A1C31FA0C0067B53E /* CCComponentLua.h in Headers */,
				507B431B1C31FA0C0067B53E /* buffer.h in Headers */,
				507B431C1C31FA0C0067B53E /* lualib.h in Headers */,
				507B431D1C31FA0C0067B53E /* Lua_web_socket.h in Headers */,
				507B431E1C31FA0C0067B53E /* tolua_event.h in Headers */,
				507B431F1C31FA0C0067B53E /* lua_cocos2dx_physics3d_manual.h in Headers */,
				507B43201C31FA0C0067B53E /* tolua++.h in Headers */,
				507B43211C31FA0C0067B53E /* lua_cocos2dx_cocosdenshion_manual.h in Headers */,
				507B43221C31FA0C0067B53E /* except.h in Headers */,
				507B43231C31FA0C0067B53E /* lua_cocos2dx_audioengine_manual.h in Headers */,
				507B43241C31FA0C0067B53E /* lua_cocos2dx_cocosbuilder_manual.h in Headers */,
				507B43251C31FA0C0067B53E /* socket.h in Headers */,
				507B43261C31FA0C0067B53E /* usocket.h in Headers */,
				507B43271C31FA0C0067B53E /* options.h in Headers */,
				507B43281C31FA0C0067B53E /* xxtea.h in Headers */,
				507B43291C31FA0C0067B53E /* CCLuaBridge.h in Headers */,
				507B432A1C31FA0C0067B53E /* CCLuaEngine.h in Headers */,
				507B432B1C31FA0C0067B53E /* CCLuaStack.h in Headers */,
				507B432D1C31FA0C0067B53E /* CCLuaValue.h in Headers */,
				507B432E1C31FA0C0067B53E /* lua_cocos2dx_network_manual.h in Headers */,
				507B432F1C31FA0C0067B53E /* Cocos2dxLuaLoader.h in Headers */,
				EEE63EE32C1FEE5C0042066B /* lua_cricocos2d_manual.hpp in Headers */,
				507B43301C31FA0C0067B53E /* LuaBasicConversions.h in Headers */,
				507B43311C31FA0C0067B53E /* lua_cocos2dx_navmesh_auto.hpp in Headers */,
				507B43321C31FA0C0067B53E /* inet.h in Headers */,
				507B43331C31FA0C0067B53E /* CCLuaObjcBridge.h in Headers */,
				507B43341C31FA0C0067B53E /* tolua_fix.h in Headers */,
				507B43351C31FA0C0067B53E /* LuaSkeletonAnimation.h in Headers */,
				507B43361C31FA0C0067B53E /* lua_cocos2dx_csloader_auto.hpp in Headers */,
				507B43371C31FA0C0067B53E /* io.h in Headers */,
				507B43391C31FA0C0067B53E /* LuaOpengl.h in Headers */,
				507B433A1C31FA0C0067B53E /* timeout.h in Headers */,
				507B433B1C31FA0C0067B53E /* lua_cocos2dx_deprecated.h in Headers */,
				507B433C1C31FA0C0067B53E /* lua_cocos2dx_ui_manual.hpp in Headers */,
				507B433D1C31FA0C0067B53E /* lua_cocos2dx_experimental_manual.hpp in Headers */,
				507B433E1C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_manual.hpp in Headers */,
				507B433F1C31FA0C0067B53E /* lua_cocos2dx_manual.hpp in Headers */,
				507B43401C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_auto.hpp in Headers */,
				507B43411C31FA0C0067B53E /* lua_cocos2dx_navmesh_manual.h in Headers */,
				507B43421C31FA0C0067B53E /* lua_cocos2dx_physics_manual.hpp in Headers */,
				507B43431C31FA0C0067B53E /* lua_cocos2dx_extension_manual.h in Headers */,
				507B43441C31FA0C0067B53E /* lua_xml_http_request.h in Headers */,
				507B43461C31FA0C0067B53E /* LuaScriptHandlerMgr.h in Headers */,
				507B43471C31FA0C0067B53E /* lua_cocos2dx_experimental_auto.hpp in Headers */,
				507B43481C31FA0C0067B53E /* lua_cocos2dx_auto.hpp in Headers */,
				507B43491C31FA0C0067B53E /* CCBProxy.h in Headers */,
				507B434A1C31FA0C0067B53E /* lua_cocos2dx_physics3d_auto.hpp in Headers */,
				507B434B1C31FA0C0067B53E /* select.h in Headers */,
				507B434C1C31FA0C0067B53E /* lua_cocos2dx_spine_manual.hpp in Headers */,
				507B434D1C31FA0C0067B53E /* lua_cocos2dx_physics_auto.hpp in Headers */,
				507B434E1C31FA0C0067B53E /* lua_cocos2dx_audioengine_auto.hpp in Headers */,
				507B434F1C31FA0C0067B53E /* luasocket_scripts.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		15C1C255198748D200A46ACC /* libluacocos2d Mac */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 15C1C2C9198748D200A46ACC /* Build configuration list for PBXNativeTarget "libluacocos2d Mac" */;
			buildPhases = (
				15C1C256198748D200A46ACC /* Sources */,
				15C1C28E198748D200A46ACC /* Frameworks */,
				15C1C290198748D200A46ACC /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "libluacocos2d Mac";
			productName = cocos2dx;
			productReference = 15C1C2CC198748D200A46ACC /* libluacocos2d Mac.a */;
			productType = "com.apple.product-type.library.static";
		};
		15EFA59E198B2DAA000C57D3 /* libluacocos2d iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 15EFA613198B2DAA000C57D3 /* Build configuration list for PBXNativeTarget "libluacocos2d iOS" */;
			buildPhases = (
				15EFA59F198B2DAA000C57D3 /* Sources */,
				15EFA5D7198B2DAA000C57D3 /* Frameworks */,
				15EFA5DA198B2DAA000C57D3 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "libluacocos2d iOS";
			productName = cocos2dx;
			productReference = 15EFA616198B2DAA000C57D3 /* libluacocos2d iOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		507B42BB1C31FA0C0067B53E /* libluacocos2d tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 507B43501C31FA0C0067B53E /* Build configuration list for PBXNativeTarget "libluacocos2d tvOS" */;
			buildPhases = (
				507B42BC1C31FA0C0067B53E /* Sources */,
				507B43091C31FA0C0067B53E /* Frameworks */,
				507B430B1C31FA0C0067B53E /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "libluacocos2d tvOS";
			productName = cocos2dx;
			productReference = 507B43531C31FA0C0067B53E /* libluacocos2d tvOS.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1551A336158F2AB200E66CFE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0600;
				ORGANIZATIONNAME = "";
			};
			buildConfigurationList = 1551A339158F2AB200E66CFE /* Build configuration list for PBXProject "cocos2d_lua_bindings" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 1551A334158F2AB200E66CFE;
			productRefGroup = 1551A334158F2AB200E66CFE;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				15C1C255198748D200A46ACC /* libluacocos2d Mac */,
				15EFA59E198B2DAA000C57D3 /* libluacocos2d iOS */,
				507B42BB1C31FA0C0067B53E /* libluacocos2d tvOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		15C1C256198748D200A46ACC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				15415A7219A718FB004F1E71 /* lua_cocos2dx_3d_auto.cpp in Sources */,
				15415A7319A718FB004F1E71 /* lua_cocos2dx_cocosbuilder_auto.cpp in Sources */,
				150906F219D556CE002C4D97 /* lua_cocos2dx_audioengine_manual.cpp in Sources */,
				15415A7519A718FB004F1E71 /* lua_cocos2dx_experimental_video_auto.cpp in Sources */,
				150983DD1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp in Sources */,
				15415A7619A718FB004F1E71 /* lua_cocos2dx_ui_auto.cpp in Sources */,
				15415A7719A718FB004F1E71 /* lua_cocos2dx_spine_auto.cpp in Sources */,
				15A561E51B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp in Sources */,
				15415A7819A718FB004F1E71 /* lua_cocos2dx_studio_auto.cpp in Sources */,
				155C7DEC19A71BF200F08B25 /* lua_cocos2dx_cocosdenshion_auto.cpp in Sources */,
				15AC69D91987710400D17520 /* tolua_event.c in Sources */,
				15AC69DA1987710400D17520 /* tolua_is.c in Sources */,
				15415ACD19A71A53004F1E71 /* tcp.c in Sources */,
				15AC69DB1987710400D17520 /* tolua_map.c in Sources */,
				ADD1C0D51C196B9500733781 /* lua_module_register.cpp in Sources */,
				15415AD519A71A53004F1E71 /* udp.c in Sources */,
				150983D01B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp in Sources */,
				15415ADD19A71A53004F1E71 /* usocket.c in Sources */,
				15AC69DC1987710400D17520 /* tolua_push.c in Sources */,
				15AC69DD1987710400D17520 /* tolua_to.c in Sources */,
				15415AAD19A71A53004F1E71 /* except.c in Sources */,
				15415ABD19A71A53004F1E71 /* mime.c in Sources */,
				15C1C2E519874C9200A46ACC /* CCLuaObjcBridge.mm in Sources */,
				155C7E1619A71CAD00F08B25 /* lua_xml_http_request.cpp in Sources */,
				15C1C2E419874C7C00A46ACC /* tolua_fix.cpp in Sources */,
				15C1C2E219874BA100A46ACC /* LuaBasicConversions.cpp in Sources */,
				F4FE0D5719ECD00100B8B12B /* luasocket_scripts.c in Sources */,
				15415AC519A71A53004F1E71 /* select.c in Sources */,
				155C7DFE19A71C5A00F08B25 /* CCBProxy.cpp in Sources */,
				1595523A1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp in Sources */,
				15415AD919A71A53004F1E71 /* unix.c in Sources */,
				155C7E0619A71C7600F08B25 /* lua_cocos2dx_extension_manual.cpp in Sources */,
				15C1C2DD19874B8800A46ACC /* CCLuaBridge.cpp in Sources */,
				EE6560AA2E4AEA410066A9AA /* lua_cjson.c in Sources */,
				EE6560AB2E4AEA410066A9AA /* fpconv.c in Sources */,
				EE6560AC2E4AEA410066A9AA /* strbuf.c in Sources */,
				15C1C2DE19874B8800A46ACC /* CCLuaEngine.cpp in Sources */,
				15415AC919A71A53004F1E71 /* serial.c in Sources */,
				BA0CBB5E1BB0756F00003364 /* CCComponentLua.cpp in Sources */,
				155C7E0219A71C6700F08B25 /* lua_cocos2dx_cocosbuilder_manual.cpp in Sources */,
				15C1C2DF19874B8800A46ACC /* CCLuaStack.cpp in Sources */,
				15C1C2E019874B8800A46ACC /* CCLuaValue.cpp in Sources */,
				155C7DF219A71C3200F08B25 /* lua_cocos2dx_3d_manual.cpp in Sources */,
				15415AB119A71A53004F1E71 /* inet.c in Sources */,
				15C1C2E119874B8800A46ACC /* Cocos2dxLuaLoader.cpp in Sources */,
				15C1C2DB19874B3D00A46ACC /* xxtea.cpp in Sources */,
				155C7E0E19A71C9600F08B25 /* lua_extensions.c in Sources */,
				15EFA1F61989E528000C57D3 /* lua_cocos2dx_experimental_auto.cpp in Sources */,
				15AC69D519876E9300D17520 /* lua_cocos2dx_physics_auto.cpp in Sources */,
				155C7E1E19A71CC700F08B25 /* LuaSkeletonAnimation.cpp in Sources */,
				15415AB519A71A53004F1E71 /* io.c in Sources */,
				15C1C2CE1987498B00A46ACC /* LuaOpengl.cpp in Sources */,
				15415AC119A71A53004F1E71 /* options.c in Sources */,
				150906F019D556C5002C4D97 /* lua_cocos2dx_audioengine_auto.cpp in Sources */,
				155C7E0A19A71C8B00F08B25 /* lua_cocos2dx_network_manual.cpp in Sources */,
				15415AD119A71A53004F1E71 /* timeout.c in Sources */,
				15C1C2CF1987498B00A46ACC /* lua_cocos2dx_deprecated.cpp in Sources */,
				155C7E2219A71CD300F08B25 /* lua_cocos2dx_experimental_video_manual.cpp in Sources */,
				155C7E2619A71CDD00F08B25 /* lua_cocos2dx_ui_manual.cpp in Sources */,
				150983D71B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp in Sources */,
				37A90C891FE0DB3800408A69 /* lua_cocos2dx_coco_studio_manual.cpp in Sources */,
				15415A7019A71768004F1E71 /* lua_cocos2dx_extension_auto.cpp in Sources */,
				EEE63EDE2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp in Sources */,
				155C7DF619A71C3E00F08B25 /* lua_cocos2dx_cocosdenshion_manual.cpp in Sources */,
				37A90C961FE1144500408A69 /* lua_cocos2dx_csloader_manual.cpp in Sources */,
				15415AA519A71A53004F1E71 /* auxiliar.c in Sources */,
				155C7E1A19A71CBC00F08B25 /* lua_cocos2dx_spine_manual.cpp in Sources */,
				15A561EC1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp in Sources */,
				15C1C2D01987498B00A46ACC /* lua_cocos2dx_experimental_manual.cpp in Sources */,
				15C1C2D11987498B00A46ACC /* lua_cocos2dx_manual.cpp in Sources */,
				15C1C2D21987498B00A46ACC /* lua_cocos2dx_physics_manual.cpp in Sources */,
				15C1C2D31987498B00A46ACC /* LuaScriptHandlerMgr.cpp in Sources */,
				15415AA919A71A53004F1E71 /* buffer.c in Sources */,
				15C1C2CD1987495500A46ACC /* lua_cocos2dx_auto.cpp in Sources */,
				15415AB919A71A53004F1E71 /* luasocket.c in Sources */,
				155C7E1219A71CA200F08B25 /* Lua_web_socket.cpp in Sources */,
				37A90C871FE0DB3800408A69 /* CustomGUIReader.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		15EFA59F198B2DAA000C57D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				ADAC23ED1C2044A60049A6A2 /* lua_module_register.cpp in Sources */,
				15415AC619A71A53004F1E71 /* select.c in Sources */,
				15A561ED1B00A3F1005D4720 /* lua_cocos2dx_physics3d_manual.cpp in Sources */,
				15EFA646198B3311000C57D3 /* tolua_event.c in Sources */,
				3E2BDB0A19C5E6100055CDCD /* lua_cocos2dx_audioengine_manual.cpp in Sources */,
				15415AA619A71A53004F1E71 /* auxiliar.c in Sources */,
				15EFA647198B3311000C57D3 /* tolua_is.c in Sources */,
				15EFA648198B3311000C57D3 /* tolua_map.c in Sources */,
				15EFA649198B3311000C57D3 /* tolua_push.c in Sources */,
				37A90C901FE10C2100408A69 /* lua_cocos2dx_coco_studio_manual.cpp in Sources */,
				15EFA64A198B3311000C57D3 /* tolua_to.c in Sources */,
				15415AC219A71A53004F1E71 /* options.c in Sources */,
				15EFA645198B32DB000C57D3 /* xxtea.cpp in Sources */,
				15EFA632198B328B000C57D3 /* CCLuaBridge.cpp in Sources */,
				150983D11B1C0554007F3818 /* lua_cocos2dx_navmesh_auto.cpp in Sources */,
				15EFA633198B328B000C57D3 /* CCLuaEngine.cpp in Sources */,
				155C7DEE19A71BFC00F08B25 /* lua_cocos2dx_experimental_video_auto.cpp in Sources */,
				15EFA634198B328B000C57D3 /* CCLuaStack.cpp in Sources */,
				15EFA635198B328B000C57D3 /* CCLuaValue.cpp in Sources */,
				EEE63EDF2C1FEE5C0042066B /* lua_cricocos2d_manual.cpp in Sources */,
				15EFA636198B328B000C57D3 /* Cocos2dxLuaLoader.cpp in Sources */,
				155C7DEB19A71BE900F08B25 /* lua_cocos2dx_cocosbuilder_auto.cpp in Sources */,
				150983DE1B1C4860007F3818 /* lua_cocos2dx_navmesh_conversions.cpp in Sources */,
				15EFA637198B328B000C57D3 /* LuaBasicConversions.cpp in Sources */,
				15EFA638198B328B000C57D3 /* CCLuaObjcBridge.mm in Sources */,
				155C7E1719A71CAF00F08B25 /* lua_xml_http_request.cpp in Sources */,
				37A90C9A1FE1147D00408A69 /* lua_cocos2dx_csloader_manual.cpp in Sources */,
				15415ACE19A71A53004F1E71 /* tcp.c in Sources */,
				15415AD219A71A53004F1E71 /* timeout.c in Sources */,
				F4FE0D5819ECD00100B8B12B /* luasocket_scripts.c in Sources */,
				EE6560A42E4AEA410066A9AA /* lua_cjson.c in Sources */,
				EE6560A52E4AEA410066A9AA /* fpconv.c in Sources */,
				EE6560A62E4AEA410066A9AA /* strbuf.c in Sources */,
				15EFA639198B328B000C57D3 /* tolua_fix.cpp in Sources */,
				15A561E61B00A09A005D4720 /* lua_cocos2dx_physics3d_auto.cpp in Sources */,
				155C7DFF19A71C5C00F08B25 /* CCBProxy.cpp in Sources */,
				1595523B1A25E1C5001E9FC9 /* lua_cocos2dx_csloader_auto.cpp in Sources */,
				15C9A1121AE4973400C15443 /* lua_cocos2dx_experimental_webview_manual.cpp in Sources */,
				155C7DF019A71C1E00F08B25 /* lua_cocos2dx_spine_auto.cpp in Sources */,
				155C7E0719A71C7800F08B25 /* lua_cocos2dx_extension_manual.cpp in Sources */,
				15EFA625198B31FB000C57D3 /* LuaOpengl.cpp in Sources */,
				15EFA626198B31FB000C57D3 /* lua_cocos2dx_deprecated.cpp in Sources */,
				37A909BA1FDFF24700408A69 /* lua_bldownload_auto.cpp in Sources */,
				15EFA627198B31FB000C57D3 /* lua_cocos2dx_experimental_manual.cpp in Sources */,
				37A909BE1FDFF25600408A69 /* lua_bole_manual.cpp in Sources */,
				155C7E0319A71C6900F08B25 /* lua_cocos2dx_cocosbuilder_manual.cpp in Sources */,
				15415ABE19A71A53004F1E71 /* mime.c in Sources */,
				15415ADA19A71A53004F1E71 /* unix.c in Sources */,
				155C7DF319A71C3400F08B25 /* lua_cocos2dx_3d_manual.cpp in Sources */,
				15415ADE19A71A53004F1E71 /* usocket.c in Sources */,
				155C7DED19A71BF400F08B25 /* lua_cocos2dx_cocosdenshion_auto.cpp in Sources */,
				15EFA628198B31FB000C57D3 /* lua_cocos2dx_manual.cpp in Sources */,
				155C7E0F19A71C9800F08B25 /* lua_extensions.c in Sources */,
				BA0CBB5F1BB0756F00003364 /* CCComponentLua.cpp in Sources */,
				15415AD619A71A53004F1E71 /* udp.c in Sources */,
				15415ABA19A71A53004F1E71 /* luasocket.c in Sources */,
				155C7E1F19A71CC800F08B25 /* LuaSkeletonAnimation.cpp in Sources */,
				15415ACA19A71A53004F1E71 /* serial.c in Sources */,
				155C7DEA19A71BDA00F08B25 /* lua_cocos2dx_3d_auto.cpp in Sources */,
				155C7DF119A71C2300F08B25 /* lua_cocos2dx_studio_auto.cpp in Sources */,
				3E2BDB0519C5E5FE0055CDCD /* lua_cocos2dx_audioengine_auto.cpp in Sources */,
				155C7E0B19A71C8D00F08B25 /* lua_cocos2dx_network_manual.cpp in Sources */,
				15415AAE19A71A53004F1E71 /* except.c in Sources */,
				15C9A10E1AE4972500C15443 /* lua_cocos2dx_experimental_webview_auto.cpp in Sources */,
				15415AAA19A71A53004F1E71 /* buffer.c in Sources */,
				155C7E2319A71CD500F08B25 /* lua_cocos2dx_experimental_video_manual.cpp in Sources */,
				150983D81B1C3F3E007F3818 /* lua_cocos2dx_navmesh_manual.cpp in Sources */,
				37A909C01FDFF25600408A69 /* lua_bole.cpp in Sources */,
				37A90C8E1FE10C2100408A69 /* CustomGUIReader.cpp in Sources */,
				155C7E2719A71CDE00F08B25 /* lua_cocos2dx_ui_manual.cpp in Sources */,
				37A909BC1FDFF24700408A69 /* lua_database_auto.cpp in Sources */,
				15415AB619A71A53004F1E71 /* io.c in Sources */,
				155C7DF719A71C4000F08B25 /* lua_cocos2dx_cocosdenshion_manual.cpp in Sources */,
				155C7DEF19A71C0900F08B25 /* lua_cocos2dx_ui_auto.cpp in Sources */,
				155C7E1B19A71CBE00F08B25 /* lua_cocos2dx_spine_manual.cpp in Sources */,
				15415A7119A71782004F1E71 /* lua_cocos2dx_extension_auto.cpp in Sources */,
				15EFA629198B31FB000C57D3 /* lua_cocos2dx_physics_manual.cpp in Sources */,
				15EFA62A198B31FB000C57D3 /* LuaScriptHandlerMgr.cpp in Sources */,
				15EFA617198B2E2B000C57D3 /* lua_cocos2dx_experimental_auto.cpp in Sources */,
				15EFA618198B2E2B000C57D3 /* lua_cocos2dx_auto.cpp in Sources */,
				15EFA619198B2E2B000C57D3 /* lua_cocos2dx_physics_auto.cpp in Sources */,
				15415AB219A71A53004F1E71 /* inet.c in Sources */,
				155C7E1319A71CA400F08B25 /* Lua_web_socket.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		507B42BC1C31FA0C0067B53E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				507B42BD1C31FA0C0067B53E /* lua_module_register.cpp in Sources */,
				507B42BE1C31FA0C0067B53E /* select.c in Sources */,
				507B42BF1C31FA0C0067B53E /* lua_cocos2dx_physics3d_manual.cpp in Sources */,
				507B42C01C31FA0C0067B53E /* tolua_event.c in Sources */,
				507B42C11C31FA0C0067B53E /* lua_cocos2dx_audioengine_manual.cpp in Sources */,
				507B42C21C31FA0C0067B53E /* auxiliar.c in Sources */,
				507B42C41C31FA0C0067B53E /* tolua_is.c in Sources */,
				507B42C51C31FA0C0067B53E /* tolua_map.c in Sources */,
				507B42C61C31FA0C0067B53E /* tolua_push.c in Sources */,
				507B42C71C31FA0C0067B53E /* tolua_to.c in Sources */,
				507B42C81C31FA0C0067B53E /* options.c in Sources */,
				507B42C91C31FA0C0067B53E /* xxtea.cpp in Sources */,
				507B42CA1C31FA0C0067B53E /* CCLuaBridge.cpp in Sources */,
				507B42CB1C31FA0C0067B53E /* lua_cocos2dx_navmesh_auto.cpp in Sources */,
				507B42CC1C31FA0C0067B53E /* CCLuaEngine.cpp in Sources */,
				507B42CD1C31FA0C0067B53E /* lua_cocos2dx_experimental_video_auto.cpp in Sources */,
				507B42CE1C31FA0C0067B53E /* CCLuaStack.cpp in Sources */,
				507B42CF1C31FA0C0067B53E /* CCLuaValue.cpp in Sources */,
				507B42D01C31FA0C0067B53E /* Cocos2dxLuaLoader.cpp in Sources */,
				507B42D11C31FA0C0067B53E /* lua_cocos2dx_cocosbuilder_auto.cpp in Sources */,
				507B42D21C31FA0C0067B53E /* lua_cocos2dx_navmesh_conversions.cpp in Sources */,
				507B42D31C31FA0C0067B53E /* LuaBasicConversions.cpp in Sources */,
				EE65609E2E4AEA410066A9AA /* lua_cjson.c in Sources */,
				EE65609F2E4AEA410066A9AA /* fpconv.c in Sources */,
				EE6560A02E4AEA410066A9AA /* strbuf.c in Sources */,
				507B42D41C31FA0C0067B53E /* CCLuaObjcBridge.mm in Sources */,
				507B42D51C31FA0C0067B53E /* lua_xml_http_request.cpp in Sources */,
				507B42D61C31FA0C0067B53E /* tcp.c in Sources */,
				507B42D71C31FA0C0067B53E /* timeout.c in Sources */,
				507B42D81C31FA0C0067B53E /* luasocket_scripts.c in Sources */,
				507B42D91C31FA0C0067B53E /* tolua_fix.cpp in Sources */,
				507B42DA1C31FA0C0067B53E /* lua_cocos2dx_physics3d_auto.cpp in Sources */,
				507B42DB1C31FA0C0067B53E /* CCBProxy.cpp in Sources */,
				507B42DC1C31FA0C0067B53E /* lua_cocos2dx_csloader_auto.cpp in Sources */,
				507B42DD1C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_manual.cpp in Sources */,
				507B42DE1C31FA0C0067B53E /* lua_cocos2dx_spine_auto.cpp in Sources */,
				507B42DF1C31FA0C0067B53E /* lua_cocos2dx_extension_manual.cpp in Sources */,
				507B42E01C31FA0C0067B53E /* LuaOpengl.cpp in Sources */,
				507B42E11C31FA0C0067B53E /* lua_cocos2dx_deprecated.cpp in Sources */,
				507B42E21C31FA0C0067B53E /* lua_cocos2dx_experimental_manual.cpp in Sources */,
				507B42E31C31FA0C0067B53E /* lua_cocos2dx_cocosbuilder_manual.cpp in Sources */,
				507B42E41C31FA0C0067B53E /* mime.c in Sources */,
				507B42E51C31FA0C0067B53E /* unix.c in Sources */,
				507B42E61C31FA0C0067B53E /* lua_cocos2dx_3d_manual.cpp in Sources */,
				507B42E71C31FA0C0067B53E /* usocket.c in Sources */,
				507B42E81C31FA0C0067B53E /* lua_cocos2dx_cocosdenshion_auto.cpp in Sources */,
				507B42E91C31FA0C0067B53E /* lua_cocos2dx_manual.cpp in Sources */,
				507B42EA1C31FA0C0067B53E /* lua_extensions.c in Sources */,
				507B42EB1C31FA0C0067B53E /* CCComponentLua.cpp in Sources */,
				507B42EC1C31FA0C0067B53E /* udp.c in Sources */,
				507B42ED1C31FA0C0067B53E /* luasocket.c in Sources */,
				507B42EE1C31FA0C0067B53E /* LuaSkeletonAnimation.cpp in Sources */,
				507B42EF1C31FA0C0067B53E /* serial.c in Sources */,
				507B42F01C31FA0C0067B53E /* lua_cocos2dx_3d_auto.cpp in Sources */,
				507B42F21C31FA0C0067B53E /* lua_cocos2dx_studio_auto.cpp in Sources */,
				507B42F31C31FA0C0067B53E /* lua_cocos2dx_audioengine_auto.cpp in Sources */,
				507B42F41C31FA0C0067B53E /* lua_cocos2dx_network_manual.cpp in Sources */,
				507B42F51C31FA0C0067B53E /* except.c in Sources */,
				507B42F61C31FA0C0067B53E /* lua_cocos2dx_experimental_webview_auto.cpp in Sources */,
				507B42F71C31FA0C0067B53E /* buffer.c in Sources */,
				507B42F81C31FA0C0067B53E /* lua_cocos2dx_experimental_video_manual.cpp in Sources */,
				507B42F91C31FA0C0067B53E /* lua_cocos2dx_navmesh_manual.cpp in Sources */,
				507B42FB1C31FA0C0067B53E /* lua_cocos2dx_ui_manual.cpp in Sources */,
				507B42FC1C31FA0C0067B53E /* io.c in Sources */,
				507B42FD1C31FA0C0067B53E /* lua_cocos2dx_cocosdenshion_manual.cpp in Sources */,
				507B42FE1C31FA0C0067B53E /* lua_cocos2dx_ui_auto.cpp in Sources */,
				507B42FF1C31FA0C0067B53E /* lua_cocos2dx_spine_manual.cpp in Sources */,
				507B43001C31FA0C0067B53E /* lua_cocos2dx_extension_auto.cpp in Sources */,
				507B43011C31FA0C0067B53E /* lua_cocos2dx_physics_manual.cpp in Sources */,
				507B43021C31FA0C0067B53E /* LuaScriptHandlerMgr.cpp in Sources */,
				507B43031C31FA0C0067B53E /* lua_cocos2dx_experimental_auto.cpp in Sources */,
				507B43041C31FA0C0067B53E /* lua_cocos2dx_auto.cpp in Sources */,
				507B43051C31FA0C0067B53E /* lua_cocos2dx_physics_auto.cpp in Sources */,
				EEE63EE02C1FEE5C0042066B /* lua_cricocos2d_manual.cpp in Sources */,
				507B43061C31FA0C0067B53E /* inet.c in Sources */,
				507B43081C31FA0C0067B53E /* Lua_web_socket.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1551A34A158F2AB200E66CFE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				COPY_PHASE_STRIP = NO;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"COCOS2D_DEBUG=1",
					USE_FILE32API,
					"CC_ENABLE_CHIPMUNK_INTEGRATION=1",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_INCOMPATIBLE_POINTER_TYPE_WARNINGS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_HIDDEN_VIRTUAL_FUNCTIONS = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.7;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../.. $(SRCROOT)/../../.. $(SRCROOT)/../../../base $(SRCROOT)/../../../3d $(SRCROOT)/../../../2d $(SRCROOT)/../../../deprecated $(SRCROOT)/../../../physics $(SRCROOT)/../../../2d/platform $(SRCROOT)/../../../audio/include $(SRCROOT)/../../../editor-support $(SRCROOT)/../../../editor-support/spine $(SRCROOT)/../../../editor-support/cocostudio $(SRCROOT)/../../../editor-support/cocosbuilder $(SRCROOT)/../../../ui $(SRCROOT)/../../../storage $(SRCROOT)/../../../../extensions $(SRCROOT)/../../../../external $(SRCROOT)/../../../../external/chipmunk/include $(SRCROOT)/../../../../external/lua $(SRCROOT)/../../../../external/lua/luajit/include $(SRCROOT)/../../../../external/lua/tolua $(SRCROOT)/../../../editor-support/cocostudio/ActionTimeline $(SRCROOT)/../../../physics3d $(SRCROOT)/../../../navmesh";
			};
			name = Debug;
		};
		1551A34B158F2AB200E66CFE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"CC_ENABLE_CHIPMUNK_INTEGRATION=1",
					NDEBUG,
					USE_FILE32API,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_INCOMPATIBLE_POINTER_TYPE_WARNINGS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_HIDDEN_VIRTUAL_FUNCTIONS = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.7;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../.. $(SRCROOT)/../../.. $(SRCROOT)/../../../base $(SRCROOT)/../../../3d $(SRCROOT)/../../../2d $(SRCROOT)/../../../deprecated $(SRCROOT)/../../../physics $(SRCROOT)/../../../2d/platform $(SRCROOT)/../../../audio/include $(SRCROOT)/../../../editor-support $(SRCROOT)/../../../editor-support/spine $(SRCROOT)/../../../editor-support/cocostudio $(SRCROOT)/../../../editor-support/cocosbuilder $(SRCROOT)/../../../ui $(SRCROOT)/../../../storage $(SRCROOT)/../../../../extensions $(SRCROOT)/../../../../external $(SRCROOT)/../../../../external/chipmunk/include $(SRCROOT)/../../../../external/lua $(SRCROOT)/../../../../external/lua/luajit/include $(SRCROOT)/../../../../external/lua/tolua $(SRCROOT)/../../../editor-support/cocostudio/ActionTimeline $(SRCROOT)/../../../physics3d $(SRCROOT)/../../../navmesh";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		15C1C2CA198748D200A46ACC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				COMBINE_HIDPI_IMAGES = YES;
				EXECUTABLE_PREFIX = "";
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					CC_TARGET_OS_MAC,
					CC_KEYBOARD_SUPPORT,
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 5.0;
				LIBRARY_SEARCH_PATHS = "$(SRCROOT)/../../../../external/lua/luajit/prebuilt/mac";
				PRODUCT_NAME = "libluacocos2d Mac";
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "$(inherited) $(SRCROOT)/../../../platform/mac $(SRCROOT)/../../../../external/glfw3/include/mac $(SRCROOT)/../../../../external/curl/include/mac";
			};
			name = Debug;
		};
		15C1C2CB198748D200A46ACC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				COMBINE_HIDPI_IMAGES = YES;
				EXECUTABLE_PREFIX = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					CC_TARGET_OS_MAC,
					CC_KEYBOARD_SUPPORT,
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 5.0;
				LIBRARY_SEARCH_PATHS = "$(SRCROOT)/../../../../external/lua/luajit/prebuilt/mac";
				PRODUCT_NAME = "libluacocos2d Mac";
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "$(inherited) $(SRCROOT)/../../../platform/mac $(SRCROOT)/../../../../external/glfw3/include/mac $(SRCROOT)/../../../../external/curl/include/mac";
			};
			name = Release;
		};
		15EFA614198B2DAA000C57D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				ENABLE_BITCODE = NO;
				EXECUTABLE_PREFIX = "";
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					CC_TARGET_OS_IPHONE,
				);
				HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../../external/criware/include/ios";
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				LIBRARY_SEARCH_PATHS = "$(SRCROOT)/../../../../external/lua/luajit/prebuilt/ios";
				PRODUCT_NAME = "libluacocos2d iOS";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "$(inherited) $(SRCROOT)/../../../platform/ios $(SRCROOT)/../../../../external/curl/include/ios $(SRCROOT)/../../../../external/criware/include/ios $(SRCROOT)/../../../../external/bullet/include/bullet $(SRCROOT)/../../../../external/bullet/include";
			};
			name = Debug;
		};
		15EFA615198B2DAA000C57D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				ENABLE_BITCODE = NO;
				EXECUTABLE_PREFIX = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					CC_TARGET_OS_IPHONE,
				);
				HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../../external/criware/include/ios";
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				LIBRARY_SEARCH_PATHS = "$(SRCROOT)/../../../../external/lua/luajit/prebuilt/ios";
				PRODUCT_NAME = "libluacocos2d iOS";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				USER_HEADER_SEARCH_PATHS = "$(inherited) $(SRCROOT)/../../../platform/ios $(SRCROOT)/../../../../external/curl/include/ios $(SRCROOT)/../../../../external/criware/include/ios $(SRCROOT)/../../../../external/bullet/include/bullet $(SRCROOT)/../../../../external/bullet/include";
			};
			name = Release;
		};
		507B43511C31FA0C0067B53E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				EXECUTABLE_PREFIX = "";
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					CC_TARGET_OS_TVOS,
				);
				LIBRARY_SEARCH_PATHS = "$(SRCROOT)/../../../../external/lua/luajit/prebuilt/ios";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				USER_HEADER_SEARCH_PATHS = "$(inherited) $(SRCROOT)/../../../platform/ios $(SRCROOT)/../../../../external/curl/include/ios";
			};
			name = Debug;
		};
		507B43521C31FA0C0067B53E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = YES;
				EXECUTABLE_PREFIX = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					CC_TARGET_OS_TVOS,
				);
				LIBRARY_SEARCH_PATHS = "$(SRCROOT)/../../../../external/lua/luajit/prebuilt/ios";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				USER_HEADER_SEARCH_PATHS = "$(inherited) $(SRCROOT)/../../../platform/ios $(SRCROOT)/../../../../external/curl/include/ios";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1551A339158F2AB200E66CFE /* Build configuration list for PBXProject "cocos2d_lua_bindings" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1551A34A158F2AB200E66CFE /* Debug */,
				1551A34B158F2AB200E66CFE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		15C1C2C9198748D200A46ACC /* Build configuration list for PBXNativeTarget "libluacocos2d Mac" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				15C1C2CA198748D200A46ACC /* Debug */,
				15C1C2CB198748D200A46ACC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		15EFA613198B2DAA000C57D3 /* Build configuration list for PBXNativeTarget "libluacocos2d iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				15EFA614198B2DAA000C57D3 /* Debug */,
				15EFA615198B2DAA000C57D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		507B43501C31FA0C0067B53E /* Build configuration list for PBXNativeTarget "libluacocos2d tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				507B43511C31FA0C0067B53E /* Debug */,
				507B43521C31FA0C0067B53E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1551A336158F2AB200E66CFE /* Project object */;
}
