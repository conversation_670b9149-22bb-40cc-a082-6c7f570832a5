package org.cocos2dx.lib;

import com.loopj.android.http.AsyncHttpResponseHandler;
import com.loopj.android.http.BinaryHttpResponseHandler;
import com.loopj.android.http.FileAsyncHttpResponseHandler;
import com.loopj.android.http.RequestHandle;
import com.loopj.android.http.SyncHttpClient;

import cz.msebera.android.httpclient.Header;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

class DataTaskHandler extends BinaryHttpResponseHandler {
    int _id;
    private Cocos2dxDownloader _downloader;
    private long _lastBytesWritten;
    private int _lastPercent;
    private int _progressCount;

    void LogD(String msg) {
        android.util.Log.d("Cocos2dxDownloader", msg);
    }

    // progressCount : 进度条被切分为多少份，默认为100（功能是一次下载的onProgress，传给cocos引擎次数为100）
    public DataTaskHandler(Cocos2dxDownloader downloader, int id, int progressCount) {
        super(new String[]{".*"});
        _downloader = downloader;
        _id = id;
        _lastBytesWritten = 0;
        _lastPercent = 0;
        _progressCount = progressCount;
    }

    @Override
    public void onProgress(long bytesWritten, long totalSize) {
        //LogD("onProgress(bytesWritten:" + bytesWritten + " totalSize:" + totalSize);
        long dlBytes = bytesWritten - _lastBytesWritten;
        long dlNow = bytesWritten;
        long dlTotal = totalSize;
        int percentNow = dlTotal > 0 ? (int) (dlNow * _progressCount / dlTotal) : 0;
        if (Cocos2dxHelper.isActivityVisible() && _lastPercent < percentNow){ // 减少向GLThread发送消息的数量
            _downloader.onProgress(_id, dlBytes, dlNow, dlTotal);
            _lastPercent = percentNow;
        }
//        _downloader.onProgress(_id, dlBytes, dlNow, dlTotal);
        _lastBytesWritten = bytesWritten;
    }

    @Override
    public void onStart() {
        _downloader.onStart(_id);
    }

    @Override
    public void onFailure(int i, Header[] headers, byte[] errorResponse, Throwable throwable) {
        LogD("onFailure(i:" + i + " headers:" + headers + " throwable:" + throwable);
        String errStr = "";
        if (null != throwable) {
            errStr = throwable.toString();
        }
        _downloader.onFinish(_id, i, errStr, null);
    }

    @Override
    public void onSuccess(int i, Header[] headers, byte[] binaryData) {
        LogD("onSuccess(i:" + i + " headers:" + headers); // binaryData 是读取.md5文件的数据
        _downloader.onFinish(_id, 0, null, binaryData);
    }
}

// 创建头文件并下载
class HeadTaskHandler extends AsyncHttpResponseHandler {
    int _id;
    String _host;
    String _url;
    String _path;
    Boolean _continueDownloading;
    private int _progressCount;
    private Cocos2dxDownloader _downloader;

    void LogD(String msg) {
        android.util.Log.d("Cocos2dxDownloader", msg);
    }

    public HeadTaskHandler(Cocos2dxDownloader downloader, int id, String host, String url, String path, Boolean continueDownloading, int progressCount) {
        super();
        _downloader = downloader;
        _id = id;
        _host = host;
        _url = url;
        _path = path;
        _continueDownloading = continueDownloading;
        _progressCount = progressCount;
    }

    @Override
    public void onSuccess(int statusCode, Header[] headers, byte[] responseBody) {
        Boolean acceptRanges = false;
        for (int i = 0; i < headers.length; ++i) {
            Header elem = headers[i];
            if (elem.getName().equals("Accept-Ranges")) {
                acceptRanges = elem.getValue().equals("bytes");
                break;
            }
        }
        Cocos2dxDownloader.setResumingSupport(_host, acceptRanges);
        Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
            @Override
            public void run() {
                Cocos2dxDownloader.createTask(_downloader, _id, _url, _path, _continueDownloading, _progressCount);
            }
        }, "cocosDownloader_createTask"));

        _downloader.runNextTaskIfExists();
    }

    @Override
    public void onFailure(int statusCode, Header[] headers, byte[] responseBody, Throwable throwable) {
        LogD("onFailure(code:" + statusCode + " headers:" + headers + " throwable:" + throwable + " id:" + _id);
        String errStr = "";
        if (null != throwable) {
            errStr = throwable.toString();
        }
        _downloader.onFinish(_id, statusCode, errStr, null);
    }
}

// 创建本地文件夹，并下载
class FileTaskHandler extends FileAsyncHttpResponseHandler {
    int _id;
    File _finalFile;

    private long _initFileLen;
    private long _lastBytesWritten;
    private int _lastPercent;
    private int _progressCount;
    private Cocos2dxDownloader _downloader;

    void LogD(String msg) {
        android.util.Log.d("Cocos2dxDownloader", msg);
    }

    // progressCount : 进度条被切分为多少份，默认为100（功能是一次下载的onProgress，传给cocos引擎次数为100）
    public FileTaskHandler(Cocos2dxDownloader downloader, int id, File temp, File finalFile, int progressCount) {
        super(temp, true);
        _finalFile = finalFile;
        _downloader = downloader;
        _id = id;
        _initFileLen = getTargetFile().length();
        _lastBytesWritten = 0;
        _lastPercent = 0;
        _progressCount = progressCount;
    }

    @Override
    public void onProgress(long bytesWritten, long totalSize) {
        //LogD("onProgress(bytesWritten:" + bytesWritten + " totalSize:" + totalSize);
        long dlBytes = bytesWritten - _lastBytesWritten;
        long dlNow = bytesWritten + _initFileLen;
        long dlTotal = totalSize + _initFileLen;
        int percentNow = dlTotal > 0 ? (int) (dlNow * _progressCount / dlTotal) : 0;
        if (Cocos2dxHelper.isActivityVisible() && _lastPercent < percentNow){ // 减少向GLThread发送消息的数量
            _downloader.onProgress(_id, dlBytes, dlNow, dlTotal);
            _lastPercent = percentNow;
        }
//        _downloader.onProgress(_id, dlBytes, dlNow, dlTotal);
        _lastBytesWritten = bytesWritten;
    }

    @Override
    public void onStart() {
        _downloader.onStart(_id);
    }

    @Override
    public void onFinish() {
        // onFinish called after onSuccess/onFailure
//        _downloader.runNextTaskIfExists();
    }

    @Override
    public void onFailure(int i, Header[] headers, Throwable throwable, File file) {
        LogD("onFailure(i:" + i + " headers:" + headers + " throwable:" + throwable + " file:" + file);
        String errStr = "";
        if (null != throwable) {
            errStr = throwable.toString();
        }
        _downloader.onFinish(_id, i, errStr, null);
    }

    @Override
    public void onSuccess(int i, Header[] headers, File file) {
        LogD("onSuccess(i:" + i + " headers:" + headers + " file:" + file);
        String errStr = null;
        do {
            // rename temp file to final file
            // if final file exist, remove it
            if (_finalFile.exists()) {
                if (_finalFile.isDirectory()) {
                    errStr = "Dest file is directory:" + _finalFile.getAbsolutePath();
                    break;
                }
                if (false == _finalFile.delete()) {
                    errStr = "Can't remove old file:" + _finalFile.getAbsolutePath();
                    break;
                }
            }

            File tempFile = getTargetFile();
            tempFile.renameTo(_finalFile);
        } while (false);
        _downloader.onFinish(_id, 0, errStr, null);
    }
}

class DownloadTask {

    DownloadTask() {
        handle = null;
        handler = null;
        resetStatus();
    }

    void resetStatus() {
        bytesReceived = 0;
        totalBytesReceived = 0;
        totalBytesExpected = 0;
        data = null;
    }

    RequestHandle handle;
    AsyncHttpResponseHandler handler;

    // progress
    long bytesReceived;
    long totalBytesReceived;
    long totalBytesExpected;
    byte[] data;

}

public class Cocos2dxDownloader {
    private int _id;
    private SyncHttpClient _httpClient = new SyncHttpClient();
    private String _tempFileNameSufix;
    public int _countOfMaxProcessingTasks;
//    private ConcurrentHashMap _taskMap = new ConcurrentHashMap(); // 用来终止异步下载的
    public Queue<Runnable> _taskQueue = new LinkedList<Runnable>();
    public int _runningTaskCount = 0; // 这个下载器，同时在下载的数量
    private DownLoadThread _downLoadThread; // 下载线程
    private static HashMap<String, Boolean> _resumingSupport = new HashMap<String, Boolean>();

    //private static int _downLoadCreateCount = 0;   // 创建 Cocos2dxDownloader 的数量

    void onProgress(final int id, final long downloadBytes, final long downloadNow, final long downloadTotal) {
//        DownloadTask task = (DownloadTask)_taskMap.get(id);
//        if (null != task) {
//            task.bytesReceived = downloadBytes;
//            task.totalBytesReceived = downloadNow;
//            task.totalBytesExpected = downloadTotal;
//        }
        Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
            @Override
            public void run() {
                nativeOnProgress(_id, id, downloadBytes, downloadNow, downloadTotal);
            }
        }, "cocosDownloader_onProgress"));
    }

    public void onStart(int id) {
//        DownloadTask task = (DownloadTask)_taskMap.get(id);
//        if (null != task) {
//            task.resetStatus();
//        }
    }

    public void onFinish(final int id, final int errCode, final String errStr, final byte[] data) {
//        DownloadTask task = (DownloadTask)_taskMap.get(id);
//        if (null == task) return;
//        _taskMap.remove(id);
        Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
            @Override
            public void run() {
                nativeOnFinish(_id, id, errCode, errStr, data);
            }
        }, "cocosDownloader_onFinish"));
        runNextTaskIfExists();
    }

    public static void setResumingSupport(String host, Boolean support) {
        Cocos2dxDownloader._resumingSupport.put(host, support);
    }

    public static Cocos2dxDownloader createDownloader(int id, int timeoutInSeconds, String tempFileNameSufix, int countOfMaxProcessingTasks) {
        Cocos2dxDownloader downloader = new Cocos2dxDownloader();
        downloader._id = id;

        downloader._httpClient.setEnableRedirects(true);
        if (timeoutInSeconds > 0) {
            downloader._httpClient.setTimeout(timeoutInSeconds * 1000);
        }
        // downloader._httpClient.setMaxRetriesAndTimeout(3, timeoutInSeconds * 1000);
        downloader._httpClient.allowRetryExceptionClass(javax.net.ssl.SSLException.class);

        downloader._tempFileNameSufix = tempFileNameSufix;
        downloader._countOfMaxProcessingTasks = countOfMaxProcessingTasks;

        //_downLoadCreateCount += 1;
        // 下载线程，负责 _httpClient 请求（不再使用UI线程开启请求下载 by sunyungao）
        downloader._downLoadThread = new DownLoadThread();
        downloader._downLoadThread.start();

        return downloader;
    }

    public static void createTask(final Cocos2dxDownloader downloader, int id_, String url_, String path_, final boolean continueDownloading, int progressCount_) {
        final int id = id_;
        final String url = url_;
        final String path = path_;
        final int progressCount = progressCount_;

        Runnable taskRunnable = new Runnable() {
            @Override
            public void run() {
                DownloadTask task = new DownloadTask();
                if (0 == path.length()) {
                    // data task 下载数据文件，比如md5，这个不用存到手机文件夹中，只看数据
                    // eg: http://************:1300/sea/dafu888/dafu888_android_314_src64.md5?_=1755*********
                    task.handler = new DataTaskHandler(downloader, id, progressCount);
                    task.handle = downloader._httpClient.get(Cocos2dxHelper.getActivity(), url, task.handler);
                }

                do {
                    if (0 == path.length()) break;

                    String domain;
                    try {
                        URI uri = new URI(url);
                        domain = uri.getHost();
                    }
                    catch (URISyntaxException e) {
                        break;
                    }
                    final String host = domain.startsWith("www.") ? domain.substring(4) : domain;
                    Boolean supportResuming = false;
                    Boolean requestHeader = true;
                    if (_resumingSupport.containsKey(host)) {
                        supportResuming = _resumingSupport.get(host);
                        requestHeader = false;
                    }

                    // 对应域名下第一次下载的话，添加head，head函数也包含与get一样的下载功能
                    // url http://************:1300/sea/dafu888/src64_3887_20250812174263.zip
                    // path /data/user/0/com.grandegames.slots.dafu.casino/files/bolesrc64/update6160605560884309207-temp.zip
                    if (requestHeader) {
                        task.handler = new HeadTaskHandler(downloader, id, host, url, path, continueDownloading, progressCount);
                        task.handle = downloader._httpClient.head(Cocos2dxHelper.getActivity(), url, null, null, task.handler);
                        break;
                    }

                    // 下载一个zip包
                    // file task url: http://************:1300/sea/dafu888/astc/res_1875_20250811202962.zip
                    // path=/data/user/0/com.grandegames.slots.dafu.casino/files/BLASTCDownloadRes/update-8502473116092192275-temp.zip
                    File tempFile = new File(path + downloader._tempFileNameSufix);
                    if (tempFile.isDirectory()) break;

                    File parent = tempFile.getParentFile();
                    if (!parent.isDirectory() && !parent.mkdirs()) break;

                    File finalFile = new File(path);
                    if (tempFile.isDirectory()) break;

                    task.handler = new FileTaskHandler(downloader, id, tempFile, finalFile, progressCount);
                    Header[] headers = null;
                    long fileLen = tempFile.length();
                    if (supportResuming && fileLen > 0 && continueDownloading) {
                        // continue download 断点续传在同步 http 下失效
                        // List<Header> list = new ArrayList<Header>();
                        // list.add(new BasicHeader("Range", "bytes=" + fileLen + "-"));
                        // headers = list.toArray(new Header[list.size()]);

                        // Remove previous downloaded context
                        try {
                            PrintWriter writer = new PrintWriter(tempFile);
                            writer.print("");
                            writer.close();
                        }
                        // Not found then nothing to do
                        catch (FileNotFoundException e) {}
                    }
                    else if (fileLen > 0) {
                        // Remove previous downloaded context
                        try {
                            PrintWriter writer = new PrintWriter(tempFile);
                            writer.print("");
                            writer.close();
                        }
                        // Not found then nothing to do
                        catch (FileNotFoundException e) {}
                    }
                    task.handle = downloader._httpClient.get(Cocos2dxHelper.getActivity(), url, headers, null, task.handler);
                } while (false);

                if (null == task.handle) {
                    final String errStr = "Can't create DownloadTask for " + url;
                    Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
                        @Override
                        public void run() {
                            downloader.nativeOnFinish(downloader._id, id, 0, errStr, null);
                        }
                    }, "cocosDownloader_createTask"));
                }
                // else {
                //     downloader._taskMap.put(id, task);
                // }
            }
        };

        downloader.enqueueTask(taskRunnable);
    }

    // c++中 assetsManager（DownloaderAndroid）析构的时候调用。 同步 SyncHttpClient 下载的话无法中止
    public static void cancelAllRequests(final Cocos2dxDownloader downloader) {
//        Cocos2dxHelper.getActivity().runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//
//                //downloader._httpClient.cancelAllRequests(true);
//                Iterator iter = downloader._taskMap.entrySet().iterator();
//                while (iter.hasNext()) {
//                    Map.Entry entry = (Map.Entry) iter.next();
//                    //Object key = entry.getKey();
//                    DownloadTask task = (DownloadTask) entry.getValue();
//                    if (null != task.handle) {
//                        task.handle.cancel(true);
//                    }
//                }
//            }
//        });
        downloader._downLoadThread.quit();
    }

    public void quitDownLoadThread() {
        if (_downLoadThread != null) {
            _downLoadThread.quit();
        }
    }

    // GL线程调用
    public void enqueueTask(Runnable taskRunnable) {
        if (_runningTaskCount < _countOfMaxProcessingTasks) {
            runOnDownLoadThread(taskRunnable);
            _runningTaskCount++;
        } else {
            _taskQueue.add(taskRunnable);
        }
    }

    public void runNextTaskIfExists() {
        Cocos2dxHelper.runOnGLThread(new GLTaskMonitor(new Runnable() {
            @Override
            public void run() {
                Runnable taskRunnable = Cocos2dxDownloader.this._taskQueue.poll();
                if (taskRunnable != null) {
                    runOnDownLoadThread(taskRunnable);
                } else {
                    _runningTaskCount--;
                }
            }
        }, "cocosDownloader_runNextTaskIfExists"));
    }

    // 执行下载线程开始下载任务
    private void runOnDownLoadThread(Runnable taskRunnable) {
        if (_downLoadThread != null) {
            _downLoadThread.startWork(taskRunnable);
        }
    }

    private static class DownLoadThread extends Thread {

        private final AtomicBoolean mAliveThread = new AtomicBoolean(true);
        private final ConcurrentLinkedQueue<Runnable> mDownloadTask = new ConcurrentLinkedQueue<>();

        DownLoadThread() {
            super("DownLoadT_cocos");
        }

        @Override
        public void run() {
            while (mAliveThread.get()) {
                try {
                    Runnable taskRunnable = mDownloadTask.poll();
                    if (taskRunnable != null) {
                        taskRunnable.run();
                    } else {
                        Thread.sleep(16);
                    }

                } catch (InterruptedException e) {
                    // Log.d(TAG, "InterruptedException message restart check...");
                }
            }
        }

        public void startWork(Runnable taskRunnable) {
            mDownloadTask.add(taskRunnable);
        }

        public void quit() {
            mAliveThread.set(false);
        }
    }

    native void nativeOnProgress(int id, int taskId, long dl, long dlnow, long dltotal);
    native void nativeOnFinish(int id, int taskId, int errCode, String errStr, final byte[] data);
}
