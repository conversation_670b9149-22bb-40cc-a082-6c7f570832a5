package com.enhance.gameservice;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;
import android.util.Log;

/**
 * Client wrapper for GameTuningMessengerService
 * Provides the same interface as the original AIDL service
 */
public class GameTuningClient {
    private static final String TAG = "GameTuningClient";
    
    private Context mContext;
    private Messenger mService = null;
    private boolean mBound = false;
    private Messenger mMessenger;
    
    /**
     * Handler for receiving replies from the service
     */
    class IncomingHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            Bundle data = msg.getData();
            int result = data.getInt(GameTuningMessengerService.KEY_RESULT);
            Log.d(TAG, "Received reply for message " + msg.what + " with result: " + result);
        }
    }
    
    /**
     * Class for interacting with the main interface of the service
     */
    private ServiceConnection mConnection = new ServiceConnection() {
        public void onServiceConnected(ComponentName className, IBinder service) {
            mService = new Messenger(service);
            mBound = true;
            Log.d(TAG, "Service connected");
        }

        public void onServiceDisconnected(ComponentName className) {
            mService = null;
            mBound = false;
            Log.d(TAG, "Service disconnected");
        }
    };
    
    public GameTuningClient(Context context) {
        mContext = context;
        mMessenger = new Messenger(new IncomingHandler());
    }
    
    public boolean bindService() {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName("com.enhance.gameservice", 
                "com.enhance.gameservice.GameTuningMessengerService"));
        return mContext.bindService(intent, mConnection, Context.BIND_AUTO_CREATE);
    }
    
    public void unbindService() {
        if (mBound) {
            mContext.unbindService(mConnection);
            mBound = false;
        }
    }
    
    public int setPreferredResolution(int resolution) {
        if (!mBound) return -1;
        
        Message msg = Message.obtain(null, GameTuningMessengerService.MSG_SET_PREFERRED_RESOLUTION);
        msg.replyTo = mMessenger;
        Bundle data = new Bundle();
        data.putInt(GameTuningMessengerService.KEY_RESOLUTION, resolution);
        msg.setData(data);
        
        try {
            mService.send(msg);
            return 0; // Async call, return success immediately
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send message", e);
            return -1;
        }
    }
    
    public int setFramePerSecond(int fps) {
        if (!mBound) return -1;
        
        Message msg = Message.obtain(null, GameTuningMessengerService.MSG_SET_FRAME_PER_SECOND);
        msg.replyTo = mMessenger;
        Bundle data = new Bundle();
        data.putInt(GameTuningMessengerService.KEY_FPS, fps);
        msg.setData(data);
        
        try {
            mService.send(msg);
            return 0;
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send message", e);
            return -1;
        }
    }
    
    public int boostUp(int seconds) {
        if (!mBound) return -1;
        
        Message msg = Message.obtain(null, GameTuningMessengerService.MSG_BOOST_UP);
        msg.replyTo = mMessenger;
        Bundle data = new Bundle();
        data.putInt(GameTuningMessengerService.KEY_SECONDS, seconds);
        msg.setData(data);
        
        try {
            mService.send(msg);
            return 0;
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send message", e);
            return -1;
        }
    }
    
    public int getAbstractTemperature() {
        if (!mBound) return -1;
        
        Message msg = Message.obtain(null, GameTuningMessengerService.MSG_GET_ABSTRACT_TEMPERATURE);
        msg.replyTo = mMessenger;
        
        try {
            mService.send(msg);
            return 25; // Return default value, actual result comes via callback
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send message", e);
            return -1;
        }
    }
    
    public int setGamePowerSaving(boolean enable) {
        if (!mBound) return -1;
        
        Message msg = Message.obtain(null, GameTuningMessengerService.MSG_SET_GAME_POWER_SAVING);
        msg.replyTo = mMessenger;
        Bundle data = new Bundle();
        data.putBoolean(GameTuningMessengerService.KEY_ENABLE, enable);
        msg.setData(data);
        
        try {
            mService.send(msg);
            return 0;
        } catch (RemoteException e) {
            Log.e(TAG, "Failed to send message", e);
            return -1;
        }
    }
}
