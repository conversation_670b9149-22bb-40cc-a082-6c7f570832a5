#include "cocos2d.h"
#include "lua_sea.h"
#include "scripting/lua-bindings/manual/LuaBasicConversions.h"
#include "sea/dialog/components/DialogComponentsHeader.h"
#include "sea/dialog/Dialog.h"
#include "sea/common/PurchaseManager.h"

#ifdef __cplusplus
extern "C" {
#endif
#include "scripting/lua-bindings/manual/tolua_fix.h"
#ifdef __cplusplus
}
#endif

//typedef cocos2d::Value::Type ccValueType;

/**-----------------注册BoleLabel----↓----------------*/
int lua_sea_BoleLabel_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = sea::BoleLabel::create();
        object_to_luaval<sea::BoleLabel>(tolua_S, "sea.BoleLabel", ret);
        
        return 1;
    }

    if (argc >= 2 && argc <= 4) {
        cocos2d::Value font;

        if (!lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 2, &font);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_create'", nullptr);
                return 0;
            }
        }

        std::string str = "";
        ok &= luaval_to_std_string(tolua_S, 3, &str);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_create'", nullptr);
            return 0;
        }

        cocos2d::Value size;

        if (argc >= 3) {
            if (!lua_isnil(tolua_S, 4)) {
                ok &= luaval_to_ccvalue_new(tolua_S, 4, &size);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_create'", nullptr);
                    return 0;
                }
            }
        }

        cocos2d::Color4B color = cocos2d::Color4B::WHITE;

        if (argc >= 4) {
            if (!lua_isnil(tolua_S, 5)) {
                ok &= luaval_to_color4b(tolua_S, 5, &color);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_create'", nullptr);
                    return 0;
                }
            }
        }

        auto ret = sea::BoleLabel::create(font, str, size, color);
        object_to_luaval<sea::BoleLabel>(tolua_S, "sea.BoleLabel", ret);
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:create", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_init(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_init'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 4) {
        cocos2d::Value font;

        if (!lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 2, &font);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_init'", nullptr);
                return 0;
            }
        }

        std::string str = "";
        ok &= luaval_to_std_string(tolua_S, 3, &str);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_init'", nullptr);
            return 0;
        }

        cocos2d::Value size;

        if (argc >= 3) {
            if (!lua_isnil(tolua_S, 4)) {
                ok &= luaval_to_ccvalue_new(tolua_S, 4, &size);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_init'", nullptr);
                    return 0;
                }
            }
        }

        cocos2d::Color4B color = cocos2d::Color4B::WHITE;

        if (argc >= 4) {
            if (!lua_isnil(tolua_S, 5)) {
                ok &= luaval_to_color4b(tolua_S, 5, &color);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_init'", nullptr);
                    return 0;
                }
            }
        }

        auto ret = cobj->init(font, str, size, color);
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:init", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_init'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setFontSize(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setFontSize'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value size;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &size);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setFontSize'", nullptr);
            return 0;
        }

        cobj->setFontSize(size);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setFontSize", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setFontSize'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getLabelType(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getLabelType'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        int type = cobj->getLabelType();
        lua_pushnumber(tolua_S, type);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getLabelType", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getLabelType'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setTextColor(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setTextColor'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Color4B color;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setTextColor'", nullptr);
            return 0;
        }

        cobj->setTextColor(color);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setTextColor", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setTextColor'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getTextColor(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getTextColor'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto color = cobj->getTextColor();
        color4b_to_luaval(tolua_S, color);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getTextColor", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getTextColor'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setPosConfig(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setPosConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setPosConfig'", nullptr);
            return 0;
        }

        cobj->setPosConfig(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setPosConfig", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setPosConfig'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setFixLang(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setFixLang'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0 = "";
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setFixLang'", nullptr);
            return 0;
        }

        cobj->setFixLang(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setFixLang", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setFixLang'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableAutoLang(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableAutoLang'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->enableAutoLang();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableAutoLang", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableAutoLang'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setString(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setString'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string arg0 = "";
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setString'", nullptr);
            return 0;
        }

        std::string arg1 = "";

        if (argc >= 2) {
            ok &= luaval_to_std_string(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setString'", nullptr);
                return 0;
            }
        }

        cobj->setString(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setString", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setString'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getString(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getString'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto str = cobj->getString();
        lua_pushstring(tolua_S, str.c_str());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getString", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getString'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableOutline(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableOutline'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Color4B color;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableOutline'", nullptr);
            return 0;
        }

        cobj->enableOutline(color);
        return 1;
    }

    if (argc == 2) {
        cocos2d::Color4B color;
        int size = 0;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableOutline'", nullptr);
            return 0;
        }

        ok &= luaval_to_int32(tolua_S, 3, &size);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableOutline'", nullptr);
            return 0;
        }

        cobj->enableOutline(color, size);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableOutline", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableOutline'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableShadow(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Color4B color;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
            return 0;
        }

        cobj->enableShadow(color);
        return 1;
    }

    if (argc == 2) {
        cocos2d::Color4B color;
        cocos2d::Size offset;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
            return 0;
        }

        ok &= luaval_to_size(tolua_S, 3, &offset);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
            return 0;
        }

        cobj->enableShadow(color, offset);
        return 1;
    }

    if (argc == 3) {
        cocos2d::Color4B color;
        cocos2d::Size offset;
        int size = 0;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
            return 0;
        }

        ok &= luaval_to_size(tolua_S, 3, &offset);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
            return 0;
        }

        ok &= luaval_to_int32(tolua_S, 4, &size);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableShadow'", nullptr);
            return 0;
        }

        cobj->enableShadow(color, offset, size);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableShadow", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableShadow'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableGlow(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableGlow'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Color4B color;
        ok &= luaval_to_color4b(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_enableGlow'", nullptr);
            return 0;
        }

        cobj->enableGlow(color);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableGlow", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableGlow'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableItalics(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableItalics'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->enableItalics();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableItalics", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableItalics'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableBold(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableBold'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->enableBold();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableBold", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableBold'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_enableUnderline(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_enableUnderline'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->enableUnderline();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:enableUnderline", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_enableUnderline'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setLineSpacing(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setLineSpacing'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        ok &= luaval_to_number(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setLineSpacing'", nullptr);
            return 0;
        }

        cobj->setLineSpacing(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setLineSpacing", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setLineSpacing'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setDimensions(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setDimensions'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        double arg0, arg1;
        ok &= luaval_to_number(tolua_S, 2, &arg0);
        ok &= luaval_to_number(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setDimensions'", nullptr);
            return 0;
        }

        cobj->setDimensions(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setDimensions", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setDimensions'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setLineBreakWithoutSpace(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setLineBreakWithoutSpace'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0;
        ok &= luaval_to_boolean(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setLineBreakWithoutSpace'", nullptr);
            return 0;
        }

        cobj->setLineBreakWithoutSpace(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setLineBreakWithoutSpace", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setLineBreakWithoutSpace'.", &tolua_err);
#endif

    return 0;
}


int lua_sea_BoleLabel_setLabelWidth(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setLabelWidth'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        ok &= luaval_to_number(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setLabelWidth'", nullptr);
            return 0;
        }

        cobj->setLabelWidth(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setLabelWidth", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setLabelWidth'.", &tolua_err);
#endif

    return 0;
}


int lua_sea_BoleLabel_setAlignment(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setAlignment'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::TextHAlignment arg0;
        ok &= luaval_to_int32(tolua_S, 2, (int *)&arg0, "sea.BoleLabel:setAlignment");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setAlignment'", nullptr);
            return 0;
        }

        cobj->setAlignment(arg0);
        return 1;
    }

    if (argc == 2) {
        cocos2d::TextHAlignment arg0;
        ok &= luaval_to_int32(tolua_S, 2, (int *)&arg0, "sea.BoleLabel:setAlignment");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setAlignment'", nullptr);
            return 0;
        }

        cocos2d::TextVAlignment arg1;
        ok &= luaval_to_int32(tolua_S, 3, (int *)&arg1, "sea.BoleLabel:setAlignment");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setAlignment'", nullptr);
            return 0;
        }

        cobj->setAlignment(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setAlignment", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setAlignment'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setHorizontalAlignment(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setHorizontalAlignment'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::TextHAlignment arg0;
        ok &= luaval_to_int32(tolua_S, 2, (int *)&arg0, "sea.BoleLabel:setHorizontalAlignment");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setHorizontalAlignment'", nullptr);
            return 0;
        }

        cobj->setHorizontalAlignment(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setHorizontalAlignment", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setHorizontalAlignment'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setVerticalAlignment(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setVerticalAlignment'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::TextVAlignment arg0;
        ok &= luaval_to_int32(tolua_S, 2, (int *)&arg0, "sea.BoleLabel:setVerticalAlignment");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setVerticalAlignment'", nullptr);
            return 0;
        }

        cobj->setVerticalAlignment(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setVerticalAlignment", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setVerticalAlignment'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getLineHeight(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getLineHeight'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        float height = cobj->getLineHeight();
        lua_pushnumber(tolua_S, height);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getLineHeight", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getLineHeight'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setOverflow(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setOverflow'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Label::Overflow arg0;
        ok &= luaval_to_int32(tolua_S, 2, (int *)&arg0, "sea.BoleLabel:setOverflow");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setOverflow'", nullptr);
            return 0;
        }

        cobj->setOverflow(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setOverflow", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setOverflow'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setAdditionalKerning(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setAdditionalKerning'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        ok &= luaval_to_number(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setAdditionalKerning'", nullptr);
            return 0;
        }

        cobj->setAdditionalKerning(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setAdditionalKerning", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setAdditionalKerning'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getContentSize(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getContentSize'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto size = cobj->getContentSize();
        size_to_luaval(tolua_S, size);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getContentSize", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getContentSize'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getBoundingBox(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getBoundingBox'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto rect = cobj->getBoundingBox();
        rect_to_luaval(tolua_S, rect);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getBoundingBox", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getBoundingBox'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getChildren(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_getChildren'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cocos2d::Vector<cocos2d::Node *> ret;
        ccvector_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:getChildren", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_getChildren'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setAnchorPoint(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setAnchorPoint'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Vec2 arg0;
        ok &= luaval_to_vec2(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setAnchorPoint'", nullptr);
            return 0;
        }

        cobj->setAnchorPoint(arg0);
        return 1;
    }

    if (argc == 2) {
        double arg0, arg1;
        ok &= luaval_to_number(tolua_S, 2, &arg0);
        ok &= luaval_to_number(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setAnchorPoint'", nullptr);
            return 0;
        }

        cobj->setAnchorPoint(cocos2d::Vec2(arg0, arg1));
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleLabel:setAnchorPoint", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setAnchorPoint'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_getAnchorPoint(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setAnchorPoint'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto anchor = cobj->getAnchorPoint();
        vec2_to_luaval(tolua_S, anchor);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setAnchorPoint", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setAnchorPoint'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleLabel_setColor(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleLabel *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleLabel_setColor'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Color3B arg0;
        ok &= luaval_to_color3b(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleLabel_setColor'", nullptr);
            return 0;
        }

        cobj->setColor(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.BoleLabel:setColor", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleLabel_setColor'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_label(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleLabel");
    tolua_cclass(tolua_S, "BoleLabel", "sea.BoleLabel", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "BoleLabel");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleLabel_create);
        tolua_function(tolua_S, "init", lua_sea_BoleLabel_init);
        tolua_function(tolua_S, "setFontSize", lua_sea_BoleLabel_setFontSize);
        tolua_function(tolua_S, "getLabelType", lua_sea_BoleLabel_getLabelType);
        tolua_function(tolua_S, "setTextColor", lua_sea_BoleLabel_setTextColor);
        tolua_function(tolua_S, "getTextColor", lua_sea_BoleLabel_getTextColor);
        tolua_function(tolua_S, "setPosConfig", lua_sea_BoleLabel_setPosConfig);
        tolua_function(tolua_S, "setFixLang", lua_sea_BoleLabel_setFixLang);
        tolua_function(tolua_S, "enableAutoLang", lua_sea_BoleLabel_enableAutoLang);
        tolua_function(tolua_S, "setString", lua_sea_BoleLabel_setString);
        tolua_function(tolua_S, "getString", lua_sea_BoleLabel_getString);
        tolua_function(tolua_S, "enableOutline", lua_sea_BoleLabel_enableOutline);
        tolua_function(tolua_S, "enableShadow", lua_sea_BoleLabel_enableShadow);
        tolua_function(tolua_S, "enableGlow", lua_sea_BoleLabel_enableGlow);
        tolua_function(tolua_S, "enableItalics", lua_sea_BoleLabel_enableItalics);
        tolua_function(tolua_S, "enableBold", lua_sea_BoleLabel_enableBold);
        tolua_function(tolua_S, "enableUnderline", lua_sea_BoleLabel_enableUnderline);
        tolua_function(tolua_S, "setLineSpacing", lua_sea_BoleLabel_setLineSpacing);
        tolua_function(tolua_S, "setDimensions", lua_sea_BoleLabel_setDimensions);
        tolua_function(tolua_S, "setLineBreakWithoutSpace", lua_sea_BoleLabel_setLineBreakWithoutSpace);
        tolua_function(tolua_S, "setLabelWidth", lua_sea_BoleLabel_setLabelWidth);
        tolua_function(tolua_S, "setAlignment", lua_sea_BoleLabel_setAlignment);
        tolua_function(tolua_S, "setHorizontalAlignment", lua_sea_BoleLabel_setHorizontalAlignment);
        tolua_function(tolua_S, "setVerticalAlignment", lua_sea_BoleLabel_setVerticalAlignment);
        tolua_function(tolua_S, "getLineHeight", lua_sea_BoleLabel_getLineHeight);
        tolua_function(tolua_S, "setOverflow", lua_sea_BoleLabel_setOverflow);
        tolua_function(tolua_S, "setAdditionalKerning", lua_sea_BoleLabel_setAdditionalKerning);
        tolua_function(tolua_S, "getContentSize", lua_sea_BoleLabel_getContentSize);
        tolua_function(tolua_S, "getBoundingBox", lua_sea_BoleLabel_getBoundingBox);
        tolua_function(tolua_S, "setAnchorPoint", lua_sea_BoleLabel_setAnchorPoint);
        tolua_function(tolua_S, "getAnchorPoint", lua_sea_BoleLabel_getAnchorPoint);
        tolua_function(tolua_S, "setColor", lua_sea_BoleLabel_setColor);
        tolua_function(tolua_S, "getChildren", lua_sea_BoleLabel_getChildren);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleLabel).name();
    g_luaType[typeName] = "sea.BoleLabel";
    g_typeCast["BoleLabel"] = "sea.BoleLabel";
    return 1;
}

/**-------------注册 BoleButton -------------*/

int lua_sea_BoleButton_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 4) {
        cocos2d::Value arg0 = cocos2d::Value::Null, arg1 = cocos2d::Value::Null, arg2 = cocos2d::Value::Null, arg3 = cocos2d::Value::Null;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 4 && !lua_isnil(tolua_S, 5)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 5, &arg3);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_create'", nullptr);
                return 0;
            }
        }

        auto ret = sea::BoleButton::create(arg0, arg1, arg2, arg3);
        object_to_luaval<sea::BoleButton>(tolua_S, "sea.BoleButton", ret);
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~4 \n", "sea.BoleButton:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_reload(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_reload'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 4) {
        cocos2d::Value arg0 = cocos2d::Value::Null, arg1 = cocos2d::Value::Null, arg2 = cocos2d::Value::Null, arg3 = cocos2d::Value::Null;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_reload'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_reload'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_reload'", nullptr);
                return 0;
            }
        }

        if (argc >= 4 && !lua_isnil(tolua_S, 5)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 5, &arg3);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_reload'", nullptr);
                return 0;
            }
        }

        cobj->reload(arg0, arg1, arg2, arg3);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~4 \n", "sea.BoleButton:reload", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_reload'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_addLoading(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_reload'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 2) {
        cocos2d::Value arg0 = cocos2d::Value::Null, arg1 = cocos2d::Value::Null;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addLoading'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addLoading'", nullptr);
                return 0;
            }
        }

        cobj->addLoading(arg0, arg1);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~2 \n", "sea.BoleButton:addLoading", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_addLoading'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_removeLoading(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_removeLoading'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->removeLoading();

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleButton:removeLoading", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_removeLoading'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_setAutoClose(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_setAutoClose'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 1) {
        cocos2d::ValueMap arg0 = cocos2d::ValueMapNull;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_setAutoClose'", nullptr);
                return 0;
            }
        }

        cobj->setAutoClose(arg0);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~1 \n", "sea.BoleButton:setAutoClose", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_setAutoClose'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_callTextNode(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_callTextNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_callTextNode'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->callTextNode([ = ](cocos2d::Node *node, int index) {
            sea::excuteLuaFunction(handler, 2, [ = ](cocos2d::LuaStack *stack) {
                if (node) {
                    stack->pushObject(node, "cc.Node");
                } else {
                    stack->pushNil();
                }

                stack->pushInt(index);
            });
        });

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButton:callTextNode", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_callTextNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_updateNode(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_updateNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 4) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cocos2d::Value arg1 = cocos2d::Value::Null;
        cocos2d::ValueMap arg2 = cocos2d::ValueMapNull;
        cocos2d::Value arg3 = cocos2d::Value::Null;

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
                return 0;
            }
        }

        if (argc >= 4 && !lua_isnil(tolua_S, 5)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 5, &arg3);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
                return 0;
            }
        }

        
        cobj->updateNode([ = ](sea::BoleButton *btn, cocos2d::Node *node1, const cocos2d::Value& params, int index, cocos2d::Node *node2) {
            sea::excuteLuaFunction(handler, 5, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, btn, "sea.BoleButton");
                sea::pushValueToStack(stack, node1, "cc.Node");
                sea::pushValueToStack(stack, params);
                stack->pushInt(index);
                sea::pushValueToStack(stack, node2, "cc.Node");
            });
        }, arg1, arg2, arg3);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~4 \n", "sea.BoleButton:updateNode", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_updateNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_updateTextNode(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_updateNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cocos2d::Value arg1 = cocos2d::Value::Null;
        cocos2d::Value arg2 = cocos2d::Value::Null;

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_updateNode'", nullptr);
                return 0;
            }
        }

        cobj->updateTextNode([ = ](sea::BoleButton *btn, cocos2d::Node *node1, const cocos2d::Value& params, int index, cocos2d::Node *node2) {
            sea::excuteLuaFunction(handler, 5, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, btn, "sea.BoleButton");
                sea::pushValueToStack(stack, node1, "cc.Node");
                sea::pushValueToStack(stack, params);
                stack->pushInt(index);
                sea::pushValueToStack(stack, node2, "cc.Node");
            });
        }, arg1, arg2);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~4 \n", "sea.BoleButton:updateNode", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_updateNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_setTouchEnabledWithBright(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_setTouchEnabledWithBright'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        bool arg0, arg1 = false;
        ok &= luaval_to_boolean(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_setTouchEnabledWithBright'", nullptr);
            return 0;
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_boolean(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_setTouchEnabledWithBright'", nullptr);
                return 0;
            }
        }

        cobj->setTouchEnabledWithBright(arg0, arg1);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleButton:setTouchEnabledWithBright", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_setTouchEnabledWithBright'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_addAutoContinueMask(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_addAutoContinueMask'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 4) {
        std::string arg0;
        std::function<void()> arg1 = nullptr;
        bool arg2 = false;
        double arg3 = -1;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addAutoContinueMask'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            if (!lua_isnil(tolua_S, 3)) {
#if COCOS2D_DEBUG >= 1

                if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addAutoContinueMask'", nullptr);
                    return 0;
                }

#endif

                LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));
                arg1 = [ = ]() {
                        sea::excuteLuaFunction(handler);
                    };
            }

            if (argc >= 3) {
                if (!lua_isnil(tolua_S, 4)) {
                    ok &= luaval_to_boolean(tolua_S, 4, &arg2);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addAutoContinueMask'", nullptr);
                        return 0;
                    }
                }

                if (argc >= 4) {
                    if (!lua_isnil(tolua_S, 5)) {
                        ok &= luaval_to_number(tolua_S, 5, &arg3);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addAutoContinueMask'", nullptr);
                            return 0;
                        }
                    }

                    cobj->addAutoContinueMask(arg0, arg1, arg2, arg3);
                } else {
                    cobj->addAutoContinueMask(arg0, arg1, arg2);
                }
            } else {
                cobj->addAutoContinueMask(arg0, arg1);
            }
        } else {
            cobj->addAutoContinueMask(arg0);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~4 \n", "sea.BoleButton:addAutoContinueMask", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_addAutoContinueMask'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_clearAutoContinueMask(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_clearAutoContinueMask'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->clearAutoContinueMask();

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleButton:clearAutoContinueMask", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_clearAutoContinueMask'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_addClickEventListenerData(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_addClickEventListenerData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addClickEventListenerData'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        auto arg0 = [ = ](sea::BoleButton *btn, const cocos2d::Value& params) {
                sea::excuteLuaFunction(handler, 2, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, btn, "sea.BoleButton");
                sea::pushValueToStack(stack, params);
            });
            };

        cocos2d::Value arg1 = cocos2d::Value::Null;
        double arg2 = -1;

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addClickEventListenerData'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_number(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_addClickEventListenerData'", nullptr);
                return 0;
            }
        }

        cobj->addClickEventListenerData(arg0, arg1, arg2);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~3 \n", "sea.BoleButton:addClickEventListenerData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_addClickEventListenerData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_setAutoDisable(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_setAutoDisable'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 1) {
        double arg0 = -1;

        if (argc >= 1) {
            if (!lua_isnil(tolua_S, 2)) {
                ok &= luaval_to_number(tolua_S, 2, &arg0);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_setAutoDisable'", nullptr);
                    return 0;
                }
            }

            cobj->setAutoDisable(arg0);
        } else {
            cobj->setAutoDisable();
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~1 \n", "sea.BoleButton:setAutoDisable", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_setAutoDisable'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButton_setClickSoundEnable(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButton_setClickSoundEnable'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0 = false;

        ok &= luaval_to_boolean(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButton_setClickSoundEnable'", nullptr);
            return 0;
        }

        cobj->setClickSoundEnable(arg0);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButton:setClickSoundEnable", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButton_setClickSoundEnable'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_button(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleButton");
    tolua_cclass(tolua_S, "BoleButton", "sea.BoleButton", "ccui.Button", nullptr);

    tolua_beginmodule(tolua_S, "BoleButton");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleButton_create);
        tolua_function(tolua_S, "reload", lua_sea_BoleButton_reload);
        tolua_function(tolua_S, "addLoading", lua_sea_BoleButton_addLoading);
        tolua_function(tolua_S, "removeLoading", lua_sea_BoleButton_removeLoading);
        tolua_function(tolua_S, "setAutoClose", lua_sea_BoleButton_setAutoClose);
        tolua_function(tolua_S, "callTextNode", lua_sea_BoleButton_callTextNode);
        tolua_function(tolua_S, "updateNode", lua_sea_BoleButton_updateNode);
        tolua_function(tolua_S, "updateTextNode", lua_sea_BoleButton_updateTextNode);
        tolua_function(tolua_S, "setTouchEnabledWithBright", lua_sea_BoleButton_setTouchEnabledWithBright);
        tolua_function(tolua_S, "addAutoContinueMask", lua_sea_BoleButton_addAutoContinueMask);
        tolua_function(tolua_S, "clearAutoContinueMask", lua_sea_BoleButton_clearAutoContinueMask);
        tolua_function(tolua_S, "addClickEventListenerData", lua_sea_BoleButton_addClickEventListenerData);
        tolua_function(tolua_S, "setAutoDisable", lua_sea_BoleButton_setAutoDisable);
        tolua_function(tolua_S, "setClickSoundEnable", lua_sea_BoleButton_setClickSoundEnable);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleButton).name();
    g_luaType[typeName] = "sea.BoleButton";
    g_typeCast["BoleButton"] = "sea.BoleButton";
    return 1;
}

/****-----------注册 BoleButtonPay -------**/
int lua_sea_BoleButtonPay_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_create'", nullptr);
            return 0;
        }

        auto ret = sea::BoleButtonPay::create(arg0);
        object_to_luaval<sea::BoleButtonPay>(tolua_S, "sea.BoleButtonPay", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setFunClick(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setFunClick'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setFunClick'", nullptr);
            return 0;
        }

        cobj->setFunClick(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setFunClick", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setFunClick'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setFunEnd(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setFunEnd'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setFunEnd'", nullptr);
            return 0;
        }

        cobj->setFunEnd(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setFunEnd", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setFunEnd'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setBuyData(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setBuyData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setBuyData'", nullptr);
            return 0;
        }

        cobj->setBuyData(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setBuyData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setBuyData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setExtraArgs(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setExtraArgs'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setExtraArgs'", nullptr);
            return 0;
        }

        cobj->setExtraArgs(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setExtraArgs", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setExtraArgs'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setWhereTag(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setWhereTag'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setWhereTag'", nullptr);
            return 0;
        }

        cobj->setWhereTag(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setWhereTag", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setWhereTag'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setText(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setText'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setText'", nullptr);
            return 0;
        }

        cobj->setText(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setText", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setText'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setTextPos(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setTextPos'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setTextPos'", nullptr);
            return 0;
        }

        cobj->setTextPos(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setTextPos", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setTextPos'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_updatePath(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_updatePath'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        cocos2d::Value arg0, arg1;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_updatePath'", nullptr);
            return 0;
        }
        
        ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_updatePath'", nullptr);
            return 0;
        }

        cobj->updatePath(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2 \n", "sea.BoleButtonPay:updatePath", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_updatePath'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_updateBtnNode(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_updateBtnNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 2) {
        if (argc == 0) {
            cobj->updateBtnNode();
            return 1;
        }
        
        cocos2d::Value arg0, arg1;
        
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_updateBtnNode'", nullptr);
            return 0;
        }
        
        if (argc == 1) {
            cobj->updateBtnNode(arg0);
            return 1;
        }
        
        ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_updateBtnNode'", nullptr);
            return 0;
        }

        cobj->updateBtnNode(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~2 \n", "sea.BoleButtonPay:updateBtnNode", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_updateBtnNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setSaleSpace(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setSaleSpace'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        ok &= luaval_to_number(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setSaleSpace'", nullptr);
            return 0;
        }

        cobj->setSaleSpace(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setSaleSpace", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setSaleSpace'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleButtonPay_setSupportBucks(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleButtonPay *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleButtonPay_setSupportBucks'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0;
        ok &= luaval_to_boolean(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleButtonPay_setSupportBucks'", nullptr);
            return 0;
        }

        cobj->setSupportBucks(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleButtonPay:setSupportBucks", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleButtonPay_setSupportBucks'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_button_pay(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleButtonPay");
    tolua_cclass(tolua_S, "BoleButtonPay", "sea.BoleButtonPay", "sea.BoleButton", nullptr);

    tolua_beginmodule(tolua_S, "BoleButtonPay");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleButtonPay_create);
        tolua_function(tolua_S, "setFunClick", lua_sea_BoleButtonPay_setFunClick);
        tolua_function(tolua_S, "setFunEnd", lua_sea_BoleButtonPay_setFunEnd);
        tolua_function(tolua_S, "setBuyData", lua_sea_BoleButtonPay_setBuyData);
        tolua_function(tolua_S, "setExtraArgs", lua_sea_BoleButtonPay_setExtraArgs);
        tolua_function(tolua_S, "setWhereTag", lua_sea_BoleButtonPay_setWhereTag);
        tolua_function(tolua_S, "setText", lua_sea_BoleButtonPay_setText);
        tolua_function(tolua_S, "setTextPos", lua_sea_BoleButtonPay_setTextPos);
        tolua_function(tolua_S, "setSaleSpace", lua_sea_BoleButtonPay_setSaleSpace);
        tolua_function(tolua_S, "setSupportBucks", lua_sea_BoleButtonPay_setSupportBucks);
        tolua_function(tolua_S, "updatePath", lua_sea_BoleButtonPay_updatePath);
        tolua_function(tolua_S, "updateBtnNode", lua_sea_BoleButtonPay_updateBtnNode);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleButton).name();
    g_luaType[typeName] = "sea.BoleButton";
    g_typeCast["BoleButton"] = "sea.BoleButton";
    return 1;
}

/**BoleProgress**/

int lua_sea_BoleProgress_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
        std::string arg0, arg1;
        cocos2d::ValueMap arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_std_string(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_std_string(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_create'", nullptr);
                return 0;
            }
        }

        sea::BoleProgress *ret = nullptr;

        if (argc == 1) {
            ret = sea::BoleProgress::create(arg0);
        } else if (argc == 2) {
            ret = sea::BoleProgress::create(arg0, arg1);
        } else {
            ret = sea::BoleProgress::create(arg0, arg1, arg2);
        }

        object_to_luaval<sea::BoleProgress>(tolua_S, "sea.BoleProgress", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~3 \n", "sea.BoleProgress:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_initWithData(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_initWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
        std::string arg0, arg1;
        cocos2d::ValueMap arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_std_string(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_initWithData'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_std_string(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_initWithData'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_initWithData'", nullptr);
                return 0;
            }
        }

        if (argc == 1) {
            cobj->initWithData(arg0);
        } else if (argc == 2) {
            cobj->initWithData(arg0, arg1);
        } else {
            cobj->initWithData(arg0, arg1, arg2);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~3 \n", "sea.BoleProgress:initWithData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_initWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_resetData(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_resetData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string arg0;
        int arg1;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_std_string(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_resetData'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_int32(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_resetData'", nullptr);
                return 0;
            }
        }

        if (argc == 1) {
            cobj->resetData(arg0);
        } else {
            cobj->resetData(arg0, static_cast<sea::TextureResType>(arg1));
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleProgress:resetData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_resetData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_setProTexture(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_setProTexture'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string arg0;
        int arg1;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_std_string(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_setProTexture'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_int32(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_setProTexture'", nullptr);
                return 0;
            }
        }

        if (argc == 1) {
            cobj->setProTexture(arg0);
        } else {
            cobj->setProTexture(arg0, static_cast<sea::TextureResType>(arg1));
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleProgress:setProTexture", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_setProTexture'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_addLabel(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleProgress *cobj = nullptr;

    tolua_Error tolua_err;


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_addLabel'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        if (tolua_isusertype(tolua_S, 2, "cc.Label", 0, &tolua_err)) {
            auto ccLabel = (cocos2d::Label *)tolua_tousertype(tolua_S, 2, 0);

            if (!ccLabel) {
#if COCOS2D_DEBUG >= 1
                goto tolua_lerror;
#else
                return 0;

#endif
            }

            cobj->addLabel(ccLabel);
        } else if (tolua_isusertype(tolua_S, 2, "sea.BoleLabel", 0, &tolua_err)) {
            auto seaLabel = (sea::BoleLabel *)tolua_tousertype(tolua_S, 2, 0);

            if (!seaLabel) {
#if COCOS2D_DEBUG >= 1
                goto tolua_lerror;
#else
                return 0;

#endif
            }

            cobj->addLabel(seaLabel);
        } else {
#if COCOS2D_DEBUG >= 1
            goto tolua_lerror;
#else
            return 0;

#endif
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleProgress:addLabel", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_addLabel'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_setLabel(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleProgress *cobj = nullptr;

    tolua_Error tolua_err;


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_setLabel'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        if (tolua_isusertype(tolua_S, 2, "cc.Label", 0, &tolua_err)) {
            auto ccLabel = (cocos2d::Label *)tolua_tousertype(tolua_S, 2, 0);

            if (!ccLabel) {
#if COCOS2D_DEBUG >= 1
                goto tolua_lerror;
#else
                return 0;

#endif
            }

            cobj->setLabel(ccLabel);
        } else if (tolua_isusertype(tolua_S, 2, "sea.BoleLabel", 0, &tolua_err)) {
            auto seaLabel = (sea::BoleLabel *)tolua_tousertype(tolua_S, 2, 0);

            if (!seaLabel) {
#if COCOS2D_DEBUG >= 1
                goto tolua_lerror;
#else
                return 0;

#endif
            }

            cobj->setLabel(seaLabel);
        } else {
#if COCOS2D_DEBUG >= 1
            goto tolua_lerror;
#else
            return 0;

#endif
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleProgress:setLabel", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_setLabel'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_getLabel(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_getLabel'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto node = cobj->getLabel();
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", node);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleProgress:getLabel", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_getLabel'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_setStringFun(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_setStringFun'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->setStringFun([ = ](float p, float t) {
            auto s = sea::excuteLuaFunction(handler, 2, [ = ](LuaStack *stack) {
                stack->pushFloat(p);
                stack->pushFloat(t);
            });
            auto ret = s.asString();
            return ret;
        });

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleProgress:setStringFun", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_setStringFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_getPercent(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_getPercent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto p = cobj->getPercent();
        lua_pushnumber(tolua_S, p);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleProgress:getPercent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_getPercent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_setPercent(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_setPercent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
        double arg0, arg1;
        cocos2d::Value arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_number(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_setPercent'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_number(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_setPercent'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_setPercent'", nullptr);
                return 0;
            }
        }

        if (argc == 1) {
            cobj->setPercent(arg0);
        } else if (argc == 2) {
            cobj->setPercent(arg0, arg1);
        } else {
            cobj->setPercent(arg0, arg1, arg2);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~3 \n", "sea.BoleProgress:setPercent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_setPercent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_setPercentage(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_setPercentage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;

        if (!lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_number(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_setPercentage'", nullptr);
                return 0;
            }
        }

        cobj->setPercentage(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleProgress:setPercentage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_setPercentage'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_resetDotNodes(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_resetDotNodes'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        cocos2d::ValueVector arg0;
        bool arg1;

        if (!lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_ccvaluevector(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_resetDotNodes'", nullptr);
                return 0;
            }
        }

        if (!lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_boolean(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_resetDotNodes'", nullptr);
                return 0;
            }
        }

        cobj->resetDotNodes(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2 \n", "sea.BoleProgress:resetDotNodes", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_resetDotNodes'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleProgress_getDotPercentWithData(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleProgress *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleProgress", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleProgress *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleProgress_getDotPercentWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 5) {
        double arg0;
        std::vector<float> arg1;
        cocos2d::ValueVector arg2;
        std::vector<std::pair<float, float> > arg3;
        bool arg4;

        if (!lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_number(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_getDotPercentWithData'", nullptr);
                return 0;
            }
        }

        if (!lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_std_vector_float(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_getDotPercentWithData'", nullptr);
                return 0;
            }
        }

        if (!lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluevector_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_getDotPercentWithData'", nullptr);
                return 0;
            }
        }

        if (!lua_isnil(tolua_S, 5)) {
            cocos2d::ValueVector argT;
            ok &= luaval_to_ccvaluevector_new(tolua_S, 5, &argT);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_getDotPercentWithData'", nullptr);
                return 0;
            }

            for (const auto& iter : argT) {
                if (iter.getType() != ccValueType::VECTOR) {
                    continue;
                }

                auto vec = iter.asValueVector();

                for (const auto& iter2 : vec) {
                    if (iter2.getType() != ccValueType::VECTOR) {
                        continue;
                    }

                    auto vec2 = iter2.asValueVector();

                    if (vec2.size() < 2) {
                        continue;
                    }

                    arg3.push_back({ vec2[0].asFloat(), vec2[1].asFloat() });
                }
            }
        }

        if (!lua_isnil(tolua_S, 6)) {
            ok &= luaval_to_boolean(tolua_S, 6, &arg4);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleProgress_getDotPercentWithData'", nullptr);
                return 0;
            }
        }

        auto ret = cobj->getDotPercentWithData(arg0, arg1, arg2, arg3, arg4);
        lua_pushnumber(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 5 \n", "sea.BoleProgress:getDotPercentWithData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleProgress_getDotPercentWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_progress(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleProgress");
    tolua_cclass(tolua_S, "BoleProgress", "sea.BoleProgress", "cc.Sprite", nullptr);

    tolua_beginmodule(tolua_S, "BoleProgress");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleProgress_create);
        tolua_function(tolua_S, "initWithData", lua_sea_BoleProgress_initWithData);
        tolua_function(tolua_S, "resetData", lua_sea_BoleProgress_resetData);
        tolua_function(tolua_S, "setProTexture", lua_sea_BoleProgress_setProTexture);
        tolua_function(tolua_S, "addLabel", lua_sea_BoleProgress_addLabel);
        tolua_function(tolua_S, "setLabel", lua_sea_BoleProgress_setLabel);
        tolua_function(tolua_S, "getLabel", lua_sea_BoleProgress_getLabel);
        tolua_function(tolua_S, "setStringFun", lua_sea_BoleProgress_setStringFun);
        tolua_function(tolua_S, "getPercent", lua_sea_BoleProgress_getPercent);
        tolua_function(tolua_S, "setPercent", lua_sea_BoleProgress_setPercent);
        tolua_function(tolua_S, "setPercentage", lua_sea_BoleProgress_setPercentage);
        tolua_function(tolua_S, "resetDotNodes", lua_sea_BoleProgress_resetDotNodes);
        tolua_function(tolua_S, "getDotPercentWithData", lua_sea_BoleProgress_getDotPercentWithData);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleProgress).name();
    g_luaType[typeName] = "sea.BoleProgress";
    g_typeCast["BoleProgress"] = "sea.BoleProgress";
    return 1;
}

/**BoleSlider**/
int lua_sea_BoleSlider_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleSlider", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 3) {
        std::string arg0;
        cocos2d::Value arg1;
        cocos2d::ValueMap arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_std_string(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_create'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_create'", nullptr);
                return 0;
            }
        }

        sea::BoleSlider *ret = nullptr;

        if (argc == 2) {
            ret = sea::BoleSlider::create(arg0, arg1);
        } else {
            ret = sea::BoleSlider::create(arg0, arg1, arg2);
        }

        object_to_luaval<sea::BoleSlider>(tolua_S, "sea.BoleSlider", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.BoleSlider:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSlider_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSlider_initWithData(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleSlider *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSlider", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSlider *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSlider_initWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 3) {
        std::string arg0;
        cocos2d::Value arg1;
        cocos2d::ValueMap arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_std_string(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_initWithData'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_initWithData'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_initWithData'", nullptr);
                return 0;
            }
        }

        if (argc == 2) {
            cobj->initWithData(arg0, arg1);
        } else {
            cobj->initWithData(arg0, arg1, arg2);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.BoleSlider:initWithData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSlider_initWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSlider_setRange(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleSlider *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSlider", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSlider *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSlider_setRange'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 3) {
        double arg0, arg1, arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_number(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_setRange'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_number(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_setRange'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_number(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_setRange'", nullptr);
                return 0;
            }
        }

        if (argc == 0) {
            cobj->setRange();
        } else if (argc == 1) {
            cobj->setRange(arg0);
        } else if (argc == 2) {
            cobj->setRange(arg0, arg1);
        } else {
            cobj->setRange(arg0, arg1, arg2);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~3 \n", "sea.BoleSlider:setRange", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSlider_setRange'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSlider_setPercent(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::BoleSlider *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSlider", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSlider *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSlider_setPercent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        ok &= luaval_to_number(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSlider_setPercent'", nullptr);
            return 0;
        }

        cobj->setPercent(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleSlider:setPercent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSlider_setPercent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSlider_getPercent(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleSlider *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSlider", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSlider *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSlider_getPercent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getPercent();
        lua_pushnumber(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleSlider:getPercent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSlider_getPercent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSlider_addEventListener(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleSlider *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSlider", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSlider *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSlider_addEventListener'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->addEventListener(handler);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleSlider:addEventListener", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSlider_addEventListener'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_slider(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleSlider");
    tolua_cclass(tolua_S, "BoleSlider", "sea.BoleSlider", "ccui.Slider", nullptr);

    tolua_beginmodule(tolua_S, "BoleSlider");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleSlider_create);
        tolua_function(tolua_S, "initWithData", lua_sea_BoleSlider_initWithData);
        tolua_function(tolua_S, "setRange", lua_sea_BoleSlider_setRange);
        tolua_function(tolua_S, "setPercent", lua_sea_BoleSlider_setPercent);
        tolua_function(tolua_S, "getPercent", lua_sea_BoleSlider_getPercent);
        tolua_function(tolua_S, "addEventListener", lua_sea_BoleSlider_addEventListener);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleSlider).name();
    g_luaType[typeName] = "sea.BoleSlider";
    g_typeCast["BoleSlider"] = "sea.BoleSlider";
    return 1;
}

/**-----------------注册 BoleOrder ----↓----------------*/
int lua_sea_BoleOrder_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_create'", nullptr);
            return 0;
        }

        sea::BoleOrder *ret = sea::BoleOrder::create(arg0);

        object_to_luaval<sea::BoleOrder>(tolua_S, "sea.BoleOrder", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_isInKeyInConfig(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_isInKeyInConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_isInKeyInConfig'", nullptr);
            return 0;
        }

        auto ret = cobj->isInKeyInConfig(arg0);
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:isInKeyInConfig", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_isInKeyInConfig'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setUserAnchorX(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setUserAnchorX'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setUserAnchorX'", nullptr);
            return 0;
        }

        cobj->setUserAnchorX(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setUserAnchorX", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setUserAnchorX'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setUserAnchorY(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setUserAnchorY'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setUserAnchorY'", nullptr);
            return 0;
        }

        cobj->setUserAnchorY(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setUserAnchorY", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setUserAnchorY'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setOffset(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setOffset'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setOffset'", nullptr);
            return 0;
        }

        cobj->setOffset(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setOffset", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setOffset'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setSpace(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setSpace'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setSpace'", nullptr);
            return 0;
        }

        cobj->setSpace(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setSpace", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setSpace'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setWidth(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setWidth'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setWidth'", nullptr);
            return 0;
        }

        cobj->setWidth(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setWidth", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setWidth'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setKeysMapAndUpdate(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setKeysMapAndUpdate'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setKeysMapAndUpdate'", nullptr);
            return 0;
        }

        cobj->setKeysMapAndUpdate(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setKeysMapAndUpdate", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setKeysMapAndUpdate'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setKeys(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setKeys'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setKeys'", nullptr);
            return 0;
        }

        cobj->setKeys(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setKeys", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setKeys'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setKeysMap(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setKeysMap'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setKeysMap'", nullptr);
            return 0;
        }

        cobj->setKeysMap(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setKeysMap", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setKeysMap'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_setText(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_setText'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_setText'", nullptr);
            return 0;
        }

        cobj->setText(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:setText", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_setText'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_updateString(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_updateString'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->updateString();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleOrder:updateString", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_updateString'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleOrder_getKeyNodeByName(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleOrder_getKeyNodeByName'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleOrder_getKeyNodeByName'", nullptr);
            return 0;
        }

        auto ret = cobj->getKeyNodeByName(arg0);
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleOrder:getKeyNodeByName", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleOrder_getKeyNodeByName'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_order(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleOrder");
    tolua_cclass(tolua_S, "BoleOrder", "sea.BoleOrder", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "BoleOrder");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleOrder_create);
        tolua_function(tolua_S, "isInKeyInConfig", lua_sea_BoleOrder_isInKeyInConfig);
        tolua_function(tolua_S, "setUserAnchorX", lua_sea_BoleOrder_setUserAnchorX);
        tolua_function(tolua_S, "setUserAnchorY", lua_sea_BoleOrder_setUserAnchorY);
        tolua_function(tolua_S, "setOffset", lua_sea_BoleOrder_setOffset);
        tolua_function(tolua_S, "setSpace", lua_sea_BoleOrder_setSpace);
        tolua_function(tolua_S, "setWidth", lua_sea_BoleOrder_setWidth);
        tolua_function(tolua_S, "updateString", lua_sea_BoleOrder_updateString);
        tolua_function(tolua_S, "setKeysMapAndUpdate", lua_sea_BoleOrder_setKeysMapAndUpdate);
        tolua_function(tolua_S, "getKeyNodeByName", lua_sea_BoleOrder_getKeyNodeByName);
        tolua_function(tolua_S, "setKeys", lua_sea_BoleOrder_setKeys);
        tolua_function(tolua_S, "setKeysMap", lua_sea_BoleOrder_setKeysMap);
        tolua_function(tolua_S, "setText", lua_sea_BoleOrder_setText);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleOrder).name();
    g_luaType[typeName] = "sea.BoleOrder";
    g_typeCast["BoleOrder"] = "sea.BoleOrder";
    return 1;
}

/**BolePageView**/
int lua_sea_BolePageView_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;
        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_create'", nullptr);
            return 0;
        }

        sea::BolePageView *ret = sea::BolePageView::create(arg0);

        object_to_luaval<sea::BolePageView>(tolua_S, "sea.BolePageView", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BolePageView:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_resetWithData(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_resetWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 3) {
        bool arg0;
        int arg1;
        double arg2;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_boolean(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_resetWithData'", nullptr);
                return 0;
            }

            if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
                ok &= luaval_to_int32(tolua_S, 3, &arg1);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_resetWithData'", nullptr);
                    return 0;
                }

                if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
                    ok &= luaval_to_number(tolua_S, 4, &arg2);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_resetWithData'", nullptr);
                        return 0;
                    }

                    cobj->resetWithData(arg0, arg1, arg2);
                } else {
                    cobj->resetWithData(arg0, arg1);
                }
            } else {
                cobj->resetWithData(arg0);
            }
        } else {
            cobj->resetWithData();
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 ~ 3 \n", "sea.BolePageView:resetWithData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_resetWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_registerEvent(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_registerEvent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        int arg0;
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_registerEvent'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_registerEvent'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        cobj->registerEvent(arg0, handler);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2 \n", "sea.BolePageView:registerEvent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_registerEvent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_callEventFun(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_callEventFun'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        int arg0;
        cocos2d::Value arg1;
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_callEventFun'", nullptr);
            return 0;
        }

        ok &= luaval_to_ccvalue(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_callEventFun'", nullptr);
            return 0;
        }

        auto ret = cobj->callEventFun((sea::BolePageView::EVENT)arg0, arg1);
        ccvalue_to_luaval(tolua_S, ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2 \n", "sea.BolePageView:callEventFun", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_callEventFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_isInit(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_isInit'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->isInit();
        lua_pushboolean(tolua_S, ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BolePageView:isInit", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_isInit'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_initPage(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_initPage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        int arg0;
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_initPage'", nullptr);
            return 0;
        }

        cobj->initPage(arg0);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BolePageView:initPage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_initPage'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_setPage(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_setPage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        int arg0;
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_setPage'", nullptr);
            return 0;
        }

        cobj->setPage(arg0);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BolePageView:setPage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_setPage'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_nextPage(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_nextPage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (1 <= argc && argc <= 2) {
        bool arg1 = false;
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_nextPage'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        if (argc >= 2) {
            ok &= luaval_to_boolean(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_nextPage'", nullptr);
                return 0;
            }
        }

        auto ret = cobj->nextPage([ = ](bool flag) {
            sea::excuteLuaFunction(handler, 1, [ = ](cocos2d::LuaStack *stack) {
                stack->pushBoolean(flag);
            });
        }, arg1);
        lua_pushnumber(tolua_S, ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BolePageView:nextPage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_nextPage'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BolePageView_prePage(lua_State *tolua_S) {
    int argc = 0;
    sea::BolePageView *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BolePageView", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BolePageView *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BolePageView_prePage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (1 <= argc && argc <= 2) {
        bool arg1 = false;
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_prePage'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        if (argc >= 2) {
            ok &= luaval_to_boolean(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BolePageView_prePage'", nullptr);
                return 0;
            }
        }
        
        auto ret = cobj->prePage([ = ](bool flag) {
            sea::excuteLuaFunction(handler, 1, [ = ](cocos2d::LuaStack *stack) {
                stack->pushBoolean(flag);
            });
        }, arg1);
        lua_pushnumber(tolua_S, ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BolePageView:prePage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BolePageView_prePage'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_page_view(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BolePageView");
    tolua_cclass(tolua_S, "BolePageView", "sea.BolePageView", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "BolePageView");
    do{
        tolua_function(tolua_S, "create", lua_sea_BolePageView_create);
        tolua_function(tolua_S, "resetWithData", lua_sea_BolePageView_resetWithData);
        tolua_function(tolua_S, "registerEvent", lua_sea_BolePageView_registerEvent);
        tolua_function(tolua_S, "callEventFun", lua_sea_BolePageView_callEventFun);
        tolua_function(tolua_S, "isInit", lua_sea_BolePageView_isInit);
        tolua_function(tolua_S, "initPage", lua_sea_BolePageView_initPage);
        tolua_function(tolua_S, "setPage", lua_sea_BolePageView_setPage);
        tolua_function(tolua_S, "nextPage", lua_sea_BolePageView_nextPage);
        tolua_function(tolua_S, "prePage", lua_sea_BolePageView_prePage);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BolePageView).name();
    g_luaType[typeName] = "sea.BolePageView";
    g_typeCast["BolePageView"] = "sea.BolePageView";
    return 1;
}


/**BoleSwitch**/
int lua_sea_BoleSwitch_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 4) {
        cocos2d::ValueMap arg0;
        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_create'", nullptr);
            return 0;
        }
        
        int funCreate = 0, funIn = 0, funOut = 0;
        
        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
#if COCOS2D_DEBUG >= 1
            if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_create'", nullptr);
                return 0;
            }
#endif
            funCreate = (toluafix_ref_function(tolua_S, 3, 0));
        }
        
        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
#if COCOS2D_DEBUG >= 1
            if (!toluafix_isfunction(tolua_S, 4, "LUA_FUNCTION", 0, &tolua_err)) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_create'", nullptr);
                return 0;
            }
#endif
            funIn = (toluafix_ref_function(tolua_S, 4, 0));
        }
        
        if (argc >= 4 && !lua_isnil(tolua_S, 5)) {
#if COCOS2D_DEBUG >= 1
            if (!toluafix_isfunction(tolua_S, 5, "LUA_FUNCTION", 0, &tolua_err)) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_create'", nullptr);
                return 0;
            }
#endif
            funOut = (toluafix_ref_function(tolua_S, 5, 0));
        }

        sea::BoleSwitch *ret = sea::BoleSwitch::create(arg0, funCreate, funIn, funOut);

        object_to_luaval<sea::BoleSwitch>(tolua_S, "sea.BoleSwitch", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~4 \n", "sea.BoleSwitch:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSwitch_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSwitch_switch(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleSwitch *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSwitch_switch'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_switch'", nullptr);
            return 0;
        }

        cobj->switchKey(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleSwitch:switch", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSwitch_switch'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSwitch_switchPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleSwitch *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSwitch_switchPlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_switchPlus'", nullptr);
            return 0;
        }

        cobj->switchPlus(arg0);
        return 1;
    }
    
    else if (argc == 2) {
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_switchPlus'", nullptr);
            return 0;
        }
        
        cocos2d::ValueMap arg1;
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleSwitch_switchPlus'", nullptr);
            return 0;
        }

        cobj->switchPlus(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleSwitch:switchPlus", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSwitch_switchPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSwitch_getShowingNode(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleSwitch *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSwitch_getShowingNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto node = cobj->getShowingNode();
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", node);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleSwitch:getShowingNode", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSwitch_getShowingNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleSwitch_getShowingKey(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleSwitch *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleSwitch_getShowingKey'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto key = cobj->getShowingKey();
        lua_pushstring(tolua_S, key.c_str());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleSwitch:getShowingKey", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleSwitch_getShowingKey'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_switch(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.BoleSwitch");
    tolua_cclass(tolua_S, "BoleSwitch", "sea.BoleSwitch", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "BoleSwitch");
    do{
        tolua_function(tolua_S, "create", lua_sea_BoleSwitch_create);
        tolua_function(tolua_S, "switch", lua_sea_BoleSwitch_switch);
        tolua_function(tolua_S, "switchPlus", lua_sea_BoleSwitch_switchPlus);
        tolua_function(tolua_S, "getShowingNode", lua_sea_BoleSwitch_getShowingNode);
        tolua_function(tolua_S, "getShowingKey", lua_sea_BoleSwitch_getShowingKey);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::BoleSwitch).name();
    g_luaType[typeName] = "sea.BoleSwitch";
    g_typeCast["BoleSwitch"] = "sea.BoleSwitch";
    return 1;
}

/**BoleRedDotNew**/
int lua_sea_BoleRedDotNew__changeLanguage(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDotNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDotNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDotNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDotNew__changeLanguage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0) {
        cobj->_changeLanguage();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleRedDotNew:_changeLanguage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDotNew__changeLanguage'.", &tolua_err);
#endif

    return 0;
}

/**BoleRedDot**/
int lua_sea_BoleRedDot_create(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 3) {
        int arg0;
        cocos2d::ValueMap arg1;
        cocos2d::ValueMap arg2 = cocos2d::ValueMapNull;
        
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_create'", nullptr);
            return 0;
        }
        
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1);
        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_create'", nullptr);
            return 0;
        }
        
        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);
            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_create'", nullptr);
                return 0;
            }
        }

        auto ret = sea::BoleRedDot::create(sea::convertInt2RedType(arg0), arg1, arg2);
        object_to_luaval<sea::BoleRedDot>(tolua_S, "sea.BoleRedDot", ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.BoleRedDot:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_resetWithData(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_resetWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 3) {
        int arg0;
        cocos2d::ValueMap arg1;
        cocos2d::ValueMap arg2 = cocos2d::ValueMapNull;
        
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_resetWithData'", nullptr);
            return 0;
        }
        
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1);
        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_resetWithData'", nullptr);
            return 0;
        }
        
        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 4, &arg2);
            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_resetWithData'", nullptr);
                return 0;
            }
        }

        cobj->resetWithData(sea::convertInt2RedType(arg0), arg1, arg2);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.BoleRedDot:resetWithData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_resetWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_removeEvent(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_removeEvent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->removeEvent();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleRedDot:removeEvent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_removeEvent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_setValue(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_setValue'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        int arg0;
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_setValue'", nullptr);
            return 0;
        }
        cobj->setValue(arg0);
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleRedDot:setValue", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_setValue'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_getValue(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_getValue'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getValue();
        lua_pushinteger(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleRedDot:getValue", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_getValue'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_checkShow(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_checkShow'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        cocos2d::ValueMap arg1 = cocos2d::ValueMapNull;
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_checkShow'", nullptr);
            return 0;
        }

#endif
        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        
        if (argc >= 2) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1);
            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_checkShow'", nullptr);
                return 0;
            }
        }
        cobj->checkShow([=](sea::BoleRedDot* redNode, cocos2d::Value val){
            sea::excuteLuaFunction(handler, 2, [&](cocos2d::LuaStack* stack){
                sea::pushValueToStack(stack, redNode, "sea.BoleRedDot");
                sea::pushValueToStack(stack, val);
            });
        }, arg1);
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleRedDot:checkShow", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_checkShow'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_stopCheckShow(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_stopCheckShow'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->stopCheckShow();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleRedDot:stopCheckShow", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_stopCheckShow'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_getType(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_getType'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getType();
        lua_pushinteger(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleRedDot:getType", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_getType'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_setType(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_setType'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        int arg0;
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_setType'", nullptr);
            return 0;
        }
        cobj->setType(sea::convertInt2RedType(arg0));
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.BoleRedDot:setType", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_setType'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_clear(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_clear'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->clear();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.BoleRedDot:clear", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_clear'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_setTypeWithData(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_setTypeWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        int arg0;
        cocos2d::ValueMap arg1 = cocos2d::ValueMapNull;
        
        ok &= luaval_to_int32(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_setTypeWithData'", nullptr);
            return 0;
        }
        
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1);
        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_setTypeWithData'", nullptr);
            return 0;
        }

        cobj->setTypeWithData(sea::convertInt2RedType(arg0), arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.BoleRedDot:setTypeWithData", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_setTypeWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_BoleRedDot_registerEvent(lua_State *tolua_S) {
    int argc = 0;
    sea::BoleRedDot *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.BoleRedDot", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::BoleRedDot *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_BoleRedDot_registerEvent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 1) {
        cocos2d::Value arg0 = cocos2d::Value::Null;
        
        if (argc >= 1) {
            ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0);
            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_BoleRedDot_registerEvent'", nullptr);
                return 0;
            }
        }

        cobj->registerEvent(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~1 \n", "sea.BoleRedDot:registerEvent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_BoleRedDot_registerEvent'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_bole_red_dot(lua_State *tolua_S) {
    //"新"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotNew");
        tolua_cclass(tolua_S, "BoleRedDotNew", "sea.BoleRedDotNew", "cc.Sprite", nullptr);

        tolua_beginmodule(tolua_S, "BoleRedDotNew");
        do{
            tolua_function(tolua_S, "_changeLanguage", lua_sea_BoleRedDotNew__changeLanguage);
        }while(0);
        tolua_endmodule(tolua_S);
        std::string typeName = typeid(sea::BoleRedDotNew).name();
        g_luaType[typeName] = "sea.BoleRedDotNew";
        g_typeCast["BoleRedDotNew"] = "sea.BoleRedDotNew";
    } while (0);
    
    //"三个点"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotPoint3");
        tolua_cclass(tolua_S, "BoleRedDotPoint3", "sea.BoleRedDotPoint3", "ccui.Scale9Sprite", nullptr);
        std::string typeName = typeid(sea::BoleRedDotPoint3).name();
        g_luaType[typeName] = "sea.BoleRedDotPoint3";
        g_typeCast["BoleRedDotPoint3"] = "sea.BoleRedDotPoint3";
    } while (0);
    
    //"加宽底板"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotWiden");
        tolua_cclass(tolua_S, "BoleRedDotWiden", "sea.BoleRedDotWiden", "sea.BoleRedDotPoint3", nullptr);
        std::string typeName = typeid(sea::BoleRedDotWiden).name();
        g_luaType[typeName] = "sea.BoleRedDotWiden";
        g_typeCast["BoleRedDotWiden"] = "sea.BoleRedDotWiden";
    } while (0);
    
    //"动效"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotAni");
        tolua_cclass(tolua_S, "BoleRedDotAni", "sea.BoleRedDotAni", "cc.Node", nullptr);
        std::string typeName = typeid(sea::BoleRedDotAni).name();
        g_luaType[typeName] = "sea.BoleRedDotAni";
        g_typeCast["BoleRedDotAni"] = "sea.BoleRedDotAni";
    } while (0);
    
    //"X"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotXNumber");
        tolua_cclass(tolua_S, "BoleRedDotXNumber", "sea.BoleRedDotXNumber", "cc.Label", nullptr);
        std::string typeName = typeid(sea::BoleRedDotXNumber).name();
        g_luaType[typeName] = "sea.BoleRedDotXNumber";
        g_typeCast["BoleRedDotXNumber"] = "sea.BoleRedDotXNumber";
    } while (0);
    
    //"+"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotAddMore");
        tolua_cclass(tolua_S, "BoleRedDotAddMore", "sea.BoleRedDotAddMore", "sea.BoleRedDotPoint3", nullptr);
        std::string typeName = typeid(sea::BoleRedDotAddMore).name();
        g_luaType[typeName] = "sea.BoleRedDotAddMore";
        g_typeCast["BoleRedDotAddMore"] = "sea.BoleRedDotAddMore";
    } while (0);
    
    //"自定义"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotDiy");
        tolua_cclass(tolua_S, "BoleRedDotDiy", "sea.BoleRedDotDiy", "cc.Node", nullptr);
        std::string typeName = typeid(sea::BoleRedDotDiy).name();
        g_luaType[typeName] = "sea.BoleRedDotDiy";
        g_typeCast["BoleRedDotDiy"] = "sea.BoleRedDotDiy";
    } while (0);
    
    //"分组"红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDotGroup");
        tolua_cclass(tolua_S, "BoleRedDotGroup", "sea.BoleRedDotGroup", "cc.Node", nullptr);
        std::string typeName = typeid(sea::BoleRedDotGroup).name();
        g_luaType[typeName] = "sea.BoleRedDotGroup";
        g_typeCast["BoleRedDotGroup"] = "sea.BoleRedDotGroup";
    } while (0);
    
    //红点
    do {
        tolua_usertype(tolua_S, "sea.BoleRedDot");
        tolua_cclass(tolua_S, "BoleRedDot", "sea.BoleRedDot", "cc.Node", nullptr);

        tolua_beginmodule(tolua_S, "BoleRedDot");
        do{
            tolua_function(tolua_S, "create", lua_sea_BoleRedDot_create);
            tolua_function(tolua_S, "resetWithData", lua_sea_BoleRedDot_resetWithData);
            tolua_function(tolua_S, "removeEvent", lua_sea_BoleRedDot_removeEvent);
            tolua_function(tolua_S, "setValue", lua_sea_BoleRedDot_setValue);
            tolua_function(tolua_S, "setString", lua_sea_BoleRedDot_setValue);
            tolua_function(tolua_S, "getValue", lua_sea_BoleRedDot_getValue);
//            tolua_function(tolua_S, "changeLanguage", lua_sea_BoleRedDot_changeLanguage);
            tolua_function(tolua_S, "checkShow", lua_sea_BoleRedDot_checkShow);
            tolua_function(tolua_S, "stopCheckShow", lua_sea_BoleRedDot_stopCheckShow);
            tolua_function(tolua_S, "getRedType", lua_sea_BoleRedDot_getType);
            tolua_function(tolua_S, "getType", lua_sea_BoleRedDot_getType);
            tolua_function(tolua_S, "setType", lua_sea_BoleRedDot_setType);
            tolua_function(tolua_S, "clear", lua_sea_BoleRedDot_clear);
            tolua_function(tolua_S, "setTypeWithData", lua_sea_BoleRedDot_setTypeWithData);
            tolua_function(tolua_S, "registerEvent", lua_sea_BoleRedDot_registerEvent);
        }while(0);
        tolua_endmodule(tolua_S);
        std::string typeName = typeid(sea::BoleRedDot).name();
        g_luaType[typeName] = "sea.BoleRedDot";
        g_typeCast["BoleRedDot"] = "sea.BoleRedDot";
    } while (0);
    return 1;
}

/**-----------------注册ScrollViewBar----↓----------------*/
int lua_sea_ScrollViewBar_update(lua_State *tolua_S) {
    int argc = 0;
    sea::ScrollViewBar *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.ScrollViewBar", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::ScrollViewBar *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_ScrollViewBar_update'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cocos2d::ValueVector arg0;

        ok &= luaval_to_ccvaluevector_new(tolua_S, 2, &arg0, "sea.ScrollViewBar:update");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_ScrollViewBar_update'", nullptr);
            return 0;
        }

        cobj->fresh();
        lua_settop(tolua_S, 1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.ScrollViewBar:update", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_ScrollViewBar_update'.", &tolua_err);
#endif
    return 0;
}

int lua_register_sea_scrollview_bar(lua_State *tolua_S) {
    do {
        tolua_usertype(tolua_S, "sea.ScrollViewBar");
        tolua_cclass(tolua_S, "ScrollViewBar", "sea.ScrollViewBar", "cc.Sprite", nullptr);
        
        tolua_beginmodule(tolua_S, "ScrollViewBar");
        do{
            tolua_function(tolua_S, "update", lua_sea_ScrollViewBar_update);
        }while(0);
        tolua_endmodule(tolua_S);
        std::string typeName = typeid(sea::ScrollViewBar).name();
        g_luaType[typeName] = "sea.ScrollViewBar";
        g_typeCast["ScrollViewBar"] = "sea.ScrollViewBar";
    } while (0);
    
    return 1;
}

/**TableViewInterface**/
int lua_register_sea_tableViewInterface(lua_State *tolua_S) {
    do {
        tolua_usertype(tolua_S, "sea.TableViewInterface");
        tolua_cclass(tolua_S, "TableViewInterface", "sea.TableViewInterface", "cc.Node", nullptr);
        std::string typeName = typeid(sea::TableViewInterface).name();
        g_luaType[typeName] = "sea.TableViewInterface";
        g_typeCast["TableViewInterface"] = "sea.TableViewInterface";
    } while (0);
    
    return 1;
}

/**-----------------注册Dialog----↓----------------*/


int lua_sea_Dialog_createNodeWithConfig(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_createNodeWithConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.Dialog:createNodeWithConfig");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_createNodeWithConfig'", nullptr);
            return 0;
        }

        auto ret = cobj->createNodeWithConfig(arg0);
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:createNodeWithConfig", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_createNodeWithConfig'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_addChildrenByConfig(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_addChildrenByConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueVector arg0;

        ok &= luaval_to_ccvaluevector_new(tolua_S, 2, &arg0, "sea.Dialog:addChildrenByConfig");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addChildrenByConfig'", nullptr);
            return 0;
        }

        cobj->addChildrenByConfig(arg0);
        lua_settop(tolua_S, 1);
        return 1;
    }

    if (argc == 2) {
        cocos2d::ValueVector arg0;
        cocos2d::Node *arg1 = nullptr;
        ok &= luaval_to_ccvaluevector_new(tolua_S, 2, &arg0, "sea.Dialog:addChildrenByConfig");
        ok &= luaval_to_object<cocos2d::Node>(tolua_S, 3, "cc.Node", &arg1, "sea.Dialog:addChildrenByConfig");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addChildrenByConfig'", nullptr);
            return 0;
        }

        cobj->addChildrenByConfig(arg0, arg1);
        lua_settop(tolua_S, 1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.Dialog:addChildrenByConfig", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_addChildrenByConfig'.", &tolua_err);
#endif
    return 0;
}

int lua_sea_Dialog_create(lua_State *tolua_S) {
    int argc = 0;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = sea::Dialog::create();
        object_to_luaval<sea::Dialog>(tolua_S, "sea.Dialog", ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:create", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getRootNode(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getRootNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getRootNode();
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getRootNode", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getRootNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_setRadioButtonCallback(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_setRadioButtonCallback'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2 || argc == 3) { //实际上只有2个参数，3个是为了兼容代码
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0, "sea.Dialog:setRadioButtonCallback");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_setRadioButtonCallback'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_setRadioButtonCallback'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        cobj->setRadioButtonCallback(arg0, handler);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:setRadioButtonCallback", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_setRadioButtonCallback'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_excuteRadioButtonCall(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_excuteRadioButtonCall'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 3) {
        cocos2d::Node *arg0;
        std::string arg1;
        cocos2d::Value arg2;

        ok &= luaval_to_object<cocos2d::Node>(tolua_S, 2, "cc.Node", &arg0, "sea.Dialog:excuteRadioButtonCall");
        ok &= luaval_to_std_string(tolua_S, 3, &arg1, "sea.Dialog:excuteRadioButtonCall");
        ok &= luaval_to_ccvalue_new(tolua_S, 4, &arg2, "sea.Dialog:excuteRadioButtonCall");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_excuteRadioButtonCall'", nullptr);
            return 0;
        }

        cobj->excuteRadioButtonCall(arg0, arg1, arg2);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:excuteRadioButtonCall", argc, 3);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_excuteRadioButtonCall'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getVertical(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getVertical'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getVertical();
        ccvalue_to_luaval(tolua_S, ret);

        return 1;
    }

    if (argc == 1 || argc == 2) {
        cocos2d::Value arg0;
        bool arg1 = false;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &arg0, "sea.Dialog:getVertical");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getVertical'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            ok &= luaval_to_boolean(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getVertical'", nullptr);
                return 0;
            }
        }
        
        auto ret = cobj->getVertical(arg0, arg1);
        ccvalue_to_luaval(tolua_S, ret);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getVertical", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getVertical'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_initWithData(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_initWithData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.Dialog:initWithData");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_initWithData'", nullptr);
            return 0;
        }

        cobj->initWithData(arg0);
        lua_settop(tolua_S, 1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:initWithData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_initWithData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getImgPathPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getImgPathPlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string arg0;
        cocos2d::ValueMap arg1 = cocos2d::ValueMapNull;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0, "sea.Dialog:getImgPathPlus");
        if (argc >= 2) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1, "sea.Dialog:getImgPathPlus");
        }
        
        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPathPlus'", nullptr);
            return 0;
        }

        auto path = cobj->getImgPathPlus(arg0, arg1);
        lua_pushlstring(tolua_S, path.c_str(), path.length());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.Dialog:getImgPathPlus", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getImgPathPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_addV(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_addV'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto vertical = cobj->getVertical();
        std::string ret = vertical.asBool() ? "_v" : "";
        lua_pushlstring(tolua_S, ret.c_str(), ret.length());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:addV", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_addV'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_clearDialog(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_clearDialog'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->clearDialog();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:clearDialog", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_clearDialog'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_setIsNode(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_setIsNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->setIsNode();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:setIsNode", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_setIsNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_pushResPathConfig(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_pushResPathConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1 || argc == 2) {
        cocos2d::ValueMap arg0;
        bool force = false;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.Dialog:pushResPathConfig");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_pushResPathConfig'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            ok &= luaval_to_boolean(tolua_S, 3, &force);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_pushResPathConfig'", nullptr);
                return 0;
            }
        }

        cobj->pushResPathConfig(arg0, force);
        lua_settop(tolua_S, 1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:pushResPathConfig", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_pushResPathConfig'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_updateResPathConfig(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_updateResPathConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1 || argc == 2) {
        cocos2d::ValueMap arg0;
        bool force = false;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.Dialog:updateResPathConfig");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_updateResPathConfig'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            ok &= luaval_to_boolean(tolua_S, 3, &force);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_updateResPathConfig'", nullptr);
                return 0;
            }
        }

        cobj->updateResPathConfig(arg0, force);
        lua_settop(tolua_S, 1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:updateResPathConfig", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_updateResPathConfig'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_pushExitCallback(lua_State *tolua_S) {
    if (nullptr == tolua_S) {
        return 0;
    }

    int argc = 0;
    sea::Dialog *self = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_cocos2dx_Widget_addClickEventListener'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (1 == argc) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        self->pushExitCallback([ = ](cocos2d::Ref *sender) {
            LuaStack *stack = LuaEngine::getInstance()->getLuaStack();
            stack->pushObject(sender, "cc.Ref");
            stack->executeFunctionByHandler(handler, 1);
            stack->clean();
        });

        ScriptHandlerMgr::getInstance()->addCustomHandler((void *)self, handler);
        return 0;
    }

    luaL_error(tolua_S, "'addClickEventListener' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'addClickEventListener'.", &tolua_err);
    return 0;

#endif
}

int lua_sea_Dialog_checkOnlyHorizontal(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_checkOnlyHorizontal'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->checkOnlyHorizontal();
        return 1;
    }

    if (argc == 1) {
        bool ok = true;
        bool arg0;

        ok &= luaval_to_boolean(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_checkOnlyHorizontal'", nullptr);
            return 0;
        }

        cobj->checkOnlyHorizontal(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:checkOnlyHorizontal", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_checkOnlyHorizontal'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getAniPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getAniPlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string key;
        cocos2d::ValueMap params = cocos2d::ValueMapNull;

        ok &= luaval_to_std_string(tolua_S, 2, &key);
        if (argc >= 2) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &params, "sea.Dialog:getAniPlus");
        }

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPlus'", nullptr);
            return 0;
        }

        auto ret = cobj->getAniPlus(key, params);
        object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.Dialog:getAniPlus", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getAniPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getAni(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getAni'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::string key;

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            sea::IntBool isLang = sea::IntBool::None;

            if (!lua_isnil(tolua_S, 3)) {
                bool lang;
                ok &= luaval_to_boolean(tolua_S, 3, &lang);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
                    return 0;
                }

                isLang = lang ? sea::IntBool::True : sea::IntBool::False;
            }

            if (argc >= 3) {
                sea::IntBool isVertical = sea::IntBool::None;

                if (!lua_isnil(tolua_S, 4)) {
                    bool vertical;
                    ok &= luaval_to_boolean(tolua_S, 4, &vertical);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
                        return 0;
                    }

                    isVertical = vertical ? sea::IntBool::True : sea::IntBool::False;
                }

                if (argc >= 4) {
                    bool notAddHead = false;

                    if (!lua_isnil(tolua_S, 5)) {
                        ok &= luaval_to_boolean(tolua_S, 5, &notAddHead);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
                            return 0;
                        }
                    }

                    if (argc >= 5) {
                        std::string resHeadKey = "";

                        if (!lua_isnil(tolua_S, 6)) {
                            ok &= luaval_to_std_string(tolua_S, 6, &resHeadKey);

                            if (!ok) {
                                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
                                return 0;
                            }
                        }

                        if (argc >= 6) {
                            sea::IntBool isActivity = sea::IntBool::None;

                            if (!lua_isnil(tolua_S, 7)) {
                                bool activity;
                                ok &= luaval_to_boolean(tolua_S, 7, &activity);

                                if (!ok) {
                                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
                                    return 0;
                                }

                                isActivity = activity ? sea::IntBool::True : sea::IntBool::False;
                            }

                            if (argc >= 7) {
                                std::string themeId = "";

                                if (!lua_isnil(tolua_S, 8)) {
                                    ok &= luaval_to_std_string(tolua_S, 8, &themeId);

                                    if (!ok) {
                                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAni'", nullptr);
                                        return 0;
                                    }
                                }

                                if (argc >= 8) {
                                    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getAni", argc, 1);
                                    return 0;
                                }

                                auto ret = cobj->getAni(key, isLang, isVertical, notAddHead, resHeadKey, isActivity, themeId);
                                object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
                                return 1;
                            } else {
                                auto ret = cobj->getAni(key, isLang, isVertical, notAddHead, resHeadKey, isActivity);
                                object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
                                return 1;
                            }
                        } else {
                            auto ret = cobj->getAni(key, isLang, isVertical, notAddHead, resHeadKey);
                            object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
                            return 1;
                        }
                    } else {
                        auto ret = cobj->getAni(key, isLang, isVertical, notAddHead);
                        object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
                        return 1;
                    }
                } else {
                    auto ret = cobj->getAni(key, isLang, isVertical);
                    object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
                    return 1;
                }
            } else {
                auto ret = cobj->getAni(key, isLang);
                object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
                return 1;
            }
        } else {
            auto ret = cobj->getAni(key);
            object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", ret);
            return 1;
        }
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getAni", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getAni'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getAniPathPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getAniPathPlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        std::string key;
        cocos2d::ValueMap params;

        ok &= luaval_to_std_string(tolua_S, 2, &key);
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &params, "sea.Dialog:getAniPathPlus");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPathPlus'", nullptr);
            return 0;
        }

        auto ret = cobj->getAniPathPlus(key, params);
        ccvector_std_string_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getAniPathPlus", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getAniPathPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getAniPath(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getAniPath'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::string key;

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            sea::IntBool isLang = sea::IntBool::None;

            if (!lua_isnil(tolua_S, 3)) {
                bool lang;
                ok &= luaval_to_boolean(tolua_S, 3, &lang);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
                    return 0;
                }

                isLang = lang ? sea::IntBool::True : sea::IntBool::False;
            }

            if (argc >= 3) {
                sea::IntBool isVertical = sea::IntBool::None;

                if (!lua_isnil(tolua_S, 4)) {
                    bool vertical;
                    ok &= luaval_to_boolean(tolua_S, 4, &vertical);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
                        return 0;
                    }

                    isVertical = vertical ? sea::IntBool::True : sea::IntBool::False;
                }

                if (argc >= 4) {
                    bool notAddHead = false;

                    if (!lua_isnil(tolua_S, 5)) {
                        ok &= luaval_to_boolean(tolua_S, 5, &notAddHead);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
                            return 0;
                        }
                    }

                    if (argc >= 5) {
                        std::string resHeadKey = "";

                        if (!lua_isnil(tolua_S, 6)) {
                            ok &= luaval_to_std_string(tolua_S, 6, &resHeadKey);

                            if (!ok) {
                                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
                                return 0;
                            }
                        }

                        if (argc >= 6) {
                            sea::IntBool isActivity = sea::IntBool::None;

                            if (!lua_isnil(tolua_S, 7)) {
                                bool activity;
                                ok &= luaval_to_boolean(tolua_S, 7, &activity);

                                if (!ok) {
                                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
                                    return 0;
                                }

                                isActivity = activity ? sea::IntBool::True : sea::IntBool::False;
                            }

                            if (argc >= 7) {
                                std::string themeId = "";

                                if (!lua_isnil(tolua_S, 8)) {
                                    ok &= luaval_to_std_string(tolua_S, 8, &themeId);

                                    if (!ok) {
                                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getAniPath'", nullptr);
                                        return 0;
                                    }
                                }

                                if (argc >= 8) {
                                    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getAniPath", argc, 1);
                                    return 0;
                                }

                                
                                auto ret = cobj->getAniPath(key, isLang, isVertical, notAddHead, resHeadKey, isActivity, themeId);
                                for (const auto& s : ret) {
                                    lua_pushstring(tolua_S, s.c_str());
                                }
                                return int(ret.size());
                            } else {
                                auto ret = cobj->getAniPath(key, isLang, isVertical, notAddHead, resHeadKey, isActivity);
                                for (const auto& s : ret) {
                                    lua_pushstring(tolua_S, s.c_str());
                                }
                                return int(ret.size());
                            }
                        } else {
                            auto ret = cobj->getAniPath(key, isLang, isVertical, notAddHead, resHeadKey);
                            for (const auto& s : ret) {
                                lua_pushstring(tolua_S, s.c_str());
                            }
                            return int(ret.size());
                        }
                    } else {
                        auto ret = cobj->getAniPath(key, isLang, isVertical, notAddHead);
                        for (const auto& s : ret) {
                            lua_pushstring(tolua_S, s.c_str());
                        }
                        return int(ret.size());
                    }
                } else {
                    auto ret = cobj->getAniPath(key, isLang, isVertical);
                    for (const auto& s : ret) {
                        lua_pushstring(tolua_S, s.c_str());
                    }
                    return int(ret.size());
                }
            } else {
                auto ret = cobj->getAniPath(key, isLang);
                for (const auto& s : ret) {
                    lua_pushstring(tolua_S, s.c_str());
                }
                return int(ret.size());
            }
        } else {
            auto ret = cobj->getAniPath(key);
            for (const auto& s : ret) {
                lua_pushstring(tolua_S, s.c_str());
            }
            return int(ret.size());
        }
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getAniPath", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getAniPath'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getPath(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getPath'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string key;

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getPath'", nullptr);
            return 0;
        }

        auto ret = cobj->getPath(key);
        lua_pushlstring(tolua_S, ret.c_str(), ret.length());
        return 1;
    }

    if (argc == 2) {
        std::string key;
        std::string resHeadKey;

        ok &= luaval_to_std_string(tolua_S, 2, &key);
        ok &= luaval_to_std_string(tolua_S, 3, &resHeadKey);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getPath'", nullptr);
            return 0;
        }

        auto ret = cobj->getPath(key, resHeadKey);
        lua_pushlstring(tolua_S, ret.c_str(), ret.length());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.Dialog:getPath", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getPath'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getFntPath(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getFntPath'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string key;
        std::string resHeadKey = "";

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getFntPath'", nullptr);
            return 0;
        }
        
        if (argc >= 2) {
            ok &= luaval_to_std_string(tolua_S, 3, &resHeadKey);
            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getFntPath'", nullptr);
                return 0;
            }
        }
        
        auto ret = cobj->getFntPath(key, resHeadKey);
        lua_pushlstring(tolua_S, ret.c_str(), ret.length());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getFntPath", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getFntPath'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getImgPath(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getImgPath'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::string key;

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            sea::IntBool isLang = sea::IntBool::None;

            if (!lua_isnil(tolua_S, 3)) {
                bool lang;
                ok &= luaval_to_boolean(tolua_S, 3, &lang);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                    return 0;
                }

                isLang = lang ? sea::IntBool::True : sea::IntBool::False;
            }

            if (argc >= 3) {
                sea::IntBool isVertical = sea::IntBool::None;

                if (!lua_isnil(tolua_S, 4)) {
                    bool vertical;
                    ok &= luaval_to_boolean(tolua_S, 4, &vertical);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                        return 0;
                    }

                    isVertical = vertical ? sea::IntBool::True : sea::IntBool::False;
                }

                if (argc >= 4) {
                    sea::TextureResType resType = sea::TextureResType::NONE;

                    if (!lua_isnil(tolua_S, 5)) {
                        int rType = -1;
                        ok &= luaval_to_int32(tolua_S, 5, &rType);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                            return 0;
                        }

                        resType = static_cast<sea::TextureResType>(rType);
                    }

                    if (argc >= 5) {
                        bool notAddHead = false;

                        if (!lua_isnil(tolua_S, 6)) {
                            ok &= luaval_to_boolean(tolua_S, 6, &notAddHead);

                            if (!ok) {
                                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                                return 0;
                            }
                        }

                        if (argc >= 6) {
                            std::string resHeadKey = "";

                            if (!lua_isnil(tolua_S, 7)) {
                                ok &= luaval_to_std_string(tolua_S, 7, &resHeadKey);

                                if (!ok) {
                                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                                    return 0;
                                }
                            }

                            if (argc >= 7) {
                                sea::IntBool isActivity = sea::IntBool::None;

                                if (!lua_isnil(tolua_S, 8)) {
                                    bool activity;
                                    ok &= luaval_to_boolean(tolua_S, 8, &activity);

                                    if (!ok) {
                                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                                        return 0;
                                    }

                                    isActivity = activity ? sea::IntBool::True : sea::IntBool::False;
                                }

                                if (argc >= 8) {
                                    std::string themeId = "";

                                    if (!lua_isnil(tolua_S, 9)) {
                                        ok &= luaval_to_std_string(tolua_S, 9, &themeId);

                                        if (!ok) {
                                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPath'", nullptr);
                                            return 0;
                                        }
                                    }

                                    if (argc >= 9) {
                                        luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgPath", argc, 1);
                                        return 0;
                                    }

                                    auto ret = cobj->getImgPath(key, isLang, isVertical, resType, notAddHead, resHeadKey, isActivity, themeId);
                                    lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                                    return 1;
                                } else {
                                    auto ret = cobj->getImgPath(key, isLang, isVertical, resType, notAddHead, resHeadKey, isActivity);
                                    lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                                    return 1;
                                }
                            } else {
                                auto ret = cobj->getImgPath(key, isLang, isVertical, resType, notAddHead, resHeadKey);
                                lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                                return 1;
                            }
                        } else {
                            auto ret = cobj->getImgPath(key, isLang, isVertical, resType, notAddHead);
                            lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                            return 1;
                        }
                    } else {
                        auto ret = cobj->getImgPath(key, isLang, isVertical, resType);
                        lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                        return 1;
                    }
                } else {
                    auto ret = cobj->getImgPath(key, isLang, isVertical);
                    lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                    return 1;
                }
            } else {
                auto ret = cobj->getImgPath(key, isLang);
                lua_pushlstring(tolua_S, ret.c_str(), ret.length());
                return 1;
            }
        } else {
            auto ret = cobj->getImgPath(key);
            lua_pushlstring(tolua_S, ret.c_str(), ret.length());
            return 1;
        }
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgPath", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getImgPath'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getImgNodePlus(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getImgNodePlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        std::string arg0;
        cocos2d::ValueMap arg1;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0, "sea.Dialog:getImgNodePlus");
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1, "sea.Dialog:getImgNodePlus");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNodePlus'", nullptr);
            return 0;
        }

        auto sprite = cobj->getImgNodePlus(arg0, arg1);
        object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", sprite);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgNodePlus", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getImgNodePlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getImgNode(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getImgNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::string key;

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            sea::IntBool isLang = sea::IntBool::None;

            if (!lua_isnil(tolua_S, 3)) {
                bool lang;
                ok &= luaval_to_boolean(tolua_S, 3, &lang);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                    return 0;
                }

                isLang = lang ? sea::IntBool::True : sea::IntBool::False;
            }

            if (argc >= 3) {
                sea::IntBool isVertical = sea::IntBool::None;

                if (!lua_isnil(tolua_S, 4)) {
                    bool vertical;
                    ok &= luaval_to_boolean(tolua_S, 4, &vertical);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                        return 0;
                    }

                    isVertical = vertical ? sea::IntBool::True : sea::IntBool::False;
                }

                if (argc >= 4) {
                    sea::TextureResType resType = sea::TextureResType::NONE;

                    if (!lua_isnil(tolua_S, 5)) {
                        int rType = -1;
                        ok &= luaval_to_int32(tolua_S, 5, &rType);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                            return 0;
                        }

                        resType = static_cast<sea::TextureResType>(rType);
                    }

                    if (argc >= 5) {
                        bool notAddHead = false;

                        if (!lua_isnil(tolua_S, 6)) {
                            ok &= luaval_to_boolean(tolua_S, 6, &notAddHead);

                            if (!ok) {
                                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                return 0;
                            }
                        }

                        if (argc >= 6) {
                            std::string resHeadKey = "";

                            if (!lua_isnil(tolua_S, 7)) {
                                ok &= luaval_to_std_string(tolua_S, 7, &resHeadKey);

                                if (!ok) {
                                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                    return 0;
                                }
                            }

                            if (argc >= 7) {
                                sea::IntBool isActivity = sea::IntBool::None;

                                if (!lua_isnil(tolua_S, 8)) {
                                    bool activity;
                                    ok &= luaval_to_boolean(tolua_S, 8, &activity);

                                    if (!ok) {
                                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                        return 0;
                                    }

                                    isActivity = activity ? sea::IntBool::True : sea::IntBool::False;
                                }

                                if (argc >= 8) {
                                    std::string themeId = "";

                                    if (!lua_isnil(tolua_S, 9)) {
                                        ok &= luaval_to_std_string(tolua_S, 9, &themeId);

                                        if (!ok) {
                                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                            return 0;
                                        }
                                    }

                                    if (argc >= 9) {
                                        luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgNode", argc, 1);
                                        return 0;
                                    }

                                    auto ret = cobj->getImgNode(key, isLang, isVertical, resType, notAddHead, resHeadKey, isActivity, themeId);
                                    object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                                    return 1;
                                } else {
                                    auto ret = cobj->getImgNode(key, isLang, isVertical, resType, notAddHead, resHeadKey, isActivity);
                                    object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                                    return 1;
                                }
                            } else {
                                auto ret = cobj->getImgNode(key, isLang, isVertical, resType, notAddHead, resHeadKey);
                                object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                                return 1;
                            }
                        } else {
                            auto ret = cobj->getImgNode(key, isLang, isVertical, resType, notAddHead);
                            object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                            return 1;
                        }
                    } else {
                        auto ret = cobj->getImgNode(key, isLang, isVertical, resType);
                        object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                        return 1;
                    }
                } else {
                    auto ret = cobj->getImgNode(key, isLang, isVertical);
                    object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                    return 1;
                }
            } else {
                auto ret = cobj->getImgNode(key, isLang);
                object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
                return 1;
            }
        } else {
            auto ret = cobj->getImgNode(key);
            object_to_luaval<cocos2d::Sprite>(tolua_S, "cc.Sprite", ret);
            return 1;
        }
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgNode", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getImgNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getLangImgInfoPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getImgPathPlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        std::string arg0;
        cocos2d::ValueMap arg1;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0, "sea.Dialog:getImgPathPlus");
        ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1, "sea.Dialog:getImgPathPlus");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgPathPlus'", nullptr);
            return 0;
        }

        auto info = cobj->getLangImgInfoPlus(arg0, arg1);
        ccvaluemap_to_luaval(tolua_S, info);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgPathPlus", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getImgPathPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getLangImgInfo(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::Dialog *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_Dialog_getImgNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::string key;

        ok &= luaval_to_std_string(tolua_S, 2, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            sea::IntBool isVertical = sea::IntBool::None;

            if (!lua_isnil(tolua_S, 3)) {
                bool vertical;
                ok &= luaval_to_boolean(tolua_S, 3, &vertical);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                    return 0;
                }

                isVertical = vertical ? sea::IntBool::True : sea::IntBool::False;
            }

            if (argc >= 3) {
                sea::TextureResType resType = sea::TextureResType::NONE;

                if (!lua_isnil(tolua_S, 4)) {
                    int rType = -1;
                    ok &= luaval_to_int32(tolua_S, 4, &rType);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                        return 0;
                    }

                    resType = static_cast<sea::TextureResType>(rType);
                }

                if (argc >= 4) {
                    sea::IntBool notAddHead = sea::IntBool::None;

                    if (!lua_isnil(tolua_S, 5)) {
                        bool _notAddHead = false;
                        ok &= luaval_to_boolean(tolua_S, 5, &_notAddHead);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                            return 0;
                        }

                        notAddHead = _notAddHead ? sea::IntBool::True : sea::IntBool::False;
                    }

                    if (argc >= 5) {
                        std::string resHeadKey = "";

                        if (!lua_isnil(tolua_S, 6)) {
                            ok &= luaval_to_std_string(tolua_S, 6, &resHeadKey);

                            if (!ok) {
                                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                return 0;
                            }
                        }

                        if (argc >= 6) {
                            sea::IntBool isActivity = sea::IntBool::None;

                            if (!lua_isnil(tolua_S, 7)) {
                                bool activity;
                                ok &= luaval_to_boolean(tolua_S, 7, &activity);

                                if (!ok) {
                                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                    return 0;
                                }

                                isActivity = activity ? sea::IntBool::True : sea::IntBool::False;
                            }

                            if (argc >= 7) {
                                std::string themeId = "";

                                if (!lua_isnil(tolua_S, 8)) {
                                    ok &= luaval_to_std_string(tolua_S, 8, &themeId);

                                    if (!ok) {
                                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getImgNode'", nullptr);
                                        return 0;
                                    }
                                }

                                if (argc >= 8) {
                                    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgNode", argc, 1);
                                    return 0;
                                }

                                auto ret = cobj->getLangImgInfo(key, isVertical, resType, notAddHead, resHeadKey, isActivity, themeId);
                                ccvaluemap_to_luaval(tolua_S, ret);
                                return 1;
                            } else {
                                auto ret = cobj->getLangImgInfo(key, isVertical, resType, notAddHead, resHeadKey, isActivity);
                                ccvaluemap_to_luaval(tolua_S, ret);
                                return 1;
                            }
                        } else {
                            auto ret = cobj->getLangImgInfo(key, isVertical, resType, notAddHead, resHeadKey);
                            ccvaluemap_to_luaval(tolua_S, ret);
                            return 1;
                        }
                    } else {
                        auto ret = cobj->getLangImgInfo(key, isVertical, resType, notAddHead);
                        ccvaluemap_to_luaval(tolua_S, ret);
                        return 1;
                    }
                } else {
                    auto ret = cobj->getLangImgInfo(key, isVertical, resType);
                    ccvaluemap_to_luaval(tolua_S, ret);
                    return 1;
                }
            } else {
                auto ret = cobj->getLangImgInfo(key, isVertical);
                ccvaluemap_to_luaval(tolua_S, ret);
                return 1;
            }
        } else {
            auto ret = cobj->getLangImgInfo(key);
            ccvaluemap_to_luaval(tolua_S, ret);
            return 1;
        }
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getImgNode", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getImgNode'.", &tolua_err);
#endif

    return 0;
}

int _lua_sea_Dialog_callRootChild(lua_State *tolua_S, cocos2d::Node *node, bool isPlusChild, int startParamIndex = 3) {
    int argc = 0;
    sea::Dialog *self = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_callRootChild'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        auto callback = [ = ](cocos2d::Node *node, const cocos2d::Value& value) {
                LuaStack *stack = LuaEngine::getInstance()->getLuaStack();

                sea::pushValueToStack(stack, node, "cc.Node");
                sea::pushValueToStack(stack, value);
                stack->executeFunctionByHandler(handler, 2);
                stack->clean();
            };

        //第二个参数解析
        if (lua_istable(tolua_S, startParamIndex)) { //如果是table
            if (argc > startParamIndex - 1) {
            } else {
                cocos2d::ValueVector arg0;
                bool ok = luaval_to_ccvaluevector_new(tolua_S, startParamIndex, &arg0, "sea.Dialog:callRootChild");

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
                    return 0;
                }

                for (auto iter : arg0) {
                    auto type = iter.getType();

                    if (type == ccValueType::STRING) {
                        self->callNodeChild(callback, iter.asString(), node, isPlusChild);
                    } else if (type == ccValueType::VECTOR) {
                        self->callNodeChild(callback, iter.asValueVector(), node, isPlusChild);
                    } else {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
                        return 0;
                    }
                }
            }
        } else if (lua_type(tolua_S, startParamIndex) == LUA_TSTRING) { //如果是字符串
            if (argc == startParamIndex - 1) {
                std::string stringValue = "";

                if (luaval_to_std_string(tolua_S, startParamIndex, &stringValue) ) {
                    self->callNodeChild(callback, stringValue, node, isPlusChild);
                    return 1;
                }
            } else {
                std::vector<std::string> keys;

                for (int i = 0; i <= argc - startParamIndex + 1; ++i) {
                    std::string stringValue = "";

                    if (luaval_to_std_string(tolua_S, startParamIndex + i, &stringValue) ) {
                        keys.push_back(stringValue);
                    } else {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
                        return 0;
                    }
                }

                self->callNodeChild(callback, keys, node, isPlusChild);
                return 1;
            }
        }

        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
        return 0;
    }

    luaL_error(tolua_S, "'callRootChild' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, startParamIndex - 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_callRootChild'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_callNodeChild(lua_State *tolua_S) {
    int argc = lua_gettop(tolua_S) - 1;
    cocos2d::Node *node = nullptr;

    if (argc >= 3) {
        bool ok = luaval_to_node(tolua_S, 3, "cc.Node", &node);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
            return 0;
        }
    }

    return _lua_sea_Dialog_callRootChild(tolua_S, node, false, 4);
}

int lua_sea_Dialog_callRootPlusChild(lua_State *tolua_S) {
    return _lua_sea_Dialog_callRootChild(tolua_S, nullptr, true);
}

int lua_sea_Dialog_callNodePlusChild(lua_State *tolua_S) {
    int argc = lua_gettop(tolua_S) - 1;
    cocos2d::Node *node = nullptr;

    if (argc >= 3) {
        bool ok = luaval_to_node(tolua_S, 3, "cc.Node", &node);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
            return 0;
        }
    }

    return _lua_sea_Dialog_callRootChild(tolua_S, node, true, 4);
}

int lua_sea_Dialog_callRootChild(lua_State *tolua_S) {
    return _lua_sea_Dialog_callRootChild(tolua_S, nullptr, false);
}

int lua_sea_Dialog_callChildrenByGroup(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_callChildrenByGroup'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 4) {
        //第一个参数group
        std::string group = "";
        ok &= luaval_to_std_string(tolua_S, 2, &group);

        //第二个参数回调函数
        std::function<void(cocos2d::Node *node, const cocos2d::Value& value)> callback = nullptr;

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
#if COCOS2D_DEBUG >= 1

            if (toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err))
#else

            if (true)
#endif
            {
                LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

                callback = [ = ](cocos2d::Node *node, const cocos2d::Value& value) {
                        LuaStack *stack = LuaEngine::getInstance()->getLuaStack();

                        sea::pushValueToStack(stack, node, "cc.Node");
                        sea::pushValueToStack(stack, value);
                        stack->executeFunctionByHandler(handler, 2);
                        stack->clean();
                    };
            }
        }

        //第三个参数节点
        cocos2d::Node *parent = nullptr;

        if (argc >= 3 && !lua_isnil(tolua_S, 4)) {
            ok &= luaval_to_node(tolua_S, 4, "cc.Node", &parent);
        }

        //第四个参数其他配置参数
        cocos2d::ValueMap params;

        if (argc >= 4 && !lua_isnil(tolua_S, 5)) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 5, &params);
        }

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callChildrenByGroup'", nullptr);
            return 0;
        }

        auto vec = self->callChildrenByGroup(group, callback, parent, params);
        ccvector_to_luaval(tolua_S, vec);
        return 1;
    }

    luaL_error(tolua_S, "'callChildrenByGroup' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_callChildrenByGroup'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getRootChildByName(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_getRootChildByName'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::vector<std::string> keys;

        for (int i = 0; i <= argc - 1; ++i) {
            std::string stringValue = "";

            if (luaval_to_std_string(tolua_S, 2 + i, &stringValue) ) {
                keys.push_back(stringValue);
            } else {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getRootChildByName'", nullptr);
                return 0;
            }
        }

        auto ret = self->getRootChildByName(keys);
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "'getRootChildByName' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getRootChildByName'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getNodeChildByName(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_getRootChildByName'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2) {
        cocos2d::Node *node = nullptr;
        ok = luaval_to_node(tolua_S, 2, "cc.Node", &node);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
            return 0;
        }

        std::vector<std::string> keys;

        for (int i = 0; i <= argc - 2; ++i) {
            std::string stringValue = "";

            if (luaval_to_std_string(tolua_S, 3 + i, &stringValue) ) {
                keys.push_back(stringValue);
            } else {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getRootChildByName'", nullptr);
                return 0;
            }
        }

        auto ret = self->getNodeChildByName(node, keys);
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "'getRootChildByName' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getRootChildByName'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getRootPlusChildByName(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_getRootChildByName'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        std::vector<std::string> keys;

        for (int i = 0; i <= argc - 1; ++i) {
            std::string stringValue = "";

            if (luaval_to_std_string(tolua_S, 2 + i, &stringValue) ) {
                keys.push_back(stringValue);
            } else {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getRootChildByName'", nullptr);
                return 0;
            }
        }

        auto ret = self->getRootPlusChildByName(keys);
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "'getRootChildByName' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getRootChildByName'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_getNodePlusChildByName(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_getRootChildByName'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2) {
        cocos2d::Node *node = nullptr;
        ok = luaval_to_node(tolua_S, 2, "cc.Node", &node);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_callRootChild'", nullptr);
            return 0;
        }

        std::vector<std::string> keys;

        for (int i = 0; i <= argc - 2; ++i) {
            std::string stringValue = "";

            if (luaval_to_std_string(tolua_S, 3 + i, &stringValue) ) {
                keys.push_back(stringValue);
            } else {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_getRootChildByName'", nullptr);
                return 0;
            }
        }

        auto ret = self->getNodePlusChildByName(node, keys);
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "'getRootChildByName' function of Widget has wrong number of arguments: %d, was expecting %d\n", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_getRootChildByName'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_addClickEvent(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_addClickEvent'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        cocos2d::ui::Widget *btn = nullptr;
        ok = luaval_to_object(tolua_S, 2, "ccui.Widget", &btn);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEvent'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEvent'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        self->addClickEvent(btn, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& params) {
            sea::excuteLuaFunction(handler, 1, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, params);
            });
        });
        return 1;
    }

    if (argc == 3) {
        cocos2d::ui::Widget *btn = nullptr;
        ok = luaval_to_object(tolua_S, 2, "ccui.Widget", &btn);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEvent'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEvent'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        cocos2d::Value params;
        ok &= luaval_to_ccvalue_new(tolua_S, 4, &params, "sea.Dialog:addClickEvent");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEvent'", nullptr);
            return 0;
        }

        self->addClickEvent(btn, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& params) {
            sea::excuteLuaFunction(handler, 1, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, params);
            });
        }, params);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.Dialog:addClickEvent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_addClickEvent'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_addClickEventMute(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_addClickEventMute'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        cocos2d::ui::Widget *btn = nullptr;
        ok = luaval_to_object(tolua_S, 2, "ccui.Widget", &btn);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventMute'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventMute'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        self->addClickEventMute(btn, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& params) {
            sea::excuteLuaFunction(handler, 1, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, params);
            });
        });
        return 1;
    }

    if (argc == 3) {
        cocos2d::ui::Widget *btn = nullptr;
        ok = luaval_to_object(tolua_S, 2, "ccui.Widget", &btn);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventMute'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventMute'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        cocos2d::Value params;
        ok &= luaval_to_ccvalue_new(tolua_S, 4, &params, "sea.Dialog:addClickEventMute");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventMute'", nullptr);
            return 0;
        }

        self->addClickEventMute(btn, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& params) {
            sea::excuteLuaFunction(handler, 1, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, params);
            });
        }, params);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.Dialog:addClickEventMute", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_addClickEventMute'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_addClickEventNoMove(lua_State *tolua_S) {
    int argc = 0;
    sea::Dialog *self = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    self = static_cast<sea::Dialog *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == self) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_Dialog_addClickEventNoMove'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        cocos2d::ui::Widget *btn = nullptr;
        ok = luaval_to_object(tolua_S, 2, "ccui.Widget", &btn);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventNoMove'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventNoMove'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        self->addClickEventNoMove(btn, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& params) {
            sea::excuteLuaFunction(handler, 1, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, params);
            });
        });
        return 1;
    }

    if (argc == 3) {
        cocos2d::ui::Widget *btn = nullptr;
        ok = luaval_to_object(tolua_S, 2, "ccui.Widget", &btn);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventNoMove'", nullptr);
            return 0;
        }

#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventNoMove'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        cocos2d::Value params;
        ok &= luaval_to_ccvalue_new(tolua_S, 4, &params, "sea.Dialog:addClickEventNoMove");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Dialog_addClickEventNoMove'", nullptr);
            return 0;
        }

        self->addClickEventNoMove(btn, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& params) {
            sea::excuteLuaFunction(handler, 1, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, params);
            });
        }, params);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 2~3 \n", "sea.Dialog:addClickEventNoMove", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_addClickEventNoMove'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Dialog_registerLuaFunctionList(lua_State *tolua_S) {
    int argc = 0;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Dialog", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;
        luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);
        sea::Dialog::registerLuaFunctionList(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:create", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Dialog_create'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialog(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.Dialog");
    tolua_cclass(tolua_S, "Dialog", "sea.Dialog", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "Dialog");
    do{
        tolua_function(tolua_S, "createNodeWithConfig", lua_sea_Dialog_createNodeWithConfig);
        tolua_function(tolua_S, "addChildrenByConfig", lua_sea_Dialog_addChildrenByConfig);
        tolua_function(tolua_S, "getRootNode", lua_sea_Dialog_getRootNode);
        tolua_function(tolua_S, "setRadioButtonCallback", lua_sea_Dialog_setRadioButtonCallback);
        tolua_function(tolua_S, "excuteRadioButtonCall", lua_sea_Dialog_excuteRadioButtonCall);
        tolua_function(tolua_S, "getVertical", lua_sea_Dialog_getVertical);
        tolua_function(tolua_S, "initWithData", lua_sea_Dialog_initWithData);
        tolua_function(tolua_S, "getImgPathPlus", lua_sea_Dialog_getImgPathPlus);
        tolua_function(tolua_S, "getImagePathPlus", lua_sea_Dialog_getImgPathPlus);
        tolua_function(tolua_S, "addV", lua_sea_Dialog_addV);
        tolua_function(tolua_S, "clearDialog", lua_sea_Dialog_clearDialog);
        tolua_function(tolua_S, "setIsNode", lua_sea_Dialog_setIsNode);
        tolua_function(tolua_S, "pushResPathConfig", lua_sea_Dialog_pushResPathConfig);
        tolua_function(tolua_S, "updateResPathConfig", lua_sea_Dialog_updateResPathConfig);
        tolua_function(tolua_S, "pushExitCallback", lua_sea_Dialog_pushExitCallback);
        tolua_function(tolua_S, "checkOnlyHorizontal", lua_sea_Dialog_checkOnlyHorizontal);
        tolua_function(tolua_S, "getAniPlus", lua_sea_Dialog_getAniPlus);
        tolua_function(tolua_S, "getAni", lua_sea_Dialog_getAni);
        tolua_function(tolua_S, "getAniPathPlus", lua_sea_Dialog_getAniPathPlus);
        tolua_function(tolua_S, "getAniPath", lua_sea_Dialog_getAniPath);
        tolua_function(tolua_S, "getPath", lua_sea_Dialog_getPath);
        tolua_function(tolua_S, "getFntPath", lua_sea_Dialog_getFntPath);
        tolua_function(tolua_S, "getImgPath", lua_sea_Dialog_getImgPath);
        tolua_function(tolua_S, "getImgNodePlus", lua_sea_Dialog_getImgNodePlus);
        tolua_function(tolua_S, "getImgNode", lua_sea_Dialog_getImgNode);
        tolua_function(tolua_S, "getLangImgInfoPlus", lua_sea_Dialog_getLangImgInfoPlus);
        tolua_function(tolua_S, "getLangImgInfo", lua_sea_Dialog_getLangImgInfo);
        tolua_function(tolua_S, "callRootChild", lua_sea_Dialog_callRootChild);
        tolua_function(tolua_S, "callNodeChild", lua_sea_Dialog_callNodeChild);
        tolua_function(tolua_S, "callRootPlusChild", lua_sea_Dialog_callRootPlusChild);
        tolua_function(tolua_S, "callNodePlusChild", lua_sea_Dialog_callNodePlusChild);
        tolua_function(tolua_S, "callChildrenByGroup", lua_sea_Dialog_callChildrenByGroup);
        tolua_function(tolua_S, "getRootChildByName", lua_sea_Dialog_getRootChildByName);
        tolua_function(tolua_S, "getNodeChildByName", lua_sea_Dialog_getNodeChildByName);
        tolua_function(tolua_S, "getRootPlusChildByName", lua_sea_Dialog_getRootPlusChildByName);
        tolua_function(tolua_S, "getNodePlusChildByName", lua_sea_Dialog_getNodePlusChildByName);
        tolua_function(tolua_S, "addClickEvent", lua_sea_Dialog_addClickEvent);
        tolua_function(tolua_S, "addClickEventMute", lua_sea_Dialog_addClickEventMute);
        tolua_function(tolua_S, "addClickEventNoMove", lua_sea_Dialog_addClickEventNoMove);
        tolua_function(tolua_S, "create", lua_sea_Dialog_create);
        tolua_function(tolua_S, "registerLuaFunctionList", lua_sea_Dialog_registerLuaFunctionList);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::Dialog).name();
    g_luaType[typeName] = "sea.Dialog";
    g_typeCast["Dialog"] = "sea.Dialog";
    return 1;
}

/**TableViewCell**/
int lua_register_sea_table_view_cell(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.TableViewCell");
    tolua_cclass(tolua_S, "TableViewCell", "sea.TableViewCell", "cc.TableViewCell", nullptr);

    tolua_beginmodule(tolua_S, "TableViewCell");
    do{
//        tolua_function(tolua_S, "create", lua_sea_TableViewDynamic_create);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::TableViewCell).name();
    g_luaType[typeName] = "sea.TableViewCell";
    g_typeCast["TableViewCell"] = "sea.TableViewCell";
    return 1;
}


/**TableViewCellData**/
int lua_sea_TableViewCellData_create(lua_State *tolua_S) {
    int argc = 0;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.TableViewCellData", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = sea::TableViewCellData::create();
        object_to_luaval<sea::TableViewCellData>(tolua_S, "sea.TableViewCellData", ret);
        return 1;
    }
    
    if (argc == 1) {
        cocos2d::ValueMap arg0;
        if (!luaval_to_ccvaluemap_new(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewCellData_create'", nullptr);
            return 0;
        }
        
        auto ret = sea::TableViewCellData::create(arg0);
        object_to_luaval<sea::TableViewCellData>(tolua_S, "sea.TableViewCellData", ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.TableViewCellData:create", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewCellData_create'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_table_view_cell_data(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.TableViewCellData");
    tolua_cclass(tolua_S, "TableViewCellData", "sea.TableViewCellData", "cc.Ref", nullptr);

    tolua_beginmodule(tolua_S, "TableViewCellData");
    do{
        tolua_function(tolua_S, "create", lua_sea_TableViewCellData_create);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::TableViewCellData).name();
    g_luaType[typeName] = "sea.TableViewCellData";
    g_typeCast["TableViewCellData"] = "sea.TableViewCellData";
    return 1;
}

/**TableViewBase**/
int lua_register_sea_table_view_base(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.TableViewBase");
    tolua_cclass(tolua_S, "TableViewBase", "sea.TableViewBase", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "TableViewBase");
    do{
//        tolua_function(tolua_S, "create", lua_sea_TableViewDynamic_create);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::TableViewBase).name();
    g_luaType[typeName] = "sea.TableViewBase";
    g_typeCast["TableViewBase"] = "sea.TableViewBase";
    return 1;
}

/**PurchaseManager**/
int lua_sea_PurchaseManager_reset(lua_State *tolua_S) {
    int argc = 0;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.PurchaseManager", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        sea::PurchaseManager::getInstance()->reset();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.PurchaseManager:reset", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_PurchaseManager_reset'.", &tolua_err);
#endif

    return 0;
}


int lua_register_sea_purchase_manager(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.PurchaseManager");
    tolua_cclass(tolua_S, "PurchaseManager", "sea.PurchaseManager", "", nullptr);

    tolua_beginmodule(tolua_S, "PurchaseManager");
    do{
        tolua_function(tolua_S, "reset", lua_sea_PurchaseManager_reset);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::PurchaseManager).name();
    g_luaType[typeName] = "sea.PurchaseManager";
    g_typeCast["PurchaseManager"] = "sea.PurchaseManager";
    return 1;
}

/**TableViewDynamic**/

int lua_sea_TableViewDynamic_create(lua_State *tolua_S) {
    int argc = 0;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        cocos2d::ValueMap arg0, arg1;
        
        if (!luaval_to_ccvaluemap_new(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_create'", nullptr);
            return 0;
        }
        
        if (argc == 1) {
            auto size = sea::getSizeFromCCValue(arg0);
            auto ret = sea::TableViewDynamic::create(size);
            object_to_luaval<sea::TableViewDynamic>(tolua_S, "sea.lua_sea_TableViewDynamic_create", ret);
            return 1;
        }
        
        if (!luaval_to_ccvaluemap_new(tolua_S, 3, &arg1)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_create'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            auto size = sea::getSizeFromCCValue(arg0);
            auto ret = sea::TableViewDynamic::create(size, arg1);
            object_to_luaval<sea::TableViewDynamic>(tolua_S, "sea.TableViewDynamic", ret);
            return 1;
        }
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting `1~2` \n", "sea.TableViewDynamic:create", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_create'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setData(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_setData'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueVector argT;
        ok &= luaval_to_ccvaluevector_new(tolua_S, 2, &argT);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setData'", nullptr);
            return 0;
        }
        
        cocos2d::Vector<sea::TableViewCellData*> list;
        for (const auto& iter : argT) {
            auto dataRef = iter.asRef();
            if (dataRef) {
                auto data = dynamic_cast<sea::TableViewCellData*>(dataRef);
                if (data) {
                    list.pushBack(data);
                }
            }
        }
        
        obj->setData(list);
        return 1;
    }

    luaL_error(tolua_S, "'setData' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setResetFun(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_setResetFun'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setResetFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        
        obj->setResetFun([=](sea::TableViewBase* tableView){
            sea::excuteLuaFunction(handler, 1, [=](cocos2d::LuaStack* stack){
                sea::pushValueToStack(stack, tableView, "sea.TableViewBase");
            });
        });
        return 1;
    }

    luaL_error(tolua_S, "'setResetFun' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setResetFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setScrollFun(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_setScrollFun'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setScrollFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        
        if (argc == 1) {
            obj->setScrollFun([=](sea::TableViewBase* tableView, cocos2d::Value val){
                sea::excuteLuaFunction(handler, 2, [=](cocos2d::LuaStack* stack){
                    sea::pushValueToStack(stack, tableView, "sea.TableViewBase");
                    sea::pushValueToStack(stack, val);
                });
            });
            return 1;
        }
        
        cocos2d::Value arg0;
        if(!luaval_to_ccvalue_new(tolua_S, 3, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setScrollFun'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            obj->setScrollFun([=](sea::TableViewBase* tableView, cocos2d::Value val){
                sea::excuteLuaFunction(handler, 2, [=](cocos2d::LuaStack* stack){
                    sea::pushValueToStack(stack, tableView, "sea.TableViewBase");
                    sea::pushValueToStack(stack, val);
                });
            }, arg0);
            return 1;
        }
    }

    luaL_error(tolua_S, "'setScrollFun' function of Widget has wrong number of arguments: %d, was expecting 1~2 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setScrollFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setCreateItemFun(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_setCreateItemFun'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setCreateItemFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        
        obj->setCreateItemFun([=](sea::TableViewBase* tableView) {
            auto ret = sea::excuteLuaFunction(handler, 1, [=](cocos2d::LuaStack* stack){
                sea::pushValueToStack(stack, tableView, "sea.TableViewBase");
            });
            auto node = ret.asNode();
            return node;
        });
        return 1;
    }

    luaL_error(tolua_S, "'setCreateItemFun' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setCreateItemFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setUpdateItemFun(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_setUpdateItemFun'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setUpdateItemFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        
        obj->setUpdateItemFun([=](sea::TableViewDynamic* tableView, cocos2d::Node* node, sea::TableViewCellData* cellData, int index){
            sea::excuteLuaFunction(handler, 4, [=](cocos2d::LuaStack* stack){
                sea::pushValueToStack(stack, tableView, "sea.TableViewDynamic");
                sea::pushValueToStack(stack, node, "cc.Node");
                sea::pushValueToStack(stack, cellData, "sea.TableViewCellData");
                stack->pushInt(index);
            });
        });
        return 1;
    }

    luaL_error(tolua_S, "'setUpdateItemFun' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setUpdateItemFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setPutItemFun(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_setPutItemFun'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setPutItemFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        
        obj->setPutItemFun([=](sea::TableViewDynamic* tableView, cocos2d::Node* node){
            sea::excuteLuaFunction(handler, 2, [=](cocos2d::LuaStack* stack){
                sea::pushValueToStack(stack, tableView, "sea.TableViewDynamic");
                sea::pushValueToStack(stack, node, "cc.Node");
            });
        });
        return 1;
    }

    luaL_error(tolua_S, "'setPutItemFun' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setPutItemFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_freshView(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_freshView'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 2) {
        bool arg0, arg1;
        
        if (argc == 0) {
            obj->freshView(false, false);
            return 1;
        }
        
        if (!luaval_to_boolean(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_freshView'", nullptr);
            return 0;
        }
        
        if (argc == 1) {
            obj->freshView(arg0, false);
            return 1;
        }
        
        if (!luaval_to_boolean(tolua_S, 3, &arg1)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_freshView'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            obj->freshView(arg0, arg1);
            return 1;
        }
        
        return 0;
    }

    luaL_error(tolua_S, "'freshView' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_freshView'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_jumpToFixIndex(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'self' in function 'lua_sea_TableViewDynamic_jumpToFixIndex'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        int arg0;
        cocos2d::ValueMap arg1;
        
        if (!luaval_to_int32(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_jumpToFixIndex'", nullptr);
            return 0;
        }
        
        if (argc == 1) {
            obj->jumpToFixIndex(arg0);
            return 1;
        }
        
        if (!luaval_to_ccvaluemap_new(tolua_S, 3, &arg1)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_jumpToFixIndex'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            obj->jumpToFixIndex(arg0, arg1);
            return 1;
        }
        
        return 0;
    }

    luaL_error(tolua_S, "'jumpToFixIndex' function of Widget has wrong number of arguments: %d, was expecting 1~2 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_jumpToFixIndex'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_getContainer(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_getContainer'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = obj->getContainer();
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", ret);
        return 1;
    }

    luaL_error(tolua_S, "'getContainer' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_getContainer'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_getViewSize(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_getViewSize'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = obj->getViewSize();
        size_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'getViewSize' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_getViewSize'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_getInnerSize(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_getInnerSize'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = obj->getInnerSize();
        size_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'getInnerSize' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_getInnerSize'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_minContainerOffset(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_minContainerOffset'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = obj->minContainerOffset();
        vec2_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'minContainerOffset' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_minContainerOffset'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_maxContainerOffset(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_maxContainerOffset'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = obj->maxContainerOffset();
        vec2_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'maxContainerOffset' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_maxContainerOffset'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_jumpToPercentVertical(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_jumpToPercentVertical'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        if (!luaval_to_number(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_jumpToPercentVertical'", nullptr);
            return 0;
        }
        obj->jumpToPercentVertical(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'jumpToPercentVertical' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_jumpToPercentVertical'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_jumpToPercentHorizontal(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_jumpToPercentHorizontal'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        double arg0;
        if (!luaval_to_number(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_jumpToPercentHorizontal'", nullptr);
            return 0;
        }
        obj->jumpToPercentHorizontal(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'jumpToPercentHorizontal' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_jumpToPercentHorizontal'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_scrollToPercentVertical(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_scrollToPercentVertical'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 3) {
        double arg0, arg1;
        bool arg2;
        if (!luaval_to_number(tolua_S, 2, &arg0) || !luaval_to_number(tolua_S, 3, &arg1)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_scrollToPercentVertical'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            obj->scrollToPercentVertical(arg0, arg1);
            return 1;
        }
        
        if (!luaval_to_boolean(tolua_S, 4, &arg2)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_scrollToPercentVertical'", nullptr);
            return 0;
        }
        
        if (argc == 3) {
            obj->scrollToPercentVertical(arg0, arg1, arg2);
            return 1;
        }
        
        return 0;
    }

    luaL_error(tolua_S, "'scrollToPercentVertical' function of Widget has wrong number of arguments: %d, was expecting 2~3 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_scrollToPercentVertical'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_scrollToPercentHorizontal(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_scrollToPercentHorizontal'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 2 && argc <= 3) {
        double arg0, arg1;
        bool arg2;
        if (!luaval_to_number(tolua_S, 2, &arg0) || !luaval_to_number(tolua_S, 3, &arg1)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_scrollToPercentHorizontal'", nullptr);
            return 0;
        }
        
        if (argc == 2) {
            obj->scrollToPercentHorizontal(arg0, arg1);
            return 1;
        }
        
        if (!luaval_to_boolean(tolua_S, 4, &arg2)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_scrollToPercentHorizontal'", nullptr);
            return 0;
        }
        
        if (argc == 3) {
            obj->scrollToPercentHorizontal(arg0, arg1, arg2);
            return 1;
        }
        
        return 0;
    }

    luaL_error(tolua_S, "'scrollToPercentHorizontal' function of Widget has wrong number of arguments: %d, was expecting 2~3 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_scrollToPercentHorizontal'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_reloadData(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_reloadData'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0;
        
        if (!luaval_to_boolean(tolua_S, 2, &arg0)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_reloadData'", nullptr);
            return 0;
        }
        
        obj->reloadData(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'reloadData' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_reloadData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setContentOffset(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_setContentOffset'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Vec2 arg0;
        
        if (!luaval_to_vec2(tolua_S, 2, &arg0)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setContentOffset'", nullptr);
            return 0;
        }
        
        obj->setContentOffset(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'setContentOffset' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setContentOffset'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_getContentOffset(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_getContentOffset'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = obj->getContentOffset();
        vec2_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'getContentOffset' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_getContentOffset'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setContentOffsetInDuration(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_setContentOffsetInDuration'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        cocos2d::Vec2 arg0;
        double arg1;
        
        if (!luaval_to_vec2(tolua_S, 2, &arg0) || !luaval_to_number(tolua_S, 3, &arg1)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setContentOffsetInDuration'", nullptr);
            return 0;
        }
        
        obj->setContentOffsetInDuration(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "'setContentOffsetInDuration' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setContentOffsetInDuration'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setTouchEnabled(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_setTouchEnabled'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0;
        
        if (!luaval_to_boolean(tolua_S, 2, &arg0)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setTouchEnabled'", nullptr);
            return 0;
        }
        
        obj->setTouchEnabled(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'setTouchEnabled' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setTouchEnabled'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setBounceAble(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_setBounceAble'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0;
        
        if (!luaval_to_boolean(tolua_S, 2, &arg0)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setBounceAble'", nullptr);
            return 0;
        }
        
        obj->setBounceAble(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'setBounceAble' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setBounceAble'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_setCanTouchOutOfBoundary(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_setCanTouchOutOfBoundary'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        bool arg0;
        
        if (!luaval_to_boolean(tolua_S, 2, &arg0)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_setCanTouchOutOfBoundary'", nullptr);
            return 0;
        }
        
        obj->setCanTouchOutOfBoundary(arg0);
        return 1;
    }

    luaL_error(tolua_S, "'setCanTouchOutOfBoundary' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_setCanTouchOutOfBoundary'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_checkAutoTouchEnabled(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_checkAutoTouchEnabled'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        obj->checkAutoTouchEnabled();
        return 1;
    }

    luaL_error(tolua_S, "'checkAutoTouchEnabled' function of Widget has wrong number of arguments: %d, was expecting 0 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_checkAutoTouchEnabled'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_isInScreen(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_isInScreen'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        int arg0;
        double arg1;
        
        if (!luaval_to_int32(tolua_S, 2, &arg0) || !luaval_to_number(tolua_S, 3, &arg1)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_isInScreen'", nullptr);
            return 0;
        }
        
        auto ret = obj->isInScreen(arg0, arg1);
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'isInScreen' function of Widget has wrong number of arguments: %d, was expecting 2 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_isInScreen'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_isInScreenPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_isInScreenPlus'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        int arg0;
        double arg1;
        
        if (!luaval_to_int32(tolua_S, 2, &arg0) || !luaval_to_number(tolua_S, 3, &arg1)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_isInScreenPlus'", nullptr);
            return 0;
        }
        
        auto ret = obj->isInScreenPlus(arg0, arg1);
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'isInScreenPlus' function of Widget has wrong number of arguments: %d, was expecting 2 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_isInScreenPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_TableViewDynamic_getItemPosSizeInfo(lua_State *tolua_S) {
    int argc = 0;
    sea::TableViewDynamic *obj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;

    if (!tolua_isusertype(tolua_S, 1, "sea.TableViewDynamic", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    obj = static_cast<sea::TableViewDynamic *>(tolua_tousertype(tolua_S, 1, 0));

#if COCOS2D_DEBUG >= 1

    if (nullptr == obj) {
        tolua_error(tolua_S, "invalid 'obj' in function 'lua_sea_TableViewDynamic_getItemPosSizeInfo'\n", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        int arg0;
        
        if (!luaval_to_int32(tolua_S, 2, &arg0)){
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_TableViewDynamic_getItemPosSizeInfo'", nullptr);
            return 0;
        }
        
        auto ret = obj->getItemPosSizeInfo(arg0);
        ccvalue_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "'getItemPosSizeInfo' function of Widget has wrong number of arguments: %d, was expecting 1 \n", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_TableViewDynamic_getItemPosSizeInfo'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_table_view_dynamic(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.TableViewDynamic");
    tolua_cclass(tolua_S, "TableViewDynamic", "sea.TableViewDynamic", "sea.TableViewInterface", nullptr);

    tolua_beginmodule(tolua_S, "TableViewDynamic");
    do{
        tolua_function(tolua_S, "create", lua_sea_TableViewDynamic_create);
        tolua_function(tolua_S, "setData", lua_sea_TableViewDynamic_setData);
        tolua_function(tolua_S, "setResetFun", lua_sea_TableViewDynamic_setResetFun);
        tolua_function(tolua_S, "setScrollFun", lua_sea_TableViewDynamic_setScrollFun);
        tolua_function(tolua_S, "setCreateItemFun", lua_sea_TableViewDynamic_setCreateItemFun);
        tolua_function(tolua_S, "setUpdateItemFun", lua_sea_TableViewDynamic_setUpdateItemFun);
        tolua_function(tolua_S, "setPutItemFun", lua_sea_TableViewDynamic_setPutItemFun);
        tolua_function(tolua_S, "freshView", lua_sea_TableViewDynamic_freshView);
        tolua_function(tolua_S, "jumpToFixIndex", lua_sea_TableViewDynamic_jumpToFixIndex);
        tolua_function(tolua_S, "getContainer", lua_sea_TableViewDynamic_getContainer);
        tolua_function(tolua_S, "getViewSize", lua_sea_TableViewDynamic_getViewSize);
        tolua_function(tolua_S, "getInnerSize", lua_sea_TableViewDynamic_getInnerSize);
        tolua_function(tolua_S, "minContainerOffset", lua_sea_TableViewDynamic_minContainerOffset);
        tolua_function(tolua_S, "maxContainerOffset", lua_sea_TableViewDynamic_maxContainerOffset);
        tolua_function(tolua_S, "jumpToPercentVertical", lua_sea_TableViewDynamic_jumpToPercentVertical);
        tolua_function(tolua_S, "jumpToPercentHorizontal", lua_sea_TableViewDynamic_jumpToPercentHorizontal);
        tolua_function(tolua_S, "scrollToPercentVertical", lua_sea_TableViewDynamic_scrollToPercentVertical);
        tolua_function(tolua_S, "scrollToPercentHorizontal", lua_sea_TableViewDynamic_scrollToPercentHorizontal);
        tolua_function(tolua_S, "reloadData", lua_sea_TableViewDynamic_reloadData);
        tolua_function(tolua_S, "setContentOffset", lua_sea_TableViewDynamic_setContentOffset);
        tolua_function(tolua_S, "getContentOffset", lua_sea_TableViewDynamic_getContentOffset);
        tolua_function(tolua_S, "setContentOffsetInDuration", lua_sea_TableViewDynamic_setContentOffsetInDuration);
        tolua_function(tolua_S, "setTouchEnabled", lua_sea_TableViewDynamic_setTouchEnabled);
        tolua_function(tolua_S, "setBounceAble", lua_sea_TableViewDynamic_setBounceAble);
        tolua_function(tolua_S, "setCanTouchOutOfBoundary", lua_sea_TableViewDynamic_setCanTouchOutOfBoundary);
        tolua_function(tolua_S, "checkAutoTouchEnabled", lua_sea_TableViewDynamic_checkAutoTouchEnabled);
        tolua_function(tolua_S, "isInScreen", lua_sea_TableViewDynamic_isInScreen);
        tolua_function(tolua_S, "isInScreenPlus", lua_sea_TableViewDynamic_isInScreenPlus);
        tolua_function(tolua_S, "getItemPosSizeInfo", lua_sea_TableViewDynamic_getItemPosSizeInfo);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::TableViewDynamic).name();
    g_luaType[typeName] = "sea.TableViewDynamic";
    g_typeCast["TableViewDynamic"] = "sea.TableViewDynamic";
    return 1;
}

/**DialogNodeBase**/

template<typename T>
int lua_sea_DialogNodeBase_getConfig_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getConfig'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto config = cobj->getConfig();
        ccvaluemap_to_luaval(tolua_S, config);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getConfig", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getConfig'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_getCCSNode_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getCCSNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getDialog();
        object_to_luaval<sea::Dialog>(tolua_S, "sea.Dialog", ret);
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Dialog:getRootNode", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getCCSNode'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_getFunPosition_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getFunPosition'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        if (argc >= 4) {
            luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getFunPosition", argc, 1);
            return 0;
        }

        cocos2d::Value pos;
        cocos2d::Node *node = nullptr;
        cocos2d::ValueMap *config = nullptr;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &pos);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_getFunPosition'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            ok &= luaval_to_node(tolua_S, 3, "cc.Node", &node);

            if (argc >= 3) {
                ok &= luaval_to_ccvaluemap_new(tolua_S, 4, config, "sea.DialogNodeBase:getFunPosition");
            }
        }

        auto ret = cobj->getFunPosition(pos, node, config);
        vec2_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getFunPosition", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getFunPosition'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_getFunColor_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getFunColor'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value color;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &color);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_getFunColor'", nullptr);
            return 0;
        }

        auto ret = cobj->getFunColor(color);
        color4b_to_luaval(tolua_S, ret);
        return 1;
    }

    if (argc == 2) {
        cocos2d::Value color;
        cocos2d::Value params = cocos2d::Value::Null;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &color);
        ok &= luaval_to_ccvalue_new(tolua_S, 3, &params);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_getFunColor'", nullptr);
            return 0;
        }

        auto ret = cobj->getFunColor(color, params);
        color4b_to_luaval(tolua_S, ret);
        return 1;
    }

    if (argc == 3) {
        cocos2d::Value color;
        cocos2d::Value params = cocos2d::Value::Null;
        int opacity = 255;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &color);
        ok &= luaval_to_ccvalue_new(tolua_S, 3, &params);
        ok &= luaval_to_int32(tolua_S, 4, (int *)&opacity);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_getFunColor'", nullptr);
            return 0;
        }

        auto ret = cobj->getFunColor(color, params, opacity);
        color4b_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getFunColor", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getFunColor'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_getFunPath_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getFunPath'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1) {
        if (argc >= 4) {
            luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getFunPath", argc, 1);
            return 0;
        }

        cocos2d::Value path;
        cocos2d::Node *node = nullptr;
        cocos2d::ValueMap *config = nullptr;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &path);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_getFunPath'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            ok &= luaval_to_node(tolua_S, 3, "cc.Node", &node);

            if (argc >= 3) {
                ok &= luaval_to_ccvaluemap_new(tolua_S, 4, config, "sea.DialogNodeBase:getFunPath");
            }
        }

        auto ret = cobj->getFunPath(path, node, config);
        ccvalue_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getFunPath", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getFunPath'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_getVertical_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_getVertical'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value config;

        ok &= luaval_to_ccvalue_new(tolua_S, 2, &config);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_getVertical'", nullptr);
            return 0;
        }

        auto ret = cobj->getVertical(config);
        ccvalue_to_luaval(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getVertical", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_getVertical'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_makeSmartChild_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_makeSmartChild'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->makeSmartChild();
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    if (argc == 1) {
        bool reset = false;

        ok &= luaval_to_boolean(tolua_S, 2, &reset, "sea.DialogNodeBase:makeSmartChild");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_makeSmartChild'", nullptr);
            return 0;
        }

        auto ret = cobj->makeSmartChild(reset);
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    if (argc == 2) {
        bool reset = false;
        cocos2d::Value userData;

        ok &= luaval_to_boolean(tolua_S, 2, &reset, "sea.DialogNodeBase:makeSmartChild");
        ok &= luaval_to_ccvalue_new(tolua_S, 3, &userData);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_makeSmartChild'", nullptr);
            return 0;
        }

        auto ret = cobj->makeSmartChild(reset, &userData);
        lua_pushboolean(tolua_S, ret);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:makeSmartChild", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_makeSmartChild'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_DialogNodeBase_setEnterAndExit_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeBase_setEnterAndExit'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 3) {
#if COCOS2D_DEBUG >= 1

        if (!lua_isnil(tolua_S, 2) && !toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

        if (!lua_isnil(tolua_S, 3) && !toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

#endif
        int enterFun = 0;
        int exitFun = 0;
        std::string key = "";

        tolua_Error tolua_err0;

        if (toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err0)) {
            enterFun = toluafix_ref_function(tolua_S, 2, 0);
        }

        if (toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err0)) {
            exitFun = toluafix_ref_function(tolua_S, 3, 0);
        }

        ok &= luaval_to_std_string(tolua_S, 4, &key);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeBase_setEnterAndExit'", nullptr);
            return 0;
        }

        cobj->setEnterAndExit(enterFun, exitFun, key);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:setEnterAndExit", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeBase_setEnterAndExit'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_dialogNodeBase_setUserData_template(lua_State *tolua_S) {
    int argc = 0;
    T *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeBase_setUserData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Value arg0;
        if (!luaval_to_ccvalue(tolua_S, 2, &arg0)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeBase_setUserData'", nullptr);
            return 0;
        }
        cobj->setUserDiyData(arg0);
        return 1;
    }
    
    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:setUserData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeBase_setUserData'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
int lua_sea_dialogNodeBase_getUserData_template(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeBase *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, T::getLuaName(), 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (T *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeBase_getUserData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto ret = cobj->getUserDiyData();
        ccvalue_to_luaval(tolua_S, ret);
        return 1;
    }
    
    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeBase:getUserData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeBase_getUserData'.", &tolua_err);
#endif

    return 0;
}

template<typename T>
void register_lua_sea_DialogNodeBase(lua_State *tolua_S) {
    tolua_function(tolua_S, "getConfig", lua_sea_DialogNodeBase_getConfig_template<T>);
    tolua_function(tolua_S, "getCCSNode", lua_sea_DialogNodeBase_getCCSNode_template<T>);
    tolua_function(tolua_S, "getFunPosition", lua_sea_DialogNodeBase_getFunPosition_template<T>);
    tolua_function(tolua_S, "getFunColor", lua_sea_DialogNodeBase_getFunColor_template<T>);
    tolua_function(tolua_S, "getFunPath", lua_sea_DialogNodeBase_getFunPath_template<T>);
    tolua_function(tolua_S, "getVertical", lua_sea_DialogNodeBase_getVertical_template<T>);
    tolua_function(tolua_S, "makeSmartChild", lua_sea_DialogNodeBase_makeSmartChild_template<T>);
    tolua_function(tolua_S, "setEnterAndExit", lua_sea_DialogNodeBase_setEnterAndExit_template<T>);
    tolua_function(tolua_S, "setUserData", lua_sea_dialogNodeBase_setUserData_template<T>);
    tolua_function(tolua_S, "getUserData", lua_sea_dialogNodeBase_getUserData_template<T>);
}

int lua_register_sea_dialogNodeBase(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeBase");
    tolua_cclass(tolua_S, "DialogNodeBase", "sea.DialogNodeBase", "", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeBase");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeBase>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeBase).name();
    g_luaType[typeName] = "sea.DialogNodeBase";
    g_typeCast["DialogNodeBase"] = "sea.DialogNodeBase";
    return 1;
}

/**DialogNodeDiy**/

int lua_register_sea_dialogNodeDiy(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeDiy");
    tolua_cclass(tolua_S, "DialogNodeDiy", "sea.DialogNodeDiy", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeDiy");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeDiy>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeDiy).name();
    g_luaType[typeName] = "sea.DialogNodeDiy";
    g_typeCast["DialogNodeDiy"] = "sea.DialogNodeDiy";
    return 1;
}

/**DialogNodeLabel**/

int lua_sea_DialogNodeLabel_changeLanguage(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeLabel *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeLabel", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeLabel *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeLabel_changeLanguage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0) {
        cocos2d::ValueMap arg0 = cocos2d::ValueMapNull;
        if (argc >= 1) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeLabel_changeLanguage'", nullptr);
                return 0;
            }
        }
        
        cobj->changeLanguage(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeLabel:changeLanguage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeLabel_changeLanguage'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeLabel(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeLabel");
    tolua_cclass(tolua_S, "DialogNodeLabel", "sea.DialogNodeLabel", "sea.BoleLabel", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeLabel");
    do{
        tolua_function(tolua_S, "changeLanguage", lua_sea_DialogNodeLabel_changeLanguage);
        register_lua_sea_DialogNodeBase<sea::DialogNodeLabel>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeLabel).name();
    g_luaType[typeName] = "sea.DialogNodeLabel";
    g_typeCast["DialogNodeLabel"] = "sea.DialogNodeLabel";
    return 1;
}

/**DialogNodeFnt**/

int lua_register_sea_dialogNodeFnt(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeFnt");
    tolua_cclass(tolua_S, "DialogNodeFnt", "sea.DialogNodeFnt", "cc.Label", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeFnt");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeFnt>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeFnt).name();
    g_luaType[typeName] = "sea.DialogNodeFnt";
    g_typeCast["DialogNodeFnt"] = "sea.DialogNodeFnt";
    return 1;
}


/**DialogNodeButton**/

int lua_sea_dialogNodeButton_addClickEventNoMove(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::DialogNodeButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeButton_addClickEventNoMove'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButton_addClickEventNoMove'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        auto arg0 = [ = ](cocos2d::ui::Button *btn, const cocos2d::Value& params) {
                sea::excuteLuaFunction(handler, 2, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, btn, "ccui.Button");
                sea::pushValueToStack(stack, params);
            });
            };

        cocos2d::Value arg1 = cocos2d::Value::Null;
        double arg2 = -1;

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButton_addClickEventNoMove'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4) && lua_isnumber(tolua_S, 4)) {
            ok &= luaval_to_number(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButton_addClickEventNoMove'", nullptr);
                return 0;
            }
        }

        cobj->addClickEventNoMove(arg0, arg1, arg2);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~3 \n", "sea.DialogNodeButton:addClickEventNoMove", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeButton_addClickEventNoMove'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeButton_changeLanguage(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeButton *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeButton_changeLanguage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0) { //C++不支持传参，但Lua支持。是否需要C++也支持？？TODO
        cobj->changeLanguage();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeButton:changeLanguage", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeButton_changeLanguage'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeButton_reloadNodeFromData(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeButton *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeButton", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeButton *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeButton_reloadNodeFromData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.DialogNodeButton:reloadNodeFromData");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButton_reloadNodeFromData'", nullptr);
            return 0;
        }

        cobj->reloadNodeFromData(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeButton:reloadNodeFromData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeButton_reloadNodeFromData'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeButton(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeButton");
    tolua_cclass(tolua_S, "DialogNodeButton", "sea.DialogNodeButton", "sea.BoleButton", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeButton");
    do{
        tolua_function(tolua_S, "addClickEventNoMove", lua_sea_dialogNodeButton_addClickEventNoMove);
        tolua_function(tolua_S, "reloadNodeFromData", lua_sea_dialogNodeButton_reloadNodeFromData);
        tolua_function(tolua_S, "changeLanguage", lua_sea_dialogNodeButton_changeLanguage);
        register_lua_sea_DialogNodeBase<sea::DialogNodeButton>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeButton).name();
    g_luaType[typeName] = "sea.DialogNodeButton";
    g_typeCast["DialogNodeButton"] = "sea.DialogNodeButton";
    return 1;
}

/**DialogNodeButtonRadio**/

int lua_sea_dialogNodeButtonRadio_checkChosen(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeButtonRadio *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeButtonRadio", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeButtonRadio *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeButtonRadio_checkChosen'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        cobj->checkChosen();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeButtonRadio:checkChosen", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeButtonRadio_checkChosen'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeButtonRadio(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeButtonRadio");
    tolua_cclass(tolua_S, "DialogNodeButtonRadio", "sea.DialogNodeButtonRadio", "sea.DialogNodeButton", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeButtonRadio");
    do{
        tolua_function(tolua_S, "checkChosen", lua_sea_dialogNodeButtonRadio_checkChosen);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeButtonRadio).name();
    g_luaType[typeName] = "sea.DialogNodeButtonRadio";
    g_typeCast["DialogNodeButtonRadio"] = "sea.DialogNodeButtonRadio";
    return 1;
}

/**DialogNodeButtonEmpty**/

int lua_register_sea_dialogNodeButtonEmpty(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeButtonEmpty");
    tolua_cclass(tolua_S, "DialogNodeButtonEmpty", "sea.DialogNodeButtonEmpty", "sea.DialogNodeButton", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeButtonEmpty");
    do{
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeButtonEmpty).name();
    g_luaType[typeName] = "sea.DialogNodeButtonEmpty";
    g_typeCast["DialogNodeButtonEmpty"] = "sea.DialogNodeButtonEmpty";
    return 1;
}

/**DialogNodeButtonMask**/

int lua_register_sea_dialogNodeButtonMask(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeButtonMask");
    tolua_cclass(tolua_S, "DialogNodeButtonMask", "sea.DialogNodeButtonMask", "sea.DialogNodeButton", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeButtonMask");
    do{
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeButtonMask).name();
    g_luaType[typeName] = "sea.DialogNodeButtonMask";
    g_typeCast["DialogNodeButtonMask"] = "sea.DialogNodeButtonMask";
    return 1;
}

/**DialogNodeButtonPay*/
int lua_sea_dialogNodeButtonPay_addClickEventNoMove(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::DialogNodeButtonPay *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeButtonPay", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeButtonPay *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeButtonPay_addClickEventNoMove'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 3) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButtonPay_addClickEventNoMove'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));
        auto arg0 = [ = ](cocos2d::ui::Button *btn, const cocos2d::Value& params) {
                sea::excuteLuaFunction(handler, 2, [ & ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, btn, "ccui.Button");
                sea::pushValueToStack(stack, params);
            });
            };

        cocos2d::Value arg1 = cocos2d::Value::Null;
        double arg2 = -1;

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButtonPay_addClickEventNoMove'", nullptr);
                return 0;
            }
        }

        if (argc >= 3 && !lua_isnil(tolua_S, 4) && lua_isnumber(tolua_S, 4)) {
            ok &= luaval_to_number(tolua_S, 4, &arg2);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeButtonPay_addClickEventNoMove'", nullptr);
                return 0;
            }
        }

        cobj->addClickEventNoMove(arg0, arg1, arg2);

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~3 \n", "sea.DialogNodeButton:addClickEventNoMove", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeButtonPay_addClickEventNoMove'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeButtonPay(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeButtonPay");
    tolua_cclass(tolua_S, "DialogNodeButtonPay", "sea.DialogNodeButtonPay", "sea.BoleButtonPay", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeButtonPay");
    do{
        tolua_function(tolua_S, "addClickEventNoMove", lua_sea_dialogNodeButtonPay_addClickEventNoMove);
        register_lua_sea_DialogNodeBase<sea::DialogNodeButtonPay>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeButtonPay).name();
    g_luaType[typeName] = "sea.DialogNodeButtonPay";
    g_typeCast["DialogNodeButtonPay"] = "sea.DialogNodeButtonPay";
    return 1;
}

/**DialogNodeSprite**/

int lua_sea_dialogNodeSprite_reloadNodeFromData(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSprite *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSprite", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSprite *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeSprite_reloadNodeFromData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.DialogNodeSprite:reloadNodeFromData");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_reloadNodeFromData'", nullptr);
            return 0;
        }

        cobj->reloadNodeFromData(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeSprite:reloadNodeFromData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeSprite_reloadNodeFromData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeSprite_checkTexture(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSprite *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSprite", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSprite *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeSprite_checkTexture'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0 = "";
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_checkTexture'", nullptr);
            return 0;
        }

        cobj->checkTexture(arg0);
        return 1;
    }

    if (argc >= 1 && argc <= 2) {
        std::string arg0 = "";
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_checkTexture'", nullptr);
            return 0;
        }

        int arg1;
        ok &= luaval_to_int32(tolua_S, 2, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_checkTexture'", nullptr);
            return 0;
        }

        sea::TextureResType resType;
        try {
            resType = static_cast<sea::TextureResType>(arg1);
        } catch (...) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_checkTexture'", nullptr);
            return 0;
        }

        cobj->checkTexture(arg0, resType);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeSprite:checkTexture", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeSprite_checkTexture'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeSprite_setTexture(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSprite *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSprite", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSprite *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeSprite_setTexture'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0 = "";
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_setTexture'", nullptr);
            return 0;
        }

        cobj->setTexture(arg0);
        return 1;
    }

    if (argc >= 1 && argc <= 2) {
        std::string arg0 = "";
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_setTexture'", nullptr);
            return 0;
        }

        int arg1;
        ok &= luaval_to_int32(tolua_S, 2, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_setTexture'", nullptr);
            return 0;
        }

        sea::TextureResType resType;
        try {
            resType = static_cast<sea::TextureResType>(arg1);
        } catch (...) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_setTexture'", nullptr);
            return 0;
        }

        cobj->setTexture(arg0, resType);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeSprite:setTexture", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeSprite_setTexture'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeSprite_checkColor(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSprite *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSprite", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSprite *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeSprite_checkColor'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::Color3B arg0;
        ok &= luaval_to_color3b(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite_checkColor'", nullptr);
            return 0;
        }

        cobj->checkColor(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeSprite:checkColor", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeSprite_checkColor'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeSprite(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeSprite");
    tolua_cclass(tolua_S, "DialogNodeSprite", "sea.DialogNodeSprite", "cc.Sprite", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeSprite");
    do{
        tolua_function(tolua_S, "reloadNodeFromData", lua_sea_dialogNodeSprite_reloadNodeFromData);
        tolua_function(tolua_S, "checkTexture", lua_sea_dialogNodeSprite_checkTexture);
        tolua_function(tolua_S, "setTexture", lua_sea_dialogNodeSprite_setTexture);
        tolua_function(tolua_S, "checkColor", lua_sea_dialogNodeSprite_checkColor);
        register_lua_sea_DialogNodeBase<sea::DialogNodeSprite>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeSprite).name();
    g_luaType[typeName] = "sea.DialogNodeSprite";
    g_typeCast["DialogNodeSprite"] = "sea.DialogNodeSprite";
    return 1;
}

/**DialogNodeSprite9**/

int lua_sea_dialogNodeSprite9_reloadNodeFromData(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSprite9 *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSprite9", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSprite9 *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeSprite9_reloadNodeFromData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0, "sea.DialogNodeSprite9:reloadNodeFromData");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeSprite9_reloadNodeFromData'", nullptr);
            return 0;
        }

        cobj->reloadNodeFromData(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeSprite9:reloadNodeFromData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeSprite9_reloadNodeFromData'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeSprite9(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeSprite9");
    tolua_cclass(tolua_S, "DialogNodeSprite9", "sea.DialogNodeSprite9", "ccui.Scale9Sprite", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeSprite9");
    do{
        tolua_function(tolua_S, "reloadNodeFromData", lua_sea_dialogNodeSprite9_reloadNodeFromData);
        register_lua_sea_DialogNodeBase<sea::DialogNodeSprite9>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeSprite9).name();
    g_luaType[typeName] = "sea.DialogNodeSprite9";
    g_typeCast["DialogNodeSprite9"] = "sea.DialogNodeSprite9";
    return 1;
}

/**DialogNodeAnimation**/

int lua_register_sea_dialogNodeAnimation(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeAnimation");
    tolua_cclass(tolua_S, "DialogNodeAnimation", "sea.DialogNodeAnimation", "sp.SkeletonAnimation", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeAnimation");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeAnimation>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeAnimation).name();
    g_luaType[typeName] = "sea.DialogNodeAnimation";
    g_typeCast["DialogNodeAnimation"] = "sea.DialogNodeAnimation";
    return 1;
}

/**DialogNodeAnimationNew**/

int lua_sea_dialogNodeAnimationNew_getAnimation(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_getAnimation'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto animation = cobj->getAnimation();
        object_to_luaval<spine::SkeletonAnimation>(tolua_S, "sp.SkeletonAnimation", animation);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:getAnimation", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_getAnimation'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_getCCSNodePlusChildByName(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_getCCSNodePlusChildByName'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;
        bool ok = luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_getCCSNodePlusChildByName'", nullptr);
            return 0;
        }
        
        auto node = cobj->cocos2d::Node::getSeaDialogChildNodeByName(arg0);
        node_to_luaval(tolua_S, "cc.Node", node);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:getCCSNodePlusChildByName", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_getCCSNodePlusChildByName'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_setAnimation(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_setAnimation'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 3) {
        int index;
        std::string name;
        bool loop;

        ok &= luaval_to_int32(tolua_S, 2, &index);
        ok &= luaval_to_std_string(tolua_S, 3, &name);
        ok &= luaval_to_boolean(tolua_S, 4, &loop);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_setAnimation'", nullptr);
            return 0;
        }

        cobj->setAnimation(index, name, loop);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:setAnimation", argc, 3);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_setAnimation'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_addAnimation(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_addAnimation'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 3) {
        int index;
        std::string name;
        bool loop;

        ok &= luaval_to_int32(tolua_S, 2, &index);
        ok &= luaval_to_std_string(tolua_S, 3, &name);
        ok &= luaval_to_boolean(tolua_S, 4, &loop);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_addAnimation'", nullptr);
            return 0;
        }

        cobj->addAnimation(index, name, loop);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:addAnimation", argc, 3);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_addAnimation'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_isSlot(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_isSlot'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto flag = cobj->isSlot();
        lua_pushboolean(tolua_S, flag);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:isSlot", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_isSlot'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_checkAnimation(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_checkAnimation'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_checkAnimation'", nullptr);
            return 0;
        }

        cobj->checkAnimation(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:checkAnimation", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_checkAnimation'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_checkAnimationPathOnly(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_checkAnimationPathOnly'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_checkAnimationPathOnly'", nullptr);
            return 0;
        }

        auto flag = cobj->checkAnimationPathOnly(arg0);
        lua_pushboolean(tolua_S, flag);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:checkAnimationPathOnly", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_checkAnimationPathOnly'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_reloadNodeFromData(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_reloadNodeFromData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto arg0 = cobj->getConfig();
        cobj->reloadNodeFromData(arg0);
        return 1;
    }

    if (argc == 1) {
        cocos2d::ValueMap arg0;

        ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_reloadNodeFromData'", nullptr);
            return 0;
        }

        cobj->reloadNodeFromData(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:reloadNodeFromData", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_reloadNodeFromData'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_registerSpineEventHandler(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_registerSpineEventHandler'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
#if COCOS2D_DEBUG >= 1

        if (lua_isnil(tolua_S, 2) || !toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            goto tolua_lerror;
        }

#endif
        int handler = toluafix_ref_function(tolua_S, 2, 0);
        int type;

        ok &= luaval_to_int32(tolua_S, 3, &type);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_registerSpineEventHandler'", nullptr);
            return 0;
        }

        cobj->registerSpineEventHandler(handler, type);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:registerSpineEventHandler", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_registerSpineEventHandler'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_unregisterSpineEventHandler(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_unregisterSpineEventHandler'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        int type;

        ok &= luaval_to_int32(tolua_S, 3, &type);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_dialogNodeAnimationNew_unregisterSpineEventHandler'", nullptr);
            return 0;
        }

        cobj->unregisterSpineEventHandler(type);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:unregisterSpineEventHandler", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_unregisterSpineEventHandler'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_dialogNodeAnimationNew_changeLanguage(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeAnimationNew *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeAnimationNew", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeAnimationNew *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_dialogNodeAnimationNew_changeLanguage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0) {
        cobj->changeLanguage();
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.DialogNodeAnimationNew:changeLanguage", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_dialogNodeAnimationNew_changeLanguage'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeAnimationNew(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeAnimationNew");
    tolua_cclass(tolua_S, "DialogNodeAnimationNew", "sea.DialogNodeAnimationNew", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeAnimationNew");
    do{
        tolua_function(tolua_S, "getCCSNodePlusChildByName", lua_sea_dialogNodeAnimationNew_getCCSNodePlusChildByName);
        tolua_function(tolua_S, "getAnimation", lua_sea_dialogNodeAnimationNew_getAnimation);
        tolua_function(tolua_S, "setAnimation", lua_sea_dialogNodeAnimationNew_setAnimation);
        tolua_function(tolua_S, "addAnimation", lua_sea_dialogNodeAnimationNew_addAnimation);
        tolua_function(tolua_S, "isSlot", lua_sea_dialogNodeAnimationNew_isSlot);
        tolua_function(tolua_S, "checkAnimation", lua_sea_dialogNodeAnimationNew_checkAnimation);
        tolua_function(tolua_S, "checkAnimationPathOnly", lua_sea_dialogNodeAnimationNew_checkAnimationPathOnly);
        tolua_function(tolua_S, "reloadNodeFromData", lua_sea_dialogNodeAnimationNew_reloadNodeFromData);
        tolua_function(tolua_S, "registerSpineEventHandler", lua_sea_dialogNodeAnimationNew_registerSpineEventHandler);
        tolua_function(tolua_S, "unregisterSpineEventHandler", lua_sea_dialogNodeAnimationNew_unregisterSpineEventHandler);
        tolua_function(tolua_S, "changeLanguage", lua_sea_dialogNodeAnimationNew_changeLanguage);
        register_lua_sea_DialogNodeBase<sea::DialogNodeAnimationNew>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeAnimationNew).name();
    g_luaType[typeName] = "sea.DialogNodeAnimationNew";
    g_typeCast["DialogNodeAnimationNew"] = "sea.DialogNodeAnimationNew";
    return 1;
}

/**DialogNodeList**/

int lua_register_sea_dialogNodeList(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeList");
    tolua_cclass(tolua_S, "DialogNodeList", "sea.DialogNodeList", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeList");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeList>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeList).name();
    g_luaType[typeName] = "sea.DialogNodeList";
    g_typeCast["DialogNodeList"] = "sea.DialogNodeList";
    return 1;
}

/**DialogNodeProgress**/

int lua_register_sea_dialogNodeProgress(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeProgress");
    tolua_cclass(tolua_S, "DialogNodeProgress", "sea.DialogNodeProgress", "sea.BoleProgress", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeProgress");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeProgress>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeProgress).name();
    g_luaType[typeName] = "sea.DialogNodeProgress";
    g_typeCast["DialogNodeProgress"] = "sea.DialogNodeProgress";
    return 1;
}

/**DialogNodeProgressTimer**/

int lua_sea_DialogNodeProgressTimer_setPercent(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;
    sea::DialogNodeProgressTimer *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeProgressTimer", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeProgressTimer *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeProgressTimer_setPercent'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0 && argc <= 2) {
        double arg0, arg1;

        if (argc >= 1 && !lua_isnil(tolua_S, 2)) {
            ok &= luaval_to_number(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeProgressTimer_setPercent'", nullptr);
                return 0;
            }
        }

        if (argc >= 2 && !lua_isnil(tolua_S, 3)) {
            ok &= luaval_to_number(tolua_S, 3, &arg1);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeProgressTimer_setPercent'", nullptr);
                return 0;
            }
        }

        if (argc == 0) {
            cobj->setPercent();
        } else if (argc == 1) {
            cobj->setPercent(arg0);
        } else {
            cobj->setPercent(arg0, arg1);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0~2 \n", "sea.DialogNodeProgressTimer:setPercent", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeProgressTimer_setPercent'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeProgressTimer(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeProgressTimer");
    tolua_cclass(tolua_S, "DialogNodeProgressTimer", "sea.DialogNodeProgressTimer", "cc.ProgressTimer", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeProgressTimer");
    do{
        tolua_function(tolua_S, "setPercent", lua_sea_DialogNodeProgressTimer_setPercent);
        register_lua_sea_DialogNodeBase<sea::DialogNodeProgressTimer>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeProgressTimer).name();
    g_luaType[typeName] = "sea.DialogNodeProgressTimer";
    g_typeCast["DialogNodeProgressTimer"] = "sea.DialogNodeProgressTimer";
    return 1;
}

/**DialogNodeClipping**/
int lua_register_sea_dialogNodeClipping(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeClipping");
    tolua_cclass(tolua_S, "DialogNodeClipping", "sea.DialogNodeClipping", "cc.ClippingNode", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeClipping");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeClipping>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeClipping).name();
    g_luaType[typeName] = "sea.DialogNodeClipping";
    g_typeCast["DialogNodeClipping"] = "sea.DialogNodeClipping";
    return 1;
}

/**DialogNodeScrollView**/
int lua_register_sea_dialogNodeScrollView(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeScrollView");
    tolua_cclass(tolua_S, "DialogNodeScrollView", "sea.DialogNodeScrollView", "ccui.ScrollView", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeScrollView");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeScrollView>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeScrollView).name();
    g_luaType[typeName] = "sea.DialogNodeScrollView";
    g_typeCast["DialogNodeScrollView"] = "sea.DialogNodeScrollView";
    return 1;
}

/**DialogNodeEdit**/

int lua_sea_DialogNodeEdit_registerEventHandler(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeEdit *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeEdit", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeEdit *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeEdit_registerEventHandler'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeEdit_registerEventHandler'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->registerEventHandler([ = ](std::string eventType, sea::DialogNodeEdit *edit) {
            sea::excuteLuaFunction(handler, 2, [ = ](cocos2d::LuaStack *stack) {
                stack->pushString(eventType.c_str());
                sea::pushValueToStack(stack, edit, "sea.DialogNodeEdit");
            });
        });

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeEdit:registerEventHandler", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeEdit_registerEventHandler'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeEdit(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeEdit");
    tolua_cclass(tolua_S, "DialogNodeEdit", "sea.DialogNodeEdit", "ccui.EditBox", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeEdit");
    do{
        tolua_function(tolua_S, "registerEventHandler", lua_sea_DialogNodeEdit_registerEventHandler);
        register_lua_sea_DialogNodeBase<sea::DialogNodeEdit>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeEdit).name();
    g_luaType[typeName] = "sea.DialogNodeEdit";
    g_typeCast["DialogNodeEdit"] = "sea.DialogNodeEdit";
    return 1;
}

/**DialogNodeSlider**/
int lua_register_sea_dialogNodeSlider(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeSlider");
    tolua_cclass(tolua_S, "DialogNodeSlider", "sea.DialogNodeSlider", "sea.BoleSlider", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeSlider");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeSlider>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeSlider).name();
    g_luaType[typeName] = "sea.DialogNodeSlider";
    g_typeCast["DialogNodeSlider"] = "sea.DialogNodeSlider";
    return 1;
}

/**DialogNodeSwitch**/

int lua_sea_DialogNodeSwitch_setCreateFun(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_setCreateFun'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_setCreateFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->setCreateFun([ = ](sea::DialogNodeSwitch *switchNode, std::string key, cocos2d::ValueVector nodes, cocos2d::Value data){
            sea::excuteLuaFunction(handler, 4, [ = ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, switchNode, "sea.DialogNodeSwitch");
                stack->pushString(key.c_str());
                sea::pushValueToStack(stack, nodes);
                sea::pushValueToStack(stack, data);
            });
        });

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeSwitch:setCreateFun", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_setCreateFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeSwitch_setInFun(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_setInFun'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_setInFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->setInFun([ = ](sea::DialogNodeSwitch *switchNode, std::string key, cocos2d::ValueVector nodes, cocos2d::Value data, bool flag) {
            sea::excuteLuaFunction(handler, 5, [ = ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, switchNode, "sea.DialogNodeSwitch");
                stack->pushString(key.c_str());
                sea::pushValueToStack(stack, nodes);
                sea::pushValueToStack(stack, data);
                stack->pushBoolean(flag);
            });
        });

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeSwitch:setInFun", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_setInFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeSwitch_setOutFun(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_setOutFun'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 2, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_setOutFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 2, 0));

        cobj->setOutFun([ = ](sea::DialogNodeSwitch *switchNode, std::string key, cocos2d::ValueVector nodes, cocos2d::Value data, bool flag) {
            sea::excuteLuaFunction(handler, 5, [ = ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, switchNode, "sea.DialogNodeSwitch");
                stack->pushString(key.c_str());
                sea::pushValueToStack(stack, nodes);
                sea::pushValueToStack(stack, data);
                stack->pushBoolean(flag);
            });
        });

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeSwitch:setOutFun", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_setOutFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeSwitch_switch(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_switch'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 4) {
        std::string arg0;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_switch'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            cocos2d::Value arg1;

            if (!lua_isnil(tolua_S, 3)) {
                bool lang;
                ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_switch'", nullptr);
                    return 0;
                }
            }

            if (argc >= 3) {
                bool arg2;

                if (!lua_isnil(tolua_S, 4)) {
                    bool vertical;
                    ok &= luaval_to_boolean(tolua_S, 4, &arg2);

                    if (!ok) {
                        tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_switch'", nullptr);
                        return 0;
                    }
                }

                if (argc >= 4) {
                    bool arg3;

                    if (!lua_isnil(tolua_S, 5)) {
                        int rType = -1;
                        ok &= luaval_to_boolean(tolua_S, 5, &arg3);

                        if (!ok) {
                            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_switch'", nullptr);
                            return 0;
                        }
                    }

                    cobj->switchKey(arg0, arg1, arg2, arg3);
                } else {
                    cobj->switchKey(arg0, arg1, arg2);
                }
            } else {
                cobj->switchKey(arg0, arg1);
            }
        } else {
            cobj->switchKey(arg0);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~4 \n", "sea.DialogNodeSwitch:switch", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_switch'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeSwitch_switchPlus(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_switchPlus'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 1 && argc <= 2) {
        std::string arg0;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_switchPlus'", nullptr);
            return 0;
        }

        if (argc >= 2) {
            cocos2d::ValueMap arg1;

            if (!lua_isnil(tolua_S, 3)) {
                bool lang;
                ok &= luaval_to_ccvaluemap_new(tolua_S, 3, &arg1);

                if (!ok) {
                    tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeSwitch_switchPlus'", nullptr);
                    return 0;
                }
            }

            cobj->switchPlus(arg0, arg1);
        } else {
            cobj->switchPlus(arg0);
        }

        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1~2 \n", "sea.DialogNodeSwitch:switchPlus", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_switchPlus'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeSwitch_getShowingNode(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_getShowingNode'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto node = cobj->getShowingNode();
        object_to_luaval<cocos2d::Node>(tolua_S, "cc.Node", node);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.DialogNodeSwitch:getShowingNode", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_getShowingNode'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeSwitch_getShowingKey(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeSwitch *cobj = nullptr;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif

#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeSwitch", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeSwitch *)tolua_tousertype(tolua_S, 1, 0);
#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeSwitch_getShowingKey'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        std::string str = cobj->getShowingKey();
        lua_pushstring(tolua_S, str.c_str());
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 0 \n", "sea.DialogNodeSwitch:getShowingKey", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeSwitch_getShowingKey'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeSwitch(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeSwitch");
    tolua_cclass(tolua_S, "DialogNodeSwitch", "sea.DialogNodeSwitch", "cc.Node", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeSwitch");
    do{
        tolua_function(tolua_S, "setCreateFun", lua_sea_DialogNodeSwitch_setCreateFun);
        tolua_function(tolua_S, "setInFun", lua_sea_DialogNodeSwitch_setInFun);
        tolua_function(tolua_S, "setOutFun", lua_sea_DialogNodeSwitch_setOutFun);
        tolua_function(tolua_S, "switch", lua_sea_DialogNodeSwitch_switch);
        tolua_function(tolua_S, "switchPlus", lua_sea_DialogNodeSwitch_switchPlus);
        tolua_function(tolua_S, "getShowingNode", lua_sea_DialogNodeSwitch_getShowingNode);
        tolua_function(tolua_S, "getShowingKey", lua_sea_DialogNodeSwitch_getShowingKey);
        register_lua_sea_DialogNodeBase<sea::DialogNodeSwitch>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeSwitch).name();
    g_luaType[typeName] = "sea.DialogNodeSwitch";
    g_typeCast["DialogNodeSwitch"] = "sea.DialogNodeSwitch";
    return 1;
}

/**DialogNodeRedDot**/
int lua_register_sea_dialogNodeRedDot(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeRedDot");
    tolua_cclass(tolua_S, "DialogNodeRedDot", "sea.DialogNodeRedDot", "sea.BoleRedDot", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeRedDot");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodeRedDot>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeRedDot).name();
    g_luaType[typeName] = "sea.DialogNodeRedDot";
    g_typeCast["DialogNodeRedDot"] = "sea.DialogNodeRedDot";
    return 1;
}

/**DialogNodeOrder**/
int lua_sea_DialogNodeOrder_changeLanguage(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeOrder_changeLanguage'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc >= 0) {
        cocos2d::ValueMap arg0 = cocos2d::ValueMapNull;
        if (argc >= 1) {
            ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg0);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeOrder_changeLanguage'", nullptr);
                return 0;
            }
        }
        
        cobj->changeLanguage(arg0);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeOrder:changeLanguage", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeOrder_changeLanguage'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_DialogNodeOrder_getCCSNodePlusChildByName(lua_State *tolua_S) {
    int argc = 0;
    sea::DialogNodeOrder *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "sea.DialogNodeOrder", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (sea::DialogNodeOrder *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_sea_DialogNodeOrder_getCCSNodePlusChildByName'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;
        ok &= luaval_to_std_string(tolua_S, 2, &arg0);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_DialogNodeOrder_getCCSNodePlusChildByName'", nullptr);
            return 0;
        }
        
        auto node = cobj->getKeyNodeByName(arg0);
        node_to_luaval(tolua_S, "cc.Node", node);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting 1 \n", "sea.DialogNodeOrder:getCCSNodePlusChildByName", argc);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_DialogNodeOrder_getCCSNodePlusChildByName'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_dialogNodeOrder(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodeOrder");
    tolua_cclass(tolua_S, "DialogNodeOrder", "sea.DialogNodeOrder", "sea.BoleOrder", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodeOrder");
    do{
        tolua_function(tolua_S, "changeLanguage", lua_sea_DialogNodeOrder_changeLanguage);
        tolua_function(tolua_S, "getCCSNodePlusChildByName", lua_sea_DialogNodeOrder_getCCSNodePlusChildByName);
        register_lua_sea_DialogNodeBase<sea::DialogNodeOrder>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodeOrder).name();
    g_luaType[typeName] = "sea.DialogNodeOrder";
    g_typeCast["DialogNodeOrder"] = "sea.DialogNodeOrder";
    return 1;
}

/**DialogNodePageView**/

int lua_register_sea_dialogNodePageView(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.DialogNodePageView");
    tolua_cclass(tolua_S, "DialogNodePageView", "sea.DialogNodePageView", "sea.BolePageView", nullptr);

    tolua_beginmodule(tolua_S, "DialogNodePageView");
    do{
        register_lua_sea_DialogNodeBase<sea::DialogNodePageView>(tolua_S);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::DialogNodePageView).name();
    g_luaType[typeName] = "sea.DialogNodePageView";
    g_typeCast["DialogNodePageView"] = "sea.DialogNodePageView";
    return 1;
}

/**Global**/

int lua_sea_Global_registerLuaFunctionVersion(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Global", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 3) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Global_registerLuaFunctionVersion'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        std::string name;
        int version = 0;
        ok &= luaval_to_std_string(tolua_S, 2, &name, "sea.Global:registerLuaFunctionVersion");
        ok &= luaval_to_int32(tolua_S, 4, &version, "sea.Global:registerLuaFunctionVersion");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Global_registerLuaFunctionVersion'", nullptr);
            return 0;
        }

        sea::Global::registerLuaFunctionVersion(name, handler, version);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Global:registerLuaFunctionVersion", argc, 3);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Global_registerLuaFunctionVersion'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Global_registerBindObjectLuaFun(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Global", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
#if COCOS2D_DEBUG >= 1

        if (!toluafix_isfunction(tolua_S, 3, "LUA_FUNCTION", 0, &tolua_err)) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Global_registerBindObjectLuaFun'", nullptr);
            return 0;
        }

#endif

        LUA_FUNCTION handler = (toluafix_ref_function(tolua_S, 3, 0));

        std::string name;
        ok &= luaval_to_std_string(tolua_S, 2, &name, "sea.Global:registerBindObjectLuaFun");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Global_registerBindObjectLuaFun'", nullptr);
            return 0;
        }

        sea::Global::registerBindObjectLuaFun(name, handler);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Global:registerBindObjectLuaFun", argc, 3);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Global_registerBindObjectLuaFun'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Global_setConfig(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Global", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        try {
            cocos2d::ValueMap arg1;
            ok &= luaval_to_ccvaluemap_new(tolua_S, 2, &arg1, "sea.Global:setConfig");

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Global_setConfig'", nullptr);
                return 0;
            }

            sea::Global::setConfig(arg1);
        } catch (...) {
#if COCOS2D_DEBUG >= 1
            sea::showMessageBox("seaConfig调用错误");
#endif
        }
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Global:setConfig", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Global_setConfig'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Global_getVersion(lua_State *tolua_S) {
    int argc = 0;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Global", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 0) {
        auto version = sea::Global::getVersion();
        lua_pushinteger(tolua_S, version);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Global:getVersion", argc, 0);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Global_getVersion'.", &tolua_err);
#endif

    return 0;
}

int lua_sea_Global_setLogAble(lua_State *tolua_S) {
    int argc = 0;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertable(tolua_S, 1, "sea.Global", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        try {
            bool able;
            ok &= luaval_to_boolean(tolua_S, 2, &able);

            if (!ok) {
                tolua_error(tolua_S, "invalid arguments in function 'lua_sea_Global_setLogAble'", nullptr);
                return 0;
            }

            sea::Global::setLogAble(able);
        } catch (...) {
#if COCOS2D_DEBUG >= 1
            sea::showMessageBox("setLogAble调用错误");
#endif
        }
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "sea.Global:setLogAble", argc, 1);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_sea_Global_setLogAble'.", &tolua_err);
#endif

    return 0;
}

int lua_register_sea_global(lua_State *tolua_S) {
    tolua_usertype(tolua_S, "sea.Global");
    tolua_cclass(tolua_S, "Global", "sea.Global", "", nullptr);

    tolua_beginmodule(tolua_S, "Global");
    do{
        tolua_function(tolua_S, "registerLuaFunctionVersion", lua_sea_Global_registerLuaFunctionVersion);
        tolua_function(tolua_S, "registerBindObjectLuaFun", lua_sea_Global_registerBindObjectLuaFun);
        tolua_function(tolua_S, "setConfig", lua_sea_Global_setConfig);
        tolua_function(tolua_S, "getVersion", lua_sea_Global_getVersion);
        tolua_function(tolua_S, "setLogAble", lua_sea_Global_setLogAble);
    }while(0);
    tolua_endmodule(tolua_S);
    std::string typeName = typeid(sea::Global).name();
    g_luaType[typeName] = "sea.Global";
    g_typeCast["Global"] = "sea.Global";
    return 1;
}

int lua_cocos2dx_Node_getSeaUserData(lua_State *tolua_S) {
    int argc = 0;
    cocos2d::Node *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "cc.Node", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (cocos2d::Node *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_cocos2dx_Node_getSeaUserData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 1) {
        std::string arg0;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0, "cc.Node:getImgPathPlus");

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_cocos2dx_Node_getSeaUserData'", nullptr);
            return 0;
        }

        auto info = cobj->getSeaUserData(arg0);
        if (!info) {
            lua_pushnil(tolua_S);
        }
        else {
            ccvalue_to_luaval(tolua_S, *info);
        }
        
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "cc.Node:getSeaUserData", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_cocos2dx_Node_getSeaUserData'.", &tolua_err);
#endif

    return 0;
}

int lua_cocos2dx_Node_setSeaUserData(lua_State *tolua_S) {
    int argc = 0;
    cocos2d::Node *cobj = nullptr;
    bool ok = true;

#if COCOS2D_DEBUG >= 1
    tolua_Error tolua_err;
#endif


#if COCOS2D_DEBUG >= 1

    if (!tolua_isusertype(tolua_S, 1, "cc.Node", 0, &tolua_err)) {
        goto tolua_lerror;
    }

#endif

    cobj = (cocos2d::Node *)tolua_tousertype(tolua_S, 1, 0);

#if COCOS2D_DEBUG >= 1

    if (!cobj) {
        tolua_error(tolua_S, "invalid 'cobj' in function 'lua_cocos2dx_Node_setSeaUserData'", nullptr);
        return 0;
    }

#endif

    argc = lua_gettop(tolua_S) - 1;

    if (argc == 2) {
        std::string arg0;
        cocos2d::Value arg1;

        ok &= luaval_to_std_string(tolua_S, 2, &arg0, "cc.Node:setSeaUserData");
        ok &= luaval_to_ccvalue_new(tolua_S, 3, &arg1);

        if (!ok) {
            tolua_error(tolua_S, "invalid arguments in function 'lua_cocos2dx_Node_setSeaUserData'", nullptr);
            return 0;
        }

        cobj->setSeaUserData(arg0, arg1);
        return 1;
    }

    luaL_error(tolua_S, "%s has wrong number of arguments: %d, was expecting %d \n", "cc.Node:setSeaUserData", argc, 2);
    return 0;

#if COCOS2D_DEBUG >= 1
 tolua_lerror:
    tolua_error(tolua_S, "#ferror in function 'lua_cocos2dx_Node_setSeaUserData'.", &tolua_err);
#endif

    return 0;
}

void extendCocos(lua_State *L) {
    lua_pushstring(L, "cc.Node");
    lua_rawget(L, LUA_REGISTRYINDEX);

    if (lua_istable(L, -1)) {
        tolua_function(L, "getSeaUserData", lua_cocos2dx_Node_getSeaUserData);
        tolua_function(L, "setSeaUserData", lua_cocos2dx_Node_setSeaUserData);
    }

    lua_pop(L, 1);
}

TOLUA_API int lua_register_all_sea(lua_State *tolua_S) {
    tolua_open(tolua_S);
    
    //版本
    do {
        auto version = sea::Global::getVersion();
        lua_pushinteger(tolua_S, version);
        lua_setglobal(tolua_S, "SEA_DIALOG_VERSION");
    } while (0);

    tolua_module(tolua_S, "sea", 0);
    tolua_beginmodule(tolua_S, "sea");

    extendCocos(tolua_S);
    lua_register_sea_global(tolua_S);
    lua_register_sea_bole_label(tolua_S);
    lua_register_sea_bole_button(tolua_S);
    lua_register_sea_bole_button_pay(tolua_S);
    lua_register_sea_bole_progress(tolua_S);
    lua_register_sea_bole_slider(tolua_S);
    lua_register_sea_bole_order(tolua_S);
    lua_register_sea_bole_page_view(tolua_S);
    lua_register_sea_bole_switch(tolua_S);
    lua_register_sea_bole_red_dot(tolua_S);
    
    lua_register_sea_scrollview_bar(tolua_S);
    lua_register_sea_tableViewInterface(tolua_S);

    lua_register_sea_dialogNodeBase(tolua_S);
    lua_register_sea_dialogNodeLabel(tolua_S);
    lua_register_sea_dialogNodeFnt(tolua_S);
    lua_register_sea_dialogNodeButton(tolua_S);
    lua_register_sea_dialogNodeButtonRadio(tolua_S);
    lua_register_sea_dialogNodeButtonEmpty(tolua_S);
    lua_register_sea_dialogNodeButtonMask(tolua_S);
    lua_register_sea_dialogNodeButtonPay(tolua_S);
    lua_register_sea_dialogNodeSprite(tolua_S);
    lua_register_sea_dialogNodeSprite9(tolua_S);
    lua_register_sea_dialogNodeAnimation(tolua_S);
    lua_register_sea_dialogNodeAnimationNew(tolua_S);
    lua_register_sea_dialogNodeList(tolua_S);
    lua_register_sea_dialogNodeProgress(tolua_S);
    lua_register_sea_dialogNodeProgressTimer(tolua_S);
    lua_register_sea_dialogNodeClipping(tolua_S);
    lua_register_sea_dialogNodeScrollView(tolua_S);
    lua_register_sea_dialogNodeEdit(tolua_S);
    lua_register_sea_dialogNodeSlider(tolua_S);
    lua_register_sea_dialogNodeOrder(tolua_S);
    lua_register_sea_dialogNodePageView(tolua_S);
    lua_register_sea_dialogNodeSwitch(tolua_S);
    lua_register_sea_dialogNodeRedDot(tolua_S);
    lua_register_sea_dialogNodeDiy(tolua_S);
    lua_register_sea_dialog(tolua_S);
    
    lua_register_sea_table_view_cell(tolua_S);
    lua_register_sea_table_view_cell_data(tolua_S);
    lua_register_sea_table_view_base(tolua_S);
    lua_register_sea_table_view_dynamic(tolua_S);
    
    lua_register_sea_purchase_manager(tolua_S);

    tolua_endmodule(tolua_S);
    return 1;
}
