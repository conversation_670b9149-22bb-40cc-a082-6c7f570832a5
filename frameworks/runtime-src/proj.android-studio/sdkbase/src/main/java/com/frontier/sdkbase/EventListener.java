package com.frontier.sdkbase;

import org.json.JSONArray;
import org.json.JSONObject;

public interface EventListener {
    void onLogin(int sdkType, int errorCode, String id, String name, String token, String icon, String email, JSONObject json, int param);
    void onLogout(int sdkType, int errorCode, JSONObject json, int param);
    void onPurchase(int sdkType, int errorCode, String productId, String orderId, String payload, String token, JSONObject json, int param);
    void onUndeliveredOrder(int sdkType, int errorCode, String productId, String orderId, String payload, String token, JSONObject json, int param);
    void onQuerySkuDetails(int sdkType, int errorCode, JSONArray data, int param);
    void onGetNotificationSound(String sound, int param);
    void onGameLaunchStatus(boolean status);
    void onGetStorageStats(long cachedBytes, long dataBytes, long appBytes, int param);
    void onCheckNotificationPermission(boolean enabled, int param);
    void onCheckNotificationPermission2(boolean enabled, int param);
    void onRequestNotificationPermission(boolean enabled, int parma);
    void onAdEvent(int sdkType, int eventCode, JSONObject data, int param);
    void onCreateShortCut(boolean enabled, int param);
    void onAddAPMTag(String key, String value);
    void onRemoveAPMTag(String key);
    void onSetUserIdToAPM(String userId);
    void onShareEvent(boolean enabled, String msg, int param);
    void loadSyncThreadAtStartApp(String key);
    void onAudioAdEvent(int sdkType, int eventCode, JSONObject data, int param);
}
