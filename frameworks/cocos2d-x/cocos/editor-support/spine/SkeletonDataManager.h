//
//  SkeletonDataManager.h
//  Spine Json和Atlas资源存储管理类
//
//  说明：由于 json和atlas有属于io操作、占有内存不大的特性，加载完之后不必移除，而是缓存到这里，常驻内存
//       对应spine不用的时候需要删除掉对应texture即可（texture会在加载进度条里添加）
//
//  Created by 孙云高 on 2022/4/27.
//
//

#ifndef __SPINE_SKELETONDATAMANAGER_
#define __SPINE_SKELETONDATAMANAGER_

#include <map>
#include "spine/spine.h"
#include "cocos2d.h"

using namespace std;
using namespace cocos2d;

namespace spine {

#define IS_SKELETON_EXIST(SKELETON_DATA, SKELETON_ATLAS) SkeletonDataManager::getInstance()->isSkeletonExist(SKELETON_DATA, SKELETON_ATLAS)

typedef std::unordered_map<std::string, spSkeletonData*> SkeletonDataMap;
typedef std::unordered_map<std::string, spAtlas*> AtlasMap;

class SkeletonDataManager : public Ref
{
public:
    static SkeletonDataManager * getInstance();
    
    static void destroyInstance();

    spSkeletonData* getSkeletonJsonData(const char* skeletonDataFile, const char* atlasFile, float scale = 1.0f);

    spSkeletonData* getSkeletonJsonData(const char* skeletonDataFile, spAtlas* atlas, float scale = 1.f);
    
    spSkeletonData* getSkeletonBinaryData(const char* skeletonDataFile, const char* atlasFile, float scale = 1.0f);

    spSkeletonData* getSkeletonBinaryData(const char* skeletonDataFile, spAtlas* atlas, float scale = 1.f);
    
    /**
     * 是否缓存里存在该文件
     */
    bool isCacheSkeltonData(const char* skeletonDataFile);
    
    const spAtlas* getAtlasByKey(const char* skeletonKey);

    void purge();
    
    /** 纹理释放
     *
     * @param skeletonKey 缓存池key
     */
    void disposeAtlasTextureByKey(const char* skeletonKey);

    /** 释放所有对应纹理
     *  释放所有skeletonData存储的数据
     */
    void disposeAllSkeletonData();

    void retainAtlasTextureByKey(const char* atlasKey);

    void releaseAtlasTextureByKey(const char* atlasKey);
    
    /** 获取帧事件时间点
     *
     * @param skeletonKey spine缓存数据key（json路径名）
     * @param aniName 动作名称
     * @param eventName 帧事件名称
     * @return 帧事件时间
     */
    float getEventTimeByKey(const std::string& skeletonKey, const std::string& aniName, const std::string& eventName);

    bool isSkeletonExist(const char* skeletonDataFile, const char* atlasFile);

private:
    SkeletonDataManager();
    virtual ~SkeletonDataManager();
    SkeletonDataManager(const SkeletonDataManager &);
    SkeletonDataManager &operator=(const SkeletonDataManager &);
    
    /** 检查spine纹理是否存在
     *
     * @param skeletonDataFile json 文件路径
     * @param atlasFile atlas 文件路径
     * @return
     */
    bool checkAtlasByKey(const char* skeletonDataFile, const char* atlasFile);

    // skeletonData 的存储器
    std::unordered_map<std::string, spSkeletonData*> m_skeletonDataCache;
    // Atlas 的存储器
    std::unordered_map<std::string, spAtlas*> m_atlasCache;
};

}
#endif /* defined(__Spine__SkeletonDataManager__) */
