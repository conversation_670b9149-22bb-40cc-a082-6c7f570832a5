/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/mana/detail/RendererResourceFactorySet.h"

#include "cricocos2d/detail/log.h"


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			namespace
			{
				std::weak_ptr<RendererResourceFactorySet> instance_weak_ptr;
			}


			std::shared_ptr<RendererResourceFactorySet> RendererResourceFactorySet::getInstance()
			{
				auto instance_ptr = instance_weak_ptr.lock();
				if (instance_ptr == nullptr) {
					instance_ptr.reset(new (std::nothrow)RendererResourceFactorySet());
					CC_ASSERT(instance_ptr != nullptr);
					instance_weak_ptr = instance_ptr;
				}
				return instance_ptr;
			}


			RendererResourceFactorySet::~RendererResourceFactorySet()
			{
				if (instance_weak_ptr.expired() == false) {
					instance_weak_ptr.reset();
				}
			}


			std::shared_ptr<RendererResource> RendererResourceFactorySet::createRenderResource(
				const CriManaMovieInfo& movie_info,
				CriManaPlayerHn			mana_player
																							  )
			{
				for (auto& f : factories_) {
					auto renderer_resource = f->createRenderResource(movie_info, mana_player);
					if (renderer_resource != nullptr) {
						return renderer_resource;
					}
				}
				return nullptr;
			}


			void RendererResourceFactorySet::onSuspend()
			{
				for (auto& factory : factories_) {
					factory->onSuspend();
				}
			}


			void RendererResourceFactorySet::onResume()
			{
				for (auto& factory : factories_) {
					factory->onResume();
				}
			}


			void RendererResourceFactorySet::onRendererRecreated()
			{
				for (auto& factory : factories_) {
					factory->onRendererRecreated();
				}
			}


			void RendererResourceFactorySet::registerFactory(std::shared_ptr<RendererResourceFactory> factory)
			{
				factories_.emplace_back(std::move(factory));
			}


		}
	}
}
