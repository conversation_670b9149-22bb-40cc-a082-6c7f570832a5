apply plugin: 'com.android.library'

android {
    namespace "com.cocos2dx.firebase"
    compileSdkVersion 34
//    buildToolsVersion "28.0.3"

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

   sourceSets.main {
        aidl.srcDir "src/main/java"
        java.srcDir  "src/main/java"
        res.srcDir "src/main/res"
        jniLibs.srcDir "libs"
        manifest.srcFile "src/main/AndroidManifest.xml"
    }
}

repositories {
    mavenCentral()
    maven {
        url "https://maven.google.com"
    }
    flatDir {
        dirs 'libs'
    }
    mavenCentral()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':comonprotocol')
    
//    api 'com.google.firebase:firebase-core:17.4.1'
//    api 'com.google.firebase:firebase-messaging:20.1.7'
//    api 'com.google.firebase:firebase-crash:16.2.1'
//    api 'com.google.firebase:firebase-perf:19.0.7'
//    api 'com.google.firebase:firebase-config:19.1.4'
//    api 'com.firebase:firebase-jobdispatcher:0.6.0'

    api platform('com.google.firebase:firebase-bom:31.0.0')
    api 'com.google.firebase:firebase-analytics:21.5.1'
    api 'com.google.firebase:firebase-messaging'
    api 'com.google.firebase:firebase-config'
    implementation 'com.google.firebase:firebase-perf'
}