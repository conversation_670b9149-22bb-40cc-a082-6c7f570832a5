//
//  Database.h
//  cocos2d_libs
//
//  Created by wodes on 14-11-4.
//
//

#ifndef __cocos2d_libs__Database__
#define __cocos2d_libs__Database__

#include <stdio.h>


#include "cocos2d.h"
#include "sqlite3.h"


NS_CC_BEGIN

namespace network {
    
	class CC_DLL Database : public cocos2d::Ref{
    
private:
    static int sqliteOpen(sqlite3 **db, const char * dbName);
public:
    static void sqliteCreateTable(const char * dbName);
    static const char * sqliteGetValueForKey(const char * key, const char * dbName);
    static void sqliteSetValueForKey(const char * key,const char * value, const char * dbName);
    static void sqliteUpdateValueForKey(const char * key,const char * value, const char * dbName);
};
}
NS_CC_END

#endif /* defined(__cocos2d_libs__Database__) */