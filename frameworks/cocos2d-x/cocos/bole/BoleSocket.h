/****************************************************************************
 Copyright (c) 2010-2012 cocos2d-x.org
 Copyright (c) 2013-2014 Chukong Technologies Inc.

 http://www.cocos2d-x.org

 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.

"[WebSocket module] is based in part on the work of the libwebsockets  project
(http://libwebsockets.org)"

 ****************************************************************************/

#ifndef __BLSOCKET_H__
#define __BLSOCKET_H__


#include "../platform/CCPlatformMacros.h"
#include "../platform/CCStdC.h"
#include "cocos2d.h"
#include "../base/CCValue.h"
#include "scripting/lua-bindings/manual/CCLuaValue.h"

#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32 ||  CC_TARGET_PLATFORM == CC_PLATFORM_WP8)
#include <winsock2.h>
#else
typedef int SOCKET;
#endif

NS_CC_BEGIN

namespace network {

    
//    enum WS_MSG {
//        WS_MSG_TO_SUBTRHEAD_SENDING_STRING = 0,
//        WS_MSG_TO_SUBTRHEAD_SENDING_BINARY,
//        WS_MSG_TO_UITHREAD_OPEN,
//        WS_MSG_TO_UITHREAD_MESSAGE,
//        WS_MSG_TO_UITHREAD_ERROR,
//        WS_MSG_TO_UITHREAD_CLOSE,
//        WS_MSG_TO_UITHREAD_DOWNLOAD
//    };
//    
    
    
class BLThreadHelper;
    
class CC_DLL BLSocket :public cocos2d::Ref
{
public:
    /**
     * @js ctor
     */
    BLSocket();
    /**
     * @js NA
     * @lua NA
     */
    virtual ~BLSocket();

    /**
     *  @brief Data structure for message
     */
    struct Data
    {
        Data():bytes(nullptr), len(0), issued(0), isBinary(false){}
        char* bytes;
        ssize_t len, issued;
        bool isBinary;
    };

    /**
     *  @brief Errors in websocket
     */
    enum class ErrorCode
    {
        TIME_OUT,
        CONNECTION_FAILURE,
        UNKNOWN,
    };

    /**
     *  Websocket state
     */
    enum class State
    {
        CONNECTING,
        OPEN,
        CLOSING,
        CLOSED,
    };

	enum SocketReason
	{
		SOCKET_REASON_ERROR,
		SOCKET_REASON_OPEN,
		SOCKET_REASON_SEND,
		SOCKET_REASON_CLOSE,
		SOCKET_REASON_RECV,
	};

    /**
     *  @brief The delegate class to process websocket events.
     */
    class BLDelegate : public Ref
    {
    public:
        virtual ~BLDelegate() {};
        virtual void onOpen(BLSocket* ws) = 0;
        virtual void onMessage(BLSocket* ws, const void* data) = 0;
        virtual void onClose(BLSocket* ws) = 0;
        virtual void onError(BLSocket* ws, const ErrorCode& error) = 0;
    };

    static BLSocket* getInstance();
    static void destroyInstance();

    /**
     *  @brief  The initialized method for websocket.
     *          It needs to be invoked right after websocket instance is allocated.
     *  @param  delegate The delegate which want to receive event from websocket.
     *  @param  url      The URL of websocket server.
     *  @return true: Success, false: Failure
     */
    bool init(const std::string& url,
              const int portNumber = 80);
    void addHost(const std::string &url, const int port);
    bool getCurrentHost(std::string &url, int &port);
    void removeHost(const std::string &url, const int port);
    void setConnectionTimeout(int seconds);
    void addDelegate(BLDelegate* dele);
    void removeDelegate(BLDelegate* dele);
    void removeAllDelegate();
    void sendString(const std::string& message);
    void sendInt(int value);
    void sendTable(Value &obj);
    void doUpdate();
    void setThreadStartedOld(bool isOld);
    bool getThreadStartedOld();

    /**
     *  @brief Closes the connection to server.
     */
    void closeSkt();
    
    /**
     *  @brief quitSubThread.
     */
    void quitSubThread();

    /**
     *  @brief Gets current state of connection.
     */
    State getReadyState();

    int getFDCount();

private:
//    void sendMsg(int value);
//	void sendMsg(long long value);
//    void sendMsg(const std::string& message);
//    void sendMsg(const unsigned char* binaryMsg, unsigned int len);
//	void sendMsg(int *value,int length);
	void sendMsg(Value &pObject);
    
    virtual void onSubThreadStartedOld();
    virtual void onSubThreadStarted();
    virtual int onSubThreadLoop();
    virtual void onSubThreadEnded();
    virtual void onUIThreadReceiveMessage(BLMessage* msg);

    friend class BLSocketCallbackWrapper;
    int onSocketCallback(SocketReason reason, void *data, ssize_t len);
    void onNetworkState(BL_WS_MSG state);
    
    void getLuaValueDACount(const LuaValue& value);
    void getLuaValueDictCount(const LuaValueDict& dict);
    void getLuaValueArrayCount(const LuaValueArray& array);
    
private:
    State        _readyState;
//    std::string  _host;
//    unsigned int _port;

    ssize_t _pendingFrameDataLen;
    ssize_t _currentDataLen;
	//ssize_t _socketDataLen;
    char *_currentData;

    friend class BLThreadHelper;
    BLThreadHelper* _wsHelper;
	SOCKET _sktContext;
    __Array *_delegateList;
    static BLSocket* instance;
    int _heart;
    
    std::list<std::pair<std::string, int> > alternativeHosts; // 这里存储多个host，当一个host连接不上时，就会切换到其他host
    int _currHostIndex;
    int _connectionTimeout;
    EventCustom* _recvEvent;
    int _luaValueDACount;
    
    char* recv_buffer;
    int idleCount;
    long reconnect;
    bool _isThreadStartedOld;
};

}

NS_CC_END

#endif /* defined(__CC_JSB_WEBSOCKET_H__) */
