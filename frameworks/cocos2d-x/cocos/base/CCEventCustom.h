/****************************************************************************
 Copyright (c) 2013-2017 Chukong Technologies Inc.
 
 http://www.cocos2d-x.org
 
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:
 
 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.
 
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
 ****************************************************************************/

#ifndef __cocos2d_libs__CCCustomEvent__
#define __cocos2d_libs__CCCustomEvent__

#include <string>
#include "base/CCEvent.h"

/**
 * @addtogroup base
 * @{
 */

NS_CC_BEGIN

/** @class EventCustom
 * @brief Custom event.
 */
class CC_DLL EventCustom : public Event
{
public:
    /** Constructor.
     *
     * @param eventName A given name of the custom event.
     * @js ctor
     */
    EventCustom(const std::string& eventName);
    
    /** Sets user data.
     *
     * @param data The user data pointer, it's a void*.
     */
    void setUserData(void* data) { _userData = data; }
    
    /** Gets user data.
     *
     * @return The user data pointer, it's a void*.
     */
    void* getUserData() const { return _userData; }
    
    /** 设置一个字符串类型（方便lua调用c++）[2022/5/26 by sunyungao]
     *
     * @param data std::string.
     */
    void setUserDataString(std::string data) { _userDataStr = data; }
    
    /** 返回一个字符串类型（方便lua调用c++）[2022/5/26 by sunyungao]
     *
     * @return std::string*.
     */
    const std::string& getUserDataString() const { return _userDataStr; }
    
    /** 设置一个整数类型（方便lua调用c++）[2022/5/26 by sunyungao]
     *
     * @param data std::string.
     */
    void setUserDataInt(int data) { _userDataInt = data; }
    
    /** 返回一个整数类型（方便lua调用c++）[2022/5/26 by sunyungao]
     *
     * @return std::string*.
     */
    int getUserDataInt() { return _userDataInt; }
    
    /** Gets event name.
     *
     * @return The name of the event.
     */
    const std::string& getEventName() const { return _eventName; }
protected:
    void* _userData;       ///< User data
    std::string _eventName;
    std::string _userDataStr;
    int         _userDataInt;
};

NS_CC_END

// end of base group
/// @}

#endif /* defined(__cocos2d_libs__CCCustomEvent__) */
