/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/* Prevention of redefinition */
#ifndef CRICOCOS2D_MANA_DETAIL_RENDERERRESOURCEFACTORYSET_H
#define CRICOCOS2D_MANA_DETAIL_RENDERERRESOURCEFACTORYSET_H


#include "cricocos2d/mana/detail/RendererResourceFactory.h"

#include <list>


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			class RendererResourceFactorySet
				: public RendererResourceFactory
			{
			private:
				RendererResourceFactorySet() = default;


			public:
				static std::shared_ptr<RendererResourceFactorySet> getInstance();

				~RendererResourceFactorySet();

				std::shared_ptr<RendererResource> createRenderResource(
					const CriManaMovieInfo& movie_info,
					CriManaPlayerHn			mana_player
																	  ) override;
				void onSuspend() override;
				void onResume() override;
				void onRendererRecreated() override;

				void registerFactory(std::shared_ptr<RendererResourceFactory> factory);


			private:
				std::list<std::shared_ptr<RendererResourceFactory>> factories_;
			};
		}
	}
}


#endif
