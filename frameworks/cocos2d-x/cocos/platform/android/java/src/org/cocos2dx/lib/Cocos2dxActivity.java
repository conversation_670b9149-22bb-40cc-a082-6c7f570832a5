/****************************************************************************
Copyright (c) 2010-2013 cocos2d-x.org

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
 ****************************************************************************/
package org.cocos2dx.lib;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.PixelFormat;
import android.media.AudioManager;
import android.opengl.GLSurfaceView;
import android.os.Build;
import android.os.Bundle;
import android.os.Looper;
import android.os.Message;
import android.os.MessageQueue;
import android.preference.PreferenceManager.OnActivityResultListener;
import android.util.Log;
import android.view.ViewGroup;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import org.cocos2dx.lib.Cocos2dxHelper.Cocos2dxHelperListener;

import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.Random;
import java.util.concurrent.ConcurrentLinkedQueue;

import javax.microedition.khronos.egl.EGL10;
import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.egl.EGLDisplay;


public abstract class Cocos2dxActivity extends Activity implements Cocos2dxHelperListener {
    // ===========================================================
    // Constants
    // ===========================================================

    private final static String TAG = Cocos2dxActivity.class.getSimpleName();

    // ===========================================================
    // Fields
    // ===========================================================
    
    public Cocos2dxGLSurfaceView mGLSurfaceView = null;
    private int[] mGLContextAttrs = null;
    private Cocos2dxHandler mHandler = null;   
    private static Cocos2dxActivity sContext = null;
    private Cocos2dxVideoHelper mVideoHelper = null;
    private Cocos2dxWebViewHelper mWebViewHelper = null;
    private Cocos2dxEditBoxHelper mEditBoxHelper = null;
    private boolean hasFocus = false;

    private int screenWidth;
    private int screenHeight;

    public Cocos2dxGLSurfaceView getGLSurfaceView(){
        return  mGLSurfaceView;
    }

    public class Cocos2dxEGLConfigChooser implements GLSurfaceView.EGLConfigChooser
    {
        protected int[] configAttribs;
        public Cocos2dxEGLConfigChooser(int redSize, int greenSize, int blueSize, int alphaSize, int depthSize, int stencilSize)
        {
            configAttribs = new int[] {redSize, greenSize, blueSize, alphaSize, depthSize, stencilSize};
        }
        public Cocos2dxEGLConfigChooser(int[] attribs)
        {
            configAttribs = attribs;
        }

        private int findConfigAttrib(EGL10 egl, EGLDisplay display,
                EGLConfig config, int attribute, int defaultValue) {
            int[] value = new int[1];
            if (egl.eglGetConfigAttrib(display, config, attribute, value)) {
                return value[0];
            }
            return defaultValue;
        }

        class ConfigValue implements Comparable<ConfigValue> {

            public EGLConfig config = null;
            public int[] configAttribs = null;
            public int value = 0;
            private void calcValue() {
                // depth factor 29bit and [6,12)bit
                if (configAttribs[4] > 0) {
                    value = value + (1 << 29) + ((configAttribs[4]%64) << 6);
                }
                // stencil factor 28bit and [0, 6)bit
                if (configAttribs[5] > 0) {
                    value = value + (1 << 28) + ((configAttribs[5]%64));
                }
                // alpha factor 30bit and [24, 28)bit
                if (configAttribs[3] > 0) {
                    value = value + (1 << 30) + ((configAttribs[3]%16) << 24);
                }
                // green factor [20, 24)bit
                if (configAttribs[1] > 0) {
                    value = value + ((configAttribs[1]%16) << 20);
                }
                // blue factor [16, 20)bit
                if (configAttribs[2] > 0) {
                    value = value + ((configAttribs[2]%16) << 16);
                }
                // red factor [12, 16)bit
                if (configAttribs[0] > 0) {
                    value = value + ((configAttribs[0]%16) << 12);
                }
            }

            public ConfigValue(int[] attribs) {
                configAttribs = attribs;
                calcValue();
            }

            public ConfigValue(EGL10 egl, EGLDisplay display, EGLConfig config) {
                this.config = config;
                configAttribs = new int[6];
                configAttribs[0] = findConfigAttrib(egl, display, config, EGL10.EGL_RED_SIZE, 0);
                configAttribs[1] = findConfigAttrib(egl, display, config, EGL10.EGL_GREEN_SIZE, 0);
                configAttribs[2] = findConfigAttrib(egl, display, config, EGL10.EGL_BLUE_SIZE, 0);
                configAttribs[3] = findConfigAttrib(egl, display, config, EGL10.EGL_ALPHA_SIZE, 0);
                configAttribs[4] = findConfigAttrib(egl, display, config, EGL10.EGL_DEPTH_SIZE, 0);
                configAttribs[5] = findConfigAttrib(egl, display, config, EGL10.EGL_STENCIL_SIZE, 0);
                calcValue();
            }

            @Override
            public int compareTo(ConfigValue another) {
                if (value < another.value) {
                    return -1;
                } else if (value > another.value) {
                    return 1;
                } else {
                    return 0;
                }
            }

            @Override
            public String toString() {
                return "{ color: " + configAttribs[3] + configAttribs[2] + configAttribs[1] + configAttribs[0] +
                        "; depth: " + configAttribs[4] + "; stencil: " + configAttribs[5] + ";}";
            }
        }

        @Override
        public EGLConfig chooseConfig(EGL10 egl, EGLDisplay display) 
        {
            int[] EGLattribs = {
                    EGL10.EGL_RED_SIZE, configAttribs[0],
                    EGL10.EGL_GREEN_SIZE, configAttribs[1],
                    EGL10.EGL_BLUE_SIZE, configAttribs[2],
                    EGL10.EGL_ALPHA_SIZE, configAttribs[3],
                    EGL10.EGL_DEPTH_SIZE, configAttribs[4],
                    EGL10.EGL_STENCIL_SIZE,configAttribs[5],
                    EGL10.EGL_RENDERABLE_TYPE, 4, //EGL_OPENGL_ES2_BIT
                    EGL10.EGL_NONE
            };
            EGLConfig[] configs = new EGLConfig[1];
            int[] numConfigs = new int[1];
            boolean eglChooseResult = egl.eglChooseConfig(display, EGLattribs, configs, 1, numConfigs);
            if (eglChooseResult && numConfigs[0] > 0)
            {
                return configs[0];
            }

            // there's no config match the specific configAttribs, we should choose a closest one
            int[] EGLV2attribs = {
                    EGL10.EGL_RENDERABLE_TYPE, 4, //EGL_OPENGL_ES2_BIT
                    EGL10.EGL_NONE
            };
            eglChooseResult = egl.eglChooseConfig(display, EGLV2attribs, null, 0, numConfigs);
            if(eglChooseResult && numConfigs[0] > 0) {
                int num = numConfigs[0];
                ConfigValue[] cfgVals = new ConfigValue[num];

                // convert all config to ConfigValue
                configs = new EGLConfig[num];
                egl.eglChooseConfig(display, EGLV2attribs, configs, num, numConfigs);
                for (int i = 0; i < num; ++i) {
                    cfgVals[i] = new ConfigValue(egl, display, configs[i]);
                }

                ConfigValue e = new ConfigValue(configAttribs);
                // bin search
                int lo = 0;
                int hi = num;
                int mi;
                while (lo < hi - 1) {
                    mi = (lo + hi) / 2;
                    if (e.compareTo(cfgVals[mi]) < 0) {
                        hi = mi;
                    } else {
                        lo = mi;
                    }
                }
                if (lo != num - 1) {
                    lo = lo + 1;
                }
                Log.w("cocos2d", "Can't find EGLConfig match: " + e + ", instead of closest one:" + cfgVals[lo]);
                return cfgVals[lo].config;
            }

            Log.e(DEVICE_POLICY_SERVICE, "Can not select an EGLConfig for rendering.");
            return null;
        }

    }
    
    public static Context getContext() {
        return sContext;
    }
    
    public void setKeepScreenOn(boolean value) {
        final boolean newValue = value;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mGLSurfaceView.setKeepScreenOn(newValue);
            }
        });
    }
    
    protected void onLoadNativeLibraries() {
        // 加载so库比较耗时，改成异步操作，加载完成后才会执行创建GLSurfaceView，以及lua初始化，然后才能调用lua代码
        Thread loadCocos2dLuaThread = new Thread(new Runnable() {
            @Override
            public void run() {
                // Log.d("scene", "onLoadNativeLibraries begin...1");
                try {
                    ApplicationInfo ai = getPackageManager().getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
                    Bundle bundle = ai.metaData;
                    String libName = bundle.getString("android.app.lib_name");
                    System.loadLibrary(libName);
                    Cocos2dxHelper.setIsLibLoadSuccess(true);
                } catch (UnsatisfiedLinkError e) {
                    Log.e(TAG, "onLoadNativeLibraries error:" + e.getMessage());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // Log.d("scene", "onLoadNativeLibraries end...1"); // 160 ms
                sContext.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        // afterLoadNativeLibs();
                        Cocos2dxHelper.loadSyncThreadAtStartApp("so");
                    }
                });
            }
        });
        loadCocos2dLuaThread.start();
        loadCocos2dLuaThread.setName("loadCocos2dLuaLibrary");
    }


    
    // ===========================================================
    // Constructors
    // ===========================================================

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState); // MARK: 27
        // Workaround in https://stackoverflow.com/questions/16283079/re-launch-of-activity-on-home-button-but-only-the-first-time/16447508
        if (!isTaskRoot()) {
            // Android launched another instance of the root activity into an existing task
            //  so just quietly finish and go away, dropping the user back into the activity
            //  at the top of the stack (ie: the last state of this task)
            finish();
            Log.w(TAG, "[Workaround] Ignore the activity started from icon!");
            return;
        }
        if (Cocos2dxHelper.isLowSDStorage()){
            // Cocos2dxHelper.killAppProcess(this);
            Cocos2dxHelper.showAlertDialog(this, 1);
            return;
        }
        // this.hideVirtualButton(); // 由OnResume执行耗时处理了
        optimizeSpTask();
        sContext = this;
        onLoadNativeLibraries();
        Log.i(TAG,"onCreate 1");
        this.mHandler = new Cocos2dxHandler(this);
        Cocos2dxHelper.init(this);
        this.mGLContextAttrs = new int[]{8, 8, 8, 8, 24, 8, 0}; // getGLContextAttrs();getGLContextAttrs();
        this.init();
        if (mVideoHelper == null) {
            mVideoHelper = new Cocos2dxVideoHelper(this, mFrameLayout);
        }
        if(mWebViewHelper == null){
            mWebViewHelper = new Cocos2dxWebViewHelper(mFrameLayout);
        }
        if(mEditBoxHelper == null){
            mEditBoxHelper = new Cocos2dxEditBoxHelper(mFrameLayout);
        }
        this.setVolumeControlStream(AudioManager.STREAM_MUSIC);
    }

    // 加载完so库之后的操作
    protected void afterLoadNativeLibs() {
        Log.d(TAG, "afterLoadNativeLibs");
        if (!Cocos2dxHelper.getIsLibLoadSuccess()){ // libcocos2dlua.so 库加载不上的时候，弹窗且停掉游戏
            Cocos2dxHelper.showAlertDialog(this, 2);
            return;
        }
        Cocos2dxHelper.initNativeMethods();
        // Cocos2dxEditText layout
        ViewGroup.LayoutParams edittext_layout_params =
                new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT);
        Cocos2dxEditBox edittext = new Cocos2dxEditBox(this);
        edittext.setLayoutParams(edittext_layout_params);
        mFrameLayout.addView(edittext);
        edittext.setVisibility(View.GONE);

        // Cocos2dxGLSurfaceView
        this.mGLSurfaceView = this.onCreateView();
        this.mGLSurfaceView.setPreserveEGLContextOnPause(true);
        // ...add to FrameLayout
        mFrameLayout.addView(this.mGLSurfaceView);

        // Switch to supported OpenGL (ARGB888) mode on emulator
        // this line dows not needed on new emulators and also it breaks stencil buffer
        //if (isAndroidEmulator())
        //   this.mGLSurfaceView.setEGLConfigChooser(8, 8, 8, 8, 16, 0);
        this.mGLSurfaceView.setCocos2dxRenderer(new Cocos2dxRenderer());
        this.mGLSurfaceView.setCocos2dxEditText(edittext);
        if (mIsResumeGLSurfaceView){
            this.mGLSurfaceView.onResume();
            mIsResumeGLSurfaceView = false;
        }

        Window window = this.getWindow();
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN);
    }

    public void init() {
        // FrameLayout
        ViewGroup.LayoutParams framelayout_params =
                new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT);

        mFrameLayout = new ResizeLayout(this);
        mFrameLayout.setLayoutParams(framelayout_params);

        // Set framelayout as the content view
        setContentView(mFrameLayout);
    }

    public void confirmANR(){}
    public void stopANRMonitor(){}
    public void printRasterMessage() {}
    public ArrayList<String> getANRPastString() { return null; }
    public String getANRCurString() { return ""; }
    public ArrayList<String> getANRPendString() { return null; }

    public void googleCMPStart(){}

    public void googleCMPOptions(){}

    public boolean googleCMPCanRequestAds(){return true;}

    public boolean isGoogleCMPPrivacyOptionsRequired(){return false;}

    //native method,call GLViewImpl::getGLContextAttrs() to get the OpenGL ES context attributions
    private static native int[] getGLContextAttrs();
    
    // ===========================================================
    // Getter & Setter
    // ===========================================================

    // ===========================================================
    // Methods for/from SuperClass/Interfaces
    // ===========================================================

    @Override
    protected void onResume() {
    	Log.d(TAG, "scene onResume()");
        super.onResume(); // MARK: 58
        // this.hideVirtualButton();
        Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                Cocos2dxHelper.hideVirtualButton(); 
                return false;
            }
        });

       	resumeIfHasFocus();
        if (mGLSurfaceView != null){
            Cocos2dxHelper.resumeGLViewRunnableList();
        }
    }
    
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
    	Log.d(TAG, "scene onWindowFocusChanged() hasFocus=" + hasFocus);
        super.onWindowFocusChanged(hasFocus);
        this.hasFocus = hasFocus;
        resumeIfHasFocus();
    }

    private boolean mIsResumeGLSurfaceView = false;
    
    private void resumeIfHasFocus() {
        if(hasFocus) {
            Log.d(TAG, "scene resumeIfHasFocus()");
//            this.hideVirtualButton();
            Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
                @Override
                public boolean queueIdle() {
                    Cocos2dxHelper.hideVirtualButton();
                    return false;
                }
            });
        	Cocos2dxHelper.onResume();
            if (mGLSurfaceView != null) {
                mGLSurfaceView.onResume();
            }else {
                mIsResumeGLSurfaceView = true;
            }
        }
    }

    @Override
    protected void onPause() {
    	Log.d(TAG, "scene onPause()");
        super.onPause();
        Cocos2dxHelper.onPause();
        if (mGLSurfaceView != null) {
            mGLSurfaceView.onPause();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void showDialog(final String pTitle, final String pMessage) {
        Message msg = new Message();
        msg.what = Cocos2dxHandler.HANDLER_SHOW_DIALOG;
        msg.obj = new Cocos2dxHandler.DialogMessage(pTitle, pMessage);
        this.mHandler.sendMessage(msg);
    }

    @Override
    public void runOnGLThread(final GLTaskMonitor pRunnable) {
//        try{
//            this.mGLSurfaceView.queueEvent(pRunnable);
//        }catch(Exception ignored) {
//            Log.e(TAG, "runOnGLThread" + ignored.getMessage());
//            String dataStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + " " + ignored.getMessage();
//            Cocos2dxHelper.addGLViewRunnableList(dataStr, pRunnable);
//        }

        if (0 < Cocos2dxHelper.getGLRunnableCountPerFrame()){
            Cocos2dxHelper.addPrepareGLRunnableList(pRunnable);
        }else{
            try{ // 如果lua里某一帧设置 getGLRunnableCountPerFrame 为0，会导致消息顺序不安全（先上一版，如果没有问题的话以后就禁止为0了) [2023/08/17 by sunyungao]
                if (this.mGLSurfaceView != null) {
                    this.mGLSurfaceView.queueEvent(pRunnable);
                }
            }catch(Exception exception) {
                Log.e(TAG, "runOnGLThread" + exception.getMessage());
                String dataStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + " " + exception.getMessage();
                Cocos2dxHelper.addGLViewRunnableList(dataStr, pRunnable);
            }
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data)
    {
        for (OnActivityResultListener listener : Cocos2dxHelper.getOnActivityResultListeners()) {
            listener.onActivityResult(requestCode, resultCode, data);
        }

        super.onActivityResult(requestCode, resultCode, data);
    }

    protected ResizeLayout mFrameLayout = null;
    // ===========================================================
    // Methods
    // ===========================================================

    private Thread mDetectThread = null; // 检测cpu的线程
    public void startDetectCPUThread(){
        if (mDetectThread != null){
            return;
        }

        //////////////////创建x个线程////////////////
        mDetectThread = new Thread(new Runnable() {
            @Override
            public void run() {
                //这里写入子线程需要做的工作
                Thread[] caluThreadArr = new Thread[4];
                for (int i = 0; i < 4; i++) {
                    caluThreadArr[i] = new Thread(new Runnable() {
                        @Override
                        public void run() {
                            boolean isC = false;
                            while (true) {
                                if (isC){
                                    createCaluThreadMethod();
                                    isC = false;
                                }else{
                                    try {
                                        Thread.sleep(1);
                                    } catch (InterruptedException e) {
                                        isC = true;
                                    }
                                }
                            }
                        }
                    });
                    caluThreadArr[i].start();
                    caluThreadArr[i].setName("caluThread_" + (i + 1));
                }

                int index = 0;
                while (true){
                    if (getGLSurfaceView() != null && getGLSurfaceView().getCocos2dxRendererThread() != null){
                        Thread.State s = getGLSurfaceView().getCocos2dxRendererThread().getState();
                        if ((s == Thread.State.WAITING || s == Thread.State.TIMED_WAITING)){
                            index++;
                            if (index < 3) {
                                try {
                                    Thread.sleep(1);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                            }else{
                                index = 3;
                                for (int i = 0; i < 4; i++) {
                                    caluThreadArr[i].interrupt();
                                }

                                try {
                                    Thread.sleep(1);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                            }
                        } else{
                            index = 0;
                            try {
                                Thread.sleep(1);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    } else{
                        index = 0;
                        try {
                            Thread.sleep(1);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        });
        mDetectThread.start(); //启动线程
        mDetectThread.setName("detectThread");
    }

    private synchronized void createCaluThreadMethod(){
        int ARRAY_SIZE = 10000;

        long startTime = System.nanoTime();
        Random rand = new Random();
        double avg = 0;
        for (int i = 0; i < ARRAY_SIZE; i++) {
            avg = avg + rand.nextDouble() / ARRAY_SIZE;
            if ((System.nanoTime() - startTime) > 1000000.0){
                break;
            }
        }

//        System.out.println("jinle 程序运行时间：" + ((System.nanoTime() - startTime) / 1000000.0) + " 毫秒");
    }

    private FrameLayout.LayoutParams calcMarginLayoutParams(int width, int height) {
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        if(width == 0 || height == 0) {
            return layoutParams;
        }
        float maxRatio = 2.55f;
        if(width > height) {
            if(width/height > maxRatio) {
                layoutParams.leftMargin = (int)((width - height * maxRatio) / 2.0);
                layoutParams.rightMargin = layoutParams.leftMargin;
            }
        } else {
            if(height/width > maxRatio) {
                layoutParams.topMargin = (int)((height - width *maxRatio) / 2.0);
                layoutParams.bottomMargin = layoutParams.topMargin;
            }
        }
        Log.d("CreateGLSurfaceView", "width="+width+", height="+height+", leftMargin="+layoutParams.leftMargin+", rightMargin="+layoutParams.rightMargin+", topMargin="+layoutParams.topMargin+", bottomMargin="+layoutParams.bottomMargin);
        return layoutParams;
    }

    public void resizeGLSurfaceView(int width, int height) {
        if(screenWidth == width && screenHeight == height) {
            return;
        }
        screenWidth = width;
        screenHeight = height;
        FrameLayout.LayoutParams surfaceViewLayout = calcMarginLayoutParams(screenWidth, screenHeight);
        if (mGLSurfaceView != null) {
            mGLSurfaceView.setLayoutParams(surfaceViewLayout);
        }
    }
    
    public Cocos2dxGLSurfaceView onCreateView() {
        Log.i("Bing", TAG + " :onCreateView");
        Cocos2dxGLSurfaceView glSurfaceView = new Cocos2dxGLSurfaceView(this);
        //this line is need on some device if we specify an alpha bits
        if(this.mGLContextAttrs[3] > 0) glSurfaceView.getHolder().setFormat(PixelFormat.TRANSLUCENT);

        Cocos2dxEGLConfigChooser chooser = new Cocos2dxEGLConfigChooser(this.mGLContextAttrs);
        glSurfaceView.setEGLConfigChooser(chooser);
        glSurfaceView.setPreserveEGLContextOnPause(true);

        return glSurfaceView;
    }

    protected void hideVirtualButton() {

        // if (Build.VERSION.SDK_INT >= 19) 
        {
            // use reflection to remove dependence of API level

            Class viewClass = View.class;

            try {
                final int SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION");
                final int SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN");
                final int SYSTEM_UI_FLAG_HIDE_NAVIGATION = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_HIDE_NAVIGATION");
                final int SYSTEM_UI_FLAG_FULLSCREEN = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_FULLSCREEN");
                final int SYSTEM_UI_FLAG_IMMERSIVE_STICKY = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_IMMERSIVE_STICKY");
                final int SYSTEM_UI_FLAG_LAYOUT_STABLE = Cocos2dxReflectionHelper.<Integer>getConstantValue(viewClass, "SYSTEM_UI_FLAG_LAYOUT_STABLE");

                // getWindow().getDecorView().setSystemUiVisibility();
                final Object[] parameters = new Object[]{SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
                        | SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
                        | SYSTEM_UI_FLAG_IMMERSIVE_STICKY};

                Cocos2dxReflectionHelper.<Void>invokeInstanceMethod(getWindow().getDecorView(),
                        "setSystemUiVisibility",
                        new Class[]{Integer.TYPE},
                        parameters);  // MARK: 39
            } catch (NullPointerException e) {
                Log.e(TAG, "hideVirtualButton", e);
            }
        }
    }

   private final static boolean isAndroidEmulator() {
      String model = Build.MODEL;
      Log.d(TAG, "model=" + model);
      String product = Build.PRODUCT;
      Log.d(TAG, "product=" + product);
      boolean isEmulator = false;
      if (product != null) {
         isEmulator = product.equals("sdk") || product.contains("_sdk") || product.contains("sdk_");
      }
      Log.d(TAG, "isEmulator=" + isEmulator);
      return isEmulator;
   }

    // ===========================================================
    // Inner and Anonymous Classes
    // ===========================================================

    // 动态代理 android.app.QueuedWork 的队列，使poll不被阻塞
    // sharedPreference 会在ActivityThread退出等3种情况时，使ui线程等待。会导致ANR
    // [sunyungao 23/09/08]
    private void optimizeSpTask() {
        if (Build.VERSION.SDK_INT < 26) {
            reflectSPendingWorkFinishers();
        } else {
            reflectSFinishers();
        }
    }

    /**
     * 8.0以上 Reflect finishers
     *
     */
    private void reflectSFinishers(){
        LinkedList<Runnable> sFinishers = null;
        Field field = null;
        try {
            Class<?> atClass = Class.forName("android.app.QueuedWork");
            field = atClass.getDeclaredField("sFinishers");
            field.setAccessible(true);
            sFinishers = (LinkedList<Runnable>)field.get(null);
            if (sFinishers != null) {
                field.set(null, new LinkedListProxy<Runnable>(sFinishers));
                Log.d(TAG, "up android 8,reflectSFinishers success.");
            }
        } catch (Exception e) {
            try {
                if (sFinishers != null && field != null) {
                    field.set(null, sFinishers);
                }
            } catch (Exception ex) {
                Log.e(TAG, "hook error" + ex.getMessage());
            }
            Log.e(TAG, "up android 8, hook sPendingWorkFinishers fail.", e);
        }
    }

    /**
     * 8.0以下 Reflect pending work finishers
     */
    public static void reflectSPendingWorkFinishers() {
        ConcurrentLinkedQueue<Runnable> sPendingWorkFinishers = null;
        Field field = null;
        try {
            Class<?> atClass = Class.forName("android.app.QueuedWork");
            field = atClass.getDeclaredField("sPendingWorkFinishers");
            field.setAccessible(true);
            sPendingWorkFinishers = (ConcurrentLinkedQueue<Runnable>) field.get(null);
            if (sPendingWorkFinishers != null) {
                field.set(null, new ConcurrentLinkedQueueProxy<Runnable>(sPendingWorkFinishers));
                Log.d(TAG, "Below android 8,reflectSPendingWorkFinishers success.");
            }
        } catch (Exception e) {
            // 出现异常
            try {
                if (sPendingWorkFinishers != null && field != null) {
                    field.set(null, sPendingWorkFinishers);
                }
            } catch (Exception ex) {
                Log.e(TAG, "hook error" + ex.getMessage());
            }
            Log.e(TAG, "Below android 0,,hook sPendingWorkFinishers fail.", e);
        }
    }


    /**
     * 在8.0以上apply()中QueuedWork.addFinisher(awaitCommit), 需要代理的是LinkedList，如下：
     * # private static final LinkedList<Runnable> sFinishers = new LinkedList<>()
     */
    private static class LinkedListProxy<E> extends LinkedList<E> {
        private static final String TAG = "LinkedListProxy";
        public LinkedList<Runnable> workFinishers;
        public LinkedListProxy(LinkedList<Runnable> c) {
            this.workFinishers = c;
        }

        @Override
        public boolean add(E e) {
            return workFinishers.add((Runnable) e);
        }

        @Override
        public boolean remove(Object e) {
            return workFinishers.remove(e);
        }

        @Override
        public E poll() {
            // 代理的poll()方法，永远返回空，这样UI线程就可以避免被阻塞，继续执行了
            return null;
        }

        @Override
        public boolean isEmpty() {
            return true;
        }

        private void printStack() {
            for (StackTraceElement stackTraceElement : Thread.currentThread().getStackTrace()) {
                Log.d("cTAG", stackTraceElement.getClassName() + "" + stackTraceElement.getMethodName() + "(" + stackTraceElement.getLineNumber() + ")");
            }
        }
    }

    /**
     * 在8.0以下代理
     * // The set of Runnables that will finish or wait on any async activities started by the application.
     * private static final ConcurrentLinkedQueue<Runnable> sPendingWorkFinishers = new ConcurrentLinkedQueue<Runnable>();
     */
    private static class ConcurrentLinkedQueueProxy<E> extends ConcurrentLinkedQueue<E> {
        private static final String TAG = "ConcurrentLinkedQueueProxy";
        public ConcurrentLinkedQueue<Runnable> workFinishers;
        public ConcurrentLinkedQueueProxy(ConcurrentLinkedQueue<Runnable> c) {
            this.workFinishers = c;
        }

        @Override
        public boolean add(E e) {
            return workFinishers.add((Runnable) e);
        }

        @Override
        public boolean remove(Object e) {
            return workFinishers.remove(e);
        }

        @Override
        public E poll() {
            // 代理的poll()方法，永远返回空，这样UI线程就可以避免被阻塞，继续执行了
            return null;
        }

        @Override
        public boolean isEmpty() {
            return true;
        }

        private void printStack() {
            for (StackTraceElement stackTraceElement : Thread.currentThread().getStackTrace()) {
                Log.d("cTAG", stackTraceElement.getClassName() + "" + stackTraceElement.getMethodName() + "(" + stackTraceElement.getLineNumber() + ")");
            }
        }
    }

    public int getLaunchPath(){
        return -1;
    }
}
