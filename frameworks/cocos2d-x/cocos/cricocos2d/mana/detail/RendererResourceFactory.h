/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/* Prevention of redefinition */
#ifndef CRICOCOS2D_MANA_DETAIL_RENDERERRESOURCEFACTORY_H
#define CRICOCOS2D_MANA_DETAIL_RENDERERRESOURCEFACTORY_H


#include "cricocos2d/mana/detail/RendererResource.h"

#include <memory>


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			class RendererResourceFactory
			{
			public:
				RendererResourceFactory(const RendererResourceFactory&)				 = delete;
				RendererResourceFactory& operator = (const RendererResourceFactory&) = delete;

				RendererResourceFactory() CRICOCOS2D_NOEXCEPT						 = default;
				virtual ~RendererResourceFactory()									 = default;

				virtual std::shared_ptr<RendererResource> createRenderResource(
					const CriManaMovieInfo& movie_info,
					CriManaPlayerHn			mana_player
																			  ) = 0;
				virtual void onSuspend()		   = 0;
				virtual void onResume()			   = 0;
				virtual void onRendererRecreated() = 0;
			};
		}
	}
}


#endif
