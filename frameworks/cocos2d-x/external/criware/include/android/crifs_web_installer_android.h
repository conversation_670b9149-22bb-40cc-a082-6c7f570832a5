﻿/****************************************************************************
 *
 * CRI Middleware SDK
 *
 * Copyright (c) 2016 CRI Middleware Co., Ltd.
 *
 * Library  : CRI File System
 * Module   : Library User's Header
 * File     : crifs_web_installer_android.h
 *
 ****************************************************************************/
/*!
 *  \file       crifs_web_installer_android.h
 */

/*JP
 * \addtogroup FSLIB_CRIFS_WEB_INSTALLER
 * @{
 */
/*EN
 * \addtogroup FSLIB_CRIFS_WEB_INSTALLER
 * @{
 */


/* 多重定義防止 */
/* Prevention of redefinition */
#ifndef CRIFS_WEB_INSTALLER_ANDROID_H_INCLUDED
#define CRIFS_WEB_INSTALLER_ANDROID_H_INCLUDED

/***************************************************************************
 *      インクルードファイル
 *      Include files
 ***************************************************************************/
#include "cri_xpt.h"
#include "jni.h"

/***************************************************************************
 *      データ型宣言
 *      Data Type Declarations
 ***************************************************************************/
typedef struct CriFsWebInstallerConfig_ANDROID
{
    CriUint8 reserved;
} CriFsWebInstallerConfig_ANDROID;

typedef CriFsWebInstallerConfig_ANDROID CriFsWebInstallerPlatformConfig;


/***************************************************************************
 *      処理マクロ
 *      Macro Functions
 ***************************************************************************/
#define criFsWebInstaller_SetDefaultConfig_ANDROID(p_config)    \
    {                                                       	\
        (p_config)->reserved = 0;                       	    \
    }

#define criFsWebInstaller_SetPlatformDefaultConfig(p_config)    criFsWebInstaller_SetDefaultConfig_ANDROID(p_config)


#define CRIFS_WEB_INSTALLER_PLATFORM_H_INCLUDED

#include "crifs_web_installer.h"

#endif  /* CRIFS_WEB_INSTALLER_ANDROID_H_INCLUDED */


/*JP
 * @}
 */
/*EN
 * @}
 */


/* --- end of file --- */
