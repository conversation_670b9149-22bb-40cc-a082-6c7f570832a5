package com.frontier.sdkbase;

import android.app.Activity;
import android.content.Intent;

import org.json.JSONObject;

public abstract class AbstractPayImpl implements ICommon, IPay {
    protected Activity activity;
    protected EventListener listener;
    protected Boolean inProcess = false;
    public AbstractPayImpl(Activity activity, EventListener listener) {
        this.activity = activity;
        this.listener = listener;
    }

    @Override
    public void initSDK() {

    }

    @Override
    public void querySkuDetails(String skuList) {
        listener.onQuerySkuDetails(sdkType(), ErrorCode.NOT_IMPLEMENTED, null, 0);
    }

    @Override
    public void uploadPlayerData(JSONObject json) {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onResume() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public boolean isInProcess() {
        return inProcess;
    }

    @Override
    public void onNewIntent(Intent intent) {

    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

    }

    @Override
    public void setNewVersion(String isNew) {

    }

    public boolean isFeatureSupported(){
        return true;
    }

    public String getBillingConfig(){return "";}

}
