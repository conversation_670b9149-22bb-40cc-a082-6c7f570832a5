plugins {
    id 'com.android.library'
}

android {
    namespace 'com.frontier.samsungadssdk'
    compileSdk 34

    defaultConfig {
        targetSdk 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}
allprojects {
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.2.1'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation project(':sdkbase')
//    implementation files('libs/CloudDevSDK_1.1.1_SDK.aar')

}