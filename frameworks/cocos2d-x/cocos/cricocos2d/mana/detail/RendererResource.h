/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/* Prevention of redefinition */
#ifndef CRICOCOS2D_MANA_DETAIL_RENDERERRESOURCE_H
#define CRICOCOS2D_MANA_DETAIL_RENDERERRESOURCE_H


#include "cricocos2d/cricocos2d.h"


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			class RendererResource
			{
			public:
				RendererResource(const RendererResource&)						  = delete;
				RendererResource& operator = (const RendererResource&)			  = delete;

				RendererResource() CRICOCOS2D_NOEXCEPT							  = default;
				virtual ~RendererResource()										  = default;

				virtual void attachToPlayer()									  = 0;

				virtual bool isSuitable(const CriManaMovieInfo& movie_info) const = 0;
				virtual bool isPrepared() const									  = 0;
				virtual bool isDrawable() const									  = 0;

				virtual void continuePreparation()								  = 0;

				virtual bool updateFrame(CriManaFrameInfo& frame_info)			  = 0;
				virtual void draw(
					const cocos2d::TrianglesCommand::Triangles& triangles,
					const cocos2d::Mat4&						mv,
					bool										additive
								 ) = 0;

			};
		}
	}
}


#endif
