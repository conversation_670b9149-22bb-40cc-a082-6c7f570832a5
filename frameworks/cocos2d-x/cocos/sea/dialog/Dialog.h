//
//  DialogBase.h
//  Slots
//
//  Created by chen<PERSON><PERSON><PERSON><PERSON> on 2024/12/9.
//
#ifndef __SEA_DIALOG_H__
#define __SEA_DIALOG_H__

#include <spine/SkeletonAnimation.h>
#include "cocos2d.h"
#include "sea/common/SeaCommon.h"

namespace sea {
class DialogNodeSprite;
class Dialog : public cocos2d::Node
{
public:    
    struct BtnClickData{
        BtnClickData(){
            pClickedBtn = nullptr;
            bIsBeginPoint = false;
        }
        ~BtnClickData(){
            clear();
        }
        void clear(){
            if (pClickedBtn) {
                pClickedBtn->release();
                pClickedBtn = nullptr;
            }
            bIsBeginPoint = false;
        }
        cocos2d::ui::Widget* pClickedBtn;
        cocos2d::Vec2 vTouchBeginPoint;
        bool bIsBeginPoint;
    };
    class BtnHandlerData : public cocos2d::Ref
    {
    public:
        BtnHandlerData(){
            this->button = nullptr;
        }
        ~BtnHandlerData(){
            if (this->button) {
                this->button->release();
                this->button = nullptr;
            }
        }
        
        static BtnHandlerData* create(cocos2d::ui::Widget* button, const cocos2d::Value& p) {
            auto object = new BtnHandlerData();
            object->autorelease();
            
            if (button) {
                button->retain();
                object->button = button;
                object->params = p;
            }
            
            return object;
        }
        
        const cocos2d::Value& getParams() {
            return this->params;
        }
        const cocos2d::ui::Widget* getButton() {
            return this->button;
        }
    protected:
        cocos2d::ui::Widget* button;
        cocos2d::Value params;
        
    };
public:
    Dialog();
    ~Dialog();
    static Dialog * create();
    bool init();

    virtual void onEnter();
    virtual void onExit();
    static void registerLuaFunctionVersion(int type, int handler);
public://Lua支持的函数
    bool initWithData(const cocos2d::ValueMap &params);
    
    void setIsNode();
    void pushResPathConfig(const cocos2d::ValueMap& params, bool force = false);
    void updateResPathConfig(const cocos2d::ValueMap& params, bool force = false);
    void pushExitCallback(const seaCallback1& callback);
    void checkOnlyHorizontal(bool isNew = false);
    const cocos2d::Value getVertical(const cocos2d::Value& value = cocos2d::Value::Null, bool force = false);
    
    spine::SkeletonAnimation * getAniPlus(const std::string& key, const cocos2d::ValueMap& params);
    //Lua调用时isLang、isVertical、isActivity用布尔型，此时C++用-1、0、1分别表示nil、false、true
    spine::SkeletonAnimation * getAni(const std::string& key, const sea::IntBool& isLang = sea::IntBool::None, const sea::IntBool& isVertical = sea::IntBool::None, const bool& notAddHead = false, const std::string& resHeadKey = "", const sea::IntBool& isActivity = sea::IntBool::None, const std::string& themeId = "");

    //获取动效路径
    std::vector<std::string> getAniPathPlus(const std::string& key, const cocos2d::ValueMap& params);
    //Lua调用时isLang、isVertical、isActivity用布尔型，此时C++用-1、0、1分别表示nil、false、true
    std::vector<std::string> getAniPath(const std::string& key, const sea::IntBool& isLang = sea::IntBool::None, const sea::IntBool& isVertical = sea::IntBool::None, const bool& notAddHead = false, const std::string& resHeadKey = "", const sea::IntBool& isActivity = sea::IntBool::None, const std::string& themeId = "");

    std::string getPath(const std::string& key, const std::string& resHeadKey);
    std::string getPath(const std::string& key);
    std::string getFntPath(const std::string& key, const std::string& resHeadKey);

    //获取切图路径
    std::string getImgPathPlus(const std::string& key, const cocos2d::ValueMap& params);
    std::string getImagePathPlus(const std::string& key, const cocos2d::ValueMap& params);
    //Lua调用时isLang、isVertical、isActivity用布尔型，此时C++用-1、0、1分别表示nil、false、true
    std::string getImgPath(const std::string& key, const sea::IntBool& isLang = sea::IntBool::None, const sea::IntBool& isVertical = sea::IntBool::None, const sea::TextureResType& resType = sea::TextureResType::NONE, const bool& notAddHead = false, const std::string& resHeadKey = "", const sea::IntBool& isActivity = sea::IntBool::None, const std::string& themeId = "");

    cocos2d::Sprite * getImgNodePlus(const std::string& key, cocos2d::ValueMap& params);
    //Lua调用时isLang、isVertical、isActivity用布尔型，此时C++用-1、0、1分别表示nil、false、true
    cocos2d::Sprite * getImgNode(const std::string& key, const sea::IntBool& isLang = sea::IntBool::None, const sea::IntBool& isVertical = sea::IntBool::None, const sea::TextureResType& resType = sea::TextureResType::NONE, const bool& notAddHead = false, const std::string& resHeadKey = "", const sea::IntBool& isActivity = sea::IntBool::None, const std::string& themeId = "");

    cocos2d::ValueMap getLangImgInfoPlus(const std::string& key, const cocos2d::ValueMap& params);
    //Lua调用时isLang、isVertical、isActivity、notAddHead用布尔型，此时C++用-1、0、1分别表示nil、false、true
    cocos2d::ValueMap getLangImgInfo(const std::string& key, const sea::IntBool& isVertical = sea::IntBool::None, const sea::TextureResType& resType = sea::TextureResType::NONE, const sea::IntBool& notAddHead = sea::IntBool::None, const std::string& resHeadKey = "", const sea::IntBool& isActivity = sea::IntBool::None, const std::string& themeId = "");
    
    //Lua为了兼容之前的写法，C++与Lua略有不同。
    //C++keys表示路径指向一个节点,calback只会调用一次；Lua当第二个参数为string时，后面可以接不定数量的string以形成路径并指向一个节点；当第二个参数是数组时，可以指向多个节点.
    void callNodeChild(const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, const std::vector<std::string>& keys, cocos2d::Node* node = nullptr, bool isPlusChild = false);
    void callNodeChild(const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, const cocos2d::ValueVector& keys, cocos2d::Node* node = nullptr, bool isPlusChild = false);
    void callNodeChild(const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback, const std::string& key, cocos2d::Node* node = nullptr, bool isPlusChild = false);
    cocos2d::Vector<cocos2d::Node*> callChildrenByGroup(const std::string& group, const std::function<void (cocos2d::Node *, const cocos2d::Value&)>& callback = nullptr, cocos2d::Node *parent = nullptr, const cocos2d::ValueMap& params = cocos2d::ValueMapNull);
    cocos2d::Node * getRootChildByName(const std::vector<std::string>& keys);
    cocos2d::Node * getNodeChildByName(cocos2d::Node *node, const std::vector<std::string>& keys);
    cocos2d::Node * getRootPlusChildByName(const std::vector<std::string>& keys);
    cocos2d::Node * getNodePlusChildByName(cocos2d::Node *node, const std::vector<std::string>& keys);
    
    //设置单选按钮的回调函数。如果手动对按钮调用addClickEvent，则此函数失效
    void setRadioButtonCallback(const std::string& group, const int handler);
    void excuteRadioButtonCall(cocos2d::Node* btn, const std::string& group, const cocos2d::Value& data);

    void addChildrenByConfig(const cocos2d::ValueVector& config, cocos2d::Node *parent = nullptr);
    cocos2d::Node * createNodeWithConfig(const cocos2d::ValueMap& config);
    cocos2d::Node * getRootNode();
    
    //按钮点击函数
    void addClickEvent(cocos2d::ui::Widget* btn, seaCallback2 handler, const cocos2d::Value& params = cocos2d::Value::Null);
    void addClickEventMute(cocos2d::ui::Widget* btn, seaCallback2 handler, const cocos2d::Value& params = cocos2d::Value::Null);
    void addClickEventNoMove(cocos2d::ui::Widget* btn, seaCallback2 handler, const cocos2d::Value& params = cocos2d::Value::Null);
    
    static void registerLuaFunctionList(const cocos2d::ValueMap& map);
    
    //销毁时调用
    void clearDialog();
private:
    void _addClickEvent(cocos2d::ui::Widget* btn, seaCallback2 handler, const cocos2d::Value& params, bool mute = false, bool noMove = false);
    BtnHandlerData* getBtnHandlerData(cocos2d::ui::Widget* button);
    
    static void initStatic();
private:
    //当前是否竖版
    bool _bIsVertical;
    //是否默认使用竖版资源
    bool _bIsDefaultVertical;
    //是否默认在res_ios根目录下
    bool _bIsInRoot;
    //根节点
    cocos2d::Node *_pRootNode;
    //退出回调
    std::vector<seaCallback1> _vExitCallbackList;
    
    /**各种路径配置**/
    //默认根路径
    std::string _sPathRootDefault;
    //根路径组
    std::unordered_map<std::string, std::string> _mPathRootMap;

    //默认fnt路径
    std::string _sPathFntDefault;
    //fnt路径组
    std::unordered_map<std::string, std::string> _mPathFntMap;

    //默认普通切图路径
    std::string _sPathImageCommonDefault;
    //普通切图路径组
    std::unordered_map<std::string, std::string> _mPathImageCommonMap;

    //默认多语切图路径
    std::string _sPathImageMultiDefault;
    //多语切图路径组
    std::unordered_map<std::string, std::string> _mPathImageMultiMap;

    //默认普通动效路径
    std::string _sPathAniCommonDefault;
    //普通动效路径组
    std::unordered_map<std::string, std::string> _mPathAniCommonMap;

    //默认多语动效路径
    std::string _sPathAniMultiDefault;
    //多语动效路径组
    std::unordered_map<std::string, std::string> _mPathAniMultiMap;
    
    //setRadioButtonCallback数据保存
    std::unordered_map<std::string, int> _mRadioGroupCallback;
    
    //按钮点击对象记录
    BtnClickData _sBtnClickData;
    
    cocos2d::Vector<BtnHandlerData*> _mButtonHandlerData;
    
private:
    static std::unordered_map<int, std::function<cocos2d::Node* (cocos2d::ValueMap, Dialog*, cocos2d::ValueMap)> > sCreateFunMap;
    static std::unordered_map<int, cocos2d::Value> sCreateHandlerMap;
    static bool sIsInitStatic;
};
}

#endif // ifndef __SEA_DIALOG_H__
