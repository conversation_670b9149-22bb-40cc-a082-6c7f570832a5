//
//  BoleLabel.cpp
//  Slots
//
//  Created by chenyongxian on 2024/12/25.
//
#include "../SeaCommon.h"
#include "BoleLabel.h"

namespace sea {
std::unordered_map<std::string, std::function<void(BoleLabel*)> > BoleLabel::mapFunList = {
    {
        "outline", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->enableOutline(blLabel->sLabelData.outlineColor, blLabel->sLabelData.outlineSize);
        },
    },
    {
        "shadow", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->enableShadow(blLabel->sLabelData.shadowColor, blLabel->sLabelData.shadowOffset, blLabel->sLabelData.shadowBlurRadius);
        },
    },
    {
        "glow", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->enableGlow(blLabel->sLabelData.glowColor);
        },
    },
    {
        "italice", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->enableItalics();
        },
    },
    {
        "bold", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->enableBold();
        },
    },
    {
        "underline", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->enableUnderline();
        },
    },
    {
        "text_color", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setTextColor(blLabel->sLabelData.colorText);
        },
    },
    {
        "anchor", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setAnchorPoint(blLabel->sLabelData.anchorPoint);
        },
    },
    {
        "line_space", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setLineSpacing(blLabel->sLabelData.lineSpace);
        },
    },
    {
        "line_height", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setLineHeight(blLabel->sLabelData.lineHeight);
        },
    },
    {
        "dimension", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setDimensions(blLabel->sLabelData.dimensionsW, blLabel->sLabelData.dimensionsH);
        },
    },
    {
        "break_without_space", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setLineBreakWithoutSpace(blLabel->sLabelData.breakWithoutSpace);
        },
    },
    {
        "alignment_x", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setHorizontalAlignment(blLabel->sLabelData.hAlignment);
        },
    },
    {
        "alignment_y", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setVerticalAlignment(blLabel->sLabelData.vAlignment);
        },
    },
    {
        "color", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setColor(blLabel->sLabelData.color);
        },
    },
    {
        "overflow", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setOverflow(blLabel->sLabelData.overflow);
        },
    },
    {
        "addtional_space", [](BoleLabel* blLabel){
            if (!blLabel || !blLabel->_pLabel) {
                return;
            }
            blLabel->_pLabel->setAdditionalKerning(blLabel->sLabelData.additionalSpace);
        },
    },
};

BoleLabel::BoleLabel() :
    _bIsAutoLang(false),
    _iFont(-999),
    _pLabel(nullptr),
    _iCurrentLabelType(LabelType::NONE),
    _bDirty(true),
    _iLimitWidth(0),
    _iFontSize(20),
    _bIsSettingLocked(false) {
        setAlignment(cocos2d::TextHAlignment::CENTER, cocos2d::TextVAlignment::CENTER);
}

BoleLabel::~BoleLabel() {
    CC_SAFE_RELEASE_NULL(_pLabel);
}

BoleLabel * BoleLabel::create(const cocos2d::Value& font, const std::string& str, const cocos2d::Value& size, const cocos2d::Color4B& color) {
    auto ret = new (std::nothrow) BoleLabel();

    if (ret && ret->init(font, str, size, color)) {
        ret->autorelease();
    } else {
        CC_SAFE_DELETE(ret);
    }

    return ret;
}

BoleLabel * BoleLabel::create() {
    auto ret = new (std::nothrow) BoleLabel();

    if (ret) {
        ret->autorelease();
    } else {
        CC_SAFE_DELETE(ret);
    }

    return ret;
}

bool BoleLabel::init(const cocos2d::Value& font, const std::string& str, const cocos2d::Value& size, const cocos2d::Color4B& color) {
    initFont(font);
    resetFontSize(size);
    setTextColor(color);
    setString(str);


    return true;
}

BoleLabel::LabelType BoleLabel::getLabelType() {
    return _iCurrentLabelType;
}

void BoleLabel::initFont(const cocos2d::Value& font) {
    _mFontMap.clear();
    _iFont = -999;

    if (font.isNull()) {
        return;
    }
    
    if (font.getType() == ccValueType::STRING && font.asString().length() == 0) {
        return;
    }

    if (font.isNumber()) {
        _iFont = font.asInt();
    }

    _mFontMap = sea::Global::getFontPathConfig(font);
}

void BoleLabel::setFontSize(const cocos2d::Value& size) {
    resetFontSize(size);
    _sFontType = "";//强制重设
    setString(_sShowString);
}

int BoleLabel::getNewFontSize() {
    int fontSize = _iFontSize;

    //语言变了，如果字体大小是配置的话，要进行处理
    if (!_vFontSize.isNull()) {
        auto size = sea::Global::translateTable(_vFontSize, { { "priority", cocos2d::Value(_sNowShowLang) } });

        if (!size.isNull()) {
            fontSize = size.asInt();
        }
    }

    return fontSize;
}

void BoleLabel::resetFontSize(const cocos2d::Value& size) {
    _iFontSize = 20;

    if (size.isNull()) {
        _vFontSize = cocos2d::Value::Null;
    } else if (size.getType() == ccValueType::MAP) {
        _vFontSize = size;
    } else if (size.isNumber()) {
        _iFontSize = size.asInt();
        _vFontSize = cocos2d::Value::Null;
    } else {
        sea::showMessageBox("ERROR BoleLabel::initFontSize");
    }
}

void BoleLabel::setString(const std::string& str, const std::string& lang) {
    if (_bIsSettingLocked) {
        _sShowString = str;

        if (lang.size() > 0) {
            _sNowShowLang = lang;
        }

        _bDirty = true;
        return;
    }

    std::string useLang = lang;

    if (lang.size() == 0) {
        if (_sFixLang.size() > 0) { //如果有固定语言
            useLang = _sFixLang;
        } else if (_bIsAutoLang) { //如果有根据内容决定语言
            if (!_bDirty && str == _sShowString) {
                return;
            } else {
                if (str.size() > 0 && sea::Global::getLocalLang() != "EN") {
                    if (sea::Global::isOnlyEnglishCode(str)) {
                        useLang = "EN";
                    }
                }
            }
        }
    }

    if (useLang.size() == 0) {
        useLang = sea::Global::getLocalLang();
    }

    if (useLang == _sNowShowLang) {
        if (!_bDirty && str == _sShowString) {
            return;
        }
    } else {
        _sNowShowLang = useLang;
    }

    _bDirty = false;
    _sShowString = str;

    //类型相同，直接设置
    std::string newFontType = "EN";
    bool isSystem = false;

    if (sea::Global::checkFontValueByName(_iFont, "SYSTEM")) {
        newFontType = sea::Global::getSysFont();
        isSystem = true;
    }
    else if (!sea::Global::checkFontValueByName(_iFont, "NEURON_ONLY")) {
        newFontType = sea::Global::getFontType(_sNowShowLang);
    }

    if (newFontType == _sFontType && _pLabel) {
        _pLabel->setString(str);
        _checkSetting(false);
    } else {
        if (_pLabel) {
            _pLabel->removeFromParent();
            CC_SAFE_RELEASE_NULL(_pLabel);
        }

        //类型不同，重新生成
        auto fontSize = getNewFontSize();
        
        //系统字
        if (isSystem) {
            auto sysFnt = sea::Global::getSysFont();
            _pLabel = cocos2d::Label::createWithSystemFont(str, sysFnt, fontSize);
            _iCurrentLabelType = LabelType::STRING_TEXTURE;
        } else {
            auto iter = _mFontMap.find(newFontType);

            if (iter != _mFontMap.end()) {
                _pLabel = cocos2d::Label::createWithTTF(str, iter->second, fontSize);
                _iCurrentLabelType = LabelType::TTF;
            } else {
                auto path = sea::Global::getLangDefaultPath(newFontType);

                if (path.size() > 0) {
                    _pLabel = cocos2d::Label::createWithTTF(str, path, fontSize);
                    _iCurrentLabelType = LabelType::TTF;
                } else {
                    auto sysFnt = sea::Global::getSysFont();
                    _pLabel = cocos2d::Label::createWithSystemFont(str, sysFnt, fontSize);
                    _iCurrentLabelType = LabelType::STRING_TEXTURE;
                }
            }
        }

        if (_pLabel) {
            addChild(_pLabel);
            CC_SAFE_RETAIN(_pLabel);

            if (newFontType == "CN") {
                _pLabel->setPosition(0, fontSize / 10);
            }
        }

        _sFontType = newFontType;
        _checkSetting(true);
    }

    resetLangPos(_sNowShowLang);
    _checkLabelLimit();
}

void BoleLabel::_checkSetting(bool isNew) {
    if (!_pLabel) {
        return;
    }

    for (auto& iter : mSetValueSign) {
        auto key = iter.first;
        auto kIter = mapFunList.find(key);

        if (kIter == mapFunList.end() || (!iter.second && !isNew) ) {
            continue;
        }

        iter.second = false;
        auto fun = kIter->second;
        fun(this);
    }
}

void BoleLabel::resetLangPos(const std::string& lang) {
    if (lang == "") {
        return;
    }

    if (_vPosConfig.isNull()) {
        return;
    }

    auto _posConfig = _vPosConfig;
    auto posConfigType = _vPosConfig.getType();

    if (posConfigType == ccValueType::LUA_FUNCTION) {
        _posConfig = sea::excuteLuaFunction(_vPosConfig, 1, [ = ](cocos2d::LuaStack *stack) {
                sea::pushValueToStack(stack, _vPosConfig);
            });
    }

    if (_posConfig.getType() == ccValueType::MAP) {
        auto posConfig = _posConfig.asValueMap();

        if (posConfig.find("EN") != posConfig.end()) {
            cocos2d::ValueMap params = { { "priority", cocos2d::Value(lang) } };
            auto pos = sea::Global::translateTable(_posConfig, params);

            if (!pos.isNull()) {
                if (pos.getType() == ccValueType::MAP) {
                    auto posMap = pos.asValueMap();

                    if (posMap.find("x") != posMap.end() && posMap.find("y") != posMap.end()) {
                        setPosition(posMap.at("x").asFloat(), posMap.at("y").asFloat());
                    }
                } else if (pos.getType() == ccValueType::VECTOR) {
                    auto posVec = pos.asValueVector();

                    if (posVec.size() == 2) {
                        setPosition(posVec[0].asFloat(), posVec[1].asFloat());
                    }
                }
            }
        } else if (posConfig.find("x") != posConfig.end() && posConfig.find("y") != posConfig.end()) {
            setPosition(posConfig.at("x").asFloat(), posConfig.at("y").asFloat());
        }
    }
}

void BoleLabel::beginSetting() {
    _bIsSettingLocked = true;
}

void BoleLabel::endSetting() {
    _bIsSettingLocked = false;
    _sFontType = "";

    if (_pLabel) {
        _pLabel->setString("");
    }

    setString(_sShowString);
}

/******以下为与Label有关的函数****/

bool BoleLabel::isSetKey(const std::string& key) const {
    return mSetValueSign.find(key) != mSetValueSign.end();
}

void BoleLabel::enableOutline(const cocos2d::Color4B& outlineColor, int outlineSize /* = -1*/) {
    _bDirty = true;
    mSetValueSign["outline"] = true;
    sLabelData.outlineColor = outlineColor;
    sLabelData.outlineSize = outlineSize;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["outline"] = false;
        _pLabel->enableOutline(outlineColor, outlineSize);
    }
}

void BoleLabel::enableShadow(const cocos2d::Color4B& shadowColor /* = cocos2d::Color4B::BLACK*/, const cocos2d::Size &offset /* = cocos2d::Size(2,-2)*/, int blurRadius /* = 0*/) {
    _bDirty = true;
    mSetValueSign["shadow"] = true;
    sLabelData.shadowColor = shadowColor;
    sLabelData.shadowOffset = offset;
    sLabelData.shadowBlurRadius = blurRadius;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["shadow"] = false;
        _pLabel->enableShadow(shadowColor, offset, blurRadius);
    }
}

void BoleLabel::enableGlow(const cocos2d::Color4B& glowColor) {
    _bDirty = true;
    mSetValueSign["glow"] = true;
    sLabelData.glowColor = glowColor;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["glow"] = false;
        _pLabel->enableGlow(glowColor);
    }
}

void BoleLabel::enableItalics() {
    _bDirty = true;
    mSetValueSign["italice"] = true;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["italice"] = false;
        _pLabel->enableItalics();
    }
}

void BoleLabel::enableBold() {
    _bDirty = true;
    mSetValueSign["bold"] = true;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["bold"] = false;
        _pLabel->enableBold();
    }
}

void BoleLabel::enableUnderline() {
    _bDirty = true;
    mSetValueSign["underline"] = true;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["underline"] = false;
        _pLabel->enableUnderline();
    }
}

void BoleLabel::setTextColor(const cocos2d::Color4B &color) {
    _bDirty = true;
    mSetValueSign["text_color"] = true;
    sLabelData.colorText = color;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["text_color"] = false;
        _pLabel->setTextColor(color);
    }
}

const cocos2d::Color4B& BoleLabel::getTextColor() const {
    if (_pLabel) {
        return _pLabel->getTextColor();
    }

    if (isSetKey("text_color")) {
        return sLabelData.colorText;
    }

    return cocos2d::Color4B::WHITE;
}

void BoleLabel::setAnchorPoint(const cocos2d::Vec2& anchorPoint) {
    _bDirty = true;
    mSetValueSign["anchor"] = true;
    sLabelData.anchorPoint = anchorPoint;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["anchor"] = false;
        _pLabel->setAnchorPoint(anchorPoint);
    }
}

const cocos2d::Vec2& BoleLabel::getAnchorPoint() const {
    if (_pLabel) {
        return _pLabel->getAnchorPoint();
    }

    if (isSetKey("anchor")) {
        return sLabelData.anchorPoint;
    }

    return cocos2d::Vec2::ANCHOR_MIDDLE;
}

void BoleLabel::setLineSpacing(float height) {
    _bDirty = true;
    mSetValueSign["line_space"] = true;
    sLabelData.lineSpace = height;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["line_space"] = false;
        _pLabel->setLineSpacing(height);
    }
}

void BoleLabel::setLineHeight(float height) {
    _bDirty = true;
    mSetValueSign["line_height"] = true;
    sLabelData.lineHeight = height;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["line_height"] = false;
        _pLabel->setLineHeight(height);
    }
}

void BoleLabel::setDimensions(float width, float height) {
    _bDirty = true;
    mSetValueSign["dimension"] = true;
    sLabelData.dimensionsW = width;
    sLabelData.dimensionsH = height;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["dimension"] = false;
        _pLabel->setDimensions(width, height);
    }
}

void BoleLabel::setLineBreakWithoutSpace(bool breakWithoutSpace) {
    _bDirty = true;
    mSetValueSign["break_without_space"] = true;
    sLabelData.breakWithoutSpace = breakWithoutSpace;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["break_without_space"] = false;
        _pLabel->setLineBreakWithoutSpace(breakWithoutSpace);
    }
}

void BoleLabel::setAlignment(cocos2d::TextHAlignment hAlignment, cocos2d::TextVAlignment vAlignment) {
    _bDirty = true;
    mSetValueSign["alignment_x"] = true;
    mSetValueSign["alignment_y"] = true;
    sLabelData.hAlignment = hAlignment;
    sLabelData.vAlignment = vAlignment;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["alignment_x"] = false;
        mSetValueSign["alignment_y"] = false;
        _pLabel->setAlignment(hAlignment, vAlignment);
    }
}

void BoleLabel::setAlignment(cocos2d::TextHAlignment hAlignment) {
    setHorizontalAlignment(hAlignment);
}

void BoleLabel::setColor(const cocos2d::Color3B& color) {
    _bDirty = true;
    mSetValueSign["color"] = true;
    sLabelData.color = color;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["color"] = false;
        _pLabel->setColor(color);
    }
}

void BoleLabel::setHorizontalAlignment(cocos2d::TextHAlignment hAlignment) {
    _bDirty = true;
    mSetValueSign["alignment_x"] = true;
    sLabelData.hAlignment = hAlignment;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["alignment_x"] = false;
        _pLabel->setHorizontalAlignment(hAlignment);
    }
}

void BoleLabel::setVerticalAlignment(cocos2d::TextVAlignment vAlignment) {
    _bDirty = true;
    mSetValueSign["alignment_y"] = true;
    sLabelData.vAlignment = vAlignment;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["alignment_y"] = false;
        _pLabel->setVerticalAlignment(vAlignment);
    }
}

const cocos2d::Size& BoleLabel::getContentSize() const {
    if (_pLabel) {
        return _pLabel->getContentSize();
    }

    return getContentSize();
}

float BoleLabel::getLineHeight() {
    const int type = getLabelType();

    if (type == LabelType::STRING_TEXTURE) {
        //cocos底层不支持系统字获取高度，这里根据测试结果进行返回。测试设备：模拟器和手机。测试条件：加投影、加描边。以上测试发现结果一致
        int lineHeightList[] = { 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 18, 19, 20, 21, 23, 24, 24, 25, 27, 28, 29, 30, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 55, 57, 57, 58, 59, 61, 62, 63, 64, 66, 67, 68, 69, 71, 72, 73, 73, 75, 76, 77, 78, 80, 81, 82, 83, 85, 86, 87, 88, 90, 90, 91, 92, 94, 95, 96, 97, 99, 100, 101, 102, 103, 105, 105, 106, 107, 109, 110, 111, 112, 114, 115, 116, 117 };
        auto size = getNewFontSize();
        int fontSize = std::floor(size + 0.001);

        if (fontSize > 0 && fontSize <= 100) {
            return lineHeightList[fontSize - 1];
        } else {
#if COCOS2D_DEBUG >= 1
            sea::showMessageBox("程序设计异常：不支持对超出字号范围的字体使用getLineHeight方法，请完善数据。");
#endif
            return fontSize;
        }
    } else {
        if (_pLabel) {
            return _pLabel->getLineHeight();
        }
    }

    return 0;
}

void BoleLabel::setOverflow(cocos2d::Label::Overflow overflow) {
    _bDirty = true;
    mSetValueSign["overflow"] = true;
    sLabelData.overflow = overflow;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["overflow"] = false;
        _pLabel->setOverflow(overflow);
    }
}

const std::string& BoleLabel::getString() const {
    if (_pLabel) {
        return _pLabel->getString();
    }

    return _sShowString;
}

void BoleLabel::setAdditionalKerning(float space) {
    _bDirty = true;
    mSetValueSign["addtional_space"] = true;
    sLabelData.additionalSpace = space;

    if (_bIsSettingLocked) {
        return;
    }

    if (_pLabel) {
        mSetValueSign["addtional_space"] = false;
        _pLabel->setAdditionalKerning(space);
    }
}

cocos2d::Rect BoleLabel::getBoundingBox() const {
    if (_pLabel) {
        return _pLabel->getBoundingBox();
    }

    return cocos2d::Node::getBoundingBox();
}

void BoleLabel::setLabelWidth(float width) {
    _iLimitWidth = width;
    if (!_pLabel) {
        return;
    }

    _checkLabelLimit();
}

void BoleLabel::_checkLabelLimit() {
    if (!_pLabel) {
        return;
    }

    if (_iLimitWidth <= 0) {
        _pLabel->setScale(1);
        return;
    }

    auto contentWidth = _pLabel->getContentSize().width;

    if (contentWidth <= 0) {
        return;
    }

    if (contentWidth > _iLimitWidth) {
        _pLabel->setScale(_iLimitWidth / contentWidth);
    }
    else {
        _pLabel->setScale(1);
    }
    
}
}
