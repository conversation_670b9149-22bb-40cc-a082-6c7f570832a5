//
//  BLConfig.cpp
//  cocos2d_libs
//
//  Created by wodes on 14-9-25.
//
//

#include "BLConfig.h"
NS_CC_BEGIN

BLConfig* BLConfig::instance = nullptr;
BLConfig* BLConfig::getInstance() {
    if (instance == nullptr) {
        instance = new BLConfig();
        instance->init();
    }
    return instance;
}

void BLConfig::init() {
    ValueMap v = FileUtils::getInstance()->getValueMapFromFile("config.plist");
    setConfigMap(v);
}

void BLConfig::setConfigMap(ValueMap m) {
    _map = m;
}

ValueMap BLConfig::getConfig() {
    return _map;
}

std::string BLConfig::getConfig(std::string key) {
    cocos2d::Value v = _map[key];
    if (!v.isNull())
        return v.asString();
    return "";
}
NS_CC_END