/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

#include "AdxAudio.h"
#include "cocos2d.h"
#include <cri_atom_ex.h>

namespace cricocos2d
{
	namespace atom
	{
    AdxAudio::AdxAudio(int maxVoices, int standardMaxSamplingRate/* = 96000*/, int hcaMxMaxSamplingRate/* = 48000*/)
    :dbas_id(-1),
    _speedVPID(1),
    _standardVPID(2),
    _hcamxVPID(3),
    _isHcaMx(false)
    {
        _maxVoices = maxVoices;
        _standardMaxSamplingRate = standardMaxSamplingRate;
        _hcaMxMaxSamplingRate = hcaMxMaxSamplingRate;
    }


    AdxAudio::~AdxAudio()
    {
        if (-1 != dbas_id)
        {
            criAtomDbas_Destroy(dbas_id);
            dbas_id = -1;
        }
        
        /* [KEY POINT: 4 destroy player] */
        for(const auto& iter : atomExPlayerHnMap) 
        {
            criAtomExPlayer_Destroy(iter.second);
        }
        atomExPlayerHnMap.clear();
        
        /* 数据和语音池的释放 */
        unloadDataAndFreeVoicePool();
    }
    
    void AdxAudio::createPlayer(std::string playerName)
    {
        auto iter = atomExPlayerHnMap.find(playerName);
        if (iter == atomExPlayerHnMap.end())
        {
            criAtomExPlayer_SetDefaultConfig(&playerConfig);
            CriAtomExPlayerHn atomExPlayerHn = criAtomExPlayer_Create(&playerConfig, NULL, 0);
            criAtomExPlayer_SetVoicePoolIdentifier(atomExPlayerHn, _standardVPID);
            atomExPlayerHnMap[playerName] = atomExPlayerHn;
        }
        else
        {
            CCLOG("error: createPlayer already has cached the acb  %s", playerName.c_str());
#if CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID && defined(COCOS2D_DEBUG) && COCOS2D_DEBUG > 0
            std::string msgBoxStr = "已经创建相同player:" + playerName;
            cocos2d::MessageBox(msgBoxStr.c_str(), "CRIWARE ERROR");
#endif
        }
        
    }
    
    // 创建在播放速度声音池里的player
    void AdxAudio::createSpeedPlayer(std::string playerName)
    {
        auto iter = atomExPlayerHnMap.find(playerName);
        if (iter == atomExPlayerHnMap.end())
        {
            criAtomExPlayer_SetDefaultConfig(&playerConfig);
            CriAtomExPlayerHn atomExPlayerHn = criAtomExPlayer_Create(&playerConfig, NULL, 0);
            criAtomExPlayer_SetVoicePoolIdentifier(atomExPlayerHn, _speedVPID);
            atomExPlayerHnMap[playerName] = atomExPlayerHn;
        }
        else
        {
            CCLOG("error: createPlayer already has cached the acb  %s", playerName.c_str());
#if CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID && defined(COCOS2D_DEBUG) && COCOS2D_DEBUG > 0
            std::string msgBoxStr = "已经创建相同player:" + playerName;
            cocos2d::MessageBox(msgBoxStr.c_str(), "CRIWARE ERROR");
#endif
        }
    }
    
    void AdxAudio::destroyPlayer(std::string playerName)
    {
        auto iter = atomExPlayerHnMap.find(playerName);
        if (iter != atomExPlayerHnMap.end())
        {
            criAtomExPlayer_Destroy(iter->second);
            atomExPlayerHnMap.erase(iter);
        }
    }
    
    void AdxAudio::destroyDbus()
    {
        criAtomDbas_Destroy(dbas_id);
        dbas_id = -1;
    }
    
    // 一个atom craf project 有一个acf，有多对acb,awb
    void AdxAudio::loadAcfData(std::string acfFileName)
    {
        /* ACF */
        criAtomEx_RegisterAcfFile(NULL, cricocos2d::getResourcePath(acfFileName.c_str()).c_str(), NULL, 0);
    }
    
    void AdxAudio::addAcbAwbData(std::string acbFileName, std::string awbFileName, std::string key)
    {
        /* ACB, AWB */
        if (atomExAcbHnMap.find(key) == atomExAcbHnMap.end())
        {
            CriAtomExAcbHn tpmAtomExAcbHn = criAtomExAcb_LoadAcbFile(
                    NULL, cricocos2d::getResourcePath(acbFileName.c_str()).c_str(),
                    NULL, cricocos2d::getResourcePath(awbFileName.c_str()).c_str(),
                    NULL, 0
            );
            atomExAcbHnMap[key] = tpmAtomExAcbHn;
        }
        else
        {
            CCLOG("error: addAcbAwbData already has cached the acb  %s", key.c_str());
#if CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID && defined(COCOS2D_DEBUG) && COCOS2D_DEBUG > 0
            std::string msgBoxStr = "已经缓存相同key值数据:" + key;
            cocos2d::MessageBox(msgBoxStr.c_str(), "CRIWARE ERROR");
#endif
        }
    }
    
    void AdxAudio::addAcbData(std::string acbFileName, std::string key)
    {
        if (atomExAcbHnMap.find(key) == atomExAcbHnMap.end())
        {
            CriAtomExAcbHn tpmAtomExAcbHn = criAtomExAcb_LoadAcbFile(
                    NULL, cricocos2d::getResourcePath(acbFileName.c_str()).c_str(),
                    NULL, NULL, NULL, 0
            );
            atomExAcbHnMap[key] = tpmAtomExAcbHn;
        }
        else
        {
            CCLOG("error: addAcbData already has cached the acb  %s", key.c_str());
#if CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID && defined(COCOS2D_DEBUG) && COCOS2D_DEBUG > 0
            std::string msgBoxStr = "已经缓存相同key值数据:" + key;
            cocos2d::MessageBox(msgBoxStr.c_str(), "CRIWARE ERROR");
#endif
        }
    }
    
    void AdxAudio::removeAcbData(std::string key)
    {
        auto iter = atomExAcbHnMap.find(key);
        if (iter == atomExAcbHnMap.end())
        {
            CCLOG("error: removeAcbData no the acb  %s", key.c_str());
            return;
        }
        
        criAtomExAcb_Release(iter->second);
        atomExAcbHnMap.erase(iter);
    }

    void AdxAudio::allocateVoicePool(bool isHcaMx)
    {
        /* [KEY POINT: 1 load data and allocate voice pool] */
        /*
         * 1. 读取样本中使用的ACF文件、ACB文件和AWB文件。
         *   另外，由于在本样本中播放的队列使用了标准语音和HCA-MX语音，生成标准语音池和HCA-MX语音池。
         *   为了抑制初始化所需的工作存储器使用量和处理负荷，语音池的最大语音数都设置为8(maxVoices)。
         */

        {
            /* 生成标准声音池 创建语音池是一个阻塞函数 */
            CriAtomExStandardVoicePoolConfig standardVoicePoolConfig;
            criAtomExVoicePool_SetDefaultConfigForStandardVoicePool(&standardVoicePoolConfig);
            standardVoicePoolConfig.num_voices						= _maxVoices;
            standardVoicePoolConfig.player_config.streaming_flag	= CRI_TRUE;
            standardVoicePoolConfig.player_config.max_sampling_rate = _standardMaxSamplingRate;
            standardVoicePoolConfig.identifier                      = _standardVPID;
            standardVoicePoolHn										= criAtomExVoicePool_AllocateStandardVoicePool(&standardVoicePoolConfig, NULL, 0);
        }

        // _isHcaMx = isHcaMx; // 不用hcaMX就不创建他的voicePool，节省内存
        _isHcaMx = false;
        if(_isHcaMx)
        {
            /* HCA-MX 创建语音池是一个阻塞函数 */
            CriAtomExHcaMxVoicePoolConfig hcaMxVoicePoolConfig;
            criAtomExVoicePool_SetDefaultConfigForHcaMxVoicePool(&hcaMxVoicePoolConfig);
            hcaMxVoicePoolConfig.num_voices						 = _maxVoices;
            hcaMxVoicePoolConfig.player_config.streaming_flag	 = CRI_TRUE;
            hcaMxVoicePoolConfig.player_config.max_sampling_rate = _hcaMxMaxSamplingRate;
            hcaMxVoicePoolConfig.identifier                      =  _hcamxVPID;
            hcaMxVoicePoolHn									 = criAtomExVoicePool_AllocateHcaMxVoicePool(&hcaMxVoicePoolConfig, NULL, 0);
        }
        
        // 添加一个控制播放速度的池子，所有对应的player需要挂在这个池子上
        {
            CriAtomExStandardVoicePoolConfig standardVoicePoolConfig;
            criAtomExVoicePool_SetDefaultConfigForStandardVoicePool(&standardVoicePoolConfig);
            standardVoicePoolConfig.num_voices                        = _maxVoices;
            standardVoicePoolConfig.player_config.streaming_flag      = CRI_TRUE;
            standardVoicePoolConfig.player_config.max_sampling_rate   = _standardMaxSamplingRate;
            standardVoicePoolConfig.identifier                        = _speedVPID;
            speedVoicePoolHn                                       = criAtomExVoicePool_AllocateStandardVoicePool(&standardVoicePoolConfig, NULL, 0);
            
            CriAtomExDspTimeStretchConfig dtsconfig;
            criAtomExVoicePool_SetDefaultConfigForDspTimeStretch(&dtsconfig);
            dtsconfig.num_dsp = _maxVoices;
            // dtsconfig.max_channels = 2;
            dtsconfig.max_sampling_rate = _standardMaxSamplingRate;
            dtsconfig.specific.reserved = 0;
            criAtomExVoicePool_AttachDspTimeStretch(speedVoicePoolHn, &dtsconfig, NULL, 0);
        }
        /* [KEY POINT: 1 load data and allocate voice pool] */
        
        /* [KEY POINT: 2 create player] 2. 生成播放器。*/
        {
//            criAtomExPlayer_SetDefaultConfig(&playerConfig);
//            atomExPlayerHn = criAtomExPlayer_Create(&playerConfig, NULL, 0);
        }
    }

    void AdxAudio::unloadDataAndFreeVoicePool()
    {
        /* 释放DSP总线混音设置 */
        criAtomEx_DetachDspBusSetting();

        /* [KEY POINT: 5 unload data and free voice pool] */
        {
            /* 语音池的释放 */
            criAtomExVoicePool_Free(standardVoicePoolHn);
            criAtomExVoicePool_Free(speedVoicePoolHn);
            if (_isHcaMx)
            {
                criAtomExVoicePool_Free(hcaMxVoicePoolHn);
            }
            
            /* 释放ACF文件、ACB文件、AWB文件 */
            criAtomExAcb_ReleaseAll();

            /* ACF 释放 */
            criAtomEx_UnregisterAcf();
        }
        /* [KEY POINT: 5 unload data and free voice pool] */
    }
    
    // 获取播放器所占内存
    int AdxAudio::getPlayerWorkSize()
    {
        return criAtomExPlayer_CalculateWorkSize(&playerConfig);
    }
    
    int AdxAudio::getNumCues(std::string key)
    {
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return -1;
        }
        auto numCues = criAtomExAcb_GetNumCues(atomExAcbHnMap[key]);
        return (int)numCues;
    }
    
    // 获取当前音效播放的时长
    long long AdxAudio::getPlayerTime(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return -1;
        }
        return criAtomExPlayer_GetTime(atomExPlayerHnMap[playerName]);
    }
    
    std::string AdxAudio::getCueInfoNameByIndex(int index, std::string key)
    {
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return "";
        }
        CriAtomExCueInfo cueInfo;
        criAtomExAcb_GetCueInfoByIndex(atomExAcbHnMap[key], index, &cueInfo);
        return cueInfo.name;
    }
    
    int AdxAudio::getCueInfoIdByIndex(int index, std::string key)
    {
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return -1;
        }
        CriAtomExCueInfo cueInfo;
        criAtomExAcb_GetCueInfoByIndex(atomExAcbHnMap[key], index, &cueInfo);
        return cueInfo.id;
    }
    
    long long AdxAudio::getCueInfoLengthByName(std::string name, std::string key)
    {
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return -1;
        }
        
//        CriAtomExCueInfo cueInfo;
//        criAtomExAcb_GetCueInfoByName(atomExAcbHnMap[key], name.c_str(), &cueInfo);
//        return cueInfo.length;
//        criAtomExPlayer_LimitLoopCount(atomExPlayerHnMap[""], 1); // 循环播放函数只能给有循环点的音效
//        criAtomExPlayer_LimitLoopCount(player, CRIATOMEXPLAYER_FORCE_LOOP); 
        return criAtomExAcb_GetLengthByName(atomExAcbHnMap[key], name.c_str());
    }
    
    // 获取播放器状态
    int AdxAudio::getPlayerStatus(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return -1;
        }
        return criAtomExPlayer_GetStatus(atomExPlayerHnMap[playerName]);
    }
    
    // 更改声音的播放速度
       void AdxAudio::changeSpeed(std::string playerName, float speedRate)
       {
           if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
               return;
           }
           criAtomExPlayer_SetDspParameter(atomExPlayerHnMap[playerName], CRIATOMDSP_TIMESTRETCH_PARAM_RATIO, 1.0f / speedRate);

           criAtomExPlayer_SetPlaybackRatio(atomExPlayerHnMap[playerName], speedRate);
           playerUpdateAll(playerName);
       }
       
       // 更改从声音中间时间段开始播放，startTime 毫秒
       void AdxAudio::setPlayerStartTime(std::string playerName, long long startTime)
       {
           if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
               return;
           }
           criAtomExPlayer_SetStartTime(atomExPlayerHnMap[playerName], startTime);
           playerUpdateAll(playerName);
       }

    /* [KEY POINT: 3 startCue] */
    /*
     * 3. 播放指定ID的队列。播放指定ID的队列
     正常使用的流程：
     setCueId等
     playerSetVolume
     playerStart
     */
    void AdxAudio::setCueId(std::string playerName, signed int cueId, std::string key)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return;
        }
        criAtomExPlayer_SetCueId(atomExPlayerHnMap[playerName], atomExAcbHnMap[key], (CriAtomExCueId)cueId);
    }

    void AdxAudio::setCueName(std::string playerName, std::string cueName, std::string key)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return;
        }
        criAtomExPlayer_SetCueName(atomExPlayerHnMap[playerName], atomExAcbHnMap[key], cueName.c_str());
    }

    void AdxAudio::setCueIndex(std::string playerName, signed int index, std::string key)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        if (key == "" || atomExAcbHnMap.find(key) == atomExAcbHnMap.end()){
            return;
        }
        criAtomExPlayer_SetCueIndex(atomExPlayerHnMap[playerName], atomExAcbHnMap[key], index);
    }
    
    int AdxAudio::getLastPlaybackId(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return -1;
        }
        return (int)criAtomExPlayer_GetLastPlaybackId(atomExPlayerHnMap[playerName]);
    }
    
    // 开始播放
    int AdxAudio::playerStart(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return -1;
        }
        int playbackId = criAtomExPlayer_Start(atomExPlayerHnMap[playerName]);
        return playbackId;
    }
    
    // 播放器更新数据,比如设置音量后
    void AdxAudio::playerUpdate(std::string playerName, int playbackId)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_Update(atomExPlayerHnMap[playerName], (CriAtomExPlaybackId)playbackId);
    }
    
    // 播放器更新数据,比如设置音量后
    void AdxAudio::playerUpdateAll(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_UpdateAll(atomExPlayerHnMap[playerName]);
    }
    
    // 准备音效
    int AdxAudio::playerPrepare(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return -1;
        }
        int playbackId = criAtomExPlayer_Prepare(atomExPlayerHnMap[playerName]);
        return playbackId;
    }
    
    
    // 设置player的循环状态
    // 第二个参数可以设置成这样：
    // CRIATOMEXPLAYER_NO_LOOP_LIMITATION -1 本身素材设置成循环的话，调用setPlayerLoopState限制了数量后，如果想恢复无限循环，只需将 count 参数指定为此
    // CRIATOMEXPLAYER_IGNORE_LOOP -2  忽略素材循环点，只播一次
    // CRIATOMEXPLAYER_FORCE_LOOP  -3  强制循环
    void AdxAudio::setPlayerLoopState(std::string playerName, int count)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_LimitLoopCount(atomExPlayerHnMap[playerName], count);
    }

    // 设置播放器音量，在开始播放前设置，或者播放后使用 playerUpdate, playerUpdateAll
    void AdxAudio::setPlayerVolume(std::string playerName, float vol)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetVolume(atomExPlayerHnMap[playerName], vol);
    }
    
    // 更改音调
    void AdxAudio::setPlayerPitch(std::string playerName, float offset)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetPitch(atomExPlayerHnMap[playerName], offset);
    }
    
    // 设置3d音源角度，正前方是0，右方是正度数，左方是负度数
    void AdxAudio::setPlayerPan3dAngle(std::string playerName, float angle)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetPan3dAngle(atomExPlayerHnMap[playerName], angle);
    }
    
    // 设置3d音源距离
    void AdxAudio::setplayerPan3dDistance(std::string playerName, float distance)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetPan3dInteriorDistance(atomExPlayerHnMap[playerName], distance);
    }
    
    // 设置3d类型（在3d音源，监听者设置的时候需要先这是3d环境声明）
    void AdxAudio::setPan3dType(std::string playerName, int type)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetPanType(atomExPlayerHnMap[playerName], (CriAtomExPanType)type);
    }
    
    // 创建3d音效监听者（耳朵）
    void AdxAudio::create3dListener(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        atomEx3dListener = criAtomEx3dListener_Create(NULL, NULL, 0);
        criAtomExPlayer_Set3dListenerHn(atomExPlayerHnMap[playerName], atomEx3dListener);
    }
    
    // 创建3d音源（包含位置信息）
    void AdxAudio::create3dSource(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        atomEx3dSource = criAtomEx3dSource_Create(NULL, NULL, 0);
        criAtomExPlayer_Set3dSourceHn(atomExPlayerHnMap[playerName], atomEx3dSource);
    }
    
    void AdxAudio::set3dListenerPos(float x, float y, float z)
    {
        CriAtomExVector pos;
        pos.x = x;
        pos.y = y;
        pos.z = z;
        criAtomEx3dListener_SetPosition(atomEx3dListener, &pos);
        criAtomEx3dListener_Update(atomEx3dListener);
    }
    
    void AdxAudio::set3dSourcePos(float x, float y, float z)
    {
        CriAtomExVector pos;
        pos.x = x;
        pos.y = y;
        pos.z = z;
        criAtomEx3dSource_SetPosition(atomEx3dSource, &pos);
    }
    
    // 设置音源最小最大的距离
    void AdxAudio::set3dSourceMinMaxDistance(float min, float max)
    {
        criAtomEx3dSource_SetMinMaxDistance(atomEx3dSource, min, max);
        criAtomEx3dSource_Update(atomEx3dSource);
    }
    
    void AdxAudio::destroy3dListener()
    {
        criAtomEx3dListener_Destroy(atomEx3dListener);
    }
    
    void AdxAudio::destroy3dSource()
    {
        criAtomEx3dSource_Destroy(atomEx3dSource);
    }
    
    // 设置声音低通滤波（公主被装在盒子里呼喊）
    void AdxAudio::setPlayerBiquadFilter(std::string playerName, int type, float frequency, float gain, float q)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetBiquadFilterParameters(atomExPlayerHnMap[playerName], (CriAtomExBiquadFilterType)type, frequency, gain, q);
    }
    
    void AdxAudio::setPlayerBandpassFilter(std::string playerName, float min, float max)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetBandpassFilterParameters(atomExPlayerHnMap[playerName], min, max);
    }
    
    // 清除滤波器
    void AdxAudio::resetPlayerFilter(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_ResetParameters(atomExPlayerHnMap[playerName]);
    }
    
    // 控制音效由远及近
    // param 0.0f ~ 1.0f
    void AdxAudio::setPlayerAisacById(std::string playerName, int controlId, float param)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetAisacControlById(atomExPlayerHnMap[playerName], controlId, param);
    }
    
    void AdxAudio::setPlayerAisacByName(std::string playerName, std::string controlName, float param)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetAisacControlByName(atomExPlayerHnMap[playerName], controlName.c_str(), param);
    }
    
    // 清除aisac设置
    void AdxAudio::resetPlayerAisac(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_ClearAisacControls(atomExPlayerHnMap[playerName]);
    }
    
    // 初始化 fader
    void AdxAudio::attatchPlayerFader(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_AttachFader(atomExPlayerHnMap[playerName], NULL, NULL, 0);
    }
    
    // 音效渐入
    // time 毫秒
    void AdxAudio::setPlayerFadeInTime(std::string playerName, float time)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetFadeInTime(atomExPlayerHnMap[playerName], time);
    }
    
    // 音效渐出
    // time 毫秒
    void AdxAudio::setPlayerFadeOutTime(std::string playerName, float time)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetFadeOutTime(atomExPlayerHnMap[playerName], time);
    }
    
    // 取消音效渐入渐出效果
    void AdxAudio::detachPlayerFader(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_DetachFader(atomExPlayerHnMap[playerName]);
    }
    
    // 开启混音（因为混音比较消耗cpu性能）
    void AdxAudio::startDspBus(std::string busName)
    {
        /* 因为有使用DSP总线混音效果的队列，所以附加DSP总线设置 */
        const CriChar8* dspBusSettingName = busName.c_str();
        criAtomEx_AttachDspBusSetting(dspBusSettingName, NULL, 0);
    }
    
    // 关闭混音
    void AdxAudio::stopDspBus()
    {
        /* 释放DSP总线混音设置 */
        criAtomEx_DetachDspBusSetting();
    }
    
    // 设置混音
    void AdxAudio::setPlayerDspBusLevel(std::string playerName, std::string busName, float level)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetBusSendLevelByName(atomExPlayerHnMap[playerName], busName.c_str(), level);
    }
    
    // 为某个音效添加分类
    void AdxAudio::setPlayerCategoryByName(std::string playerName, std::string categoryName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_SetCategoryByName(atomExPlayerHnMap[playerName], categoryName.c_str());
    }
    
    // 为某个音效取消分类
    void AdxAudio::unsetPlayerCategory(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_UnsetCategory(atomExPlayerHnMap[playerName]);
    }
    
    void AdxAudio::setCategoryVolumeByName(std::string categoryName, float vol)
    {
        criAtomExCategory_SetVolumeByName(categoryName.c_str(), vol);
    }
    
    float AdxAudio::getCategoryVolumeByName(std::string categoryName)
    {
        return criAtomExCategory_GetVolumeByName(categoryName.c_str());
    }
    
    // 设置分类是否静音
    void AdxAudio::setCategoryMuteByName(std::string categoryName, bool isMute)
    {
        if (isMute) {
            criAtomExCategory_MuteByName(categoryName.c_str(), CRI_TRUE);
        }else{
            criAtomExCategory_MuteByName(categoryName.c_str(), CRI_FALSE);
        }
    }
    
    // 某分类是否静音
    bool AdxAudio::isCategoryMuteByName(std::string categoryName){
        CriBool isMute = criAtomExCategory_IsMutedByName(categoryName.c_str());
        return (bool)isMute;
    }
    
    // 独奏给定分类的声音
    void AdxAudio::setCategorySoloByName(std::string categoryName, bool isSolo, float muteVol)
    {
        if (isSolo) {
            criAtomExCategory_SoloByName(categoryName.c_str(), CRI_TRUE, muteVol);
        } else {
            criAtomExCategory_SoloByName(categoryName.c_str(), CRI_FALSE, muteVol);
        }
    }
    
    // 某分类是否独奏
    bool AdxAudio::isCategorySoloByName(std::string categoryName)
    {
        CriBool isSolo = criAtomExCategory_IsSoloedByName(categoryName.c_str());
        return (bool)isSolo;
    }
    
    int AdxAudio::getPlaybackStatus(int playbackId)
    {
        return criAtomExPlayback_GetStatus((CriAtomExPlaybackId)playbackId);
    }
    
    long long AdxAudio::getPlayBackTime(int playbackId)
    {
        auto time = criAtomExPlayback_GetTime((CriAtomExPlaybackId)playbackId);
        return time;
    }
    
    int AdxAudio::getStandardNumUsedVoices()
    {
        CriSint32 voiceNum;
        CriSint32 voiceNumLimit;
        criAtomExVoicePool_GetNumUsedVoices(standardVoicePoolHn, &voiceNum, &voiceNumLimit);
        return voiceNum;
    }
    
    int AdxAudio::getHCANumUsedVoices()
    {
        CriSint32 voiceNum;
        CriSint32 voiceNumLimit;
        criAtomExVoicePool_GetNumUsedVoices(hcaMxVoicePoolHn, &voiceNum, &voiceNumLimit);
        return voiceNum;
    }
    
    void AdxAudio::stopCueByPlaybackId(int playbackId)
    {
        criAtomExPlayback_Stop((CriAtomExPlaybackId)playbackId);
    }
    
    void AdxAudio::stopPlayer(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_Stop(atomExPlayerHnMap[playerName]);
    }
    
    // true是暂停 false恢复
    void AdxAudio::pauseCueByPlaybackId(int playbackId, bool isPause)
    {
        if (isPause)
        {
            criAtomExPlayback_Pause((CriAtomExPlaybackId)playbackId, CRI_TRUE);
        }
        else
        {
            criAtomExPlayback_Pause((CriAtomExPlaybackId)playbackId, CRI_FALSE);
        }
    }
    // true是暂停 false恢复
    void AdxAudio::pausePlayer(std::string playerName, bool isPause)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        if (isPause)
        {
            criAtomExPlayer_Pause(atomExPlayerHnMap[playerName], CRI_TRUE);
        }
        else
        {
            criAtomExPlayer_Pause(atomExPlayerHnMap[playerName], CRI_FALSE);
        }
    }
    
    // 可恢复由 ::criAtomExPlayer_Pause 或 ::criAtomExPlayback_Pause 函数暂停的音频的播放
    void AdxAudio::resumeCueByPlaybackId(int playbackId)
    {
        criAtomExPlayback_Resume((CriAtomExPlaybackId)playbackId, CRIATOMEX_RESUME_PAUSED_PLAYBACK);
    }
    // 暂定，回头完善
    void AdxAudio::resumePlayer(std::string playerName)
    {
        if (playerName == "" || atomExPlayerHnMap.find(playerName) == atomExPlayerHnMap.end()){
            return;
        }
        criAtomExPlayer_Resume(atomExPlayerHnMap[playerName], CRIATOMEX_RESUME_PAUSED_PLAYBACK);
    }
    
    void AdxAudio::startDbas()
    {
        // D-BAS是负责管理播放器的流缓冲区的系统。流缓冲区被划分成块，D-BAS根据需要将其分配给各种播放器。
        dbas_id = criAtomDbas_Create(NULL, NULL, 0);
    }
    
	}
}
