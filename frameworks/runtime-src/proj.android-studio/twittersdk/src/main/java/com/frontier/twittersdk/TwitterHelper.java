package com.frontier.twittersdk;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.AsyncTask;
import android.util.Log;

import com.frontier.sdkbase.AbstractAccountImpl;
import com.frontier.sdkbase.ErrorCode;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKType;

import twitter4j.Twitter;
import twitter4j.TwitterException;
import twitter4j.TwitterFactory;
import twitter4j.User;
import twitter4j.auth.AccessToken;
import twitter4j.auth.RequestToken;
import twitter4j.conf.ConfigurationBuilder;

public class TwitterHelper extends AbstractAccountImpl {

    private static final String TAG = "TwitterHelper";

    private String consumerKey = "*************************";
    private String consumerSecret = "WcbMMCyWVXD1n42NWeCbl46fI8YxJbs1GnCdrlDfHsWABoY3hy";
    private Twitter twitter;
    private RequestToken requestToken;
    private String verifier;

    private String oauth_token;

    private String uid;
    private String name;
    private String email;
    private boolean isLogin = false;

    private boolean requestLogin = false;

    public TwitterHelper(Activity activity, EventListener listener) {
        super(activity, listener);

    }

    @Override
    public void login(int sdkType) {
        if(isLogin) {
            listener.onLogin(sdkType(), ErrorCode.NO_ERROR, uid, name, "", "", "", null, -1);
        } else {
            requestLogin = true;
            ConfigurationBuilder cb = new ConfigurationBuilder();
            cb.setDebugEnabled(true)
                    .setOAuthConsumerKey(consumerKey)
                    .setOAuthConsumerSecret(consumerSecret)
                    .setIncludeEmailEnabled(true);
            TwitterFactory tf = new TwitterFactory(cb.build());
            twitter = tf.getInstance();
            GetOAuthTokenTask task = new GetOAuthTokenTask();
            task.execute();
        }
    }

    @Override
    public void logout(int sdkType) {
        isLogin = false;
        uid = "";
        name = "";
        email = "";
    }

    @Override
    public void onNewIntent(Intent intent) {
        if(requestLogin) {
            requestLogin = false;
            if(intent != null) {
                Uri uri = intent.getData();
                if(uri == null) {
                    listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, -1);
                    return;
                }
                oauth_token = uri.getQueryParameter("oauth_token");
                verifier = uri.getQueryParameter("oauth_verifier");

                if(oauth_token != null && verifier != null) {
                    AccessTokenAsyncTask task = new AccessTokenAsyncTask();
                    task.execute();
                } else {
                    listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, -1);
                }
            } else {
                listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, -1);
            }
        }
    }

    @Override
    public void onResume() {
        if(requestLogin) {
            requestLogin = false;
            listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, -1);
        }
    }

    @Override
    public boolean isLogin() {
        return isLogin;
    }

    @Override
    public String getID() {
        return uid;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getToken() {
        return null;
    }

    @Override
    public int sdkType() {
        return SDKType.TWITTER;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == SDKType.TWITTER;
    }

    private class GetOAuthTokenTask extends AsyncTask<Void, Void, RequestToken> {

        @Override
        protected RequestToken doInBackground(Void... voids) {
            try {
                return twitter.getOAuthRequestToken("twitterkit-*************************://");
            } catch (TwitterException e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(RequestToken requestToken) {
            if(requestToken != null) {
                TwitterHelper.this.requestToken = requestToken;
                // activity.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(requestToken.getAuthenticationURL())));
                // resolveActivity() 查询设备上安装的应用，判断是否有 Activity 能够响应指定的 Intent
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(requestToken.getAuthenticationURL()));
                if (intent.resolveActivity(activity.getPackageManager()) != null) {
                    activity.startActivity(intent);
                }
            } else {
                listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, -1);
            }
        }
    }

    private class AccessTokenAsyncTask extends AsyncTask<Void, Void, AccessToken> {

        @Override
        protected AccessToken doInBackground(Void... voids) {
            try {
                AccessToken accessToken = twitter.getOAuthAccessToken(requestToken, verifier);
                try {
                    User user = twitter.verifyCredentials();
                    email = user.getEmail();
                } catch (TwitterException e) {
                    e.printStackTrace();
                }
                return accessToken;
            } catch (TwitterException e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(AccessToken accessToken) {
            if(accessToken != null) {
                isLogin = true;
                uid = String.valueOf(accessToken.getUserId());
                name = accessToken.getScreenName();
                listener.onLogin(sdkType(), ErrorCode.NO_ERROR, uid, name, "", "", email, null, -1);
            } else {
                listener.onLogin(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", "", null, -1);
            }
        }
    }
}
