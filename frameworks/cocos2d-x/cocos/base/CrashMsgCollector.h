#ifndef CRASH_MSG_COLLECTOR
#define CRASH_MSG_COLLECTOR

#include <map>
#include <string>
#include <mutex>

class CrashMsgCollector {
public:
	static CrashMsgCollector *getInstance();
	void init() {}
	void release() {
		if(ins) {
			delete ins;
			ins = 0;
		}
	}
	void addMsg(const std::string key, const char *value);
	void addMsg(const std::string key, const std::string value);
	void addMsg(const std::string key, const int value);
	void addMsg(const std::string key, const unsigned int value);
	void addMsg(const std::string key, const float value);
	void addMsg(const std::string key, bool value);
	void removeMsg(const std::string key);
	void addMsg(const std::string key, const unsigned char *rawData, const int len);
	std::string getInterface0();
	void clear();
	std::string toString();
	void testCrash();

	void clearAssetManagerMsg();
	void pushAssetManagerMsg(std::string key, int value);
	void printAssetManagerMsg();

	// 耗时统计相关接口,开始和结束必须成对出现。必须在GL线程调用
	// step可选，可以在一个过程当中插入多个step，标记过程
	void timeConsumeStart(const std::string& key);
	void timeConsumeStep(const std::string& key, const std::string& step);
	void timeConsumeEnd(const std::string& key);

	static const std::string ON_FINISH_SUCCESS;
	static const std::string ON_FINISH_SUCCESS_2;
    static const std::string NATIVE_INIT;
	static const std::string SPRITE_3D;
	static const std::string HTTP_CLIENT;
	static const std::string HTTP_CLIENT_2;
	static const std::string ASSETS_SUCCESS;
	static const std::string ASSETS_SUCCESS_2;
	static const std::string ASSETS_SUCCESS_3;
	static const std::string ASSETS_SUCCESS_4;
	static const std::string ASSETS_SUCCESS_5;
	static const std::string ASSETS_SUCCESS_6;
	static const std::string BOLE_SOCKET;
	static const std::string SPRITE_FRAMES;
	static const std::string SPRITE_FRAMES_RE;
	static const std::string IMAGE_ASYNC;

private:
	CrashMsgCollector();
	typedef std::chrono::steady_clock::time_point TimePoint;
	struct TimeConsumeStruct;

private:
	static CrashMsgCollector *ins;
	std::map<std::string, std::string> msgs;
	std::mutex lock;
	int interface_index;

	std::map<std::string, int> assetManagerMap;
	std::map<std::string, TimeConsumeStruct*> timeConsumeMap;
};

#endif // CRASH_MSG_COLLECTOR