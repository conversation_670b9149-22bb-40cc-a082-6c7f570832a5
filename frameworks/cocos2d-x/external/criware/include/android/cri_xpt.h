﻿/****************************************************************************
 *
 * CRI Middleware SDK
 *
 * Copyright (c) 2010-2010 CRI Middleware Co.,Ltd.
 *
 * Library  : CRI Middleware Library
 * Module   : CRI Common Header for Android
 * File     : cri_xpt.h
 * Date     : 2010-10-07
 * Version  : 1.01
 *
 ****************************************************************************/

/****************************************************************************
 *
 ****************************************************************************/

#ifndef CRI_XPT_H
#define CRI_XPT_H

#define XPT_TGT_ANDROID
#define XPT_CCS_LEND

#if defined(__arm__)
    #if defined(__ARM_ARCH_7A__)
	#define CRI_TARGET_STR "Android_ARMv7A"
	#endif
#elif defined(__i386__)
	#define CRI_TARGET_STR "Android_x86"
#elif defined(__aarch64__)
	#define CRI_TARGET_STR "Android_ARMv8A"
#elif defined(__x86_64__)
	#define CRI_TARGET_STR "Android_x86_64"
#else
	#error unsupported architecture
#endif

#include "cri_xpts_android.h"

#define XPT_DISABLE_FSV1API

#include "cri_xpt_post.h"

#endif  /* CRI_XPT_H */
/* End Of File */
