//
//  DialogNodeButtonPay.cpp
//  cocos2d_libs
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/4/17.
//

#include "../Dialog.h"
#include "../DialogHelper.h"
#include "DialogNodeButtonPay.h"
#include "sea/common/FunctionList.h"

namespace sea {
DialogNodeButtonPay::DialogNodeButtonPay(const cocos2d::ValueMap& cfg, Dialog *dialog, const cocos2d::ValueMap& params) :
BoleButtonPay(),
DialogNodeBase(cfg, dialog, this, params) {
    this->setSeaUserData("type", cocos2d::Value(NodeType::BUTTON_PAY));
}

DialogNodeButtonPay::~DialogNodeButtonPay() {
}

DialogNodeButtonPay * DialogNodeButtonPay::create(const cocos2d::ValueMap& cfg, Dialog *dialog, const cocos2d::ValueMap& params) {
    DialogNodeButtonPay *ret = new (std::nothrow) DialogNodeButtonPay(cfg, dialog, params);
    
    if (ret && ret->init()) {
        ret->autorelease();
    } else {
        CC_SAFE_DELETE(ret);
    }
    
    return ret;
}

void DialogNodeButtonPay::onExit() {
    DialogNodeBase::doExit();
    BoleButton::onExit();
}

void DialogNodeButtonPay::onEnter() {
    DialogNodeBase::doEnter();
    BoleButton::onEnter();
}

void DialogNodeButtonPay::addClickEventNoMove(ADD_CLICK_EVENT_FUN fun, const cocos2d::Value& params /* = cocos2d::Value::Null*/, float autoDisable /* = -1*/) {
    auto dialog = this->getDialog();
    
    if (dialog) {
        dialog->addClickEventNoMove(this, [ = ](cocos2d::ui::Widget *, const cocos2d::Value& _params) {
            if (fun) {
                fun(this, _params);
            }
            
            bool mute = sea::getBoolFromMap(_params, "mute");
            if (!mute) {
                sea::Global::playBtnClickSound();
            }
            
            bool bright = sea::getBoolFromMap(_params, "bright");
            
            if (autoDisable > 0) {
                if (bright) {
                    this->setTouchEnabled(false);
                } else {
                    this->setTouchEnabledWithBright(false);
                }
                
                sea::createDelayAction(this, [ = ](cocos2d::Node *) {
                    if (bright) {
                        this->setTouchEnabled(true);
                    } else {
                        this->setTouchEnabledWithBright(true);
                    }
                    
                    this->removeLoading();
                }, autoDisable);
            }
        });
    }
}

bool DialogNodeButtonPay::init() {
    auto cfg = this->getConfig();
    
    cocos2d::ValueMap params;
    FunctionList<cocos2d::Value> preFunList;
    FunctionList<cocos2d::Value> nodeFunList;
    FunctionList<std::pair<std::string, cocos2d::Value>> funElseParamList;
    
    std::string key;
    sea::IntBool activity = sea::IntBool::None;
    std::string themeId = "";
    sea::IntBool multi = sea::IntBool::None;
    sea::IntBool vertical = sea::IntBool::None;
    std::string res;
    sea::TextureResType resType = sea::TextureResType::LOCAL;
    
    for (const auto& iter : cfg) {
        auto k = iter.first;
        auto v = iter.second;
        
        if (k == "key") {
            if (v.getType() == ccValueType::STRING) {
                key = v.asString();
            }
            nodeFunList.push([&](cocos2d::Value _v){
                doCommonKeyForAnyNode("key", _v, this);
            }, v);
        }
        else if (k == "swallow") {
            nodeFunList.push([&](cocos2d::Value _v){
                if (_v.getType() == ccValueType::BOOLEAN) {
                    this->setSwallowTouches(_v.asBool());
                }
            }, v);
        }
        else if (k == "activity") {
            activity = sea::getIntBoolFromCCValue(v);
        }
        else if (k == "id") {
            if (v.getType() == ccValueType::STRING) {
                themeId = v.asString();
            }
        }
        else if (k == "res_type") {
            resType = sea::getTextureResTypeFromCCValue(v);
        }
        else if (k == "res") {
            if (v.getType() == ccValueType::STRING) {
                res = v.asString();
            }
        }
        else if (k == "multi") {
            multi = sea::getIntBoolFromCCValue(v);
        }
        else if (k == "vertical") {
            vertical = sea::getIntBoolFromCCValue(v);
        }
        else if (k == "buy_data" || k == "extra_args" || k == "where_tag" || k == "fun_click" || k == "fun_end" || k == "no_move" || k == "sale_space" || k == "width" || k == "text_pos" || k == "async_load") {
            params[k] = v;
        }
        else if (k == "normal" || k == "bucks") {
            if (v.isTable()) {
                preFunList.push([&](cocos2d::Value _data){
                    if (_data.getType() != ccValueType::MAP) {
                        return;
                    }
                    auto _dataMap = _data.asValueMap();
                    cocos2d::Value _v = sea::getValueFromMap(_dataMap, "data");
                    std::string _key = sea::getStringFromMap(_dataMap, "key");
                    
                    cocos2d::ValueVector vector;
                    auto arr = sea::getVectorFromCCValue(_v, 0);
                    for (int i = 0; i < arr.size(); ++i) {
                        auto item = arr[i];
                        auto iType = item.getType();
                        if (iType == ccValueType::MAP) {
                            auto _map = item.asValueMap();
                            sea::IntBool _multi = multi;
                            sea::IntBool _vertical = vertical;
                            std::string _res = res;
                            
                            cocos2d::Value path_abs, path_abs_mv, path;
                            for (const auto& _iter : _map) {
                                auto kk = _iter.first;
                                auto vv = _iter.second;
                                if (kk == "path_abs") {
                                    path_abs = vv;
                                }
                                else if (kk == "path_abs_mv") {
                                    path_abs_mv = vv;
                                }
                                else if (kk == "path") {
                                    path = vv;
                                }
                                else if (kk == "multi") {
                                    _multi = sea::getIntBoolFromCCValue(vv);
                                }
                                else if (kk == "vertical") {
                                    _vertical = sea::getIntBoolFromCCValue(vv);
                                }
                                else if (kk == "res") {
                                    _res = vv.asString();
                                }
                            }
                            
                            if (!path_abs.isNull()) {
                                vector.push_back(this->getFunPath(path_abs, nullptr, &cfg));
                            }
                            else if (!path_abs_mv.isNull()) {
                                auto p = this->getFunPath(path_abs_mv, nullptr, &cfg);
                                if (p.getType() == ccValueType::STRING) {
                                    auto p2 = this->getImgPath(p.asString(), _multi, _vertical, resType, true, _res, activity, themeId);
                                    vector.push_back(cocos2d::Value(p2));
                                }
                                else {
                                    vector.push_back(cocos2d::Value(""));
                                }
                            }
                            else if (!path.isNull()) {
                                auto p = this->getFunPath(path_abs_mv, nullptr, &cfg);
                                if (p.getType() == ccValueType::STRING) {
                                    auto p2 = this->getImgPath(p.asString(), _multi, _vertical, resType, false, _res, activity, themeId);
                                    vector.push_back(cocos2d::Value(p2));
                                }
                                else {
                                    vector.push_back(cocos2d::Value(""));
                                }
                            }
                            else {
                                vector.push_back(cocos2d::Value(""));
                            }
                        }
                        if (iType == ccValueType::STRING) {
                            auto p = this->getImgPath(item.asString(), multi, vertical, resType, false, res, activity, themeId);
                            vector.push_back(cocos2d::Value(p));
                        }
                        else {
                            vector.push_back(cocos2d::Value(""));
                        }
                    }
                    params[_key] = vector;
                }, cocos2d::Value(cocos2d::ValueMap({{"data", v}, {"key", cocos2d::Value(k) }})));
            }
            else {
                params[k] = v;
            }
        }
        else if (k == "text") {
            if (v.getType() == ccValueType::MAP) {
                preFunList.push([&](cocos2d::Value _v){
                    auto& map = _v.asValueMap();
                    for (auto& _iter : map) {
                        auto& item = _iter.second;
                        if (item.getType() != ccValueType::MAP) {
                            continue;
                        }
                        
                        FunctionList<cocos2d::Value> tempFunList;
                        auto& _map = item.asValueMap();
                        sea::IntBool _multi = sea::IntBool::None;
                        sea::IntBool _vertical = sea::IntBool::None;
                        sea::IntBool _activity = activity;
                        std::string _res = "";
                        std::string _id = themeId;
                        sea::TextureResType _resType = sea::TextureResType::LOCAL;
                        
                        
                        for (auto& _iter2 : _map) {
                            auto kkk = _iter2.first;
                            auto& vvvData = _iter2.second;
                            if (kkk == "path_abs") {
                                _map["path"] = getFunPath(vvvData, nullptr, &_map);
                            }
                            else if (kkk == "path_abs_mv") {
                                auto p = getFunPath(vvvData, nullptr, &_map);
                                std::string _path = p.getType() == ccValueType::STRING ? p.asString() : "";
                                tempFunList.push([&](cocos2d::Value _){
                                    _map["path"] = getImgPath(_path, _multi, _vertical, _resType, true, _res, _activity, _id);
                                }, vvvData);
                            }
                            else if (kkk == "path") {
                                auto p = getFunPath(vvvData, nullptr, &_map);
                                std::string _path = p.getType() == ccValueType::STRING ? p.asString() : "";
                                tempFunList.push([&](cocos2d::Value _){
                                    _map["path"] = getImgPath(_path, _multi, _vertical, _resType, false, _res, _activity, _id);
                                }, vvvData);
                            }
                            else if (kkk == "multi") {
                                _multi = sea::getIntBoolFromCCValue(vvvData);
                            }
                            else if (kkk == "vertical") {
                                _vertical = sea::getIntBoolFromCCValue(vvvData);
                            }
                            else if (kkk == "res_type") {
                                _resType = sea::getTextureResTypeFromCCValue(vvvData);
                            }
                            else if (kkk == "res") {
                                _res = vvvData.asString();
                            }
                            else if (kkk == "id") {
                                _id = vvvData.asString();
                            }
                            else if (kkk == "activity") {
                                _activity = sea::getIntBoolFromCCValue(vvvData);
                            }
                        }
                        tempFunList.run();
                    }
                    params["text"] = _v;
                }, v);
            }
            else {
                params[k] = v;
            }
        }
        else if (k == "clicked_fun") {
            bool mute = false;
            float autoDisable = -1;
            int fHandler = -1;
            auto vType = v.getType();
            
            if (vType != ccValueType::LUA_FUNCTION) {
                if (vType == ccValueType::MAP) {
                    auto m = v.asValueMap();
                    
                    if (m.find("fun") != m.end()) {
                        auto f = m.at("fun");
                        
                        if (f.getType() == ccValueType::LUA_FUNCTION) {
                            fHandler = f.asInt();
                            
                            if (m.find("mute") != m.end()) {
                                mute = m.at("mute").asBool();
                            }
                            
                            if (m.find("auto_disable") != m.end()) {
                                autoDisable = m.at("auto_disable").asFloat();
                            }
                        }
                    }
                }
            } else {
                fHandler = v.asInt();
            }

            
            if (fHandler > 0) {
                cocos2d::ValueMap p = { { "mute", cocos2d::Value(mute) } };
                this->addClickEventListenerData([ = ](BoleButton *btn, const cocos2d::Value& _m) {
                    sea::excuteLuaFunction(fHandler, 2, [ = ](cocos2d::LuaStack *stack) {
                        sea::pushValueToStack(stack, this->getDialog(), "sea.Dialog");
                        sea::pushValueToStack(stack, btn, "sea.BoleButton");
                    });
                }, cocos2d::Value(p), autoDisable);
            }
        } else {
            funElseParamList.push([=](std::pair<std::string, cocos2d::Value> item){
                this->doCommonKeyForAnyNode(item.first, item.second, this);
            }, {k, v});
        }
    }
    
    preFunList.run();
    initWithData(cocos2d::Value(params));
    funElseParamList.run();
    this->doCommonForAnyNode(cfg, this);
    nodeFunList.run();
    
    return true;
}
}
