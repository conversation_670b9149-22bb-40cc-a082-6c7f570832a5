/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/LibraryContext.h"

#include "cricocos2d/mana/detail/RendererResourceFactorySet.h"
#include "cricocos2d/detail/log.h"

#include "cri_xpt.h"
#if defined(XPT_TGT_ANDROID)
#    include <platform/android/jni/JniHelper.h>
#endif

#include "cricocos2d/expansions/sofdec2_vp9.h"
#if (CRICOCOS2D_EXPANSIONS_USE_SOFDEC2_VP9 > 0)
#include "expansion/sofdec2_codecs/cri_mana_vp9.h"
#endif

namespace cricocos2d
{
	namespace detail
	{
#if defined(XPT_TGT_IOS)
		std::shared_ptr<void> createPlatformSpecificResource(const cricocos2d::LibraryContext::Config& config);
#else
		namespace
		{
			inline
			std::shared_ptr<void> createPlatformSpecificResource(const cricocos2d::LibraryContext::Config& config)
			{
				return nullptr;
			}


		}
#endif

		bool libraryContextExists = false;
	}


	namespace mana
	{
		namespace detail
		{
#if defined(XPT_TGT_PC) || defined(XPT_TGT_MACOSX)
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactorySofdecPrimeGLES2();
#elif defined(XPT_TGT_IOS)
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactorySofdecPrimeGLES2();
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactoryH264Ios();
#elif defined(XPT_TGT_ANDROID)
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactorySofdecPrimeGLES2();
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactoryH264Android();
#endif
		}
	}
}


namespace cricocos2d
{
	class LibraryContext::Impl
	{
	public:
		Impl(const Impl&)			   = delete;
		Impl& operator = (const Impl&) = delete;

		explicit
		Impl(const Config& config)
			: enable_mana_(config.enable_mana)
			, platform_specific_resource_(detail::createPlatformSpecificResource(config))
			, event_listener_on_renderer_recreated_(
				cocos2d::EventListenerCustom::create(EVENT_RENDERER_RECREATED, [this](cocos2d::EventCustom* event_custom) { onRendererRecreated(); })
												   )
		{
			if (detail::libraryContextExists) {
				detail::printLog(detail::LogSeverity::Error, "::cricocos2d::LibraryContext already exists.");
			}
#if defined(XPT_TGT_PC)
			{
				criErr_SetCallback(config.error_callback_func);
				/* FileSystem */
				{
					criFs_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criFs_InitializeLibrary(&config.fs, nullptr, 0);
				}
				/* AtomEx */
				{
					criAtomEx_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criAtomEx_Initialize_WASAPI(&config.atomex, nullptr, 0);
				}
				/* Dbus */
				{
					dbas_id_ = criAtomExDbas_Create(&config.dbas, nullptr, 0);
				}
				/* Mana */
				if (enable_mana_) {
					/* RendererResourceFactory */
					{
						renderer_resource_factory_set_ = mana::detail::RendererResourceFactorySet::getInstance();
						renderer_resource_factory_set_->registerFactory(
							mana::detail::createRendererResourceFactorySofdecPrimeGLES2()
																	   );
					}

					criMana_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					SetupManaVP9(config.allocator);
					criMana_Initialize_PC(&config.mana, nullptr, 0);

					criMana_SetDecodeSkipFlag(CRI_FALSE);
				}
			}
#elif defined(XPT_TGT_MACOSX)
			{
				criErr_SetCallback(config.error_callback_func);
				/* FileSystem */
				{
					criFs_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criFs_InitializeLibrary(&config.fs, nullptr, 0);
				}
				/* AtomEx */
				{
					criAtomEx_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criAtomEx_Initialize_MACOSX(&config.atomex, nullptr, 0);
				}
				/* Dbus */
				{
					dbas_id_ = criAtomExDbas_Create(&config.dbas, nullptr, 0);
				}
				/* Mana */
				if (enable_mana_) {
					/* RendererResourceFactory */
					{
						renderer_resource_factory_set_ = mana::detail::RendererResourceFactorySet::getInstance();
						renderer_resource_factory_set_->registerFactory(
							mana::detail::createRendererResourceFactorySofdecPrimeGLES2()
																	   );
					}

					criMana_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					SetupManaVP9(config.allocator);
					criMana_Initialize(&config.mana, nullptr, 0);

					criMana_SetDecodeSkipFlag(CRI_FALSE);
				}
			}
#elif defined(XPT_TGT_IOS)
			{
				criErr_SetCallback(config.error_callback_func);
				/* FileSystem */
				{
					criFs_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criFs_InitializeLibrary(&config.fs, nullptr, 0);
				}
				/* AtomEx */
				{
					criAtomEx_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criAtomEx_Initialize_IOS(&config.atomex, nullptr, 0);
				}
				/* Dbus */
				{
					dbas_id_ = criAtomExDbas_Create(&config.dbas, nullptr, 0);
				}
				/* Mana */
				if (enable_mana_) {
					/* RendererResourceFactory */
					{
						renderer_resource_factory_set_ = mana::detail::RendererResourceFactorySet::getInstance();
						renderer_resource_factory_set_->registerFactory(
							mana::detail::createRendererResourceFactorySofdecPrimeGLES2()
																	   );
						renderer_resource_factory_set_->registerFactory(
							mana::detail::createRendererResourceFactoryH264Ios()
																	   );
					}

					criMana_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					SetupManaVP9(config.allocator);

					/* Attach H264 decoder */
					{
						CriManaVideoToolboxH264DecoderConfig_IOS h264_config;
						criMana_SetDefaultVideoToolboxH264DecoderConfig_IOS(&h264_config);
						criMana_SetupVideoToolboxH264Decoder_IOS(&h264_config, nullptr, 0);
					}

					criMana_Initialize(&config.mana, nullptr, 0);

					criMana_SetDecodeSkipFlag(CRI_FALSE);
				}
			}
#elif defined(XPT_TGT_ANDROID)
			{
				criErr_SetCallback(config.error_callback_func);
				/* FileSystem */
				{
					criFs_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criFs_InitializeLibrary(&config.fs, nullptr, 0);

					// Enable accessing to 'assets' directory.
					// Get a context of activity through a JNI helper.
					cocos2d::JniMethodInfo methodInfo;
					cocos2d::JniHelper::getStaticMethodInfo(
						methodInfo,
						"org/cocos2dx/lib/Cocos2dxActivity", "getContext", "()Landroid/content/Context;"
														   );
					auto android_context_object = (jobject)methodInfo.env->CallStaticObjectMethod(methodInfo.classID, methodInfo.methodID);
					criFs_EnableAssetsAccess_ANDROID(cocos2d::JniHelper::getJavaVM(), android_context_object);
				}
				/* AtomEx */
				{
					criAtomEx_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					criAtomEx_SetJavaVM(cocos2d::JniHelper::getJavaVM());
					criAtomEx_Initialize_ANDROID(&config.atomex, nullptr, 0);
					// criAtom_ApplyHardwareProperties_ANDROID(cocos2d::JniHelper::getJavaVM(), android_context_object);
				}
				/* Dbus */
				{
					dbas_id_ = criAtomExDbas_Create(&config.dbas, nullptr, 0);
				}
				/* Mana */
				if (enable_mana_) {
					/* RendererResourceFactory */
					{
						renderer_resource_factory_set_ = mana::detail::RendererResourceFactorySet::getInstance();
						renderer_resource_factory_set_->registerFactory(
							mana::detail::createRendererResourceFactorySofdecPrimeGLES2()
																	   );
						renderer_resource_factory_set_->registerFactory(
							mana::detail::createRendererResourceFactoryH264Android()
																	   );
					}

					criMana_SetUserAllocator(config.allocator.allocate, config.allocator.deallocate, config.allocator.obj);
					SetupManaVP9(config.allocator);

					/* Attach H264 decoder */
					{
						CriManaJNIMeidiaCodecH264DecoderConfig_ANDROID h264_config;
						criMana_SetDefaultJNIMeidiaCodecH264DecoderConfig_ANDROID(&h264_config);
						criMana_SetupJNIMeidiaCodecH264Decoder_ANDROID(&h264_config, nullptr, 0);
					}

					criMana_Initialize(&config.mana, nullptr, 0);

					criMana_SetDecodeSkipFlag(CRI_FALSE);
				}
			}
#endif

			{
				auto event_dispatcher = cocos2d::Director::getInstance()->getEventDispatcher();
				event_dispatcher->addEventListenerWithFixedPriority(event_listener_on_renderer_recreated_, 1);
			}
			detail::libraryContextExists = true;
		}


		~Impl()
		{
			{
				auto event_dispatcher = cocos2d::Director::getInstance()->getEventDispatcher();
				event_dispatcher->removeEventListener(event_listener_on_renderer_recreated_);
			}

#if defined(XPT_TGT_PC)
			{
				if (enable_mana_) {
					criMana_Finalize_PC();
				}
				criAtomExDbas_Destroy(dbas_id_);
				criAtomEx_Finalize_WASAPI();
				criFs_Finalize();
			}
#elif defined(XPT_TGT_MACOSX)
			{
				if (enable_mana_) {
					criMana_Finalize();
				}
				criAtomExDbas_Destroy(dbas_id_);
				criAtomEx_Finalize_MACOSX();
				criFs_Finalize();
			}
#elif defined(XPT_TGT_IOS)
			{
				if (enable_mana_) {
					criMana_Finalize();
				}
				criAtomExDbas_Destroy(dbas_id_);
				criAtomEx_Finalize_IOS();
				criFs_Finalize();
			}
#elif defined(XPT_TGT_ANDROID)
			{
				if (enable_mana_) {
					criMana_Finalize();
				}
				criAtomExDbas_Destroy(dbas_id_);
				criAtomEx_Finalize_ANDROID();
				criFs_DisableAssetsAccess_ANDROID();
				criFs_Finalize();
			}
#endif
			detail::libraryContextExists = false;
		}


		void onApplicationDidEnterBackgroundProc()
		{
			if (is_suspended_) {
				return;
			}

#if defined(XPT_TGT_PC)
			{
				if (enable_mana_) {
					criMana_SetAllPauseFlag(CRI_TRUE);
					renderer_resource_factory_set_->onSuspend();
				}
			}
#elif defined(XPT_TGT_MACOSX)
			{
				if (enable_mana_) {
					criMana_SetAllPauseFlag(CRI_TRUE);
					renderer_resource_factory_set_->onSuspend();
				}
			}
#elif defined(XPT_TGT_IOS)
			{
				if (enable_mana_) {
					renderer_resource_factory_set_->onSuspend();
					/* 残りの処理は platform_specific_resource_ で行なっている */
				}
			}
#elif defined(XPT_TGT_ANDROID)
			{
				if (enable_mana_) {
					criMana_SetAllPauseFlag(CRI_TRUE);
				}
				criAtomEx_StopSound_ANDROID();
				if (enable_mana_) {
					renderer_resource_factory_set_->onSuspend();
				}
			}
#endif

			is_suspended_ = true;
		}


		void onApplicationWillEnterForegroundProc()
		{
			if (!is_suspended_) {
				return;
			}

#if defined(XPT_TGT_PC)
			{
				if (enable_mana_) {
					renderer_resource_factory_set_->onResume();
					criMana_SetAllPauseFlag(CRI_FALSE);
				}
			}
#elif defined(XPT_TGT_MACOSX)
			{
				if (enable_mana_) {
					renderer_resource_factory_set_->onResume();
					criMana_SetAllPauseFlag(CRI_FALSE);
				}
			}
#elif defined(XPT_TGT_IOS)
			{
				if (enable_mana_) {
					renderer_resource_factory_set_->onResume();
				}
				/* 残りの処理は platform_specific_resource_ で行なっている */
			}
#elif defined(XPT_TGT_ANDROID)
			{
				if (enable_mana_) {
					renderer_resource_factory_set_->onResume();
					criMana_SetAllPauseFlag(CRI_FALSE);
				}
				criAtomEx_StartSound_ANDROID();
			}
#endif

			is_suspended_ = false;
		}


	private:
		void onRendererRecreated()
		{
			CRICOCOS2D_DEBUG_LOG_PLACE;
			if (enable_mana_) {
				renderer_resource_factory_set_->onRendererRecreated();
			}
		}

		void SetupManaVP9(Allocator allocator)
		{
#if (CRICOCOS2D_EXPANSIONS_USE_SOFDEC2_VP9 > 0)
			CriManaVp9DecoderConfig vp9_config;
			criMana_SetDefaultVp9DecoderConfig(&vp9_config);
			vp9_config.mem_alloc_func = allocator.allocate;
			vp9_config.mem_free_func = allocator.deallocate;
			vp9_config.mem_usr_obj = allocator.obj;
			criMana_SetupVp9Decoder(&vp9_config, NULL, 0);
#else
			(void)allocator;
#endif
		}

	private:
		bool													  enable_mana_;
		CriAtomExDbasId dbas_id_ = CRIATOMEXDBAS_ILLEGAL_ID;
		std::shared_ptr<void>									  platform_specific_resource_;
		std::shared_ptr<mana::detail::RendererResourceFactorySet> renderer_resource_factory_set_;
		cocos2d::EventListenerCustom*							  event_listener_on_renderer_recreated_;
		bool													  is_suspended_ = false;
	};

	// 单例实现
	static LibraryContext *s_pEngine = nullptr;
	LibraryContext* LibraryContext::getInstance() {
		if (! s_pEngine) {
            int maxVoices = 20;
            auto config = cricocos2d::LibraryContext::Config();
            config.fs.num_loaders   += maxVoices;
            config.fs.num_binders   += maxVoices;
            config.fs.max_files     += maxVoices;
            config.dbas.max_streams += maxVoices;
			s_pEngine = new LibraryContext(config);
		}

		return s_pEngine;
	}

	void LibraryContext::end() {
		if (s_pEngine) {
			delete s_pEngine;
			s_pEngine = nullptr;
		}
	}

	LibraryContext::LibraryContext(const Config& config)
		: impl_(std::make_shared<LibraryContext::Impl>(config))
	{
		CRICOCOS2D_DEBUG_LOG_PLACE;
	}


	LibraryContext::~LibraryContext()
	{
		CRICOCOS2D_DEBUG_LOG_PLACE;
	}


	void LibraryContext::onApplicationDidEnterBackgroundProc()
	{
		CRICOCOS2D_DEBUG_LOG_PLACE;
		impl_->onApplicationDidEnterBackgroundProc();
	}


	void LibraryContext::onApplicationWillEnterForegroundProc()
	{
		CRICOCOS2D_DEBUG_LOG_PLACE;
		impl_->onApplicationWillEnterForegroundProc();
	}

    void LibraryContext::criAtomExecuteMain()
    {
        criAtomEx_ExecuteMain();
    }

}
