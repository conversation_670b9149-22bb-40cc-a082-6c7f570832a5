// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        mavenCentral()
        google()
        // maven {
        //    url 'https://maven.fabric.io/public'
        // }
        maven {url 'https://developer.huawei.com/repo/'}
        maven {
            url "https://artifact.bytedance.com/repository/Volcengine/"
        }
        maven {
            url "https://artifact.bytedance.com/repository/byteX/"
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.1'
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:perf-plugin:1.4.1'
        // Remove Fabric plugin
        // classpath 'io.fabric.tools:gradle:1.28.0'
        classpath 'com.huawei.agconnect:agcp:1.4.1.300'
//        classpath "com.volcengine:apm_insight_plugin:1.4.2"
        classpath "com.volcengine:apmplus_upload_plugin:0.0.1" // 自动上传符号表
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}

allprojects {
    repositories {
        mavenCentral()
        mavenLocal()
        maven {
            url "https://maven.google.com"
        }
        google()
//        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.google.com' }
        maven {url 'https://developer.huawei.com/repo/'}
        maven { url "https://hyprmx.jfrog.io/artifactory/hyprmx" }

	    maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }

//        maven {url 'https://android-sdk.is.com/'}

//        maven { url "https://sdk.tapjoy.com/"}

        maven {url 'https://cboost.jfrog.io/artifactory/chartboost-ads/' }

        maven { url "https://jitpack.io" }

        maven {
            url "https://artifact.bytedance.com/repository/Volcengine/"
        }
        maven { url 'https://repo.onestore.net/repository/onestore-sdk-public' }
    }
}