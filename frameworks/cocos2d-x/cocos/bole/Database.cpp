//
//  Database.cpp
//  cocos2d_libs
//
//  Created by wodes on 14-11-4.
//
//

#include "Database.h"
//
//#define dbName "sample.db"

NS_CC_BEGIN


USING_NS_CC;
namespace network {
int Database::sqliteOpen(sqlite3 **db,const char * dbName){
    
    auto filePath = FileUtils::getInstance()->getWritablePath();
    filePath += dbName;
//    CCLOG("open db path: %s, %s",dbName, filePath.c_str());
    return sqlite3_open(filePath.c_str(),db);
    
}

void Database::sqliteCreateTable(const char * dbName){
    
    sqlite3 *db = NULL;
    if(sqliteOpen(&db, dbName) == SQLITE_OK){
        
        const char *sql_createtable = "CREATE TABLE kv(key TEXT,value TEXT,primary key(key))";
        sqlite3_stmt *stmt = NULL;
        if (sqlite3_prepare_v2(db, sql_createtable, -1, &stmt, NULL) == SQLITE_OK) {
            
            if (sqlite3_step(stmt) == SQLITE_DONE) {
                
                CCLOG("create table done");
            }
            
            sqlite3_reset(stmt);
            
            
        }
        
        sqlite3_finalize(stmt);
    }
    sqlite3_close(db);
}


const char * Database::sqliteGetValueForKey(const char * key, const char * dbName){
    
     __String *strValue = NULL;
    sqlite3 *db = NULL;
    if (sqliteOpen(&db,dbName) == SQLITE_OK) {
        
        const char *sql_select = "SELECT value FROM kv WHERE key=?";
        sqlite3_stmt *stmt = NULL;
        if (sqlite3_prepare_v2(db, sql_select, -1, &stmt, NULL) == SQLITE_OK) {
            
            sqlite3_bind_text(stmt, 1, key, -1, SQLITE_TRANSIENT);
            int ret = sqlite3_step(stmt);
            if (ret == SQLITE_DONE || ret == SQLITE_ROW) {
                
                const char *val = (const char*)sqlite3_column_text(stmt, 0);
                if (val != NULL)
                    strValue = __String::create(val);
            }
            sqlite3_reset(stmt);
        }
        sqlite3_finalize(stmt);
    }
    sqlite3_close(db);
    
    if (strValue != NULL) {
        
        return strValue->getCString();
    }
    
    return NULL;
}


void Database::sqliteSetValueForKey(const char* key,const char* value, const char* dbName){
    
    
    std::string fullpath = CCFileUtils::getInstance()->getWritablePath();
    fullpath += dbName;
    sqlite3 *db = NULL;
    
    if (sqlite3_open(fullpath.c_str(), &db) == SQLITE_OK) {
        
        const char *sql_select = "REPLACE INTO kv(key,value) VALUES(?,?)";
        sqlite3_stmt *stmt = NULL;
        
        if (sqlite3_prepare_v2(db, sql_select, -1, &stmt, NULL) == SQLITE_OK) {
            
            sqlite3_bind_text(stmt, 1, key, -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 2, value, -1, SQLITE_TRANSIENT);
            
            if (sqlite3_step(stmt) == SQLITE_DONE) {
                
                CCLOG("replace key:%s value:%s",key,value);
                
            }
            
            sqlite3_reset(stmt);
            
        }
        
        sqlite3_finalize(stmt);
        
    }
    
    sqlite3_close(db);
    
    
}

void Database::sqliteUpdateValueForKey(const char* key,const char* value, const char* dbName){
    
    
    std::string fullpath = CCFileUtils::getInstance()->getWritablePath();
    fullpath += dbName;
    sqlite3 *db = NULL;
    
    if (sqlite3_open(fullpath.c_str(), &db) == SQLITE_OK) {
        
        const char *sql_select = "UPDATE kv SET value =? WHERE key =? ";
        sqlite3_stmt *stmt = NULL;
        
        if (sqlite3_prepare_v2(db, sql_select, -1, &stmt, NULL) == SQLITE_OK) {
            
            sqlite3_bind_text(stmt, 1, value, -1, SQLITE_TRANSIENT);
            sqlite3_bind_text(stmt, 2, key, -1, SQLITE_TRANSIENT);
            
            if (sqlite3_step(stmt) == SQLITE_DONE) {
                
                CCLOG("change key:%s value:%s",key,value);
                
            }
            
            sqlite3_reset(stmt);
            
        }
        
        sqlite3_finalize(stmt);
        
    }
    
    sqlite3_close(db);
    
    
}
}
NS_CC_END