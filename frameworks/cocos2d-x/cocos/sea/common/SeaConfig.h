//
//  Config.h
//  Slots
//
//  Created by chen<PERSON><PERSON><PERSON><PERSON> on 2024/12/19.
//

#ifndef __SEA_CONFIG_H__
#define __SEA_CONFIG_H__
#include "cocos2d.h"

namespace sea {

//切图纹理类型
enum class TextureResType
{
    NONE = -1, //空
    LOCAL = 0, //散图
    PLIST = 1, //图集
};

//Lua与C++布尔型转换
enum class IntBool
{
    None = -1,
    False = 0,
    True = 1,
};

//TableView的方向
enum TABLE_VIEW_DIRECTION {
    NONE = -1,
    HORIZONTAL = 0,
    VERTICAL = 1,
    BOTH  = 2,
};

//节点类型
enum NodeType {
    SPRITE = 0,
    SPRITE_9 = 1, //九宫格图片
    SPRITE9 = 1, //九宫格图片另外一种写法
    ANI = 10, //不支持自动切换语言，但返回的是 SkeletonAnimation
    ANI_NEW = 11, //支持切换语言更新，但要获取 SkeletonAnimation，需要调用 getAnimation 函数
    BUTTON_NORMAL = 20, //常规按钮,为了兼容旧写法，即普通图默认带_normal
    BUTTON_NORMAL_NEW = 21, //常规按钮,命名遵循新规则，即默认普通图不带_normal
    BUTTON = 21, //同BUTTON_NORMAL_NEW，为了方便书写，添加这种默认的按钮
    BUTTON_TRADITION = 22, //传统按钮，即由两张图或三张图生成，但是为了兼容旧写法，即普通图默认带_normal
    BUTTON_TRADITION_NEW = 23, //传统按钮，即由两张图或三张图生成，但命名遵循新规则，即默认普通图不带_normal
    BUTTON_PAY = 24,//支付按钮
    BUTTON_WIDGET = 25, //无资源按钮，基于BLWidget
    BUTTON_EMPTY = 26, //无资源按钮，基于资源 commonpics/empty.png
    BUTTON_MASK = 27, //全屏遮罩按钮
    FNT = 30, //FNT字体
    LABEL = 31, //系统字
    LABEL_COMBINE = 32, //混合系统字，实际同ORDER类型
    PROGRESS = 40, //FONTS.addProgressNew，常见的横向切图进度首选，效率更高
    PROGRESS_TIMER = 41,//cocos系统进度条，支持的功能更全面
    SLIDER = 45, //横向可拖动的滑动条FONTS.addSlider
    EDIT = 50,//输入框
    RADIO = 60, //单选按钮，⚠️ 注意：同一个组必须在同一个节点层级，即拥有共同的父节点
    SWITCH = 61, //多节点切换
    PAGE_VIEW = 62, //页签
    LIST = 70, //列表，暂不支持自动排序
    ORDER = 80, //顺序节点
    SCROLL_VIEW = 100, //滑动面板
    CLIPPING = 110,
    RED = 120,//红点
    RED_VAR = 120,//类型可变的红点
    REWARD = 130, //通用奖励
    DIY = 1000
};

//Lua与C++版本控制,第1位为C++版本，第2位暂时不用
static const std::unordered_map<std::string, std::pair<int, int> > sLuaFunctionVersionKey = {
    {"Dialog." + std::to_string(static_cast<int>(NodeType::SPRITE)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::SPRITE9)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::ANI)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::ANI_NEW)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_NORMAL)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_TRADITION)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_NORMAL_NEW)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_TRADITION_NEW)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::RADIO)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_PAY)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_EMPTY)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::BUTTON_MASK)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::FNT)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::LABEL)), {4, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::PROGRESS)), {3, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::PROGRESS_TIMER)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::SLIDER)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::EDIT)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::LIST)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::ORDER)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::SCROLL_VIEW)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::CLIPPING)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::RED)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::SWITCH)), {2, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::PAGE_VIEW)), {1, 0}},
    {"Dialog." + std::to_string(static_cast<int>(NodeType::DIY)), {1, 0}},
};

}

#endif
