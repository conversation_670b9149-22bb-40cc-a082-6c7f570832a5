package com.frontier.samsungpaysdk;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.widget.Toast;

import com.frontier.sdkbase.AbstractPayImpl;
//import com.frontier.samsungpaysdk.BuildConfig;
import com.frontier.sdkbase.ErrorCode;
import com.frontier.sdkbase.EventListener;
import com.frontier.sdkbase.SDKType;
import com.samsung.android.sdk.iap.lib.constants.HelperDefine;
import com.samsung.android.sdk.iap.lib.helper.IapHelper;
import com.samsung.android.sdk.iap.lib.listener.OnGetOwnedListListener;
import com.samsung.android.sdk.iap.lib.listener.OnGetProductsDetailsListener;
import com.samsung.android.sdk.iap.lib.vo.ConsumeVo;
import com.samsung.android.sdk.iap.lib.vo.ErrorVo;
import com.samsung.android.sdk.iap.lib.vo.OwnedProductVo;
import com.samsung.android.sdk.iap.lib.listener.OnPaymentListener;
import com.samsung.android.sdk.iap.lib.vo.ProductVo;
import com.samsung.android.sdk.iap.lib.vo.PurchaseVo;
import com.samsung.android.sdk.iap.lib.listener.OnConsumePurchasedItemsListener;

import android.content.Context;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class SamsungPayHelper extends AbstractPayImpl {
    private static final String TAG = "SamsungPayHelper";
    private IapHelper mIapHelper = null;

    private Boolean isDetailConnected = false;

    public SamsungPayHelper(Activity activity, final EventListener listener) {
        super(activity, listener);
        mIapHelper = IapHelper.getInstance(activity.getApplicationContext());
        // Use ApplicationInfo to check if app is in debug mode
        boolean isDebuggable = false;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.DONUT) {
            isDebuggable = (activity.getApplicationInfo().flags & activity.getApplicationInfo().FLAG_DEBUGGABLE) != 0;
        }

        if (isDebuggable) {
            mIapHelper.setOperationMode(HelperDefine.OperationMode.OPERATION_MODE_TEST);
            Log.d(TAG, "debug mode enabled");
        } else {
            mIapHelper.setOperationMode(HelperDefine.OperationMode.OPERATION_MODE_PRODUCTION);
            Log.d(TAG, "release mode enabled");
        }

    }

    @Override
    public int sdkType() {
        return SDKType.SAMSUNG_PAY;
    }

    @Override
    public boolean isSupportSDKType(int sdkType) {
        return sdkType == sdkType();
    }

    @Override
    public void purchase(final String itemId, final String payload, final JSONObject jsonData) {

        Log.d(TAG, "purchase start   111");
        mIapHelper.startPayment(itemId, payload, new OnPaymentListener() {
            @Override
            public void onPayment(ErrorVo errorVo, PurchaseVo purchaseVo) {
                Log.d(TAG, "purchase start   222");
                if(errorVo != null && errorVo.getErrorCode() == IapHelper.IAP_ERROR_NONE) {
                    if(purchaseVo != null) {
                        // 购买成功，处理购买结果
                        Log.d(TAG, "purchase succes!"+purchaseVo.getPurchaseId()+" "+payload);
                        String itemId2 = purchaseVo.getItemId();

                        if (itemId.equals(itemId2)) {
                            JSONObject json = new JSONObject();
                            try {
                                json.put("payment_id",purchaseVo.getPaymentId());
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                            listener.onPurchase(sdkType(),
                                    ErrorCode.NO_ERROR,
                                    purchaseVo.getItemId(),
                                    purchaseVo.getPurchaseId(),
                                    payload,
                                    purchaseVo.getCurrencyCode(),
                                    json,
                                    0);
                        }else{
                            Log.d(TAG, "purchase error3!");
                            listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                        }
                    }else{
                        Log.d(TAG, "purchase error1!");
                        listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                    }
                } else {
                    // 购买失败，处理错误
                    Log.d(TAG, "purchase error2!");
                    listener.onPurchase(sdkType(), ErrorCode.COMMON_ERROR, "", "", "", "", null, 0);
                }
            }
        });
    }

    @Override
    public void querySkuDetails(String skuList) {
        if(!isDetailConnected){
            isDetailConnected = true;
            OnGetProductsDetailsListener detailListener = new OnGetProductsDetailsListener() {
                @Override
                public void onGetProducts(ErrorVo _errorVO, ArrayList<ProductVo> _productList) {
                    Log.d(TAG, "onGetProducts is invoked");
                    if (_errorVO == null) return;
                    if (_errorVO.getErrorCode() == IapHelper.IAP_ERROR_NONE) {
                        if (_productList == null) return;
                        JSONArray skuArray = new JSONArray();
                        for(ProductVo productv : _productList) {
                            String productId = productv.getItemId();
                            String price = productv.getItemPriceString();
                            String description = productv.getItemDesc();
                            String title = productv.getItemName();
                            Double priceAmountMicros = productv.getItemPrice();
                            JSONObject json = new JSONObject();
                            try {
                                json.put("product_id", productId);
                                json.put("price", price);
                                json.put("description", description);
                                json.put("title", title);
                                json.put("price_amount_micros", priceAmountMicros);
                                skuArray.put(json);
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }
                        listener.onQuerySkuDetails(sdkType(), ErrorCode.NO_ERROR, skuArray, 0);
                    } else {
                        Log.d(TAG,"onGetProducts _errorVO:"+_errorVO);
                    }
                    isDetailConnected = false;
                }
            };
            mIapHelper.getProductsDetails(skuList, detailListener);
        }
    }

    @Override
    public void consumePurchase(String purchaseId) {
        Log.d(TAG, "onConsumePurchasedItems is enter:"+purchaseId);
        mIapHelper.consumePurchasedItems(purchaseId, new OnConsumePurchasedItemsListener() {
            @Override
            public void onConsumePurchasedItems(ErrorVo _errorVO, ArrayList<ConsumeVo> _consumeList) {
                Log.d(TAG, "onConsumePurchasedItems");
                if (_errorVO == null) return;
                if (_errorVO.getErrorCode() == IapHelper.IAP_ERROR_NONE) {
                    if (_consumeList == null) return;
                    for (ConsumeVo item : _consumeList) {
                        if (item.getStatusCode() == 0) {
                            Log.d(TAG, "onConsumePurchasedItems Plus a gage!");
                        }
                    }
                } else {
                    Log.d(TAG,"onConsumePurchasedItems _errorVO:"+_errorVO);
                }
            }
        });
    }

    @Override
    public void checkUnDeliveredOrders() {
        if(!isDetailConnected){
            isDetailConnected = true;
            Log.d(TAG, "onGetOwnedProducts");
            mIapHelper.getOwnedList(IapHelper.PRODUCT_TYPE_ALL, new OnGetOwnedListListener() {
                @Override
                public void onGetOwnedProducts(ErrorVo _errorVO, ArrayList<OwnedProductVo> _ownedList) {
                    Log.d(TAG, "onGetOwnedProducts is invoked");
                    if (_errorVO == null) return;
                    if (_errorVO.getErrorCode() == IapHelper.IAP_ERROR_NONE) {
                        Log.d(TAG, "onGetOwnedProducts 333");
                        if (_ownedList == null) return;
                        for (OwnedProductVo item : _ownedList) {
                            if (item.getIsConsumable()) {
                                Log.d(TAG, "onGetOwnedProducts has item:"+item.getPurchaseId());
                                JSONObject json = new JSONObject();
                                try {
                                    json.put("payment_id",item.getPaymentId());
                                    String purchaseDateString = item.getPurchaseDate();
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    Date date = sdf.parse(purchaseDateString);
                                    long timestamp = date.getTime(); // 获取时间戳
                                    json.put("start_time", timestamp);

                                } catch (JSONException | ParseException e) {
                                    e.printStackTrace();
                                }
                                listener.onUndeliveredOrder(sdkType(),
                                    ErrorCode.NO_ERROR,
                                    item.getItemId(),
                                    item.getPurchaseId(),
                                    "",
                                    item.getCurrencyCode(),
                                    json,
                                    0);
                            } else {
                                Log.d(TAG, "onGetOwnedProducts is 111 没有该产品");
                            }
                        }

                        listener.onUndeliveredOrder(sdkType(),
                                ErrorCode.NO_MORE_UNDELIVERED_ORDER,
                                "",
                                "",
                                "",
                                "",
                                null,
                                0);

                    } else {
                        Log.d(TAG, "onGetOwnedProducts _errorVO:" + _errorVO);
                    }
                    isDetailConnected = false;
                }
            });
        }

    }
}

