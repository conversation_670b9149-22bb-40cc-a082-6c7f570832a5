/****************************************************************************
Copyright (c) 2008-2010 Ricardo <PERSON>
Copyright (c) 2010-2012 cocos2d-x.org
Copyright (c) 2011      Zynga Inc.
Copyright (c) 2013-2017 Chukong Technologies Inc.

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/

#include "renderer/CCTextureCache.h"

#include <errno.h>
#include <stack>
#include <cctype>
#include <list>

#include "renderer/CCTexture2D.h"
#include "base/ccMacros.h"
#include "base/ccUTF8.h"
#include "base/CCDirector.h"
#include "base/CCScheduler.h"
#include "platform/CCFileUtils.h"
#include "base/ccUtils.h"
#include "base/CCNinePatchImageParser.h"
#include "base/CrashMsgCollector.h"
#include "base/CCConfiguration.h"

using namespace std;

NS_CC_BEGIN

std::string TextureCache::s_etc1AlphaFileSuffix = "@alpha";
std::string TextureCache::s_pngFileSuffix = ".png";
std::string TextureCache::s_astcFileSuffix = ".astc";

// implementation TextureCache

void TextureCache::setETC1AlphaFileSuffix(const std::string& suffix)
{
    s_etc1AlphaFileSuffix = suffix;
}

std::string TextureCache::getETC1AlphaFileSuffix()
{
    return s_etc1AlphaFileSuffix;
}

TextureCache * TextureCache::getInstance()
{
    return Director::getInstance()->getTextureCache();
}

TextureCache::TextureCache()
: _loadingThread(nullptr)
, _needQuit(false)
, _asyncRefCount(0)
,_asyncMultiRefCount(0)
,_imageAsyncMultiHandler(0)
,_isIgnorCompressedASTCType(true)
{
}

TextureCache::~TextureCache()
{
    CCLOGINFO("deallocing TextureCache: %p", this);

    for (auto& texture : _textures)
        texture.second->release();

    CC_SAFE_DELETE(_loadingThread);
}

void TextureCache::destroyInstance()
{
}

TextureCache * TextureCache::sharedTextureCache()
{
    return Director::getInstance()->getTextureCache();
}

void TextureCache::purgeSharedTextureCache()
{
}

std::string TextureCache::getDescription() const
{
    return StringUtils::format("<TextureCache | Number of textures = %d>", static_cast<int>(_textures.size()));
}

struct TextureCache::AsyncStruct
{
public:
    AsyncStruct
    ( const std::string& fn,const std::function<void(Texture2D*)>& f,
      const std::string& key )
      : filename(fn), callback(f),callbackKey( key ),
        pixelFormat(Texture2D::getDefaultAlphaPixelFormat()),
        loadSuccess(false)
    {}

    std::string filename;
    std::function<void(Texture2D*)> callback;
    std::string callbackKey;
    Image image;
    Image imageAlpha;
    Texture2D::PixelFormat pixelFormat;
    bool loadSuccess;
};

// 每个异步线程一套数据结构
struct TextureCache::AsyncThreadStruct
{
public:
    AsyncThreadStruct(){}
    std::thread* loadingThread;
    std::deque<AsyncStruct*> asyncStructQueue;
    std::deque<AsyncStruct*> requestQueue;
    std::deque<AsyncStruct*> responseQueue;
    
    std::mutex requestMutex;
    std::mutex responseMutex;
    
    std::condition_variable sleepCondition;
    int asyncRefCount;
};

// 多线程异步加载线程 begin

// 如果文件都存在的话，分配子线程开始多线程异步加载
void TextureCache::addImageAsyncMulti(const std::vector<std::string> &filepathVec, const int threadCount, const std::function<void(Texture2D*)>& callback)
{
    // 未加载过的图片
    std::vector<std::string> noLoadImageVec;
    
    for (auto& path : filepathVec)
    {
        std::string fullpath = FileUtils::getInstance()->fullPathForFilename(path);
        
        // 如果有文件不存在的话,结束并返回
        if (fullpath.empty() || !FileUtils::getInstance()->isFileExist(fullpath))
        {
            if (callback) callback(nullptr);
            return;
        }
        
        auto it = _textures.find(fullpath);
        if (it == _textures.end())
        {
            noLoadImageVec.push_back(fullpath);
        }
        else
        {
            Texture2D* texture = it->second;
            if (callback) callback(texture);
        }
    }

    // 所有文件都已经加载过了
    if (0 == noLoadImageVec.size())
    {
        return;
    }

    if (0 == _asyncMultiRefCount)
    {
        Director::getInstance()->getScheduler()->schedule(CC_SCHEDULE_SELECTOR(TextureCache::addImageAsyncMultiCallBack), this, 0, false);
    }
    
    // 开启线程
    for (int i = (int)_asyncThreadStructVector.size(); i < threadCount; i++)
    {
        AsyncThreadStruct* asyncThreadData = new (std::nothrow) AsyncThreadStruct();
        _asyncThreadStructVector.push_back(asyncThreadData);
        
        asyncThreadData->loadingThread = new (std::nothrow) std::thread(&TextureCache::loadImageMulti, this, i);
    }
    
    _asyncMultiRefCount = _asyncMultiRefCount + (int)noLoadImageVec.size();
    
    // 顺序分给不同线程
    for (int i = 0; i < noLoadImageVec.size(); i++)
    {
        std::string fullpath = noLoadImageVec.at(i);
        // generate async struct
        AsyncStruct *data = new (std::nothrow) AsyncStruct(fullpath, callback, fullpath);
        
        // add async struct into queue
        int threadIndex = i % threadCount;
        _asyncThreadStructVector.at(threadIndex)->asyncStructQueue.push_back(data);
        std::unique_lock<std::mutex> ul(_asyncThreadStructVector.at(threadIndex)->requestMutex);
        _asyncThreadStructVector.at(threadIndex)->requestQueue.push_back(data);
        _asyncThreadStructVector.at(threadIndex)->sleepCondition.notify_one();
    }
}


void TextureCache::loadImageMulti(int index)
{
    AsyncStruct *asyncStruct = nullptr;
    while (!_needQuit)
    {
        std::unique_lock<std::mutex> ul(_asyncThreadStructVector.at(index)->requestMutex);
        // pop an AsyncStruct from request queue
        if (_asyncThreadStructVector.at(index)->requestQueue.empty())
        {
            asyncStruct = nullptr;
        }
        else
        {
            asyncStruct = _asyncThreadStructVector.at(index)->requestQueue.front();
            _asyncThreadStructVector.at(index)->requestQueue.pop_front();
        }

        if (nullptr == asyncStruct) {
            if (_needQuit) {
                break;
            }
            _asyncThreadStructVector.at(index)->sleepCondition.wait(ul); // 线程不死等待下一个加载的到来
            continue;
        }
        ul.unlock();

        // load image
        asyncStruct->loadSuccess = asyncStruct->image.initWithImageFileThreadSafe(asyncStruct->filename);
        
    //    CCLOG("loadImageMulti %d  %s", index, asyncStruct->filename.c_str());

        // ETC1 ALPHA supports.
        if (asyncStruct->loadSuccess && asyncStruct->image.getFileType() == Image::Format::ETC1 && !s_etc1AlphaFileSuffix.empty())
        { // check whether alpha texture exists & load it
            auto alphaFile = asyncStruct->filename + s_etc1AlphaFileSuffix;
            if (FileUtils::getInstance()->isFileExist(alphaFile))
                asyncStruct->imageAlpha.initWithImageFileThreadSafe(alphaFile);
        }

        // push the asyncStruct to response queue
        _asyncThreadStructVector.at(index)->responseMutex.lock();
        _asyncThreadStructVector.at(index)->responseQueue.push_back(asyncStruct);
        _asyncThreadStructVector.at(index)->responseMutex.unlock();
    }
}


void TextureCache::addImageAsyncMultiCallBack(float /*dt*/)
{
    Texture2D *texture = nullptr;
    AsyncStruct *asyncStruct = nullptr;
    
    for(int i = 0; i < _asyncThreadStructVector.size(); i++)
    {
        // pop an AsyncStruct from response queue
        while (true)
        {
            _asyncThreadStructVector.at(i)->responseMutex.lock();
            if (_asyncThreadStructVector.at(i)->responseQueue.empty())
            {
                asyncStruct = nullptr;
            }
            else
            {
                asyncStruct = _asyncThreadStructVector.at(i)->responseQueue.front();
                _asyncThreadStructVector.at(i)->responseQueue.pop_front();

                // the asyncStruct's sequence order in _asyncStructQueue must equal to the order in _responseQueue
                CC_ASSERT(asyncStruct == _asyncThreadStructVector.at(i)->asyncStructQueue.front());
                _asyncThreadStructVector.at(i)->asyncStructQueue.pop_front();
            }
            _asyncThreadStructVector.at(i)->responseMutex.unlock();

            if (nullptr == asyncStruct) {
                break;
            }

            // check the image has been convert to texture or not
            auto it = _textures.find(asyncStruct->filename);
            if (it != _textures.end())
            {
                texture = it->second;
            //    CCLOG("cocos2d: call TextureCache::addImageAsyncMultiCallBack(%s)  exist...", asyncStruct->filename.c_str());
            }
            else
            {
                // convert image to texture
                if (asyncStruct->loadSuccess)
                {
                    Image* image = &(asyncStruct->image);
                    // generate texture in render thread
                    texture = new (std::nothrow) Texture2D();

                    texture->initWithImage(image, asyncStruct->pixelFormat);
                    //parse 9-patch info
                    this->parseNinePatchImage(image, texture, asyncStruct->filename);
    #if CC_ENABLE_CACHE_TEXTURE_DATA
                    // cache the texture file name
                    VolatileTextureMgr::addImageTexture(texture, asyncStruct->filename);
    #endif
                    // cache the texture. retain it, since it is added in the map
                    _textures.emplace(asyncStruct->filename, texture);
                    texture->retain();

                    texture->autorelease();
                    // ETC1 ALPHA supports.
                    if (asyncStruct->imageAlpha.getFileType() == Image::Format::ETC1) {
                        auto alphaTexture = new(std::nothrow) Texture2D();
                        if(alphaTexture != nullptr && alphaTexture->initWithImage(&asyncStruct->imageAlpha, asyncStruct->pixelFormat)) {
                            texture->setAlphaTexture(alphaTexture);
                        }
                        CC_SAFE_RELEASE(alphaTexture);
                    }
                    
                //    CCLOG("cocos2d: call TextureCache::addImageAsyncMulti(%s)  loadSuccess...", asyncStruct->filename.c_str());
                }
                else {
                    texture = nullptr;
                    CCLOG("cocos2d: failed to call TextureCache::addImageAsyncMulti(%s)", asyncStruct->filename.c_str());
                }
            }

            // call callback function
            if (asyncStruct->callback)
            {
                (asyncStruct->callback)(texture);
            }

        //    CCLOG("cocos2d: counting TextureCache::addImageAsyncMulti(%d)  _asyncMultiRefCount...", _asyncMultiRefCount);
            // release the asyncStruct
            delete asyncStruct;
            --_asyncMultiRefCount;
        }
    }
        
        
    if (0 == _asyncMultiRefCount)
    {
        if (0 != _imageAsyncMultiHandler)
        {
            ScriptEngineManager::getInstance()->getScriptEngine()->removeScriptHandler(_imageAsyncMultiHandler);
            _imageAsyncMultiHandler = 0;
        }
        Director::getInstance()->getScheduler()->unschedule(CC_SCHEDULE_SELECTOR(TextureCache::addImageAsyncMultiCallBack), this);
    }
}

void TextureCache::setImageAsyncMultiHandler(int handler)
{
    _imageAsyncMultiHandler = handler;
}

// 多线程异步加载线程 end

/**
 The addImageAsync logic follow the steps:
 - find the image has been add or not, if not add an AsyncStruct to _requestQueue  (GL thread)
 - get AsyncStruct from _requestQueue, load res and fill image data to AsyncStruct.image, then add AsyncStruct to _responseQueue (Load thread)
 - on schedule callback, get AsyncStruct from _responseQueue, convert image to texture, then delete AsyncStruct (GL thread)

 the Critical Area include these members:
 - _requestQueue: locked by _requestMutex
 - _responseQueue: locked by _responseMutex

 the object's life time:
 - AsyncStruct: construct and destruct in GL thread
 - image data: new in Load thread, delete in GL thread(by Image instance)

 Note:
 - all AsyncStruct referenced in _asyncStructQueue, for unbind function use.

 How to deal add image many times?
 - At first, this situation is abnormal, we only ensure the logic is correct.
 - If the image has been loaded, the after load image call will return immediately.
 - If the image request is in queue already, there will be more than one request in queue,
 - In addImageAsyncCallback, will deduplicate the request to ensure only create one texture.

 Does process all response in addImageAsyncCallback consume more time?
 - Convert image to texture faster than load image from disk, so this isn't a
 problem.

 Call unbindImageAsync(path) to prevent the call to the callback when the
 texture is loaded.
 */
void TextureCache::addImageAsync(const std::string &path, const std::function<void(Texture2D*)>& callback)
{
    addImageAsync( path, callback, path );
}

/**
 The addImageAsync logic follow the steps:
 - find the image has been add or not, if not add an AsyncStruct to _requestQueue  (GL thread)
 - get AsyncStruct from _requestQueue, load res and fill image data to AsyncStruct.image, then add AsyncStruct to _responseQueue (Load thread)
 - on schedule callback, get AsyncStruct from _responseQueue, convert image to texture, then delete AsyncStruct (GL thread)
 
 the Critical Area include these members:
 - _requestQueue: locked by _requestMutex
 - _responseQueue: locked by _responseMutex
 
 the object's life time:
 - AsyncStruct: construct and destruct in GL thread
 - image data: new in Load thread, delete in GL thread(by Image instance)
 
 Note:
 - all AsyncStruct referenced in _asyncStructQueue, for unbind function use.
 
 How to deal add image many times?
 - At first, this situation is abnormal, we only ensure the logic is correct.
 - If the image has been loaded, the after load image call will return immediately.
 - If the image request is in queue already, there will be more than one request in queue,
 - In addImageAsyncCallback, will deduplicate the request to ensure only create one texture.
 
 Does process all response in addImageAsyncCallback consume more time?
 - Convert image to texture faster than load image from disk, so this isn't a
 problem.

 The callbackKey allows to unbind the callback in cases where the loading of
 path is requested by several sources simultaneously. Each source can then
 unbind the callback independently as needed whilst a call to
 unbindImageAsync(path) would be ambiguous.
 */
void TextureCache::addImageAsync(const std::string &path, const std::function<void(Texture2D*)>& callback, const std::string& callbackKey)
{
    Texture2D *texture = nullptr;

    std::string fullpath = FileUtils::getInstance()->fullPathForFilename(path);

    auto it = _textures.find(fullpath);
    if (it != _textures.end())
        texture = it->second;

    if (texture != nullptr)
    {
        if (callback) callback(texture);
        return;
    }

    // check if file exists
    if (fullpath.empty() || !FileUtils::getInstance()->isFileExist(fullpath)) {
        if (callback) callback(nullptr);
        return;
    }

    // lazy init
    if (_loadingThread == nullptr)
    {
        // create a new thread to load images
        _loadingThread = new (std::nothrow) std::thread(&TextureCache::loadImage, this);
        _needQuit = false;
    }

    if (0 == _asyncRefCount)
    {
        Director::getInstance()->getScheduler()->schedule(CC_SCHEDULE_SELECTOR(TextureCache::addImageAsyncCallBack), this, 0, false);
    }

    ++_asyncRefCount;

    // generate async struct
    AsyncStruct *data =
      new (std::nothrow) AsyncStruct(fullpath, callback, callbackKey);
    
    // add async struct into queue
    _asyncStructQueue.push_back(data);
    _requestMutex.lock();
    _requestQueue.push_back(data);
    _requestMutex.unlock();

    _sleepCondition.notify_one();
}

void TextureCache::unbindImageAsync(const std::string& callbackKey)
{
    if (_asyncStructQueue.empty())
    {
        return;
    }

    for (auto& asyncStruct : _asyncStructQueue)
    {
        if (asyncStruct->callbackKey == callbackKey)
        {
            asyncStruct->callback = nullptr;
        }
    }
}

void TextureCache::unbindAllImageAsync()
{
    if (_asyncStructQueue.empty())
    {
        return;

    }
    for (auto& asyncStruct : _asyncStructQueue)
    {
        asyncStruct->callback = nullptr;
    }
}

// 单独线程下执行，io加载纹理数据
void TextureCache::loadImage()
{
    AsyncStruct *asyncStruct = nullptr;
    std::mutex signalMutex;
    std::unique_lock<std::mutex> signal(signalMutex);
    while (!_needQuit)
    {
        // pop an AsyncStruct from request queue
        _requestMutex.lock();
        if (_requestQueue.empty())
        {
            asyncStruct = nullptr;
        }
        else
        {
            asyncStruct = _requestQueue.front();
            _requestQueue.pop_front();
        }
        _requestMutex.unlock();

        if (nullptr == asyncStruct) {
            _sleepCondition.wait(signal);
            continue;
        }

        // load image
        asyncStruct->loadSuccess = asyncStruct->image.initWithImageFileThreadSafe(asyncStruct->filename);

        // ETC1 ALPHA supports.
        if (asyncStruct->loadSuccess && asyncStruct->image.getFileType() == Image::Format::ETC1 && !s_etc1AlphaFileSuffix.empty())
        { // check whether alpha texture exists & load it
            auto alphaFile = asyncStruct->filename + s_etc1AlphaFileSuffix;
            if (FileUtils::getInstance()->isFileExist(alphaFile))
                asyncStruct->imageAlpha.initWithImageFileThreadSafe(alphaFile);
        }
        // push the asyncStruct to response queue
        _responseMutex.lock();
        _responseQueue.push_back(asyncStruct);
        _responseMutex.unlock();
    }
}

// 每一帧检测加载纹理线程是否有io加载完的
void TextureCache::addImageAsyncCallBack(float /*dt*/)
{
    CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::IMAGE_ASYNC);
    Texture2D *texture = nullptr;
    AsyncStruct *asyncStruct = nullptr;
    while (true)
    {
        // pop an AsyncStruct from response queue
        _responseMutex.lock();
        if (_responseQueue.empty())
        {
            asyncStruct = nullptr;
        }
        else
        {
            asyncStruct = _responseQueue.front();
            _responseQueue.pop_front();

            // the asyncStruct's sequence order in _asyncStructQueue must equal to the order in _responseQueue
            CC_ASSERT(asyncStruct == _asyncStructQueue.front());
            _asyncStructQueue.pop_front();
        }
        _responseMutex.unlock();

        if (nullptr == asyncStruct) {
            break;
        }

        // check the image has been convert to texture or not
        auto it = _textures.find(asyncStruct->filename);
        if (it != _textures.end())
        {
            texture = it->second;
        }
        else
        {
            // convert image to texture
            if (asyncStruct->loadSuccess)
            {
                Image* image = &(asyncStruct->image);
                // generate texture in render thread
                texture = new (std::nothrow) Texture2D();

                texture->initWithImage(image, asyncStruct->pixelFormat);
                //parse 9-patch info
                this->parseNinePatchImage(image, texture, asyncStruct->filename);
#if CC_ENABLE_CACHE_TEXTURE_DATA
                // cache the texture file name
                VolatileTextureMgr::addImageTexture(texture, asyncStruct->filename);
#endif
                // cache the texture. retain it, since it is added in the map
                _textures.emplace(asyncStruct->filename, texture);
                texture->retain();

                texture->autorelease();
                // ETC1 ALPHA supports.
                if (asyncStruct->imageAlpha.getFileType() == Image::Format::ETC1) {
                    auto alphaTexture = new(std::nothrow) Texture2D();
                    if(alphaTexture != nullptr && alphaTexture->initWithImage(&asyncStruct->imageAlpha, asyncStruct->pixelFormat)) {
                        texture->setAlphaTexture(alphaTexture);
                    }
                    CC_SAFE_RELEASE(alphaTexture);
                }
            }
            else {
                texture = nullptr;
                CCLOG("cocos2d: failed to call TextureCache::addImageAsync(%s)", asyncStruct->filename.c_str());
            }
        }

        // call callback function
        if (asyncStruct->callback)
        {
            (asyncStruct->callback)(texture);
        }

        // release the asyncStruct
        delete asyncStruct;
        --_asyncRefCount;
    }

    if (0 == _asyncRefCount)
    {
        Director::getInstance()->getScheduler()->unschedule(CC_SCHEDULE_SELECTOR(TextureCache::addImageAsyncCallBack), this);
    }
    CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::IMAGE_ASYNC);
}

Texture2D * TextureCache::addImage(const std::string &path)
{
    Texture2D * texture = nullptr;
    Image* image = nullptr;
    // Split up directory and filename
    // MUTEX:
    // Needed since addImageAsync calls this method from a different thread
    CrashMsgCollector::getInstance()->addMsg("TextureCache::addImage", path);
    
    std::string fullpath = FileUtils::getInstance()->fullPathForFilename(path);
    if (fullpath.size() == 0)
    {
        return nullptr;
    }
    auto it = _textures.find(fullpath);
    if (it != _textures.end())
        texture = it->second;

    if (!texture)
    {
        // all images are handled by UIImage except PVR extension that is handled by our own handler
        do
        {
            image = new (std::nothrow) Image();
            CC_BREAK_IF(nullptr == image);

            bool bRet = image->initWithImageFile(fullpath);
            CC_BREAK_IF(!bRet);

            texture = new (std::nothrow) Texture2D();

            if (texture && texture->initWithImage(image))
            {
#if CC_ENABLE_CACHE_TEXTURE_DATA
                // cache the texture file name
                VolatileTextureMgr::addImageTexture(texture, fullpath);
#endif
                // texture already retained, no need to re-retain it
                _textures.emplace(fullpath, texture);

                //-- ANDROID ETC1 ALPHA SUPPORTS.
                std::string alphaFullPath = path + s_etc1AlphaFileSuffix;
                if (image->getFileType() == Image::Format::ETC1 && !s_etc1AlphaFileSuffix.empty() && FileUtils::getInstance()->isFileExist(alphaFullPath))
                {
                    Image alphaImage;
                    if (alphaImage.initWithImageFile(alphaFullPath))
                    {
                        Texture2D *pAlphaTexture = new(std::nothrow) Texture2D;
                        if(pAlphaTexture != nullptr && pAlphaTexture->initWithImage(&alphaImage)) {
                            texture->setAlphaTexture(pAlphaTexture);
                        }
                        CC_SAFE_RELEASE(pAlphaTexture);
                    }
                }

                //parse 9-patch info
                this->parseNinePatchImage(image, texture, path);
            }
            else
            {
                CCLOG("cocos2d: Couldn't create texture for file:%s in TextureCache", path.c_str());
                CC_SAFE_RELEASE(texture);
                texture = nullptr;
            }
        } while (0);
    }

    CC_SAFE_RELEASE(image);

    return texture;
}

void TextureCache::parseNinePatchImage(cocos2d::Image *image, cocos2d::Texture2D *texture, const std::string& path)
{
    if (NinePatchImageParser::isNinePatchImage(path))
    {
        Rect frameRect = Rect(0, 0, image->getWidth(), image->getHeight());
        NinePatchImageParser parser(image, frameRect, false);
        texture->addSpriteFrameCapInset(nullptr, parser.parseCapInset());
    }

}

Texture2D* TextureCache::addImage(Image *image, const std::string &key)
{
    CCASSERT(image != nullptr, "TextureCache: image MUST not be nil");
    CCASSERT(image->getData() != nullptr, "TextureCache: image MUST not be nil");

    Texture2D * texture = nullptr;

    do
    {
        auto it = _textures.find(key);
        if (it != _textures.end()) {
            texture = it->second;
            break;
        }

        texture = new (std::nothrow) Texture2D();

        if (texture)
        {
            if (texture->initWithImage(image))
            {
                texture->setPath(key);
                _textures.emplace(key, texture);
#if CC_ENABLE_CACHE_TEXTURE_DATA
                VolatileTextureMgr::addImage(texture, image); // 放在这里避免 image 变成野指针
#endif
            }
            else
            {
                CC_SAFE_RELEASE(texture);
                texture = nullptr;
                CCLOG("cocos2d: initWithImage failed!");
            }
        }
        else
        {
            CCLOG("cocos2d: Allocating memory for Texture2D failed!");
        }

    } while (0);

    return texture;
}

bool TextureCache::reloadTexture(const std::string& fileName)
{
    Texture2D * texture = nullptr;
    Image * image = nullptr;

    std::string fullpath = FileUtils::getInstance()->fullPathForFilename(fileName);
    if (fullpath.size() == 0)
    {
        return false;
    }

    auto it = _textures.find(fullpath);
    if (it != _textures.end()) {
        texture = it->second;
    }

    bool ret = false;
    if (!texture) {
        texture = this->addImage(fullpath);
        ret = (texture != nullptr);
    }
    else
    {
        do {
            image = new (std::nothrow) Image();
            CC_BREAK_IF(nullptr == image);

            bool bRet = image->initWithImageFile(fullpath);
            CC_BREAK_IF(!bRet);

            ret = texture->initWithImage(image);
        } while (0);
    }

    CC_SAFE_RELEASE(image);

    return ret;
}

// TextureCache - Remove

void TextureCache::removeAllTextures()
{
    for (auto it = _textures.cbegin(); it != _textures.cend(); /* nothing */) {
        Texture2D *tex = it->second;
        tex->release();
    }
    _textures.clear();
}

void TextureCache::removeUnusedTextures()
{
    for (auto it = _textures.cbegin(); it != _textures.cend(); /* nothing */) {
        Texture2D *tex = it->second;
        if (tex->getReferenceCount() == 1) {
            CCLOG("cocos2d: TextureCache: removing unused texture: %s", it->first.c_str());

            tex->release();
            it = _textures.erase(it);
        }
        else {
            ++it;
        }

    }
}

void TextureCache::removeTexture(Texture2D* texture)
{
    if (!texture)
    {
        return;
    }

    for (auto it = _textures.cbegin(); it != _textures.cend(); /* nothing */) {
        if (it->second == texture) {
            it->second->release();
            it = _textures.erase(it);
            break;
        }
        else
            ++it;
    }
}

void TextureCache::removeTextureForKey(const std::string &textureKeyName)
{
    std::string key = textureKeyName;
    auto it = _textures.find(key);

    if (it == _textures.end()) {
        key = FileUtils::getInstance()->fullPathForFilename(textureKeyName);
        it = _textures.find(key);
    }

    if (it != _textures.end()) {
        if (it->second->getReferenceCount() > 1) {
            _texturesRemoved.emplace(key, it->second);
        }
        it->second->release();
        _textures.erase(it);
    }
}

void TextureCache::releaseTextureRemoved(const std::string &textureKeyName)
{
    std::string key = textureKeyName;
    auto it = _texturesRemoved.find(key);

    if (it == _texturesRemoved.end()) {
        key = FileUtils::getInstance()->fullPathForFilename(textureKeyName);
        it = _texturesRemoved.find(key);
    }

    if (it != _texturesRemoved.end() && it->second->getReferenceCount() <= 1) {
        _texturesRemoved.erase(it);
    }
}

Texture2D* TextureCache::getTextureForKey(const std::string &textureKeyName) const
{
    std::string key = textureKeyName;
    auto it = _textures.find(key);

    if (it == _textures.end()) {
        key = FileUtils::getInstance()->fullPathForFilename(textureKeyName);
        it = _textures.find(key);
    }

    if (it != _textures.end())
        return it->second;
    return nullptr;
}

void TextureCache::reloadAllTextures()
{
    //will do nothing
    // #if CC_ENABLE_CACHE_TEXTURE_DATA
    //     VolatileTextureMgr::reloadAllTextures();
    // #endif
}

std::string TextureCache::getTextureFilePath(cocos2d::Texture2D* texture) const
{
    for (auto& item : _textures)
    {
        if (item.second == texture)
        {
            return item.first;
            break;
        }
    }
    return "";
}

void TextureCache::waitForQuit()
{
    // notify sub thread to quick
    _needQuit = true;
    _sleepCondition.notify_one();
    if (_loadingThread) _loadingThread->join();

    for(auto ats : _asyncThreadStructVector){
        ats->sleepCondition.notify_one();
        ats->loadingThread->join();
    }
    // _asyncThreadStructVector.clear();
}

std::string TextureCache::getCachedTextureInfo() const
{
    std::string buffer;
    char buftmp[4096];

    unsigned int count = 0;
    unsigned int totalBytes = 0;

    for (auto& texture : _textures) {

        memset(buftmp, 0, sizeof(buftmp));


        Texture2D* tex = texture.second;
        float bpp = tex->getBitsPerPixelForFormat();
        // Each texture takes up width * height * bytesPerPixel bytes.
        auto bytes = tex->getPixelsWide() * tex->getPixelsHigh() * bpp / 8;
        totalBytes += bytes;
        count++;
        snprintf(buftmp, sizeof(buftmp) - 1, "\"%s\" rc=%lu id=%lu %lu x %lu @ %.2f bpp => %lu KB\n",
            texture.first.c_str(),
            (long)tex->getReferenceCount(),
            (long)tex->getName(),
            (long)tex->getPixelsWide(),
            (long)tex->getPixelsHigh(),
            (float)bpp,
            (long)bytes / 1024);

        buffer += buftmp;
    }

    snprintf(buftmp, sizeof(buftmp) - 1, "TextureCache dumpDebugInfo: %ld textures, for %lu KB (%.2f MB)\n", (long)count, (long)totalBytes / 1024, totalBytes / (1024.0f*1024.0f));
    buffer += buftmp;

    return buffer;
}

std::string TextureCache::getCachedTextureRemovedInfo() const
{
    std::string buffer;
    char buftmp[4096];

    unsigned int count = 0;
    unsigned int totalBytes = 0;

    for (auto& texture : _texturesRemoved) {

        memset(buftmp, 0, sizeof(buftmp));


        Texture2D* tex = texture.second;
        float bpp = tex->getBitsPerPixelForFormat();
        // Each texture takes up width * height * bytesPerPixel bytes.
        auto bytes = tex->getPixelsWide() * tex->getPixelsHigh() * bpp / 8;
        totalBytes += bytes;
        count++;
        snprintf(buftmp, sizeof(buftmp) - 1, "\"%s\" rc=%lu id=%lu %lu x %lu @ %.2f bpp => %lu KB\n",
            texture.first.c_str(),
            (long)tex->getReferenceCount(),
            (long)tex->getName(),
            (long)tex->getPixelsWide(),
            (long)tex->getPixelsHigh(),
            (float)bpp,
            (long)bytes / 1024);

        buffer += buftmp;
    }

    snprintf(buftmp, sizeof(buftmp) - 1, "TextureCache dumpDebugRemovedInfo: %ld textures, for %lu KB (%.2f MB)\n", (long)count, (long)totalBytes / 1024, totalBytes / (1024.0f*1024.0f));
    buffer += buftmp;

    return buffer;
}

void TextureCache::renameTextureWithKey(const std::string& srcName, const std::string& dstName)
{
    std::string key = srcName;
    auto it = _textures.find(key);

    if (it == _textures.end()) {
        key = FileUtils::getInstance()->fullPathForFilename(srcName);
        it = _textures.find(key);
    }

    if (it != _textures.end()) {
        std::string fullpath = FileUtils::getInstance()->fullPathForFilename(dstName);
        Texture2D* tex = it->second;

        Image* image = new (std::nothrow) Image();
        if (image)
        {
            bool ret = image->initWithImageFile(dstName);
            if (ret)
            {
                tex->initWithImage(image);
                _textures.emplace(fullpath, tex);
                _textures.erase(it);
            }
            CC_SAFE_DELETE(image);
        }
    }
}

/** 对fileName进行尾缀进行png转astc处理
* [by sunyungao 2024/12/17]
*/
void TextureCache::checkCompressedASTCType(std::string *fileName)
{
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
    if (!_isIgnorCompressedASTCType
        && Configuration::getInstance()->supportsASTC()
        && fileName->size() >= s_pngFileSuffix.size()
        && fileName->compare(fileName->size() - s_pngFileSuffix.size(), s_pngFileSuffix.size(), s_pngFileSuffix) == 0)
    {
        fileName->replace(fileName->size() - s_pngFileSuffix.size(), s_pngFileSuffix.size(), s_astcFileSuffix);
    }
#endif
}

void TextureCache::setIgnorCompressedASTCType(bool isIgnor)
{
    _isIgnorCompressedASTCType = isIgnor;
}

bool TextureCache::getIgnorCompressedASTCType()
{
    return _isIgnorCompressedASTCType;
}


#if CC_ENABLE_CACHE_TEXTURE_DATA

std::list<VolatileTexture*> VolatileTextureMgr::_textures;
bool VolatileTextureMgr::_isReloading = false;

VolatileTexture::VolatileTexture(Texture2D *t)
: _texture(t)
, _uiImage(nullptr)
, _cashedImageType(kInvalid)
, _textureData(nullptr)
, _pixelFormat(Texture2D::PixelFormat::RGBA8888)
, _fileName("")
, _hasMipmaps(false)
, _text("")
{
    _texParams.minFilter = GL_LINEAR;
    _texParams.magFilter = GL_LINEAR;
    _texParams.wrapS = GL_CLAMP_TO_EDGE;
    _texParams.wrapT = GL_CLAMP_TO_EDGE;
}

VolatileTexture::~VolatileTexture()
{
    CC_SAFE_RELEASE(_uiImage);
}

void VolatileTextureMgr::addImageTexture(Texture2D *tt, const std::string& imageFileName)
{
    if (_isReloading)
    {
        return;
    }

    VolatileTexture *vt = findVolotileTexture(tt);

    vt->_cashedImageType = VolatileTexture::kImageFile;
    vt->_fileName = imageFileName;
    vt->_pixelFormat = tt->getPixelFormat();
}

void VolatileTextureMgr::addImage(Texture2D *tt, Image *image)
{
    if (tt == nullptr || image == nullptr)
        return;
    
    VolatileTexture *vt = findVolotileTexture(tt);
    image->retain();
    vt->_uiImage = image;
    vt->_cashedImageType = VolatileTexture::kImage;
}

VolatileTexture* VolatileTextureMgr::findVolotileTexture(Texture2D *tt)
{
    VolatileTexture *vt = nullptr;
    for (const auto& texture : _textures)
    {
        VolatileTexture *v = texture;
        if (v->_texture == tt)
        {
            vt = v;
            break;
        }
    }

    if (!vt)
    {
        vt = new (std::nothrow) VolatileTexture(tt);
        _textures.push_back(vt);
    }

    return vt;
}

void VolatileTextureMgr::addDataTexture(Texture2D *tt, void* data, int dataLen, Texture2D::PixelFormat pixelFormat, const Size& contentSize)
{
    if (_isReloading)
    {
        return;
    }

    VolatileTexture *vt = findVolotileTexture(tt);

    vt->_cashedImageType = VolatileTexture::kImageData;
    vt->_textureData = data;
    vt->_dataLen = dataLen;
    vt->_pixelFormat = pixelFormat;
    vt->_textureSize = contentSize;
}

void VolatileTextureMgr::addStringTexture(Texture2D *tt, const char* text, const FontDefinition& fontDefinition)
{
    if (_isReloading)
    {
        return;
    }

    VolatileTexture *vt = findVolotileTexture(tt);

    vt->_cashedImageType = VolatileTexture::kString;
    vt->_text = text;
    vt->_fontDefinition = fontDefinition;
}

void VolatileTextureMgr::setHasMipmaps(Texture2D *t, bool hasMipmaps)
{
    VolatileTexture *vt = findVolotileTexture(t);
    vt->_hasMipmaps = hasMipmaps;
}

void VolatileTextureMgr::setTexParameters(Texture2D *t, const Texture2D::TexParams &texParams)
{
    VolatileTexture *vt = findVolotileTexture(t);

    if (texParams.minFilter != GL_NONE)
        vt->_texParams.minFilter = texParams.minFilter;
    if (texParams.magFilter != GL_NONE)
        vt->_texParams.magFilter = texParams.magFilter;
    if (texParams.wrapS != GL_NONE)
        vt->_texParams.wrapS = texParams.wrapS;
    if (texParams.wrapT != GL_NONE)
        vt->_texParams.wrapT = texParams.wrapT;
}

void VolatileTextureMgr::removeTexture(Texture2D *t)
{
    for (auto& item : _textures)
    {
        VolatileTexture *vt = item;
        if (vt->_texture == t)
        {
            _textures.remove(vt);
            delete vt;
            break;
        }
    }
}

void VolatileTextureMgr::reloadAllTextures()
{
    _isReloading = true;

    // we need to release all of the glTextures to avoid collisions of texture id's when reloading the textures onto the GPU
    for (auto& item : _textures)
    {
        item->_texture->releaseGLTexture();
    }

    CCLOG("reload all texture");
    log("reloadAllTextures size: %d", static_cast<int>(_textures.size()));

    for (auto& texture : _textures)
    {
        VolatileTexture *vt = texture;

        switch (vt->_cashedImageType)
        {
        case VolatileTexture::kImageFile:
        {
            reloadTexture(vt->_texture, vt->_fileName, vt->_pixelFormat);

            // etc1 support check whether alpha texture exists & load it
            auto alphaFile = vt->_fileName + TextureCache::getETC1AlphaFileSuffix();
            reloadTexture(vt->_texture->getAlphaTexture(), alphaFile, vt->_pixelFormat);
        }
        break;
        case VolatileTexture::kImageData:
        {
            vt->_texture->initWithData(vt->_textureData,
                vt->_dataLen,
                vt->_pixelFormat,
                vt->_textureSize.width,
                vt->_textureSize.height,
                vt->_textureSize);
        }
        break;
        case VolatileTexture::kString:
        {
            vt->_texture->initWithString(vt->_text.c_str(), vt->_fontDefinition);
        }
        break;
        case VolatileTexture::kImage:
        {
            vt->_texture->initWithImage(vt->_uiImage);
        }
        break;
        default:
            break;
        }
        if (vt->_hasMipmaps) {
            vt->_texture->generateMipmap();
        }
        vt->_texture->setTexParameters(vt->_texParams);
    }

    log("reloadAllTextures end");

    _isReloading = false;
}

void VolatileTextureMgr::reloadTexture(Texture2D* texture, const std::string& filename, Texture2D::PixelFormat pixelFormat)
{
    if (!texture)
        return;

    Image* image = new (std::nothrow) Image();
    Data data = FileUtils::getInstance()->getDataFromFile(filename);

    if (image && image->initWithImageData(data.getBytes(), data.getSize()))
        texture->initWithImage(image, pixelFormat);

    CC_SAFE_RELEASE(image);
}

#endif // CC_ENABLE_CACHE_TEXTURE_DATA

NS_CC_END

