/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/

/**
 * @addtogroup CRICOCOS2D
 * @{
 */

/**
 * @file cricocos2d/mana/Sprite.h
 */


/* Prevention of redefinition */
#ifndef CRICOCOS2D_MANA_SPRITE_H
#define CRICOCOS2D_MANA_SPRITE_H


#include "cricocos2d/mana/SofdecPlayer.h"


/**
 * @~japanese
 * @brief cricocos2d::mana::Sprite::getPolygonInfo, cricocos2d::mana::Sprite::setPolygonInfo が使用可能か
 */
#define CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO    (COCOS2D_VERSION >= 0x00030700)


namespace cricocos2d
{
	namespace mana
	{
        /**
         * @headerfile cricocos2d/mana/Sprite.h
         *
         * @~chinese
         * @brief 作为精灵渲染电影（@ref cricocos2d::mana::Player）的类
         *
         * 此类用于将 Player 输出的电影帧作为精灵进行渲染。\n
         * 本类不包含控制电影播放的成员函数。
         * 请对通过 #setPlayer 设置或通过 #getPlayer 成员函数获取的 Player 对象进行电影播放控制。\n
         * 另外，您还可以共享一个 Player 在多个 Sprite 中显示。
         *
         * @remarks
         * Sprite 的初始四边形大小为 128x128。\n
         * 根据电影的解析度等，您可以使用 #setQuadVertices 设置四边形的顶点坐标，
         * 或者使用 #setPolygonInfo 设置多边形信息。
         *
         */
		class SofdecSprite : public cocos2d::Node
		{
		public:
            /**
             * @~chinese
             * @brief 创建 Sprite 的实例
             *
             * 创建一个 Sprite 的实例。\n
             * 通过这个函数生成的 Sprite 并未设置 Player。\n
             * 要渲染电影，您需要使用 #setPlayer 设置一个有效的 Player。
             *
             * @return    创建的 Sprite 实例
             */
			static SofdecSprite* create();

            /**
             * @~chinese
             * @brief 创建已设置 Player 的 Sprite 实例
             *
             * 创建一个已设置了 Player 的 Sprite 实例。\n
             * 该函数生成的 Sprite 已经设置了默认构建的 Player。\n
             * 要播放和渲染电影，请使用 #getPlayer 获取 Player 进行播放控制。
             *
             * @return    创建的已设置 Player 的 Sprite 实例
             */
			static SofdecSprite* createWithPlayer();

            /**
             * @~chinese
             * @brief 获取设置的 Player
             *
             * 获取设置的 Player。\n
             * 如果需要进行电影播放控制、播放参数设置，
             * 或者在多个 Sprite 中共享 Player，请使用此方法。
             *
             * @return    指向设置的 Player 的 shared_ptr（如果没有设置 Player，则为 nullptr）
             */
            SofdecPlayer* getPlayer() const CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置 Player
             *
             * 设置 Player。\n
             * 请设置预先生成的 Player，或者从其他 Sprite 的 #getPlayer 取得的 Player。\n
             * 如果传递 nullptr，将取消设置的 Player。
             *
             * @param[in]    player    指向 Player 的 shared_ptr
             */
			void setPlayer(SofdecPlayer* player) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置加法合成模式
             *
             * @param[in]    sw    是否执行加法合成
             */
			void setAdditive(bool sw) CRICOCOS2D_NOEXCEPT;

            /**
             * @~chinese
             * @brief 设置四边形的顶点坐标
             *
             * 设置用于绘制的四边形的顶点坐标。
             *
             * @attention
             * 调用此成员函数将会丢弃通过 #setPolygonInfo 设置的多边形，不再用于绘制。
             *
             * @param[in]    rect    表示顶点坐标的矩形区域
             */
			void setQuadVertices(const cocos2d::Rect& rect);

            /**
             * @~chinese
             * @brief 设置四边形的纹理坐标
             *
             * 设置用于绘制的四边形的纹理坐标。
             *
             * @attention
             * 调用此成员函数将会丢弃通过 #setPolygonInfo 设置的多边形，不再用于绘制。
             *
             * @param[in]    rect    表示纹理坐标的矩形区域
             */
			void setQuadTexCoords(const cocos2d::Rect& rect);

#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
            /**
             * @~chinese
             * @brief 获取 cocos2d::PolygonInfo 的副本
             *
             * 获取用于绘制的 cocos2d::PolygonInfo 的副本。
             *
             * @attention
             * 此函数获取的 cocos2d::PolygonInfo 是内部持有的 cocos2d::PolygonInfo 的副本，所以即使对其进行修改，
             * 也不会改变用于绘制的 cocos2d::PolygonInfo。\n
             * 要更改用于绘制的 cocos2d::PolygonInfo，请使用 #setPolygonInfo。
             *
             * @return 用于绘制的 cocos2d::PolygonInfo 的副本
             */
			cocos2d::PolygonInfo getPolygonInfo() const;

            /**
             * @~chinese
             * @brief 设置 cocos2d::PolygonInfo
             *
             * 设置用于绘制的 cocos2d::PolygonInfo。
             *
             * @attention
             * 调用此成员函数后，通过 #setQuadTexCoords 设置的四边形纹理坐标和
             * 通过 #setQuadVertices 设置的四边形顶点信息将不再用于绘制。
             *
             * @attention
             * 此函数会复制参数的 cocos2d::PolygonInfo 并持有，因此，即使对传递的 cocos2d::PolygonInfo 进行更改，
             * 也不会更改用于绘制的 cocos2d::PolygonInfo。\n
             * 若要再次更改用于绘制的 cocos2d::PolygonInfo，请再次使用 #setPolygonInfo。
             *
             * @param[in]    polygon_info    用于绘制的 cocos2d::PolygonInfo
             */
			void setPolygonInfo(const cocos2d::PolygonInfo& polygon_info);
#endif
            
            /**
             * @~chinese
             * @brief 绘制函数
             *
             * 这是绘制函数。\n
             * 它由 Cocos2d 的绘制系统调用。
             *
             * @attention
             * 请不要直接从应用程序中调用。
             *
             * @param[in]    renderer
             * @param[in]    transform
             * @param[in]    transformUpdated
             *
             */
			void draw(cocos2d::Renderer* renderer, const cocos2d::Mat4& transform, uint32_t transformUpdated) override;


			/** @cond false */


		protected:
			void updateColor() override;


		  CC_CONSTRUCTOR_ACCESS:
			// Sprite should be created using create;
			SofdecSprite() CRICOCOS2D_NOEXCEPT;
			~SofdecSprite();

			bool init() override;
			/** @endcond */


		private:
            SofdecPlayer*       	  player_;
			cocos2d::CustomCommand	  custom_command_;
			bool					  render_additive_;
			cocos2d::Mat4			  transform_;
			cocos2d::V3F_C4B_T2F_Quad quad_;
#if CRICOCOS2D_MANA_SPRITE_SUPPORT_POLYGON_INFO
			cocos2d::PolygonInfo	  polygon_info_;
#endif
		};
	}
}


#endif


/**
 * @}
 */
