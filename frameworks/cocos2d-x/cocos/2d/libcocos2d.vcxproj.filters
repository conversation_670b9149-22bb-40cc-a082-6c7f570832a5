﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="physics">
      <UniqueIdentifier>{08593631-5bf5-46aa-9436-62595c4f7bf6}</UniqueIdentifier>
    </Filter>
    <Filter Include="deprecated">
      <UniqueIdentifier>{0b1152b1-c732-4560-8629-87843b0fbd7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="2d">
      <UniqueIdentifier>{0965e868-aacd-4d73-9c78-31d3cdaaed52}</UniqueIdentifier>
    </Filter>
    <Filter Include="platform">
      <UniqueIdentifier>{4d0146d9-df5b-4430-a426-60aa395750a6}</UniqueIdentifier>
    </Filter>
    <Filter Include="renderer">
      <UniqueIdentifier>{a495c9fc-5276-476d-ba6b-5d627b31ef30}</UniqueIdentifier>
    </Filter>
    <Filter Include="base">
      <UniqueIdentifier>{87a7d557-f382-477f-b183-69901a320c17}</UniqueIdentifier>
    </Filter>
    <Filter Include="platform\win32">
      <UniqueIdentifier>{9ac3f4cb-fb7b-4bcc-8b5b-1d454f2ff564}</UniqueIdentifier>
    </Filter>
    <Filter Include="platform\desktop">
      <UniqueIdentifier>{fdea951b-e91d-45da-b5bd-22a1b875960a}</UniqueIdentifier>
    </Filter>
    <Filter Include="platform\win32\compat">
      <UniqueIdentifier>{3622d05e-fcef-4d4b-a51d-771d1c13a2ef}</UniqueIdentifier>
    </Filter>
    <Filter Include="math">
      <UniqueIdentifier>{a565f213-2d13-4d2d-a3a0-5b2d06cbd05f}</UniqueIdentifier>
    </Filter>
    <Filter Include="external">
      <UniqueIdentifier>{ec174f10-2de3-4568-9f30-b2f0a4e9396e}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\tinyxml2">
      <UniqueIdentifier>{364c3b8c-77eb-46ed-a866-a8686b1b65bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\unzip">
      <UniqueIdentifier>{589928bf-e550-41f1-ae21-64443dd5fe21}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\edtaa">
      <UniqueIdentifier>{1849ae10-8a10-4784-bbad-66fa67a1ed04}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\ConvertUTF">
      <UniqueIdentifier>{6c1e4a6b-c168-436b-aa63-0af7f4caebf9}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\xxhash">
      <UniqueIdentifier>{b4e2b1e5-2d79-44a3-af45-728d47b7bdb2}</UniqueIdentifier>
    </Filter>
    <Filter Include="storage">
      <UniqueIdentifier>{44bdf58f-4af2-433c-b4af-58dc05ef96b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="3d">
      <UniqueIdentifier>{63733c51-582a-4f0f-9a82-e066da459a72}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio">
      <UniqueIdentifier>{dbdbbfad-71fc-4f1d-a809-b43651f87267}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosdenshion">
      <UniqueIdentifier>{3bd71d42-dfa4-4649-bb01-ad607fdca1c2}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui">
      <UniqueIdentifier>{704c4ce6-b7ad-4406-9414-71169734bbc1}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension">
      <UniqueIdentifier>{976bf662-699e-482e-b5db-a20d4abab137}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\AssetsManager">
      <UniqueIdentifier>{ad47c713-2196-4c8f-9205-2a2a7ecc0b80}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\GUI">
      <UniqueIdentifier>{b27aba95-51a2-413c-8570-0aff9adf2b6b}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\GUI\CCScrollView">
      <UniqueIdentifier>{a1f539bc-d5be-4224-a4d2-01c0b6f17d6e}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\physics_nodes">
      <UniqueIdentifier>{1de7fce7-0dee-4571-8fcd-43eb617aaf8b}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosdenshion\Header Files">
      <UniqueIdentifier>{f42979de-0079-4eba-b469-81ebff9ec588}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosdenshion\Source Files">
      <UniqueIdentifier>{f42ec238-cddc-475a-b343-6d0984cb9681}</UniqueIdentifier>
    </Filter>
    <Filter Include="network">
      <UniqueIdentifier>{31338a7d-ebe1-4867-9c17-d3645122a864}</UniqueIdentifier>
    </Filter>
    <Filter Include="network\Header Files">
      <UniqueIdentifier>{5a094fe5-f941-4dd4-a892-28d721162ea7}</UniqueIdentifier>
    </Filter>
    <Filter Include="network\Source Files">
      <UniqueIdentifier>{9702eb68-42c9-405a-bc89-a1bd85a40ec7}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\BaseClasses">
      <UniqueIdentifier>{ad654e6b-96ee-4693-9789-dc4aa1c52e70}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\Layouts">
      <UniqueIdentifier>{177a9a30-a4a6-41e6-93f0-4a4cbe9e4039}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\System">
      <UniqueIdentifier>{c2ebbbc1-85b3-4d6f-a3c5-116eae2124e1}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\UIWidgets">
      <UniqueIdentifier>{0d201449-ce7a-4f87-86ab-9c30e803c901}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\UIWidgets\ScrollWidget">
      <UniqueIdentifier>{6ac0e3c8-d5b1-44d9-8c41-21662a767cc6}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\TimelineAction">
      <UniqueIdentifier>{c75d4c37-d555-4a5b-a0ba-4bc9a3d846e1}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader">
      <UniqueIdentifier>{67bdb22e-8cfc-41ed-bb07-861e88a31752}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader">
      <UniqueIdentifier>{284a709e-9efa-484a-a595-83db6c7836c8}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ButtonReader">
      <UniqueIdentifier>{3c7267f2-06ea-4c7c-a13c-552076a3dc8c}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\CheckBoxReader">
      <UniqueIdentifier>{b47c2297-bf5e-43e6-ae70-741efee00689}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ImageViewReader">
      <UniqueIdentifier>{1b5e8a9e-87cf-4800-bb7b-4f63504fb132}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\LayoutReader">
      <UniqueIdentifier>{d47fb3dd-9ab8-4559-b8b5-776d87aba319}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ListViewReader">
      <UniqueIdentifier>{65c0429b-5a85-46ac-ab4a-edb0aeb5d388}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\LoadingBarReader">
      <UniqueIdentifier>{8bdf34ea-1e66-41b0-9dbf-7530ba14dce5}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\PageViewReader">
      <UniqueIdentifier>{690fb572-be0b-4bb6-9b8b-a007afb99b39}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ScrollViewReader">
      <UniqueIdentifier>{5e397fa9-4e36-43f3-8c4f-1dd3382c1458}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SliderReader">
      <UniqueIdentifier>{fa2abcd0-6362-4741-a144-a3b0226fe4f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextAtlasReader">
      <UniqueIdentifier>{03596848-0d59-4d69-8193-30f404683c7a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextBMFontReader">
      <UniqueIdentifier>{4d27423a-d9e8-496b-bcc4-8684230c6c18}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextFieldReader">
      <UniqueIdentifier>{075b4cf0-63ae-4cb1-a6d8-e7cb24146531}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TextReader">
      <UniqueIdentifier>{9421ece2-69f8-4cee-8e59-575c3ba06f15}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Node3DReader">
      <UniqueIdentifier>{9421ece2-69f8-4cee-8e59-51043ba06f15}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\UserCameraReader">
      <UniqueIdentifier>{9421e104-69f8-4cee-8e59-51043ba06f15}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Particle3DReader">
      <UniqueIdentifier>{9421e104-1028-4cee-8e59-51043ba06f15}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\Sprite3DReader">
      <UniqueIdentifier>{94210432-69f8-4cee-8e59-51043ba06f15}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\json">
      <UniqueIdentifier>{a9901b6a-0c7b-4adb-8e17-105c872d744d}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\json\rapidjson">
      <UniqueIdentifier>{a9a3bf20-9daf-465d-9525-cce2ab0d85a3}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\json\rapidjson\internal">
      <UniqueIdentifier>{0cf2210c-3544-41ed-9d4e-88987a1c7bcf}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\components">
      <UniqueIdentifier>{dc6e53e4-e518-403f-b2a9-97f6f7cd0961}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature">
      <UniqueIdentifier>{22a4587f-d4ca-44fa-a734-ded122cd79e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\animation">
      <UniqueIdentifier>{137efcbb-0d14-4f1c-a856-7b1669a6d2af}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\datas">
      <UniqueIdentifier>{4825cd2d-ca8b-434c-8c79-2d3d3258086f}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\display">
      <UniqueIdentifier>{57ba2fcb-04bd-4b2f-9ae6-375992ad363a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\physics">
      <UniqueIdentifier>{acdfaa6f-1374-4611-a5e7-9038dc6900c4}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\armature\utils">
      <UniqueIdentifier>{f195e2f1-1030-4848-9498-f8142bf89009}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\action">
      <UniqueIdentifier>{d48c8016-e933-48dd-a5c0-202b0a84b82a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosbuilder">
      <UniqueIdentifier>{e25a6c9d-eaf2-446f-b9b2-d89fd21a9bdd}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosbuilder\Header Files">
      <UniqueIdentifier>{bbba68d3-e519-4e06-8a01-d8ee89b544f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocosbuilder\Source Files">
      <UniqueIdentifier>{8579ed15-b266-4f80-818d-a3a9251c4248}</UniqueIdentifier>
    </Filter>
    <Filter Include="ui\UIWidgets\EditBox">
      <UniqueIdentifier>{89eb6312-dc7f-4fda-b9b2-ab67c31ddd55}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\GUI\CCControlExtensions">
      <UniqueIdentifier>{118d80ca-ccf2-480c-b87d-25220af23440}</UniqueIdentifier>
    </Filter>
    <Filter Include="audioengine">
      <UniqueIdentifier>{e916e2b4-0a6e-4d25-8b36-66fa03719f48}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\TimelineAction\trigger">
      <UniqueIdentifier>{0554a5b1-03a1-4d38-87a5-976dbe9a39d9}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\json\flatbuffers">
      <UniqueIdentifier>{db915acb-1838-413d-b290-dc280365eabc}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ComAudioReader">
      <UniqueIdentifier>{b71e98df-9863-4ee5-9818-04fad2ef8da1}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\GameMapReader">
      <UniqueIdentifier>{78a3fbcc-4233-47f0-9b53-5a445dad9400}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\NodeReader">
      <UniqueIdentifier>{737cc376-2a40-4203-8280-842362c5db49}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ParticleReader">
      <UniqueIdentifier>{7604b98b-40ff-440f-8335-7729e1fa310c}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ProjectNodeReader">
      <UniqueIdentifier>{da363675-4650-4c12-892e-d5755a8b608f}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SingleNodeReader">
      <UniqueIdentifier>{39a43e29-dd58-47a3-9906-bf714b185f6a}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SpriteReader">
      <UniqueIdentifier>{9aee531c-f935-4836-bf84-be42e78e38bb}</UniqueIdentifier>
    </Filter>
    <Filter Include="base\allocator">
      <UniqueIdentifier>{92ff4e66-3943-47da-a439-c8f182bb871a}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\Particle3D">
      <UniqueIdentifier>{f646bed2-5709-4436-82a4-b553ff7cd85c}</UniqueIdentifier>
    </Filter>
    <Filter Include="extension\Particle3D\ParticleUniverse">
      <UniqueIdentifier>{ed9d0632-d777-44fe-b754-29d08f3b0572}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\ArmatureNodeReader">
      <UniqueIdentifier>{e1848cce-b225-42c4-bb6e-6430b6da123b}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\poly2tri">
      <UniqueIdentifier>{46294cdb-c29a-4480-9988-2e017f5f7846}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\poly2tri\common">
      <UniqueIdentifier>{b0703876-33ee-433c-bba3-45304ce83813}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\poly2tri\sweep">
      <UniqueIdentifier>{c37eceeb-5702-4ff7-88de-94680a22266f}</UniqueIdentifier>
    </Filter>
    <Filter Include="physics3d">
      <UniqueIdentifier>{e492faef-2169-4117-8d73-e0c66271fe25}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\clipper">
      <UniqueIdentifier>{ff65e340-b79d-4f87-9b4c-ed46bda5d20f}</UniqueIdentifier>
    </Filter>
    <Filter Include="navmesh">
      <UniqueIdentifier>{0f3fa25d-9e2b-4a2e-a1df-834b796b3fb3}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\SkeletonReader">
      <UniqueIdentifier>{8405cf93-e86c-4ec1-8b16-01058f542b09}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\TimelineAction\Skeleton">
      <UniqueIdentifier>{ca5e1475-3bfd-4aae-ad54-2e19d5021b92}</UniqueIdentifier>
    </Filter>
    <Filter Include="cocostudio\reader\WidgetReader\TabControlReader">
      <UniqueIdentifier>{8686c220-18af-4bea-abce-f4797afd01f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="vr">
      <UniqueIdentifier>{5cbd879f-02ae-4f28-af33-2c4f980dd6f0}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\md5">
      <UniqueIdentifier>{d3d38486-e13f-47b4-96b0-51a7c78e6d69}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\physics\CCPhysicsBody.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\physics\CCPhysicsContact.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\physics\CCPhysicsJoint.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\physics\CCPhysicsShape.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\physics\CCPhysicsWorld.cpp">
      <Filter>physics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\xxhash\xxhash.c">
      <Filter>external\xxhash</Filter>
    </ClCompile>
    <ClCompile Include="..\deprecated\CCArray.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\deprecated\CCDeprecated.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\deprecated\CCDictionary.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\deprecated\CCNotificationCenter.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\deprecated\CCSet.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\deprecated\CCString.cpp">
      <Filter>deprecated</Filter>
    </ClCompile>
    <ClCompile Include="..\cocos2d.cpp" />
    <ClCompile Include="..\..\external\ConvertUTF\ConvertUTF.c">
      <Filter>external\ConvertUTF</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\ConvertUTF\ConvertUTFWrapper.cpp">
      <Filter>external\ConvertUTF</Filter>
    </ClCompile>
    <ClCompile Include="CCAction.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionCamera.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionCatmullRom.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionEase.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionGrid3D.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionInstant.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionInterval.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionManager.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionPageTurn3D.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionProgressTimer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionTiledGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCActionTween.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCAnimation.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCAnimationCache.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCAtlasNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCClippingNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCClippingRectangleNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCComponent.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCComponentContainer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCDrawingPrimitives.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCDrawNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCFontAtlas.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCFontAtlasCache.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCFontCharMap.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCFontFNT.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCFontFreeType.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCGLBufferedNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCGrabber.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLabel.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLabelAtlas.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLabelBMFont.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLabelTextFormatter.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLabelTTF.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLayer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCMenu.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCMenuItem.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCMotionStreak.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCNodeGrid.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCParallaxNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCParticleBatchNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCParticleExamples.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCParticleSystem.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCParticleSystemQuad.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCProgressTimer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCRenderTexture.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCScene.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCSprite.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCSpriteBatchNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCSpriteFrame.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCSpriteFrameCache.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTextFieldTTF.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTileMapAtlas.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTMXLayer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTMXObjectGroup.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTMXTiledMap.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTMXXMLParser.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTransition.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTransitionPageTurn.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTransitionProgress.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCTweenFunction.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\base\atitc.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\base64.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCAutoreleasePool.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ccCArray.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCConfiguration.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCConsole.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCData.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCDataVisitor.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCDirector.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEvent.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventAcceleration.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventCustom.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventDispatcher.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventFocus.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventKeyboard.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListener.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListenerAcceleration.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListenerCustom.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListenerFocus.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListenerKeyboard.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListenerMouse.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventListenerTouch.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventMouse.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCEventTouch.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ccFPSImages.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCNS.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCProfiling.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCRef.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCScheduler.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCScriptSupport.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCTouch.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ccTypes.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCUserDefault.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ccUTF8.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ccUtils.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCValue.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\etc1.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\pvr.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\base\s3tc.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\TGAlib.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ZipUtils.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCBatchCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCCustomCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCTrianglesCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCGLProgram.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCGLProgramCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCGLProgramState.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCGLProgramStateCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\ccGLStateCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCGroupCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCQuadCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCRenderCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCRenderer.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\ccShaders.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCTexture2D.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCTextureAtlas.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCTextureCache.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\math\CCAffineTransform.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\CCGeometry.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\CCVertex.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\Mat4.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\MathUtil.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\Quaternion.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\TransformUtils.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\Vec2.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\Vec3.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\math\Vec4.cpp">
      <Filter>math</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\CCFileUtils.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\CCImage.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\CCSAXParser.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\CCThread.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\tinyxml2\tinyxml2.cpp">
      <Filter>external\tinyxml2</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\unzip\ioapi_mem.cpp">
      <Filter>external\unzip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\unzip\ioapi.cpp">
      <Filter>external\unzip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\unzip\unzip.cpp">
      <Filter>external\unzip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\edtaa3func\edtaa3func.cpp">
      <Filter>external\edtaa</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCIMEDispatcher.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCMeshCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ObjectFactory.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="CCFastTMXLayer.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCFastTMXTiledMap.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\storage\local-storage\LocalStorage.cpp">
      <Filter>storage</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\CCGLView.cpp">
      <Filter>platform</Filter>
    </ClCompile>
    <ClCompile Include="CCProtectedNode.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCPrimitive.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCPrimitiveCommand.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCVertexIndexBuffer.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCVertexIndexData.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\base\ccRandom.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCAnimate3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCAnimation3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCAttachNode.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCBundle3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCBundleReader.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCMesh.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCMeshSkin.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCOBB.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCObjLoader.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCRay.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCSkeleton3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCSprite3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCSprite3DMaterial.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCMotionStreak3D.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\assets-manager\AssetsManager.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCScrollView\CCScrollView.cpp">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCScrollView\CCTableView.cpp">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCScrollView\CCTableViewCell.cpp">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\physics-nodes\CCPhysicsDebugNode.cpp">
      <Filter>extension\physics_nodes</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\physics-nodes\CCPhysicsSprite.cpp">
      <Filter>extension\physics_nodes</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\MciPlayer.cpp">
      <Filter>cocosdenshion\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\SimpleAudioEngine.cpp">
      <Filter>cocosdenshion\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\network\HttpClient.cpp">
      <Filter>network\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\network\SocketIO.cpp">
      <Filter>network\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\network\WebSocket.cpp">
      <Filter>network\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIScale9Sprite.cpp">
      <Filter>ui\BaseClasses</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIWidget.cpp">
      <Filter>ui\BaseClasses</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIHBox.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UILayout.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UILayoutManager.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UILayoutParameter.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIRelativeBox.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIVBox.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\CocosGUI.cpp">
      <Filter>ui\System</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIDeprecated.cpp">
      <Filter>ui\System</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIHelper.cpp">
      <Filter>ui\System</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIButton.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIAbstractCheckButton.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UICheckBox.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIRadioButton.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIImageView.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UILoadingBar.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIRichText.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UISlider.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIText.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UITextAtlas.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UITextBMFont.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UITextField.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIListView.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIPageView.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIPageViewIndicator.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIScrollView.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIScrollViewBar.cpp">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\TriggerBase.cpp">
      <Filter>cocostudio\TimelineAction\trigger</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\TriggerMng.cpp">
      <Filter>cocostudio\TimelineAction\trigger</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\TriggerObj.cpp">
      <Filter>cocostudio\TimelineAction\trigger</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCActionTimeline.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCFrame.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCActionTimelineCache.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCTimeLine.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCSGUIReader.cpp">
      <Filter>cocostudio\reader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCSSceneReader.cpp">
      <Filter>cocostudio\reader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\WidgetReader.cpp">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ButtonReader\ButtonReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ButtonReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\CheckBoxReader\CheckBoxReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\CheckBoxReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ImageViewReader\ImageViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ImageViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\LayoutReader\LayoutReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\LayoutReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ListViewReader\ListViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ListViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\LoadingBarReader\LoadingBarReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\LoadingBarReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\PageViewReader\PageViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\PageViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ScrollViewReader\ScrollViewReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ScrollViewReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\SliderReader\SliderReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SliderReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\TextAtlasReader\TextAtlasReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextAtlasReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\TextBMFontReader\TextBMFontReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextBMFontReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\TextFieldReader\TextFieldReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextFieldReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\TextReader\TextReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TextReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\Node3DReader\Node3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Node3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\Sprite3DReader\Sprite3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Sprite3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\UserCameraReader\UserCameraReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\UserCameraReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\Particle3DReader\Particle3DReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\Particle3DReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CocoLoader.cpp">
      <Filter>cocostudio\json</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\DictionaryHelper.cpp">
      <Filter>cocostudio\json</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCComAttribute.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCComAudio.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCComController.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCComRender.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCInputDelegate.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCArmature.cpp">
      <Filter>cocostudio\armature</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCBone.cpp">
      <Filter>cocostudio\armature</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCArmatureAnimation.cpp">
      <Filter>cocostudio\armature\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCProcessBase.cpp">
      <Filter>cocostudio\armature\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCTween.cpp">
      <Filter>cocostudio\armature\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCDatas.cpp">
      <Filter>cocostudio\armature\datas</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCBatchNode.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCDecorativeDisplay.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCDisplayFactory.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCDisplayManager.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCSkin.cpp">
      <Filter>cocostudio\armature\display</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCColliderDetector.cpp">
      <Filter>cocostudio\armature\physics</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCArmatureDataManager.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCArmatureDefine.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCDataReaderHelper.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCSpriteFrameCacheHelper.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCTransformHelp.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCUtilMath.cpp">
      <Filter>cocostudio\armature\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCActionFrame.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCActionFrameEasing.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCActionManagerEx.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCActionNode.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCActionObject.cpp">
      <Filter>cocostudio\action</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCMeshVertexIndexData.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCBAnimationManager.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCBFileLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCBKeyframe.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCBReader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCBSequence.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCBSequenceProperty.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCControlButtonLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCControlLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCLabelBMFontLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCLabelTTFLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCLayerColorLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCLayerGradientLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCLayerLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCMenuItemImageLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCMenuItemLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCNode+CCBRelativePositioning.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCNodeLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCNodeLoaderLibrary.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCParticleSystemQuadLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCScale9SpriteLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCScrollViewLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocosbuilder\CCSpriteLoader.cpp">
      <Filter>cocosbuilder\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCBillBoard.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIEditBox\UIEditBox.cpp">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControl.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlButton.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlColourPicker.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlHuePicker.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlPotentiometer.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlSaturationBrightnessPicker.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlSlider.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlStepper.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlSwitch.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCControlUtils.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\GUI\CCControlExtension\CCInvocation.cpp">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\win32\CCApplication-win32.cpp">
      <Filter>platform\win32</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\win32\CCCommon-win32.cpp">
      <Filter>platform\win32</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\win32\CCDevice-win32.cpp">
      <Filter>platform\win32</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\win32\CCFileUtils-win32.cpp">
      <Filter>platform\win32</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\win32\CCStdC-win32.cpp">
      <Filter>platform\win32</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\win32\CCUtils-win32.cpp">
      <Filter>platform\win32</Filter>
    </ClCompile>
    <ClCompile Include="..\platform\desktop\CCGLViewImpl-desktop.cpp">
      <Filter>platform\desktop</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIEditBox\UIEditBoxImpl-win32.cpp">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\assets-manager\AssetsManagerEx.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\assets-manager\CCEventAssetsManagerEx.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\assets-manager\CCEventListenerAssetsManagerEx.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\assets-manager\Manifest.cpp">
      <Filter>extension\AssetsManager</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UIWebView.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\AudioEngine.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioCache.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioEngine-win32.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioPlayer.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CSLoader.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UILayoutComponent.cpp">
      <Filter>ui\Layouts</Filter>
    </ClCompile>
    <ClCompile Include="CCCamera.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="CCLight.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_cpp.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_fbs.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_general.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_go.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_text.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_parser.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_js.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_php.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\idl_gen_python.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\reflection.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\flatbuffers\util.cpp">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\FlatBuffersSerialize.cpp">
      <Filter>cocostudio\json</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ComAudioReader\ComAudioReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ComAudioReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\GameMapReader\GameMapReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\GameMapReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\NodeReader\NodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\NodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ParticleReader\ParticleReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ParticleReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ProjectNodeReader\ProjectNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ProjectNodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\SingleNodeReader\SingleNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SingleNodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\SpriteReader\SpriteReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SpriteReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\NodeReaderDefine.cpp">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\NodeReaderProtocol.cpp">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetCallBackHandlerProtocol.cpp">
      <Filter>cocostudio\json</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCActionTimelineNode.cpp">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCAsyncTaskPool.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\allocator\CCAllocatorDiagnostics.cpp">
      <Filter>base\allocator</Filter>
    </ClCompile>
    <ClCompile Include="..\base\allocator\CCAllocatorGlobal.cpp">
      <Filter>base\allocator</Filter>
    </ClCompile>
    <ClCompile Include="..\base\allocator\CCAllocatorGlobalNewDelete.cpp">
      <Filter>base\allocator</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\ArmatureNodeReader\ArmatureNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\ArmatureNodeReader</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCAABB.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCTerrain.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCSkybox.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUAffectorManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUAlignAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUAlignAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBaseCollider.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBaseColliderTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBaseForceAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBaseForceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBeamRender.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBehaviour.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBehaviourManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBehaviourTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBillboardChain.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBoxCollider.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBoxColliderTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBoxEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUBoxEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUCircleEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUCircleEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUColorAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUColorAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDynamicAttribute.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUDynamicAttributeTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUEmitterManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUEventHandler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUEventHandlerManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUEventHandlerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUForceField.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUForceFieldAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUForceFieldAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUGeometryRotator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUGeometryRotatorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUGravityAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUGravityAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUInterParticleCollider.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUInterParticleColliderTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUJetAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUJetAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPULineAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPULineAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPULinearForceAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPULinearForceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPULineEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPULineEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUListener.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUMaterialManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUMaterialTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUNoise.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUObserverManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnClearObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnClearObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnCollisionObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnCollisionObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnCountObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnCountObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnEmissionObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnEmissionObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnExpireObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnExpireObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnPositionObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnPositionObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnQuotaObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnQuotaObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnRandomObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnRandomObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnTimeObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnTimeObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnVelocityObserver.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUOnVelocityObserverTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUParticleFollower.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUParticleFollowerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUParticleSystem3D.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUParticleSystem3DTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPathFollower.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPathFollowerTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPlane.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPlaneCollider.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPlaneColliderTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPointEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPointEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPositionEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUPositionEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPURandomiser.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPURandomiserTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPURender.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPURendererTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPURibbonTrail.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPURibbonTrailRender.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScaleAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScaleAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScriptCompiler.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScriptLexer.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScriptParser.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUScriptTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSimpleSpline.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSineForceAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSineForceAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSlaveBehaviour.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSlaveBehaviourTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSlaveEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSlaveEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSphere.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSphereCollider.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSphereColliderTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitterTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUTechniqueTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUTextureAnimator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUTextureAnimatorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUTextureRotator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUTextureRotatorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUTranslateManager.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUUtil.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUVertexEmitter.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUVortexAffector.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\PU\CCPUVortexAffectorTranslator.cpp">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\CCParticle3DAffector.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\CCParticle3DEmitter.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\CCParticle3DRender.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\..\extensions\Particle3D\CCParticleSystem3D.cpp">
      <Filter>extension\Particle3D</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CocoStudio.cpp">
      <Filter>cocostudio\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\poly2tri\common\shapes.cc">
      <Filter>external\poly2tri\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\poly2tri\sweep\advancing_front.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\poly2tri\sweep\cdt.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\poly2tri\sweep\sweep.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\poly2tri\sweep\sweep_context.cc">
      <Filter>external\poly2tri\sweep</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3D.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3DComponent.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3DConstraint.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3DDebugDrawer.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3DObject.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3DShape.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysics3DWorld.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\physics3d\CCPhysicsSprite3D.cpp">
      <Filter>physics3d</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCPass.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCRenderState.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCTechnique.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCMaterial.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCProperties.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCVertexAttribBinding.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\navmesh\CCNavMesh.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\navmesh\CCNavMeshAgent.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\navmesh\CCNavMeshDebugDraw.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\navmesh\CCNavMeshObstacle.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\navmesh\CCNavMeshUtils.cpp">
      <Filter>navmesh</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCNinePatchImageParser.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCStencilStateManager.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\base\CCNinePatchImageParser.cpp" />
    <ClCompile Include="..\renderer\CCFrameBuffer.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="CCAutoPolygon.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\clipper\clipper.cpp">
      <Filter>external\clipper</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\GameNode3DReader\GameNode3DReader.cpp" />
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\SkeletonReader\BoneNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\SkeletonReader\SkeletonNodeReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCBoneNode.cpp">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCSkeletonNode.cpp">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\ActionTimeline\CCSkinNode.cpp">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\CCComExtensionData.cpp">
      <Filter>cocostudio\components</Filter>
    </ClCompile>
    <ClCompile Include="..\network\CCDownloader.cpp">
      <Filter>network\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CCCameraBackgroundBrush.cpp">
      <Filter>2d</Filter>
    </ClCompile>
    <ClCompile Include="..\renderer\CCTextureCube.cpp">
      <Filter>renderer</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\Light3DReader\Light3DReader.cpp" />
    <ClCompile Include="..\editor-support\cocostudio\WidgetReader\TabControlReader\TabControlReader.cpp">
      <Filter>cocostudio\reader\WidgetReader\TabControlReader</Filter>
    </ClCompile>
    <ClCompile Include="..\ui\UITabControl.cpp">
      <Filter>ui\UIWidgets</Filter>
    </ClCompile>
    <ClCompile Include="..\editor-support\cocostudio\LocalizationManager.cpp">
      <Filter>cocostudio\json</Filter>
    </ClCompile>
    <ClCompile Include="..\vr\CCVRDistortion.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\vr\CCVRDistortionMesh.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\vr\CCVRGenericHeadTracker.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\vr\CCVRGenericRenderer.cpp">
      <Filter>vr</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioDecoder.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioDecoderMp3.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioDecoderOgg.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\audio\win32\AudioDecoderManager.cpp">
      <Filter>audioengine</Filter>
    </ClCompile>
    <ClCompile Include="..\network\CCDownloader-curl.cpp">
      <Filter>network\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\network\Uri.cpp">
      <Filter>network\Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCFrustum.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\3d\CCPlane.cpp">
      <Filter>3d</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\md5\md5.c">
      <Filter>external\md5</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\physics\CCPhysicsBody.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\physics\CCPhysicsContact.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\physics\CCPhysicsJoint.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\physics\CCPhysicsShape.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\physics\CCPhysicsWorld.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\xxhash\xxhash.h">
      <Filter>external\xxhash</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCArray.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCBool.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCDeprecated.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCDictionary.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCDouble.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCFloat.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCInteger.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCNotificationCenter.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCSet.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\deprecated\CCString.h">
      <Filter>deprecated</Filter>
    </ClInclude>
    <ClInclude Include="..\cocos2d.h" />
    <ClInclude Include="..\..\external\ConvertUTF\ConvertUTF.h">
      <Filter>external\ConvertUTF</Filter>
    </ClInclude>
    <ClInclude Include="CCAction.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionCamera.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionCatmullRom.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionEase.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionGrid3D.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionInstant.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionInterval.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionManager.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionPageTurn3D.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionProgressTimer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionTiledGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCActionTween.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCAnimation.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCAnimationCache.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCAtlasNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCClippingNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCClippingRectangleNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCComponent.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCComponentContainer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCDrawingPrimitives.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCDrawNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFont.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFontAtlas.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFontAtlasCache.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFontCharMap.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFontFNT.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFontFreeType.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCGLBufferedNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCGrabber.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCLabel.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCLabelAtlas.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCLabelBMFont.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCLabelTTF.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCLayer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCMenu.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCMenuItem.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCMotionStreak.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCNodeGrid.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCParallaxNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCParticleBatchNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCParticleExamples.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCParticleSystem.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCParticleSystemQuad.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCProgressTimer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCRenderTexture.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCScene.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCSprite.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCSpriteBatchNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCSpriteFrame.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCSpriteFrameCache.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTextFieldTTF.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTileMapAtlas.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTMXLayer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTMXObjectGroup.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTMXTiledMap.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTMXXMLParser.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTransition.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTransitionPageTurn.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTransitionProgress.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCTweenFunction.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\base\atitc.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\base64.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCAutoreleasePool.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccCArray.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccConfig.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCConfiguration.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCConsole.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCData.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCDataVisitor.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCDirector.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEvent.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventAcceleration.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventCustom.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventDispatcher.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventFocus.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventKeyboard.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListener.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListenerAcceleration.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListenerCustom.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListenerFocus.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListenerKeyboard.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListenerMouse.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventListenerTouch.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventMouse.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventTouch.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCEventType.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccFPSImages.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccMacros.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCMap.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCNS.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCProfiling.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCProtocols.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCRef.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCRefPtr.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCScheduler.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCScriptSupport.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCTouch.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccTypes.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCUserDefault.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccUTF8.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccUtils.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCValue.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCVector.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\etc1.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\pvr.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\base\firePngData.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\s3tc.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\TGAlib.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\uthash.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\utlist.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ZipUtils.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCBatchCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCTrianglesCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCCustomCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCGLProgram.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCGLProgramCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCGLProgramState.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCGLProgramStateCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\ccGLStateCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCGroupCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCQuadCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCRenderCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCRenderCommandPool.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCRenderer.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\ccShaders.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCTexture2D.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCTextureAtlas.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCTextureCache.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\compat\stdint.h">
      <Filter>platform\win32\compat</Filter>
    </ClInclude>
    <ClInclude Include="..\math\CCAffineTransform.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\CCGeometry.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\CCMath.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\CCMathBase.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\CCVertex.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\Mat4.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\MathUtil.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\Quaternion.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\TransformUtils.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\Vec2.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\Vec3.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\math\Vec4.h">
      <Filter>math</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCApplicationProtocol.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCCommon.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCDevice.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCFileUtils.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCImage.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCSAXParser.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCThread.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\tinyxml2\tinyxml2.h">
      <Filter>external\tinyxml2</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\unzip\unzip.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\unzip\ioapi.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\unzip\ioapi_mem.h">
      <Filter>external\unzip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\edtaa3func\edtaa3func.h">
      <Filter>external\edtaa</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCIMEDelegate.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCIMEDispatcher.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCMeshCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ObjectFactory.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="CCFastTMXTiledMap.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCFastTMXLayer.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\storage\local-storage\LocalStorage.h">
      <Filter>storage</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCGLView.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="CCProtectedNode.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCPrimitive.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCPrimitiveCommand.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCVertexIndexBuffer.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCVertexIndexData.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\base\ccRandom.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCAABB.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCAnimate3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCAnimation3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCAnimationCurve.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCAttachNode.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCBundle3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCBundle3DData.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCBundleReader.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCMesh.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCMeshSkin.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCOBB.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCObjLoader.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCRay.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCSkeleton3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCSprite3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCSprite3DMaterial.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCMotionStreak3D.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\cocos3d.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\assets-manager\AssetsManager.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCScrollView\CCScrollView.h">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCScrollView\CCTableView.h">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCScrollView\CCTableViewCell.h">
      <Filter>extension\GUI\CCScrollView</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\physics-nodes\CCPhysicsDebugNode.h">
      <Filter>extension\physics_nodes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\physics-nodes\CCPhysicsSprite.h">
      <Filter>extension\physics_nodes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\cocos-ext.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\ExtensionExport.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\ExtensionMacros.h">
      <Filter>extension</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\include\Export.h">
      <Filter>cocosdenshion\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\MciPlayer.h">
      <Filter>cocosdenshion\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\include\SimpleAudioEngine.h">
      <Filter>cocosdenshion\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\network\HttpClient.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\network\HttpRequest.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\network\HttpResponse.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\network\SocketIO.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\network\WebSocket.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIScale9Sprite.h">
      <Filter>ui\BaseClasses</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIWidget.h">
      <Filter>ui\BaseClasses</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIHBox.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UILayout.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UILayoutManager.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UILayoutParameter.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIRelativeBox.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIVBox.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\CocosGUI.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\GUIExport.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIDeprecated.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIHelper.h">
      <Filter>ui\System</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIButton.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UICheckBox.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIImageView.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UILoadingBar.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIRichText.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UISlider.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIText.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UITextAtlas.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UITextBMFont.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UITextField.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIListView.h">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIPageView.h">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIScrollView.h">
      <Filter>ui\UIWidgets\ScrollWidget</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CocosStudioExport.h">
      <Filter>cocostudio</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\TriggerBase.h">
      <Filter>cocostudio\TimelineAction\trigger</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\TriggerMng.h">
      <Filter>cocostudio\TimelineAction\trigger</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\TriggerObj.h">
      <Filter>cocostudio\TimelineAction\trigger</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCActionTimeline.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCFrame.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCActionTimelineCache.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCTimeLine.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCTimelineMacro.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCSGUIReader.h">
      <Filter>cocostudio\reader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCSSceneReader.h">
      <Filter>cocostudio\reader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\WidgetReader.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\WidgetReaderProtocol.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ButtonReader\ButtonReader.h">
      <Filter>cocostudio\reader\WidgetReader\ButtonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\CheckBoxReader\CheckBoxReader.h">
      <Filter>cocostudio\reader\WidgetReader\CheckBoxReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ImageViewReader\ImageViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\ImageViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\LayoutReader\LayoutReader.h">
      <Filter>cocostudio\reader\WidgetReader\LayoutReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ListViewReader\ListViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\ListViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\LoadingBarReader\LoadingBarReader.h">
      <Filter>cocostudio\reader\WidgetReader\LoadingBarReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\PageViewReader\PageViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\PageViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ScrollViewReader\ScrollViewReader.h">
      <Filter>cocostudio\reader\WidgetReader\ScrollViewReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\SliderReader\SliderReader.h">
      <Filter>cocostudio\reader\WidgetReader\SliderReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\TextAtlasReader\TextAtlasReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextAtlasReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\TextBMFontReader\TextBMFontReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextBMFontReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\TextFieldReader\TextFieldReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextFieldReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\TextReader\TextReader.h">
      <Filter>cocostudio\reader\WidgetReader\TextReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\Node3DReader\Node3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Node3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\UserCameraReader\UserCameraReader.h">
      <Filter>cocostudio\reader\WidgetReader\UserCameraReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\Particle3DReader\Particle3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Particle3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\Sprite3DReader\Sprite3DReader.h">
      <Filter>cocostudio\reader\WidgetReader\Sprite3DReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CocoLoader.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CocoStudio.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\DictionaryHelper.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\document.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\filereadstream.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\filewritestream.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\prettywriter.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\rapidjson.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\reader.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\stringbuffer.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\writer.h">
      <Filter>cocostudio\json\rapidjson</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\internal\pow10.h">
      <Filter>cocostudio\json\rapidjson\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\internal\stack.h">
      <Filter>cocostudio\json\rapidjson\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\json\internal\strfunc.h">
      <Filter>cocostudio\json\rapidjson\internal</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCComAttribute.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCComAudio.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCComBase.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCComController.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCComRender.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCInputDelegate.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCArmature.h">
      <Filter>cocostudio\armature</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCBone.h">
      <Filter>cocostudio\armature</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCArmatureAnimation.h">
      <Filter>cocostudio\armature\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCProcessBase.h">
      <Filter>cocostudio\armature\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCTween.h">
      <Filter>cocostudio\armature\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCDatas.h">
      <Filter>cocostudio\armature\datas</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCBatchNode.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCDecorativeDisplay.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCDisplayFactory.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCDisplayManager.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCSkin.h">
      <Filter>cocostudio\armature\display</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCColliderDetector.h">
      <Filter>cocostudio\armature\physics</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCArmatureDataManager.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCArmatureDefine.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCDataReaderHelper.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCSpriteFrameCacheHelper.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCTransformHelp.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCUtilMath.h">
      <Filter>cocostudio\armature\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCActionFrame.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCActionFrameEasing.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCActionManagerEx.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCActionNode.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCActionObject.h">
      <Filter>cocostudio\action</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCMeshVertexIndexData.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBAnimationManager.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBFileLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBKeyframe.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBMemberVariableAssigner.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBReader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBSelectorResolver.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBSequence.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCBSequenceProperty.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCControlButtonLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCControlLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCLabelBMFontLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCLabelTTFLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCLayerColorLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCLayerGradientLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCLayerLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCMenuItemImageLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCMenuItemLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCMenuLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCNode+CCBRelativePositioning.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCNodeLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCNodeLoaderLibrary.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCNodeLoaderListener.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCParticleSystemQuadLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCScale9SpriteLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCScrollViewLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CCSpriteLoader.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocosbuilder\CocosBuilder.h">
      <Filter>cocosbuilder\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCBillBoard.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIEditBox\UIEditBox.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIEditBox\UIEditBoxImpl.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControl.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlButton.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlColourPicker.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlExtensions.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlHuePicker.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlPotentiometer.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlSaturationBrightnessPicker.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlSlider.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlStepper.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlSwitch.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCControlUtils.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\GUI\CCControlExtension\CCInvocation.h">
      <Filter>extension\GUI\CCControlExtensions</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\CCApplication-win32.h">
      <Filter>platform\win32</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\CCFileUtils-win32.h">
      <Filter>platform\win32</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\CCGL-win32.h">
      <Filter>platform\win32</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\CCPlatformDefine-win32.h">
      <Filter>platform\win32</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\CCStdC-win32.h">
      <Filter>platform\win32</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\win32\CCUtils-win32.h">
      <Filter>platform\win32</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\desktop\CCGLViewImpl-desktop.h">
      <Filter>platform\desktop</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIEditBox\UIEditBoxImpl-win32.h">
      <Filter>ui\UIWidgets\EditBox</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\assets-manager\AssetsManagerEx.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\assets-manager\CCEventAssetsManagerEx.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\assets-manager\CCEventListenerAssetsManagerEx.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\assets-manager\Manifest.h">
      <Filter>extension\AssetsManager</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIWebView-inl.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UIWebView.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\include\AudioEngine.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioCache.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioEngine-win32.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioPlayer.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CSLoader.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UILayoutComponent.h">
      <Filter>ui\Layouts</Filter>
    </ClInclude>
    <ClInclude Include="CCCamera.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="CCLight.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\flatbuffers\flatbuffers.h">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\flatbuffers\idl.h">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\flatbuffers\util.h">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\flatbuffers\hash.h">
      <Filter>cocostudio\json\flatbuffers</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CSParseBinary_generated.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CSParse3DBinary_generated.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\FlatBuffersSerialize.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ComAudioReader\ComAudioReader.h">
      <Filter>cocostudio\reader\WidgetReader\ComAudioReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\GameMapReader\GameMapReader.h">
      <Filter>cocostudio\reader\WidgetReader\GameMapReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\NodeReader\NodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\NodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ParticleReader\ParticleReader.h">
      <Filter>cocostudio\reader\WidgetReader\ParticleReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ProjectNodeReader\ProjectNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\ProjectNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\SingleNodeReader\SingleNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\SingleNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\SpriteReader\SpriteReader.h">
      <Filter>cocostudio\reader\WidgetReader\SpriteReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\NodeReaderDefine.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\NodeReaderProtocol.h">
      <Filter>cocostudio\reader\WidgetReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetCallBackHandlerProtocol.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCActionTimelineNode.h">
      <Filter>cocostudio\TimelineAction</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCAsyncTaskPool.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorGlobal.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorBase.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorDiagnostics.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorMacros.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorMutex.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorStrategyDefault.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorStrategyFixedBlock.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorStrategyGlobalSmallBlock.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\base\allocator\CCAllocatorStrategyPool.h">
      <Filter>base\allocator</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ArmatureNodeReader\ArmatureNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\ArmatureNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\ArmatureNodeReader\CSArmatureNode_generated.h">
      <Filter>cocostudio\reader\WidgetReader\ArmatureNodeReader</Filter>
    </ClInclude>
    <ClInclude Include="..\physics\CCPhysicsHelper.h">
      <Filter>physics</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCTerrain.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCSkybox.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCPlatformConfig.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\platform\CCPlatformMacros.h">
      <Filter>platform</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUAffectorManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUAlignAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUAlignAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBaseCollider.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBaseColliderTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBaseForceAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBaseForceAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBeamRender.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBehaviour.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBehaviourManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBehaviourTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBillboardChain.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBoxCollider.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBoxColliderTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBoxEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUBoxEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUCircleEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUCircleEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUCollisionAvoidanceAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUColorAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUColorAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoAffectorEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoEnableComponentEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoExpireEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoFreezeEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoPlacementParticleEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoScaleEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDoStopSystemEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDynamicAttribute.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUDynamicAttributeTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUEmitterManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUEventHandler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUEventHandlerManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUEventHandlerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUFlockCenteringAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUForceField.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUForceFieldAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUForceFieldAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUGeometryRotator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUGeometryRotatorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUGravityAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUGravityAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUInterParticleCollider.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUInterParticleColliderTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUJetAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUJetAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPULineAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPULineAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPULinearForceAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPULinearForceAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPULineEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPULineEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUListener.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUMaterialManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUMaterialTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUMeshSurfaceEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUNoise.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUObserverManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnClearObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnClearObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnCollisionObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnCollisionObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnCountObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnCountObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnEmissionObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnEmissionObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnEventFlagObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnExpireObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnExpireObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnPositionObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnPositionObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnQuotaObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnQuotaObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnRandomObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnRandomObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnTimeObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnTimeObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnVelocityObserver.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUOnVelocityObserverTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUParticleFollower.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUParticleFollowerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUParticleSystem3D.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUParticleSystem3DTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPathFollower.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPathFollowerTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPlane.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPlaneCollider.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPlaneColliderTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPointEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPointEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPositionEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUPositionEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPURandomiser.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPURandomiserTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPURender.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPURendererTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPURibbonTrail.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPURibbonTrailRender.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScaleAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScaleAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScaleVelocityAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScriptCompiler.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScriptLexer.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScriptParser.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUScriptTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSimpleSpline.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSineForceAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSineForceAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSlaveBehaviour.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSlaveBehaviourTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSlaveEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSlaveEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSphere.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSphereCollider.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSphereColliderTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUSphereSurfaceEmitterTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUTechniqueTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUTextureAnimator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUTextureAnimatorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUTextureRotator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUTextureRotatorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUTranslateManager.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUUtil.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUVelocityMatchingAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUVertexEmitter.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUVortexAffector.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\PU\CCPUVortexAffectorTranslator.h">
      <Filter>extension\Particle3D\ParticleUniverse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\CCParticle3DAffector.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\CCParticle3DEmitter.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\CCParticle3DRender.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\extensions\Particle3D\CCParticleSystem3D.h">
      <Filter>extension\Particle3D</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\poly2tri.h">
      <Filter>external\poly2tri</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\common\shapes.h">
      <Filter>external\poly2tri\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\common\utils.h">
      <Filter>external\poly2tri\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\sweep\advancing_front.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\sweep\cdt.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\sweep\sweep.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\poly2tri\sweep\sweep_context.h">
      <Filter>external\poly2tri\sweep</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3D.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3DComponent.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3DConstraint.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3DDebugDrawer.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3DObject.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3DShape.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysics3DWorld.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\physics3d\CCPhysicsSprite3D.h">
      <Filter>physics3d</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCPass.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCRenderState.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCTechnique.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCMaterial.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCProperties.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCVertexAttribBinding.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\navmesh\CCNavMesh.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\navmesh\CCNavMeshAgent.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\navmesh\CCNavMeshDebugDraw.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\navmesh\CCNavMeshObstacle.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\navmesh\CCNavMeshUtils.h">
      <Filter>navmesh</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCNinePatchImageParser.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCStencilStateManager.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\base\CCNinePatchImageParser.h" />
    <ClInclude Include="..\renderer\CCFrameBuffer.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="CCAutoPolygon.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\clipper\clipper.hpp">
      <Filter>external\clipper</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\GameNode3DReader\GameNode3DReader.h" />
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\SkeletonReader\BoneNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\SkeletonReader\CSBoneBinary_generated.h">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\SkeletonReader\SkeletonNodeReader.h">
      <Filter>cocostudio\reader\WidgetReader\SkeletonReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCBoneNode.h">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCSkeletonNode.h">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\ActionTimeline\CCSkinNode.h">
      <Filter>cocostudio\TimelineAction\Skeleton</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CCComExtensionData.h">
      <Filter>cocostudio\components</Filter>
    </ClInclude>
    <ClInclude Include="..\network\CCDownloader.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\network\CCIDownloaderImpl.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CCCameraBackgroundBrush.h">
      <Filter>2d</Filter>
    </ClInclude>
    <ClInclude Include="..\renderer\CCTextureCube.h">
      <Filter>renderer</Filter>
    </ClInclude>
    <ClInclude Include="..\network\CCDownloader-curl.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\Light3DReader\Light3DReader.h" />
    <ClInclude Include="..\ui\UIAbstractCheckButton.h">
      <Filter>ui</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\CSLanguageDataBinary_generated.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\TabControlReader\CSTabControl_generated.h">
      <Filter>cocostudio\reader\WidgetReader\TabControlReader</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\WidgetReader\TabControlReader\TabControlReader.h">
      <Filter>cocostudio\reader\WidgetReader\TabControlReader</Filter>
    </ClInclude>
    <ClInclude Include="..\ui\UITabControl.h">
      <Filter>ui\UIWidgets</Filter>
    </ClInclude>
    <ClInclude Include="..\editor-support\cocostudio\LocalizationManager.h">
      <Filter>cocostudio\json</Filter>
    </ClInclude>
    <ClInclude Include="..\vr\CCVRProtocol.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\vr\CCVRDistortion.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\vr\CCVRDistortionMesh.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\vr\CCVRGenericHeadTracker.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\vr\CCVRGenericRenderer.h">
      <Filter>vr</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioDecoder.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioMacros.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioDecoderMp3.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioDecoderOgg.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\audio\win32\AudioDecoderManager.h">
      <Filter>audioengine</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\flatbuffers\gprc.h" />
    <ClInclude Include="..\..\external\flatbuffers\code_generators.h" />
    <ClInclude Include="..\..\external\flatbuffers\reflection.h" />
    <ClInclude Include="..\..\external\flatbuffers\reflection_generated.h" />
    <ClInclude Include="..\network\Uri.h">
      <Filter>network\Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCFrustum.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\3d\CCPlane.h">
      <Filter>3d</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\md5\md5.h">
      <Filter>external\md5</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\math\Mat4.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\math\MathUtil.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\math\MathUtilNeon.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\math\Quaternion.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\math\Vec2.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\math\Vec3.inl">
      <Filter>math</Filter>
    </None>
    <None Include="..\math\Vec4.inl">
      <Filter>math</Filter>
    </None>
    <None Include="cocos2d.def" />
    <None Include="..\3d\CCAnimationCurve.inl">
      <Filter>3d</Filter>
    </None>
  </ItemGroup>
</Project>