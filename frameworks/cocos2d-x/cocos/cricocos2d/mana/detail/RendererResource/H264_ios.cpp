/****************************************************************************
 *
 * Copyright (c) 2015 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/


#include "cricocos2d/mana/detail/RendererResourceFactory.h"
#include "cricocos2d/mana/detail/util.h"
#include "cricocos2d/detail/log.h"

#include <list>


#if defined(XPT_TGT_IOS)


namespace
{
	namespace gl_util = cricocos2d::mana::detail::gl;

	const CriUint32 decode_buf_quantization_width  = 32;
	const CriUint32 decode_buf_quantization_height = 8;

	/***************************************************************************
	 *      Shader Definitions for YUV -> RGB (considering alpha channel)
	 ***************************************************************************/
	const GLchar* const frag_shader_rgb =
		R"*!(
precision mediump float;
varying vec2 v_TexCoord;
varying vec4 v_Color;
uniform sampler2D texture_y;
uniform sampler2D texture_uv;
void main() {
   vec2 uv = texture2D(texture_uv, v_TexCoord).rg - 0.5;
   vec3 color = vec3((texture2D(texture_y, v_TexCoord).r - (16.0 / 255.0)) * 1.164);
	color.r += uv.y * 1.793;
	color.g += -uv.x * 0.213 - uv.y * 0.533;
	color.b += uv.x * 2.112;
	gl_FragColor = vec4(color, 1.0) * v_Color;
}
)*!";

const GLchar* const frag_shader_rgba =
	R"*!(
precision mediump float;
varying vec2 v_TexCoord;
varying vec4 v_Color;
uniform sampler2D texture_y;
uniform sampler2D texture_uv;
uniform sampler2D texture_a;
void main() {
   vec2 uv = texture2D(texture_uv, v_TexCoord).rg - 0.5;
   vec3 color = vec3((texture2D(texture_y, v_TexCoord).r - (16.0 / 255.0)) * 1.164);
   float alpha = texture2D(texture_a, v_TexCoord).r;
   color.r += uv.y * 1.793;
   color.g += -uv.x * 0.213 - uv.y * 0.533;
   color.b += uv.x * 2.112;
   gl_FragColor = vec4(color, alpha) * v_Color;
}
)*!";

	struct ShaderLocation
	{
		gl_util::GenericShaderLocation generic;
		GLint						   texture_y;
		GLint						   texture_uv;
		GLint						   texture_a;
	};


	inline
	GLsizei movieWidthToTextureWidth(CriUint32 movie_width) noexcept
	{
		/* 動画サイズを、デコードバッファの最小単位の2倍に切り上げた値を取得 */
		CriUint32 quantized_width = (movie_width + (2 * decode_buf_quantization_width - 1)) & ~(2 * decode_buf_quantization_width - 1);

		/* テクスチャの作成 (テクスチャサイズはムービサイズを2のべき乗で切り上げ) */
		return cricocos2d::mana::detail::roundUpPowerOfTwo(quantized_width);
	}


	inline
	GLsizei movieHeightToTextureHeight(CriUint32 movie_height) noexcept
	{
		/* 動画サイズを、デコードバッファの最小単位の2倍に切り上げた値を取得 */
		CriUint32 quantized_height = (movie_height + (2 * decode_buf_quantization_height - 1)) & ~(2 * decode_buf_quantization_height - 1);

		/* テクスチャの作成 (テクスチャサイズはムービサイズを2のべき乗で切り上げ) */
		return cricocos2d::mana::detail::roundUpPowerOfTwo(quantized_height);
	}


	/***************************************************************************
	 *      Implementation of Renderer
	 ***************************************************************************/

	class RendererImpl
	{
	public:
		RendererImpl()
		{
			createShaders();
		}


		~RendererImpl()
		{
			cocos2d::GL::deleteProgram(shader_rgb_);
		}


		void draw(
			GLuint										texture_y,
			GLuint										texture_uv,
			GLuint										texture_a,
			const cocos2d::TrianglesCommand::Triangles& triangles,
			const cocos2d::Mat4&						mv,
			const cocos2d::Mat4&						movie_uv_transform,
			bool										additive,
			bool										use_alpha
				 ) const
		{
			if (use_alpha) {
				cocos2d::GL::useProgram(shader_rgba_);
				gl_util::bindTextureAndSetUniform(0, texture_y, shader_location_rgba_.texture_y);
				gl_util::bindTextureAndSetUniform(1, texture_uv, shader_location_rgba_.texture_uv);
				gl_util::bindTextureAndSetUniform(2, texture_a, shader_location_rgba_.texture_a);
			} else {
				cocos2d::GL::useProgram(shader_rgb_);
				gl_util::bindTextureAndSetUniform(0, texture_y, shader_location_rgb_.texture_y);
				gl_util::bindTextureAndSetUniform(1, texture_uv, shader_location_rgb_.texture_uv);
			}
			gl_util::genericDrawMovieSprite(triangles, mv, movie_uv_transform, shader_location_rgb_.generic, use_alpha, additive);
		}


	private:
		void createShaders()
		{
			{
				shader_rgb_ = gl_util::createShaderProgram(
					gl_util::generic_vertex_shader,
					frag_shader_rgb
														  );
				gl_util::getShaderLocation(
					shader_rgb_,
				{
					{shader_location_rgb_.generic.position, "a_Position"},
					{shader_location_rgb_.generic.texcoord, "a_TexCoord"},
					{shader_location_rgb_.generic.color, "a_Color"},
				},
				{
					{shader_location_rgb_.generic.mvp_matrix, "u_MVPMatrix"},
					{shader_location_rgb_.generic.movie_uv_transform, "u_MovieUvTransform"},
					{shader_location_rgb_.texture_y, "texture_y"},
					{shader_location_rgb_.texture_uv, "texture_uv"},
				}
										  );
			}
			{
				shader_rgba_ = gl_util::createShaderProgram(
					gl_util::generic_vertex_shader,
					frag_shader_rgba
														  );
				gl_util::getShaderLocation(
					shader_rgba_,
				{
					{shader_location_rgba_.generic.position, "a_Position"},
					{shader_location_rgba_.generic.texcoord, "a_TexCoord"},
					{shader_location_rgba_.generic.color, "a_Color"},
				},
				{
					{shader_location_rgba_.generic.mvp_matrix, "u_MVPMatrix"},
					{shader_location_rgba_.generic.movie_uv_transform, "u_MovieUvTransform"},
					{shader_location_rgba_.texture_y, "texture_y"},
					{shader_location_rgba_.texture_uv, "texture_uv"},
					{shader_location_rgba_.texture_a, "texture_a"},
				}
										  );
			}
		}


	private:
		GLuint		   shader_rgb_;
		ShaderLocation shader_location_rgb_;
		GLuint		   shader_rgba_;
		ShaderLocation shader_location_rgba_;
	};

	/***************************************************************************
	 *      Implementation of Renderer Resource
	 ***************************************************************************/

	class RendererResourceImpl
		: public cricocos2d::mana::detail::RendererResource
	{
	public:
		static const GLsizei texture_set_num = 1;
		static const GLsizei max_plane_num	 = 3;

		RendererResourceImpl(const CriManaMovieInfo& movie_info, CriManaPlayerHn mana_player, RendererImpl* renderer)
			: mana_player_(mana_player)
			, renderer_(renderer)
			, texture_set_idx_(0)
			, has_alpha_(movie_info.num_alpha_streams == 1)
			, plane_num_(movie_info.num_alpha_streams == 1 ? 3 : 2)
			, y_width_(movieWidthToTextureWidth(movie_info.video_prm[0].width))
			, y_height_(movieHeightToTextureHeight(movie_info.video_prm[0].height))
		{
			createTextures();
		}


		~RendererResourceImpl()
		{
			deleteTextures();
		}


		void attachToPlayer() override
		{
			// Do nothing
		}


		bool isSuitable(const CriManaMovieInfo& movie_info) const override
		{
			bool is_codec_suitable		= movie_info.video_prm[0].codec_type == CRIMANA_VIDEO_CODEC_H264;
			bool is_alphatype_suitable	= has_alpha_ == (movie_info.num_alpha_streams == 1);
			bool is_movie_size_suitable = ((y_width_ >= movie_info.video_prm[0].width) && (y_height_ >= movie_info.video_prm[0].height));
			return is_codec_suitable && is_alphatype_suitable && is_movie_size_suitable;
		}


		bool isPrepared() const override
		{
			// Immediately prepared
			return true;
		}


		bool isDrawable() const override
		{
			return is_drawable_;
		}


		void continuePreparation() override
		{
			// Do nothing
		}


		bool updateFrame(CriManaFrameInfo& frame_info) override
		{
			CriManaFrameInfo frame_info_tmp;
			if (criManaPlayer_ReferFrame(mana_player_, &frame_info_tmp)) {
				if (criManaPlayer_IsFrameOnTime(mana_player_, &frame_info_tmp)) {
					frame_info		 = frame_info_tmp;
					texture_set_idx_ = (texture_set_idx_ + 1) % texture_set_num;
					uploadTexture(0, textures_[0 * texture_set_num + texture_set_idx_], GL_LUMINANCE, frame_info.image_info[0]);
					uploadTexture(1, textures_[1 * texture_set_num + texture_set_idx_], GL_RG_EXT, frame_info.image_info[1]);
					if (has_alpha_) {
						uploadTexture(2, textures_[2 * texture_set_num + texture_set_idx_], GL_LUMINANCE, frame_info.image_info[2]);
					}
					criManaPlayer_DiscardFrame(mana_player_, &frame_info_tmp);

					movie_uv_transform_.m[0] = static_cast<float>(frame_info_tmp.disp_width - 1) / y_width_;
					movie_uv_transform_.m[5] = static_cast<float>(frame_info_tmp.disp_height - 1) / y_height_;

					is_drawable_			 = true;
					return true;
				}
			}
			return false;
		}


		void draw(
			const cocos2d::TrianglesCommand::Triangles& triangles,
			const cocos2d::Mat4&						mv,
			bool										additive
				 ) override
		{
			if (isDrawable()) {
				renderer_->draw(
					textures_[0 * texture_set_num + texture_set_idx_],
					textures_[1 * texture_set_num + texture_set_idx_],
					textures_[2 * texture_set_num + texture_set_idx_],
					triangles,
					mv,
					movie_uv_transform_,
					additive,
					has_alpha_
							   );
			}
		}


	private:
		void createTextures()
		{
			/* ここで作成されるテクスチャ配列はY..Y,UV..UV(,A..A)として扱う */
			glGenTextures(texture_set_num * plane_num_, textures_);
			CHECK_GL_ERROR_DEBUG();

			/* テクスチャのサイズ指定を行う */
			for (GLsizei i = 0; i < texture_set_num; i++) {
				setupTexture(0, textures_[0 * texture_set_num + i], GL_LUMINANCE, y_width_, y_height_);
				setupTexture(1, textures_[1 * texture_set_num + i], GL_RG_EXT, y_width_ / 2, y_height_ / 2);
				if (has_alpha_) {
					setupTexture(2, textures_[2 * texture_set_num + i], GL_LUMINANCE, y_width_, y_height_);
				}
			}
		}


		void deleteTextures()
		{
			for (std::size_t ite = 0; ite < texture_set_num * plane_num_; ++ite) {
				cocos2d::GL::deleteTexture(textures_[ite]);
			}
		}


		void setupTexture(GLuint tex_unit, GLuint tex_obj, GLenum format, GLsizei width, GLsizei height)
		{
			cocos2d::GL::activeTexture(GL_TEXTURE0 + tex_unit);
			cocos2d::GL::bindTexture2DN(tex_unit, tex_obj);
			glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
			glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
			glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
			glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
			glTexImage2D(GL_TEXTURE_2D, 0, format, width, height, 0, format, GL_UNSIGNED_BYTE, nullptr);
			CHECK_GL_ERROR_DEBUG();
		}


		void uploadTexture(GLuint tex_unit, GLuint tex_obj, GLenum format, CriManaImageBufferInfo& bufinfo)
		{
			GLuint bpp = 1;     // bytes per pixel
			switch (format) {
			case GL_LUMINANCE :
				bpp = 1;
				break;

			case GL_RG_EXT :
			case GL_LUMINANCE_ALPHA :
				bpp = 2;
				break;
			}

			cocos2d::GL::activeTexture(GL_TEXTURE0 + tex_unit);
			cocos2d::GL::bindTexture2DN(tex_unit, tex_obj);
			glTexSubImage2D(GL_TEXTURE_2D, 0, 0, 0, bufinfo.line_pitch / bpp, bufinfo.num_lines,
							format, GL_UNSIGNED_BYTE, bufinfo.imageptr);
			CHECK_GL_ERROR_DEBUG();
		}


	private:
		CriManaPlayerHn mana_player_;
		RendererImpl*	renderer_;
		cocos2d::Mat4	movie_uv_transform_;
		GLuint			textures_[texture_set_num * max_plane_num];
		GLsizei			texture_set_idx_;
		GLsizei			plane_num_;
		GLsizei			y_width_;
		GLsizei			y_height_;
		bool			has_alpha_;
		bool			is_drawable_ = false;
	};

	/***************************************************************************
	 *      Implementation of Renderer Resource Factory
	 ***************************************************************************/

	class RendererResourceFactoryImpl
		: public cricocos2d::mana::detail::RendererResourceFactory
	{
	public:
		~RendererResourceFactoryImpl()
		{
			CC_ASSERT(renderer_resources_.empty());
		}


		std::shared_ptr<cricocos2d::mana::detail::RendererResource> createRenderResource(
			const CriManaMovieInfo& movie_info,
			CriManaPlayerHn			mana_player
																						) override
		{
			bool is_codec_suitable = movie_info.video_prm[0].codec_type == CRIMANA_VIDEO_CODEC_H264;
			if (!is_codec_suitable) {
				return nullptr;
			}

			std::unique_ptr<RendererResourceImpl> renderer_resource_u(new RendererResourceImpl(movie_info, mana_player, std::addressof(renderer_)));
			auto								  iter = renderer_resources_.insert(std::end(renderer_resources_), renderer_resource_u.get());
			std::shared_ptr<RendererResourceImpl> renderer_resource(
				renderer_resource_u.release(),
				[this, iter](RendererResourceImpl* p)
			{
				renderer_resources_.erase(iter);
				delete p;
			}
																   );
			return renderer_resource;
		}


		void onSuspend() override
		{}


		void onResume() override
		{}


		void onRendererRecreated() override
		{
			/*
			 * iOS では勝手に描画リソースが破棄されることがないため、何もしない。
			 * この関数自体が呼ばれない。
			 */
		}


	private:
		RendererImpl					 renderer_;
		std::list<RendererResourceImpl*> renderer_resources_;
	};
}


namespace cricocos2d
{
	namespace mana
	{
		namespace detail
		{
			std::shared_ptr<RendererResourceFactory> createRendererResourceFactoryH264Ios()
			{
				return std::make_shared<RendererResourceFactoryImpl>();
			}


		}
	}
}


#endif
