/****************************************************************************
 Copyright (c) 2010-2012 cocos2d-x.org
 Copyright (c) 2013-2014 Chukong Technologies Inc.
 
 http://www.cocos2d-x.org
 
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:
 
 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.
 
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 THE SOFTWARE.
 
 "[WebSocket module] is based in part on the work of the libwebsockets  project
 (http://libwebsockets.org)"
 
 ****************************************************************************/

#include "BoleSocket.h"
#include "DataUtils.h"

#include "../base/CCDirector.h"
#include "../base/CCScheduler.h"
#include "../base/CCScriptSupport.h"
#include "../base/CrashMsgCollector.h"
//#include "CCLuaEngine.h""
//#include "../scripting/lua-bindings/manual/CCLuaEngine.h"
#include "scripting/lua-bindings/manual/CCLuaValue.h"
#include "scripting/lua-bindings/manual/CCLuaEngine.h"


#include <thread>
#include <mutex>
#include <queue>
#include <signal.h>
#include <errno.h>
#include <list>
#include <string>
#include <vector>
#include <fcntl.h>
#include <sys/types.h>
#include <atomic>
#include <sys/ioctl.h>
#include <netinet/tcp.h>
#if (CC_TARGET_PLATFORM !=CC_PLATFORM_WIN32 &&  CC_TARGET_PLATFORM != CC_PLATFORM_WP8)
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <netdb.h>
#endif
#ifndef SHUT_RDWR
#define SHUT_RDWR SD_BOTH
#endif

#ifndef MSG_DONTWAIT
#define MSG_DONTWAIT 0
#endif


#define SELECT_TIMEOUT              0.2
#define KEEP_ALIVE_INTERVAL			4 / SELECT_TIMEOUT
#define RECONNECT_INTERVAL          3

#define MAX_HEAERT_TIMES            3 / SELECT_TIMEOUT
#define WS_WRITE_BUFFER_SIZE 1024*1024
#define WS_READ_BUFFER_SIZE 1024*1024

#define CONNECTION_TIMEOUT          3

void mylog(const char *msg) {
    //CCLOG("%s, time=%ld", msg, time(0));
}

void mylog2(const char *msg, int param) {
//    static char buffer[512];
//    sprintf(buffer, msg, param);
//    CCLOG("%s, time=%ld", buffer, time(0));
}

void mylog3(const char *msg, const char *param) {
    //static char buffer[512];
    //sprintf(buffer, msg, param);
    //CCLOG("%s, time=%ld", param, time(0));
}

//static char recv_buffer[WS_READ_BUFFER_SIZE]={0};
//static std::atomic<int> idleCount(0);
//static long reconnect = 0;

NS_CC_BEGIN

namespace network {

//    BLMessage::BLMessage():what(WS_MSG_TO_SUBTRHEAD_SENDING_STRING), obj(nullptr){}
//    BLMessage::BLMessage(WS_MSG type, void* o)
//    {
//        what = type;
//        obj = o;
//    }
//    void* BLMessage::getObj() {return obj;}
//    WS_MSG BLMessage::getType() {return what;}
//    BLMessage::~BLMessage()
//    {
//    }
    
    
    /**
     *  @brief Websocket thread helper, it's used for sending message between UI thread and websocket thread.
     */
    class BLThreadHelper : public Ref
    {
    public:
        BLThreadHelper();
        ~BLThreadHelper();
        
        // Creates a new thread
        bool createThread(const BLSocket& ws);
        // Quits sub-thread (websocket thread).
        void quitSubThread();
        
        // Schedule callback function
        virtual void update(float dt);
        
        // Sends message to UI thread. It's needed to be invoked in sub-thread.
        void sendMessageToUIThread(BLMessage *msg);
        
        // Sends message to sub-thread(websocket thread). It's needs to be invoked in UI thread.
        void sendMessageToSubThread(BLMessage *msg);
        
        // Waits the sub-thread (websocket thread) to exit,
        void joinSubThread();
        
    protected:
        void wsThreadEntryFunc();
        
    private:
        std::list<BLMessage*>* _UIWsMessageQueue;
        std::list<BLMessage*>* _subThreadWsMessageQueue;
        std::mutex   _UIWsMessageQueueMutex;
        std::mutex   _subThreadWsMessageQueueMutex;
        std::thread* _subThreadInstance;
        BLSocket* _ws;
        bool _needQuit;
        friend class BLSocket;
    };
    
    // Implementation of WsThreadHelper
    BLThreadHelper::BLThreadHelper()
    : _subThreadInstance(nullptr)
    , _ws(nullptr)
    , _needQuit(false)
    {
        _UIWsMessageQueue = new std::list<BLMessage*>();
        _subThreadWsMessageQueue = new std::list<BLMessage*>();
        
        Director::getInstance()->getScheduler()->scheduleUpdate(this, 0, false);
//        Director::getInstance()->getEventDispatcher()->addCustomEventListener(Director::EVENT_AFTER_DRAW, std::bind(&BLThreadHelper::update, this, std::placeholders::_1));
    }
    
    BLThreadHelper::~BLThreadHelper()
    {
        Director::getInstance()->getScheduler()->unscheduleAllForTarget(this);
        joinSubThread();
        CC_SAFE_DELETE(_subThreadInstance);
        delete _UIWsMessageQueue;
        delete _subThreadWsMessageQueue;
    }
    
    bool BLThreadHelper::createThread(const BLSocket& ws)
    {
        _ws = const_cast<BLSocket*>(&ws);
        
        // Creates websocket thread
#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32) || (CC_TARGET_PLATFORM == CC_PLATFORM_WP8) || (CC_TARGET_PLATFORM == CC_PLATFORM_WINRT)
		WSADATA win_ws = {0};
		int v = WSAStartup(MAKEWORD(2, 2), &win_ws);
		if (v != 0)
		{
			CCLOG("error initialising winsock: %d", v);
			return 1;
		}
#endif
        _subThreadInstance = new std::thread(&BLThreadHelper::wsThreadEntryFunc, this);
        return true;
    }
    
    void BLThreadHelper::quitSubThread()
    {
        _needQuit = true;
    }
    
    void BLThreadHelper::wsThreadEntryFunc()
    {
        if (_ws->getThreadStartedOld()) {
            _ws->onSubThreadStartedOld();
        }else{
            _ws->onSubThreadStarted();
        }
        
//        _ws->test();
        while (!_needQuit)
        {
            if (_ws->onSubThreadLoop())
            {
                break;
            }
            
        }
        
    }
    
    void BLThreadHelper::sendMessageToUIThread(BLMessage *msg)
    {
        std::lock_guard<std::mutex> lk(_UIWsMessageQueueMutex);
        _UIWsMessageQueue->push_back(msg);
//        _UIWsMessageQueue->emplace_back(std::move(msg));
    }
    
    void BLThreadHelper::sendMessageToSubThread(BLMessage *msg)
    {
        std::lock_guard<std::mutex> lk(_subThreadWsMessageQueueMutex);
        _subThreadWsMessageQueue->push_back(msg);
    }
    
    void BLThreadHelper::joinSubThread()
    {
        if (_subThreadInstance && _subThreadInstance->joinable())
        {
            quitSubThread();
            _subThreadInstance->join();
            _subThreadInstance = 0;
        }
    }
    
    void BLThreadHelper::update(float dt)
    {
        CrashMsgCollector::getInstance()->timeConsumeStart(CrashMsgCollector::BOLE_SOCKET);
        if (LuaEngine::getInstance()->getLuaStack()->updateFrame())
        {
            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::BOLE_SOCKET);
            return;
        }
        
        if (_UIWsMessageQueueMutex.try_lock()) {
            BLMessage *msg = nullptr;
            
            if (0 == _UIWsMessageQueue->size())
            {
                _UIWsMessageQueueMutex.unlock();
                CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::BOLE_SOCKET);
                return;
            }
            // Gets message
            msg = *(_UIWsMessageQueue->begin());
            _UIWsMessageQueue->pop_front();
            
            _UIWsMessageQueueMutex.unlock();
            
            if (_ws)
            {
                _ws->onUIThreadReceiveMessage(msg);
            }
            
//            delete (LuaValue*)msg->obj;
//            msg->obj = nullptr;
//            CC_SAFE_DELETE(msg);
        }
        else
        {
            CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::BOLE_SOCKET);
//            CCLOG("BLThreadHelper::update is lock return");
            return;
        }
        CrashMsgCollector::getInstance()->timeConsumeEnd(CrashMsgCollector::BOLE_SOCKET);
    }

    BLSocket::BLSocket()
    : _readyState(State::CONNECTING)
    , _currHostIndex(-1)
    , _wsHelper(nullptr)
    , _pendingFrameDataLen(0)
    , _currentDataLen(0)
    , _currentData(nullptr)
    , _sktContext(-1)
    , _heart(0)
    , _connectionTimeout(CONNECTION_TIMEOUT)
    , _isThreadStartedOld(false)
    {
        _delegateList = __Array::create();
        _delegateList->retain();
        
        recv_buffer = new char[WS_READ_BUFFER_SIZE];
        memset(recv_buffer, 0, WS_READ_BUFFER_SIZE);
        
        idleCount = 0;
        reconnect = 0;
        
        _recvEvent = new (std::nothrow) EventCustom("bole_socket_rec_len_msg");
    }
    
    
    BLSocket::~BLSocket()
    {
        _delegateList->removeAllObjects();
        _delegateList->release();
        closeSkt();
        CC_SAFE_RELEASE_NULL(_wsHelper);
        CC_SAFE_RELEASE_NULL(_recvEvent);
        CC_SAFE_DELETE(recv_buffer);
    }
    
    BLSocket * BLSocket::instance = NULL;
    BLSocket* BLSocket::getInstance()
    {
        if (!instance) {
            instance = new BLSocket();
        }
        return instance;
    }
    
    void BLSocket::destroyInstance()
    {
        if(instance) {
            delete instance;
        }
        instance = 0;
    }
    
    bool BLSocket::init(const std::string& url,
                         const int portNumber)
    {
        bool ret = false;
        CC_SAFE_RELEASE_NULL(_wsHelper);
        _wsHelper = new BLThreadHelper();
        ret = _wsHelper->createThread(*this);
        
        return ret;
    }

    void BLSocket::addHost(const std::string &url, const int port) {
        for(auto iter = alternativeHosts.begin(); iter != alternativeHosts.end(); ++ iter) {
            std::pair<std::string, int> &host = *iter;
            if(host.first == url && host.second == port) {
                return;
            }
        }
        alternativeHosts.push_back(std::make_pair(url, port));
    }

    void BLSocket::removeHost(const std::string &url, const int port) {
        for(auto iter = alternativeHosts.begin(); iter != alternativeHosts.end(); ++ iter) {
            std::pair<std::string, int> &host = *iter;
            if(host.first == url && host.second == port) {
                alternativeHosts.erase(iter);
                break;
            }
        }
    }

    bool BLSocket::getCurrentHost(std::string &url, int &port) {
        if(alternativeHosts.empty()) {
            return false;
        }
        auto hostInfo = std::next(alternativeHosts.begin(), _currHostIndex % alternativeHosts.size());
        std::pair<std::string, int> host = *hostInfo;
        url = host.first;
        port = host.second;
        return true;
    }

    void BLSocket::setConnectionTimeout(int seconds) {
        if(seconds <= 0) {
            _connectionTimeout = CONNECTION_TIMEOUT;
        } else {
            _connectionTimeout = seconds;
        }
    }
    
    void BLSocket::addDelegate(BLDelegate *dele)
    {
        if (dele) {
            _delegateList->addObject(dele);
        }
    }
    
    void BLSocket::removeDelegate(BLDelegate *dele)
    {
        if (dele) {
            _delegateList->removeObject(dele);
        }
    }
    
    void BLSocket::removeAllDelegate()
    {
        _delegateList->removeAllObjects();
    }
    
    void BLSocket::sendTable(Value &obj){
        sendMsg(obj);
    }
 
    void BLSocket::sendMsg(Value &pObject)
    {
        if (_readyState == State::OPEN && pObject.getType() != Value::Type::NONE)
        {
            int length = 0;
            Data* data = new Data();
            data->bytes = NULL;
            DataUtils::dump(pObject, (unsigned char**)&data->bytes, &length);
            DUMP_HEAD_LENGTH(data->bytes,(length - SOCKET_HEAD_LENGTH));
            data->len = static_cast<ssize_t>(length);
            BLMessage* msg = new BLMessage(BL_WS_MSG_TO_SUBTRHEAD_SENDING_STRING,data);
            _wsHelper->sendMessageToSubThread(msg);
        }
    }
    
   
    
    void BLSocket::closeSkt()
    {
        Director::getInstance()->getScheduler()->unscheduleAllForTarget(_wsHelper);
        
        if (_readyState == State::CLOSING || _readyState == State::CLOSED)
        {
            return;
        }

//        shutdown(_sktContext, SHUT_RDWR);
        if (_sktContext >= 0) {
            close(_sktContext);
        }
        _wsHelper->joinSubThread();
        if (_sktContext >= 0) {
            _sktContext = -1;
        }
 
#if (CC_TARGET_PLATFORM == CC_PLATFORM_WIN32) || (CC_TARGET_PLATFORM == CC_PLATFORM_WP8) || (CC_TARGET_PLATFORM == CC_PLATFORM_WINRT)
		WSACleanup();
		CCLOG("was cleanup");
#endif
        //onNetworkState(BL_WS_MSG_TO_UITHREAD_CLOSE);
    }

    void BLSocket::quitSubThread()
    {
        _wsHelper->quitSubThread();
    }
    
    void BLSocket::onNetworkState(BL_WS_MSG state)
    {
        if (state == BL_WS_MSG_TO_UITHREAD_OPEN) {
            _readyState = State::OPEN;
            reconnect = RECONNECT_INTERVAL;
            CC_SAFE_DELETE_ARRAY(_currentData);
            _currentDataLen = 0;
            _wsHelper->_subThreadWsMessageQueue->clear();
            CCLOG("socket reconnect success && clear data");
        }else{
            _readyState = State::CLOSED;
            int err = errno;
            CCLOG("BLSocket::onNetworkState error=%d, str=%s", err, strerror(err));
            //reconnect += RECONNECT_INTERVAL;
        }
        BLMessage* msg = new BLMessage();
        msg->what = state;
        _wsHelper->sendMessageToUIThread(msg);
    }
    
    BLSocket::State BLSocket::getReadyState()
    {
        return _readyState;
    }

    bool BLSocket::getThreadStartedOld()
    {
        return _isThreadStartedOld;
    }

    void BLSocket::setThreadStartedOld(bool isOld)
    {
        _isThreadStartedOld = isOld;
    }

    void BLSocket::onSubThreadStartedOld()
    {
        if(alternativeHosts.empty()) {
            log("no hosts found, do nothing");
            return;
        }
        ++ _currHostIndex;
        
        log("BLSocket_onSubThreadStartedOld_currHostIndex %d", _currHostIndex);

        auto hostInfo = std::next(alternativeHosts.begin(), _currHostIndex % alternativeHosts.size());
        std::pair<std::string, int> host = *hostInfo;
        std::string hostUrl = host.first;
        int port = host.second;
        
        log("BLSocket_host  %s %d", hostUrl.c_str(), port);

        struct addrinfo * res, *resalloc;
        int errcode = getaddrinfo(hostUrl.c_str(),NULL, NULL, &res);
        if (errcode != 0){
            // cocos2d::log("failed to getaddrinfo socket %s", gai_strerror((errcode)));
            log("failed to getaddrinfo socket %d", errcode);
            onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
            return;
        }

        resalloc = res;
        int count = 0; //get count
        bool ipv6 = false;
        while (res) {
            count += 1;
            if (!ipv6 && res->ai_family == AF_INET6) ipv6 = true;
            res = res->ai_next;
        }
        
        srand (time(NULL));
        int start = rand() % count;
        _readyState = State::CONNECTING;
        res = resalloc;
        
        log("BLSocket start %d", start);
        log("BLSocket count %d", count);
        
        for (int i=1;i<start;i++)
            res = res->ai_next;
        // CCLOG("address count %i, start %i",count, start);
        for (int i=0;i<count;i++) {
            res = res->ai_next;
            if (!res) res = resalloc;
            struct sockaddr_in* saddr = (struct sockaddr_in*) res->ai_addr;
            log("ai_addr host name %i , %s\n",i, inet_ntoa(saddr->sin_addr));
            log("ai_family-> %i", res->ai_family);
            if (ipv6 && res->ai_family != AF_INET6) continue;
            _sktContext = socket(res->ai_family, SOCK_STREAM, IPPROTO_TCP);
            if (_sktContext < 0) {
                log("socket error %d", _sktContext);
                continue;
            }
#if defined(SO_NOSIGPIPE)
            // ios
            // CCLOG("@gongpixin, BLSocket::onSubThreadStarted(), #if defined(SO_NOSIGPIPE)");
            int set = 1;
            setsockopt(_sktContext, SOL_SOCKET, SO_NOSIGPIPE, (void *)&set, sizeof(int));
#endif
//#if defined(MSG_NOSIGNAL)
            // android
            // CCLOG("@gongpixin, BLSocket::onSubThreadStarted(), #if defined(MSG_NOSIGNAL)");
//#endif
            int size;
            sockaddr* sockaddr;
            struct sockaddr_in6 input_socket6;
            struct sockaddr_in addrSrv;
            if (res->ai_family == AF_INET6) {
                memset(&input_socket6, 0, sizeof(struct sockaddr_in6));
                memcpy(&input_socket6, res->ai_addr, res->ai_addrlen);
                input_socket6.sin6_port =htons(port);
                input_socket6.sin6_family = AF_INET6;
                sockaddr = (struct sockaddr*) &input_socket6;
                size = sizeof(struct sockaddr_in6);
            }
            else {
                memset(&addrSrv, 0, sizeof(struct sockaddr_in));
                memcpy(&addrSrv, res->ai_addr, res->ai_addrlen);
                addrSrv.sin_family=AF_INET;
                addrSrv.sin_port=htons(port);
                sockaddr = (struct sockaddr*)&addrSrv;
                size = sizeof(struct sockaddr_in);
            }
            
            log("BLSocket_ai_family %d", (int)res->ai_family);
            log("BLSocket_size %d", size);
            log("BLSocket_ai_addrlen %d", (int)res->ai_addrlen);
            
            int blockState = 1; // 为了防止connect阻塞事件太长，需要先设置socket为非阻塞模式
            int ret = ioctl(_sktContext, FIONBIO, &blockState);
            ret = connect(_sktContext, sockaddr, size);
            
            if (ret < 0 and errno != EINPROGRESS) {
                // CCLOGERROR("connect(_sktContext failed %s", strerror(errno));
                log("connect(_sktContext failed ret %d   errno %d", ret, (int)errno);
                close(_sktContext);
                _sktContext = -1;
                continue;
            }
            
            blockState = 0; // 调用完connect之后，再回复_sktContext为阻塞模式
            ret = ioctl(_sktContext, FIONBIO, &blockState);
            fd_set write_fd, error_fd;
            FD_ZERO(&write_fd);
            FD_ZERO(&error_fd);
            FD_SET(_sktContext, &write_fd);
            FD_SET(_sktContext, &error_fd);
            timeval timeout;
            timeout.tv_sec = _connectionTimeout;
            timeout.tv_usec = 0;
            
            ret = select(_sktContext+1, 0, &write_fd, &error_fd, &timeout);
            
            if(ret == -1) {
                log("_sktContext failed %d", (int)errno);
                // CCLOGERROR("connect(_sktContext failed %s", strerror(errno));
                close(_sktContext);
                _sktContext = -1;
                continue;
            }
            
            if(FD_ISSET(_sktContext, &write_fd)) {
                _heart = 0;
                onNetworkState(BL_WS_MSG_TO_UITHREAD_OPEN);
                log("connect success");
                break;  /* okay we got one */
            } else {
                log("failed to connect server");
                close(_sktContext);
                _sktContext = -1;
                continue;
            }
        }
        freeaddrinfo(resalloc);
        if (_sktContext < 0) {
            onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
            log("connect failed");
            CC_SAFE_DELETE_ARRAY(_currentData);
            _currentDataLen = 0;
            memset(recv_buffer, 0, WS_READ_BUFFER_SIZE);
        }
    }
    

    int BLSocket::getFDCount()
    {
#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
        std::string ret = "-1";
        JniMethodInfo t;
        int count = -1;
        if (JniHelper::getStaticMethodInfo(t, "org/cocos2dx/bole/BoleJavaUtil", "getIntFDCount", "()I")) {
            count = t.env->CallStaticIntMethod(t.classID, t.methodID);
            t.env->DeleteLocalRef(t.classID);
        }
        return count;
#endif
        return -1;
    }
        
    void BLSocket::onSubThreadStarted()
    {
        if(alternativeHosts.empty()) {
            log("no hosts found, do nothing");
            return;
        }
        ++ _currHostIndex;
        log("BLSocket_currHostIndex %d", _currHostIndex);

        auto hostInfo = std::next(alternativeHosts.begin(), _currHostIndex % alternativeHosts.size());
        std::pair<std::string, int> host = *hostInfo;
        std::string hostUrl = host.first;
        int port = host.second;
        char portStr[10];
        snprintf(portStr, sizeof(portStr), "%d", port);
        log("BLSocket_host  %s %d", hostUrl.c_str(), port);

        struct addrinfo hints;
        memset(&hints, 0, sizeof(struct addrinfo));
        hints.ai_family = AF_UNSPEC;     // Allow IPv4 or IPv6
        hints.ai_socktype = SOCK_STREAM; // TCP Stream sockets
        
        struct addrinfo * res, *resalloc;
        int errcode = getaddrinfo(hostUrl.c_str(), portStr, &hints, &res);
        if (errcode != 0){
            // cocos2d::log("failed to getaddrinfo socket %s", gai_strerror((errcode)));
            log("failed to getaddrinfo socket %d", errcode);
            onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
            return;
        }

        resalloc = res;
        int count = 0; //get count
        bool ipv6 = false;
        while (res) {
            count += 1;
            if (!ipv6 && res->ai_family == AF_INET6) ipv6 = true;
            res = res->ai_next;
        }
        
        srand (static_cast<unsigned int>(time(NULL)));
        int start = rand() % count;
        _readyState = State::CONNECTING;
        res = resalloc;
        
        for (int i=1;i<start;i++)
            res = res->ai_next;
        log("address count %i, start %i,  ipv6 %d",count, start, ipv6);
        for (int i=0;i<count;i++) {
            res = res->ai_next;
            if (!res) res = resalloc;
            if (ipv6 && res->ai_family != AF_INET6) continue;
            // _sktContext = socket(res->ai_family, SOCK_STREAM, IPPROTO_TCP);
            _sktContext = socket(res->ai_family, res->ai_socktype, res->ai_protocol);
            if (_sktContext < 0) {
                log("socket error %d", _sktContext);
                continue;
            }
#if defined(SO_NOSIGPIPE)
            // ios
            // CCLOG("@gongpixin, BLSocket::onSubThreadStarted(), #if defined(SO_NOSIGPIPE)");
            log("BLSocket::onSubThreadStarted(), #if defined(SO_NOSIGPIPE)");
            int set = 1;
            setsockopt(_sktContext, SOL_SOCKET, SO_NOSIGPIPE, (void *)&set, sizeof(int));
#endif
//#if defined(MSG_NOSIGNAL)
            // android
            // CCLOG("@gongpixin, BLSocket::onSubThreadStarted(), #if defined(MSG_NOSIGNAL)");
//#endif
/*
            socklen_t size;
            sockaddr* sockaddr;
            if (res->ai_family == AF_INET6) {
                struct sockaddr_in6 input_socket6;
                memset(&input_socket6, 0, sizeof(struct sockaddr_in6));
                input_socket6.sin6_family = AF_INET6;
                input_socket6.sin6_port = htons(port);
                struct sockaddr_in6* sAddr6 = (struct sockaddr_in6*) res->ai_addr;
                // input_socket6.sin6_addr = sAddr6->sin6_addr;
                // sockaddr = (struct sockaddr*)&input_socket6;
                // size = sizeof(sockaddr_in6);

                char str[INET6_ADDRSTRLEN];
                inet_ntop(AF_INET6, &sAddr6->sin6_addr, str, INET6_ADDRSTRLEN);
                log("sockaddr_in6 host name %i , %s\n",i, str);
                log("ai_family-> %i", res->ai_family);

                inet_pton(AF_INET6, str, &(input_socket6.sin6_addr));
                sockaddr = (struct sockaddr*)&input_socket6;
                size = sizeof(sockaddr_in6);
            }
            else {
                struct sockaddr_in addrSrv;
                memset(&addrSrv, 0, sizeof(struct sockaddr_in));
                addrSrv.sin_family = AF_INET;
                addrSrv.sin_port = htons(port);
                struct sockaddr_in* sAddr4 = (struct sockaddr_in*) res->ai_addr;
                // addrSrv.sin_addr = sAddr4->sin_addr;
                // sockaddr = (struct sockaddr*)&addrSrv;
                // size = sizeof(sockaddr_in);

                char str[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &sAddr4->sin_addr, str, INET_ADDRSTRLEN);
                log("sockaddr_in host name %i , %s\n",i, str);
                log("ai_family-> %i", res->ai_family);

                inet_pton(AF_INET, str, &(addrSrv.sin_addr));
                sockaddr = (struct sockaddr*)&addrSrv;
                size = sizeof(sockaddr_in);
            }
*/
            
            int blockState = 1; // 为了防止connect阻塞事件太长，需要先设置socket为非阻塞模式
            int ret = ioctl(_sktContext, FIONBIO, &blockState);
            // ret = connect(_sktContext, sockaddr, size);
            ret = connect(_sktContext, res->ai_addr, res->ai_addrlen);
            
            if (ret < 0 and errno != EINPROGRESS) {
                // CCLOGERROR("connect(_sktContext failed %s", strerror(errno));
                log("connect(_sktContext failed ret %d   errno %d", ret, (int)errno);
                close(_sktContext);
                _sktContext = -1;
                continue;
            }
            
            blockState = 0; // 调用完connect之后，再回复_sktContext为阻塞模式
            ret = ioctl(_sktContext, FIONBIO, &blockState);

            int fdCount = getFDCount(); // 防止 fd_set 栈溢出
            if (fdCount > 1012)
            {
                log("fdCount  %d break", fdCount);
                close(_sktContext);
                _sktContext = -2;
                break;
            }

            fd_set write_fd, error_fd;
            FD_ZERO(&write_fd);
            FD_ZERO(&error_fd);
            FD_SET(_sktContext, &write_fd);
            FD_SET(_sktContext, &error_fd);
            timeval timeout;
            timeout.tv_sec = _connectionTimeout;
            timeout.tv_usec = 0;
            
            ret = select(_sktContext+1, nullptr, &write_fd, &error_fd, &timeout);
            
            if(ret == -1) {
                log("_sktContext failed %d  %s", (int)errno, strerror(errno));
                // CCLOGERROR("connect(_sktContext failed %s", strerror(errno));
                close(_sktContext);
                _sktContext = -1;
                continue;
            }
            
            if(FD_ISSET(_sktContext, &write_fd)) {
                _heart = 0;
                onNetworkState(BL_WS_MSG_TO_UITHREAD_OPEN);
                log("connect success");
                break;  /* okay we got one */
            } else {
                log("failed to connect server");
                close(_sktContext);
                _sktContext = -1;
                continue;
            }
        }
        freeaddrinfo(resalloc);
        if (_sktContext < 0) {
            if (_sktContext == -2)
            {
                onNetworkState(BL_WS_MSG_TO_UITHREAD_FD_MAX);
            }
            else
            {
                onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
            }
            CC_SAFE_DELETE_ARRAY(_currentData);
            _currentDataLen = 0;
            memset(recv_buffer, 0, WS_READ_BUFFER_SIZE);
            log("connect failed");
        }
    }
    
    int BLSocket::onSubThreadLoop()
    {
        if (_readyState == State::CLOSED || _readyState == State::CLOSING)
        {
            if(_sktContext >= 0) {
                close(_sktContext);
                _sktContext = -1;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(reconnect*1000));
//			shutdown(_sktContext, SHUT_RDWR);
            CCLOG("reconnect");
            if (_isThreadStartedOld) {
                onSubThreadStartedOld();
            }else{
                onSubThreadStarted();
            }
            
//            test();
            return 0;
        }
        
        timeval timeout;
        timeout.tv_sec = SELECT_TIMEOUT;
        timeout.tv_usec = 0;
        fd_set fread;
        fd_set fsend;
        fd_set ferror;
        FD_ZERO(&fread);
        FD_ZERO(&fsend);
        FD_ZERO(&ferror);
        FD_SET(_sktContext, &fread);
        FD_SET(_sktContext, &ferror);
        _wsHelper->_subThreadWsMessageQueueMutex.lock();
        ssize_t msgNum = _wsHelper->_subThreadWsMessageQueue->size();
        _wsHelper->_subThreadWsMessageQueueMutex.unlock();

        if (msgNum > 0){
            FD_SET(_sktContext, &fsend);
        }
        
        int reCode = select(_sktContext+1, &fread, &fsend, &ferror, &timeout);
        mylog2("send msg log select log 1 code=%d", reCode);
//        CCLOG("select ret %d",reCode);
        if (reCode > 0) {
            mylog2("send msg log select log 2 code=%d", reCode);
            if (FD_ISSET(_sktContext, &fread)) {
                mylog2("send msg log select log 3 code=%d", reCode);
                FD_CLR(_sktContext, &fread);
                onSocketCallback(SOCKET_REASON_RECV,NULL,0);
            }
            mylog2("send msg log select log 4 code=%d", reCode);
            if (FD_ISSET(_sktContext, &fsend) && (_readyState == State::OPEN)) {
                mylog2("send msg log select log 5 code=%d", reCode);
                FD_CLR(_sktContext, &fsend);
                onSocketCallback(SOCKET_REASON_SEND,NULL,0);
            }
            mylog2("send msg log select log 6 code=%d", reCode);
            if (FD_ISSET(_sktContext, &ferror)) {
                mylog2("send msg log select log 7 code=%d", reCode);
                FD_CLR(_sktContext, &ferror);
            }
        }
        else if (reCode == 0) {
            mylog2("send msg log select log 8 code=%d", reCode);
            idleCount ++;
            if (idleCount >= KEEP_ALIVE_INTERVAL) {
                mylog2("send msg log select log 9 code=%d", reCode);
                idleCount = 0;
//                ValueVector *ar = new ValueVector();
//                Value *data = new Value(9999);
//                ar->push_back(*data);                
                ValueVector v;
                v.push_back(Value("keepalive"));
                ValueMap map;
                v.push_back(Value(map));
                Value value(v);
                sendMsg(value);
                _heart ++;
            }
//            ssize_t length = recv(_sktContext, recv_buffer, WS_READ_BUFFER_SIZE, MSG_DONTWAIT);
//            CCLOG("check recv length %d %d", int(length), int(errno));
            if (FD_ISSET(_sktContext, &ferror)) {
                FD_CLR(_sktContext, &ferror);
            }
        }else{
            mylog2("send msg log select log 10 code=%d", reCode);
            if (errno != EAGAIN && errno != EINTR && errno != EINPROGRESS) {
                mylog2("send msg log select log 11 code=%d", reCode);
                onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
            }
            if (FD_ISSET(_sktContext, &ferror)) {
                FD_CLR(_sktContext, &ferror);
            }
        }
//        if (_heart >= MAX_HEAERT_TIMES) {
//            CCLOG("blsock: heart %i",_heart);
//            onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
//        }
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        return 0;
    }
    
    void BLSocket::onSubThreadEnded()
    {
        
    }
    
    int BLSocket::onSocketCallback(SocketReason reason, void *data, ssize_t len)
    {
        static int index = 0;
        ++ index;
        switch (reason)
        {
            case SOCKET_REASON_SEND:
            {
                mylog2("send msg log 1, index=%d", index);
                std::lock_guard<std::mutex> lk(_wsHelper->_subThreadWsMessageQueueMutex);
                
                std::list<BLMessage*>::iterator iter = _wsHelper->_subThreadWsMessageQueue->begin();
                
                int bytesWrite = 0;
                for (; iter != _wsHelper->_subThreadWsMessageQueue->end();)
                {
                    mylog("send msg log 2");
                    BLMessage* subThreadMsg = *iter;
                    Data* sendData = (Data*)subThreadMsg->obj;
                    const size_t c_bufferSize = WS_WRITE_BUFFER_SIZE;
                    
                    size_t remaining = sendData->len - sendData->issued;
                    size_t n = std::min(remaining, c_bufferSize );
                    char* buf = new char[n];
                    memcpy(buf, sendData->bytes + sendData->issued, n);
                    
                    bytesWrite = send(_sktContext,buf,n,MSG_DONTWAIT);
                    if(bytesWrite < 0){
                        mylog3("send msg log 3, error=%s", strerror(errno));
                        CC_SAFE_DELETE_ARRAY(buf);
                        if (errno != EAGAIN && errno != EINTR && errno != EINPROGRESS) {
                            onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
                        }
                        break;
                    }
                    mylog("send msg log 4");
                    CCLOG("send bytes %d",bytesWrite);
                    
                    if (bytesWrite < n)
                    {
                        mylog("send msg log 5");
                        sendData->issued += bytesWrite;
                    } else {
                        mylog("send msg log 6");
                        CC_SAFE_DELETE_ARRAY(sendData->bytes);
                        CC_SAFE_DELETE(sendData);
                        iter = _wsHelper->_subThreadWsMessageQueue->erase(iter);
                        CC_SAFE_DELETE(subThreadMsg);
                    }
                    mylog("send msg log 7");
                    CC_SAFE_DELETE_ARRAY(buf);
                    //                    idleCount = 0;
                }
                
                /* get notified as soon as we can write again */
            }
                break;
                
            case SOCKET_REASON_RECV:
			{
				ssize_t length = recv(_sktContext, recv_buffer, WS_READ_BUFFER_SIZE, MSG_DONTWAIT);
                mylog2("send msg log ===1001=== recv length=%d", int(length));
				if(length <= 0){
                    mylog2("send msg log ===1002=== recv length=%d", int(length));
                    if (errno != EAGAIN && errno != EINTR && errno != EINPROGRESS) {
                        mylog2("send msg log ===1003=== recv length=%d", int(length));
                        onNetworkState(BL_WS_MSG_TO_UITHREAD_ERROR);
                    }
				}
                else if (length > 0){
                    
//                    idleCount = 0;
                    if (_currentData == NULL)
                    {
                        _currentData = new char[length];
                        _currentDataLen = 0;
                    }
                    
					if(_currentDataLen == 0){
						memcpy (_currentData, recv_buffer, length);
						_currentDataLen = length; // 收到消息的总字节数
					}else{
						char *new_data = new char [_currentDataLen + length];
						memcpy (new_data, _currentData, _currentDataLen);
						memcpy (new_data + _currentDataLen, recv_buffer, length);
						CC_SAFE_DELETE_ARRAY(_currentData);
						_currentData = new_data;
						_currentDataLen = _currentDataLen + length;
					}
                    
					// if(length >= WS_READ_BUFFER_SIZE){ // need recv more
     //                    CCLOG("recv length %ld > %d",length,WS_READ_BUFFER_SIZE);
					// 	return 0;
					// }
                    
                    int dataLen = (((int)_currentData[0] << 24) & 0xFF000000) | (((int)_currentData[1] << 16) & 0xFF0000) | (((int)_currentData[2] << 8) & 0xFF00) | ((int)_currentData[3]  & 0xFF);
                    CCLOG("data length %d, recv len %zd  _sktContext %d",dataLen,_currentDataLen, _sktContext);
//                    struct timeval tv2;
//                    gettimeofday(&tv2, NULL);
//                    CCLOG("onSocketCallback::recv time %ld", tv2.tv_sec * 1000 + tv2.tv_usec / 1000);
                    
                    if (dataLen + 4 > _currentDataLen) {// need recv more
                        //CCLOG("head bit %d %d %d %d",_currentData[0],_currentData[1],_currentData[2],_currentData[3]);
                        return 0;
                    }
                    
                    char* tmp = _currentData;
                    int totalLen = 0;
//                    CrashMsgCollector::getInstance()->addMsg("received_raw_data", (unsigned char *)_currentData, (int)_currentDataLen);
//                    CrashMsgCollector::getInstance()->addMsg("received_data_len", (int)_currentDataLen);
                    while (totalLen < _currentDataLen) { // 处理收到总消息里的每一条逻辑消息
//                        log("boloSocket while1: totalLen:%d  _currentDataLen:%zd", totalLen, _currentDataLen);
                        if (totalLen + 4 > _currentDataLen) { // need recv more
                            _currentDataLen = _currentDataLen - totalLen;
                            log("boloSocket while5:  _currentDataLen:%zd", _currentDataLen);
                            char *new_data = new char [_currentDataLen];
                            memcpy (new_data, tmp, _currentDataLen);
                            CC_SAFE_DELETE_ARRAY(_currentData);
                            _currentData = new_data;
                            return 0;
                        }
                        
                        int outLen = 0;
                        int socketDataLen = (((int)tmp[0] << 24) & 0xFF000000) | (((int)tmp[1] << 16) & 0xFF0000) | (((int)tmp[2] << 8) & 0xFF00) | ((int)tmp[3]  & 0xFF); // 代表当前逻辑消息的总字节数
                        // if(socketDataLen > WS_READ_BUFFER_SIZE || socketDataLen < 0){
                        //     CCLOG("head bit %d %d %d %d",tmp[0],tmp[1],tmp[2],tmp[3]);
                        //     CCLOG("head length error: len %d",socketDataLen);
                        //     CC_SAFE_DELETE_ARRAY(_currentData);
                        //     _currentData = nullptr;
                        //     _currentDataLen = 0;
                        //     return 0;
                        // } // 一条总消息里会包含多条消息
                        
//                        log("boloSocket while2: socketDataLen:%d  _currentDataLen:%zd", socketDataLen, _currentDataLen);
                        
                        if (totalLen + socketDataLen + 4 > _currentDataLen){ // need recv more
                            _currentDataLen = _currentDataLen - totalLen;
//                            log("boloSocket while3:  _currentDataLen:%zd", _currentDataLen);
                            char *new_data = new char [_currentDataLen];
                            memcpy (new_data, tmp, _currentDataLen);
                            CC_SAFE_DELETE_ARRAY(_currentData);
                            _currentData = new_data;
                            return 0;
                        }
                        
                        totalLen += 4;
                        tmp += 4; // 右移4字节，去掉了消息长度
                        
//                        struct timeval tv;
//                        gettimeofday(&tv, NULL);
//                        CCLOG("onSocketCallback::SOCKET_REASON_RECV %ld", tv.tv_sec * 1000 + tv.tv_usec / 1000);
                        
                        LuaValue* va = DataUtils::loadLuaValue((unsigned char *)tmp,&outLen);
                        totalLen += socketDataLen;
                        tmp += socketDataLen;
                        _heart=0;
                        
//                    #if COCOS2D_DEBUG == 0
//                        if (LuaValueTypeArray == va->getType() && 2 == va->arrayValue().size()) {
//                            cocos2d::LuaValue headMsg = va->arrayValue().front();
//                            if(LuaValueTypeString == headMsg.getType())
//                            {
//                                log("boloSocket while4:  headStr:%s", headMsg.stringValue().c_str());
//                            }
//                        }
//                    #endif
                        
                    #if COCOS2D_DEBUG >= 1
                        // 添加一个判断消息长度超过一定值，会发送消息，用于检测过长的消息 【2022/5/26 by sunyungao】
                        if (va->getType() == LuaValueTypeArray && va->arrayValue().size() == 2)
                        {
                            const cocos2d::LuaValueArray luaArr = va->arrayValue();
                            if(socketDataLen > 1000 && luaArr.size() == 2)
                            {
                                cocos2d::LuaValue headMsg = luaArr.front();
                                std::string headStr = headMsg.stringValue();
                                int recvLength = socketDataLen;
                                
                                Director::getInstance()->getScheduler()->performFunctionInCocosThread([headStr, recvLength, this](){
                                    if (nullptr == _recvEvent) {
                                        CCLOG("_recvEvent nullptr");
                                        return;
                                    }
                                    CCLOG("setUserDataString  %s", headStr.c_str());
                                    _recvEvent->setUserDataString(headStr);
                                    _recvEvent->setUserDataInt(recvLength);
                                    Director::getInstance()->getEventDispatcher()->dispatchEvent(_recvEvent);
                                });
                            }
                        } 
                        else
                        {
                            CCLOG("COCOS2D_DEBUG wrong luaValueType %d", va->getType());
                        }
                        
                    #endif
                        
                        _luaValueDACount = 0;
                        getLuaValueDACount(*va);
//                        CCLOG("msg key: %s  frameCount: %d", va->arrayValue().front().stringValue().c_str(), _luaValueDACount);
                        
                        BLMessage* msg = new BLMessage();
                        msg->what = BL_WS_MSG_TO_UITHREAD_MESSAGE;
                        msg->obj = va;
                        msg->count = _luaValueDACount;
                        _wsHelper->sendMessageToUIThread(msg);
                       
                    }
                    CC_SAFE_DELETE_ARRAY(_currentData);
					_currentDataLen = 0;
                    memset(recv_buffer, 0, WS_READ_BUFFER_SIZE);
//					CC_SAFE_DELETE_ARRAY(_currentData);
//					_currentData = nullptr;
//					_currentDataLen = 0;
                }
            }
                break;
            default:
                break;
                
        }
        
        return 0;
    }

    void BLSocket::getLuaValueDACount(const LuaValue& value)
    {
        const LuaValueType type = value.getType();
        if (type == LuaValueTypeInt || type == LuaValueTypeFloat || type == LuaValueTypeBoolean || type == LuaValueTypeString || type == LuaValueTypeObject)
        {
            _luaValueDACount ++;
        }
        else if (type == LuaValueTypeDict)
        {
            _luaValueDACount ++;
            getLuaValueDictCount(value.dictValue());
        }
        else if (type == LuaValueTypeArray)
        {
            _luaValueDACount ++;
            getLuaValueArrayCount(value.arrayValue());
        }
    }

    void BLSocket::getLuaValueDictCount(const LuaValueDict& dict)
    {
        for (LuaValueDictIterator it = dict.begin(); it != dict.end(); ++it)
        {
            getLuaValueDACount(it->second);
        }
    }

    void BLSocket::getLuaValueArrayCount(const LuaValueArray& array)
    {
        for (LuaValueArrayIterator it = array.begin(); it != array.end(); ++it)
        {
            getLuaValueDACount(*it);
        }
    }
    
    void BLSocket::onUIThreadReceiveMessage(BLMessage* msg)
    {
        switch (msg->what) {
            case BL_WS_MSG_TO_UITHREAD_OPEN:
            {
                Ref *obj;
                CCARRAY_FOREACH(_delegateList, obj){
                    BLDelegate *dele = (BLDelegate*)obj;
                    dele->onOpen(this);
                }
            }
                break;
            case BL_WS_MSG_TO_UITHREAD_MESSAGE:
            {
                Ref *obj;
                CCARRAY_FOREACH(_delegateList, obj){
                    BLDelegate *dele = (BLDelegate*)obj;
                    dele->onMessage(this, msg->obj);
                }

            }
                break;
            case BL_WS_MSG_TO_UITHREAD_CLOSE:
            {
                Ref *obj;
                CCARRAY_FOREACH(_delegateList, obj){
                    BLDelegate *dele = (BLDelegate*)obj;
                    dele->onClose(this);
                }

            }
                break;
            case BL_WS_MSG_TO_UITHREAD_ERROR:
            {
                BLSocket::ErrorCode err = ErrorCode::CONNECTION_FAILURE;
                Ref *obj;
                CCARRAY_FOREACH(_delegateList, obj){
                    BLDelegate *dele = (BLDelegate*)obj;
                    dele->onError(this, err);
                }
        
            }
                break;
            default:
                break;
        }
        BasicScriptData data(this, msg);
        ScriptEvent scriptEvent(kBLSocketEvent, &data);
        ScriptEngineManager::getInstance()->getScriptEngine()->sendEvent(&scriptEvent);
    }
    void BLSocket::doUpdate()
    {
        _wsHelper->update(1.f/60);
    }
}

NS_CC_END
