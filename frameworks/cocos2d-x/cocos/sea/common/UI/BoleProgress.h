//
//  BoleProgress.h
//  cocos2d_libs
//
//  Created by chen<PERSON><PERSON><PERSON><PERSON> on 2025/2/20.
//

#ifndef __SEA_BOLE_PROGRESS_H__
#define __SEA_BOLE_PROGRESS_H__
#include "cocos2d.h"
#include "../SeaConfig.h"
#include "sea/common/SeaCommon.h"
#include <array>

namespace sea {

class BoleLabel;

class BoleProgress: public cocos2d::Sprite
{
private:
    typedef std::function<std::string(float, float)> SET_STRING_FUN;
    class BarData {
    public:
        BarData(){
            pBarNode = nullptr;
        }
        ~BarData(){
            CC_SAFE_RELEASE_NULL(pBarNode);
        }
        void setBarNode(cocos2d::Sprite* barNode){
            CC_SAFE_RELEASE_NULL(pBarNode);
            pBarNode = barNode;
            CC_SAFE_RETAIN(pBarNode);
        }
        cocos2d::Sprite* getBarNode(){
            return pBarNode;
        }
    public:
        cocos2d::Rect originRect;
        cocos2d::Size originSize;
    private:
        cocos2d::Sprite* pBarNode;
    };
public:
    BoleProgress();
    virtual ~BoleProgress();
    static BoleProgress* create(const std::string& path_bg, const std::string& path_pro = "", const cocos2d::ValueMap& params = cocos2d::ValueMapNull);
    bool initWithData(const std::string& path_bg, const std::string& path_pro = "", const cocos2d::ValueMap& params = cocos2d::ValueMapNull);
    void resetData(const std::string& path, const sea::TextureResType& resType = sea::TextureResType::LOCAL);
    void setProTexture(const std::string& path, const sea::TextureResType& resType = sea::TextureResType::LOCAL);
    void addLabel(cocos2d::Label* node);
    void addLabel(BoleLabel* node);
    void setLabel(cocos2d::Label* node);
    void setLabel(BoleLabel* node);
    cocos2d::Node* getLabel();
    void setStringFun(SET_STRING_FUN fun);
    float getPercent();
    void setPercent(float percent, float total = -1, const cocos2d::Value& aniTime = cocos2d::Value::Null);
    void setPercentage(float p);
    void resetDotNodes(const cocos2d::ValueVector& dotNodes, bool isVertical);
    float getDotPercentWithData(float finishPointNum, std::vector<float> pointsNumList, const cocos2d::ValueVector& dotNodes, std::vector<std::pair<float, float>> sectionHidePointsWidth, bool isVertical);
private:
    void _setPercent(float percent, float total = -1);
private:
    bool bIsClip, bIsTextureRotated;
    cocos2d::Value pLabel;
    cocos2d::ValueMap mParams;
    cocos2d::Node *pProNode, *pActionNode;
    float fProLeft, fProRight, fProWidth, fProX;
    BarData *pBar1, *pBar2;
    float fPercent;
    SET_STRING_FUN funSetString;
    std::vector<float> vPercentList, vWidthList;
};

}

#endif

