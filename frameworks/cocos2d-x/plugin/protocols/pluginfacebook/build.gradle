apply plugin: 'com.android.library'

android {
    namespace "com.cocos2dx.facebook"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

   sourceSets.main {
        java.srcDir  "src/main/java"
        res.srcDir "src/main/res"
        jniLibs.srcDir "libs"
        manifest.srcFile "src/main/AndroidManifest.xml"
    }
}

repositories {
    mavenCentral()
    maven {
        url "https://maven.google.com"
    }
    flatDir {
        dirs 'libs'
    }
    mavenCentral()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar',])
    implementation project(':comonprotocol')

    api 'com.facebook.android:facebook-android-sdk:latest.release'
}