include_directories(
  ../external/ConvertUTF
  ../external/poly2tri
  ../external/poly2tri/common
  ../external/poly2tri/sweep
)

set(COCOS_2D_SRC

  2d/CCActionCamera.cpp
  2d/CCActionCatmullRom.cpp
  2d/CCAction.cpp
  2d/CCActionEase.cpp
  2d/CCActionGrid3D.cpp
  2d/CCActionGrid.cpp
  2d/CCActionInstant.cpp
  2d/CCActionInterval.cpp
  2d/CCActionManager.cpp
  2d/CCActionPageTurn3D.cpp
  2d/CCActionProgressTimer.cpp
  2d/CCActionTiledGrid.cpp
  2d/CCActionTween.cpp
  2d/CCAnimationCache.cpp
  2d/CCAnimation.cpp
  2d/CCAtlasNode.cpp
  2d/CCCamera.cpp
  2d/CCCameraBackgroundBrush.cpp
  2d/CCClippingNode.cpp
  2d/CCClippingRectangleNode.cpp
  2d/CCComponentContainer.cpp
  2d/CCComponent.cpp
  2d/CCDrawingPrimitives.cpp
  2d/CCDrawNode.cpp
  2d/CCFastTMXLayer.cpp
  2d/CCFastTMXTiledMap.cpp
  2d/CCFontAtlasCache.cpp
  2d/CCFontAtlas.cpp
  2d/CCFontCharMap.cpp
  2d/CCFont.cpp
  2d/CCFontFNT.cpp
  2d/CCFontFreeType.cpp
  2d/CCGLBufferedNode.cpp
  2d/CCGrabber.cpp
  2d/CCGrid.cpp
  2d/CCLabelAtlas.cpp
  2d/CCLabelBMFont.cpp
  2d/CCLabel.cpp
  2d/CCLabelTextFormatter.cpp
  2d/CCLabelTTF.cpp
  2d/CCLayer.cpp
  2d/CCLight.cpp
  2d/CCMenu.cpp
  2d/CCMenuItem.cpp
  2d/CCMotionStreak.cpp
  2d/CCNode.cpp
  2d/CCNodeGrid.cpp
  2d/CCParallaxNode.cpp
  2d/CCParticleBatchNode.cpp
  2d/CCParticleExamples.cpp
  2d/CCParticleSystem.cpp
  2d/CCParticleSystemQuad.cpp
  2d/CCProgressTimer.cpp
  2d/CCProtectedNode.cpp
  2d/CCRenderTexture.cpp
  2d/CCScene.cpp
  2d/CCSpriteBatchNode.cpp
  2d/CCSprite.cpp
  2d/CCSpriteFrameCache.cpp
  2d/CCSpriteFrame.cpp
  2d/CCAutoPolygon.cpp
  ../external/clipper/clipper.cpp
  2d/CCTextFieldTTF.cpp
  2d/CCTileMapAtlas.cpp
  2d/CCTMXLayer.cpp
  2d/CCTMXObjectGroup.cpp
  2d/CCTMXTiledMap.cpp
  2d/CCTMXXMLParser.cpp
  2d/CCTransition.cpp
  2d/CCTransitionPageTurn.cpp
  2d/CCTransitionProgress.cpp
  2d/CCTweenFunction.cpp

)
