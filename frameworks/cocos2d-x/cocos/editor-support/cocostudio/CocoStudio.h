/****************************************************************************
Copyright (c) 2013-2014 Chukong Technologies Inc.

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/

#ifndef __EDITOR_SUPPORT_COCOSTUDIO_H__
#define __EDITOR_SUPPORT_COCOSTUDIO_H__

#include "cocostudio/CCActionFrame.h"
#include "cocostudio/CCActionFrameEasing.h"
#include "cocostudio/CCActionManagerEx.h"
#include "cocostudio/CCActionNode.h"
#include "cocostudio/CCActionObject.h"
#include "cocostudio/CCArmature.h"
#include "cocostudio/CCBone.h"
#include "cocostudio/CCArmatureAnimation.h"
#include "cocostudio/CCProcessBase.h"
#include "cocostudio/CCTween.h"
#include "cocostudio/CCDatas.h"
#include "cocostudio/CCBatchNode.h"
#include "cocostudio/CCDecorativeDisplay.h"
#include "cocostudio/CCDisplayFactory.h"
#include "cocostudio/CCDisplayManager.h"
#include "cocostudio/CCSkin.h"
#include "cocostudio/CCColliderDetector.h"
#include "cocostudio/CCArmatureDataManager.h"
#include "cocostudio/CCArmatureDefine.h"
#include "cocostudio/CCDataReaderHelper.h"
#include "cocostudio/CCSpriteFrameCacheHelper.h"
#include "cocostudio/CCTransformHelp.h"
#include "cocostudio/CCUtilMath.h"
#include "cocostudio/CCComBase.h"
#include "cocostudio/CCComAttribute.h"
#include "cocostudio/CCComAudio.h"
#include "cocostudio/CCComController.h"
#include "cocostudio/CCComRender.h"
#include "cocostudio/CCInputDelegate.h"
#include "cocostudio/DictionaryHelper.h"
#include "cocostudio/CCSGUIReader.h"
#include "cocostudio/CCSSceneReader.h"
#include "cocostudio/TriggerBase.h"
#include "cocostudio/ActionTimeline/CCActionTimelineCache.h"
#include "cocostudio/ActionTimeline/CCFrame.h"
#include "cocostudio/ActionTimeline/CCTimeLine.h"
#include "cocostudio/ActionTimeline/CCActionTimeline.h"
#include "cocostudio/CocosStudioExport.h"
#include "cocostudio/ActionTimeline/CSLoader.h"

#endif
