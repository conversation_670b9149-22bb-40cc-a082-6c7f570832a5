package org.cocos2dx.plugin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.Log;

import com.adjust.sdk.Adjust;
import com.adjust.sdk.AdjustConfig;
import com.mopub.common.MoPub;
import com.mopub.common.MoPubReward;
import com.mopub.common.SdkInitializationListener;
import com.mopub.mobileads.MoPubErrorCode;
import com.mopub.mobileads.MoPubInterstitial;
import com.mopub.mobileads.MoPubRewardedVideoListener;
import com.mopub.mobileads.MoPubRewardedVideos;
import com.mopub.network.ImpressionData;
import com.mopub.network.ImpressionListener;
import com.mopub.network.ImpressionsEmitter;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Hashtable;
import java.util.Iterator;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

public class PluginMoPub implements InterfaceAds, PluginListener{

	private static final String TAG = "PluginMoPub";
	private static Context mContext = null;
	private static Activity mActivity = null;
	private MoPubRewardedVideoListener rewardedVideoListener;
	private MoPubInterstitial mInterstitial;
	private ImpressionListener mImpressionListener;
	private String ivUid;

//	private static Object lock = new Object(); // 线程同步对象

//	public Map<String,Boolean> rewardVideoMap = new HashMap< String,Boolean>(); //保存多个视频广告状态。
//	public Boolean adReady = false;  //插屏广告。

	public class Task extends TimerTask {
		@Override
		public void run() {
			if (MoPubWrapper.INITMOPUB) {
				Log.i(TAG, "Send init ok to lua 222 ");
				AdsWrapper.onAdsResult(PluginMoPub.this, 100, "", "");
//				task.cancel();
				timer.cancel();
			}
		}
	}
	//	轮询查询初始化情况。
	private Task task = new Task();
	Timer timer = new Timer(true);

	public PluginMoPub(Context context)
	{
		mContext = context;
		mActivity = (Activity) context;
		timer.schedule(new Task(),1000,1*1000);

		mImpressionListener = new ImpressionListener() {
			@Override
			public void onImpression(@NonNull String adUnitId, @Nullable ImpressionData impressionData) {
				Log.i("ILRD", "impression for adUnitId = " + adUnitId);
				if (impressionData == null) {
				} else {
					try {
						JSONObject ret = new JSONObject();
						try {
							ret.put("adGroupID",			impressionData.getAdGroupId());
							ret.put("adGroupName",		impressionData.getAdGroupName());
							ret.put("adGroupType",		impressionData.getAdGroupType());
							ret.put("adUnitID",			impressionData.getAdUnitId());
							ret.put("adUnitFormat",		impressionData.getAdUnitFormat());
							ret.put("adUnitName",			impressionData.getAdUnitName());
							ret.put("country",			impressionData.getCountry());
							ret.put("networkName",		impressionData.getNetworkName());
							ret.put("publisherRevenue",	impressionData.getPublisherRevenue());
						} catch (Exception e) {
							e.printStackTrace();
						}

						String retStr = ret.toString();
						Log.i("ILRD", "impressionData retStr = \n" + retStr);
						AdsWrapper.onAdsResult(PluginMoPub.this, 26, adUnitId, retStr);

						Log.i("ILRD", "impression data adUnitId = " + adUnitId + " data = \n" + impressionData.getJsonRepresentation().toString(2));
						Adjust.trackAdRevenue(AdjustConfig.AD_REVENUE_MOPUB, impressionData.getJsonRepresentation());
					} catch (JSONException e) {
						Log.d("ILRD", "Can't format impression data. err = " + e.toString());
					}
				}
			}
		};

		// subscribe to start listening for impression data
		ImpressionsEmitter.addListener(mImpressionListener);
	}

	// Attention: 错误码从1开始
	private static String formatMoPubError(MoPubErrorCode errorCode) {
		return "{\"code\":" + (errorCode.ordinal()+1)+ ",\"description\":\"" + errorCode.toString() + "\"}";
	}

	public MoPubRewardedVideoListener RewardedVideoListener = new MoPubRewardedVideoListener() {
		//Video Ad
		@Override
		public void onRewardedVideoLoadSuccess(@NonNull String adUnitId) {
			Log.i(TAG,"onRewardedVideoLoadSuccess:"+adUnitId);
			boolean ret = MoPubRewardedVideos.hasRewardedVideo(adUnitId);
			Log.i(TAG,"hasRewardedVideo: " + adUnitId + " : "+ String.valueOf(ret));
			AdsWrapper.onAdsResult(PluginMoPub.this, 0 , adUnitId, "");
		}

		@Override
		public void onRewardedVideoLoadFailure(@NonNull String adUnitId, @NonNull MoPubErrorCode errorCode) {
			Log.i(TAG,"onRewardedVideoLoadFailure " +  errorCode.toString() + " adId "+adUnitId);
			AdsWrapper.onAdsResult(PluginMoPub.this, 5, adUnitId, PluginMoPub.formatMoPubError(errorCode));
		}

		@Override
		public void onRewardedVideoStarted(@NonNull String adUnitId) {
			Log.i(TAG,"onRewardedVideoStarted "+adUnitId);
			AdsWrapper.onAdsResult(PluginMoPub.this, 1, adUnitId, "");
		}

		@Override
		public void onRewardedVideoPlaybackError(@NonNull String adUnitId, @NonNull MoPubErrorCode errorCode) {

			Log.i(TAG,"onRewardedVideoPlaybackError " +  errorCode.toString() + " adId "+adUnitId);
			AdsWrapper.onAdsResult(PluginMoPub.this, 5, adUnitId, PluginMoPub.formatMoPubError(errorCode));
		}

		@Override
		public void onRewardedVideoClicked(@NonNull String adUnitId) {
			Log.i(TAG,"onRewardedVideoClicked");
			AdsWrapper.onAdsResult(PluginMoPub.this, 4, adUnitId, "");
		}

		@Override
		public void onRewardedVideoClosed(@NonNull String adUnitId) {
			Log.i(TAG,"onRewardedVideoClosed " + adUnitId);
			AdsWrapper.onAdsResult(PluginMoPub.this, 2, adUnitId, "");
		}

		@Override
		public void onRewardedVideoCompleted(@NonNull Set<String> adUnitIds, @NonNull MoPubReward reward) {
			Log.i(TAG,"onRewardedVideoCompleted " + adUnitIds.toString());
			if (!adUnitIds.isEmpty()) {
				Iterator iter = adUnitIds.iterator();
				while (iter.hasNext()) {
					String unitId = (String) iter.next();
					AdsWrapper.onAdsResult(PluginMoPub.this, 3, unitId, "");
				}
			} else {
				AdsWrapper.onAdsResult(PluginMoPub.this, 3, "", "");
			}
		}
	};


	public MoPubInterstitial.InterstitialAdListener InterstitialAdListener = new MoPubInterstitial.InterstitialAdListener() {
		// InterstitialAdListener methods
		@Override
		public void onInterstitialLoaded(MoPubInterstitial interstitial) {
			Log.i(TAG,"onInterstitialLoaded");
//			PluginMoPub.this.adReady = true;
				AdsWrapper.onAdsResult(PluginMoPub.this, 10, "", "");
		}

		@Override
		public void onInterstitialFailed(MoPubInterstitial interstitial, MoPubErrorCode errorCode) {
			Log.i(TAG,"onInterstitialFailed " + errorCode.toString());

//			PluginMoPub.this.adReady = false;
//			PluginMoPub.this.loadAds();
			AdsWrapper.onAdsResult(PluginMoPub.this, 15, "", PluginMoPub.formatMoPubError(errorCode));
		}

		@Override
		public void onInterstitialShown(MoPubInterstitial interstitial) {
			AdsWrapper.onAdsResult(PluginMoPub.this, 11, "", "");
		}

		@Override
		public void onInterstitialClicked(MoPubInterstitial interstitial) {
			Log.i(TAG,"onInterstitialClicked");
			AdsWrapper.onAdsResult(PluginMoPub.this, 14, "", "");
		}

		@Override
		public void onInterstitialDismissed(MoPubInterstitial interstitial) {
			Log.i(TAG,"onInterstitialDismissed");
			AdsWrapper.onAdsResult(PluginMoPub.this, 12, "", "");
//			PluginMoPub.this.adReady = false;
//			PluginMoPub.this.loadAds();
		}
	};

	// PARAMS
	// videoadid
	// interstitial_unit_id
	public void init(JSONObject o)
	{
		final JSONObject info = o;

		Log.i(TAG, TAG +" init");
		PluginWrapper.addListener(this);
		PluginWrapper.runOnMainThread(new Runnable() {

			@Override
			public void run() {
//				synchronized (lock) {
					Log.i(TAG, TAG +" run");
					try {

						MoPubRewardedVideos.setRewardedVideoListener(PluginMoPub.this.RewardedVideoListener);

//						String videoadid = info.getString("Param1")
//						MoPubRewardedVideos.setRewardedVideoListener(RewardedVideoListener);
//						MoPubRewardedVideos.loadRewardedVideo(videoadid);
//
//						//interstitial_unit_id
//						String interstitial_unit_id = info.getString("Param2");
//						mInterstitial = new MoPubInterstitial(mActivity, interstitial_unit_id);
//						mInterstitial.setInterstitialAdListener(InterstitialAdListener);
//						mInterstitial.load();

					} catch (Exception e) {
						e.printStackTrace();
					}
//				}
			}
		});
	}

	private SdkInitializationListener initSdkListener() {
		return new SdkInitializationListener() {
			@Override
			public void onInitializationFinished() {
				/* MoPub SDK initialized.
				Check if you should show the consent dialog here, and make your ad requests. */
				Log.i(TAG,"onInitializationFinished");
			}
		};
	}

	@Override
	public void configDeveloperInfo(Hashtable<String, String> devInfo) {
		// TODO Auto-generated method stub
	}


	public void preLoadRewardVideo(final String adid) {
		Log.i(TAG, "preLoadRewardVideo " + adid);
		PluginWrapper.runOnMainThread(new Runnable() {
			public void run() {
				MoPubRewardedVideos.loadRewardedVideo(adid);
			}
		});
	}

	public boolean isRewardVideoLoaded(String unit_id)
	{
		Log.i(TAG,"isRewardVideoLoaded:"+unit_id);
		return MoPubRewardedVideos.hasRewardedVideo(unit_id);
	}

	public void showRewardVideo(final String uid)
	{
		Log.i(TAG,"showRewardVideo "+uid);
		PluginWrapper.runOnMainThread(new Runnable() {
			@Override
			public void run() {
				Log.i(TAG,"showRewardVideo run");
				if (MoPubRewardedVideos.hasRewardedVideo(uid)) {
					Log.i(TAG,"showRewardVideo can show "+uid);
					MoPubRewardedVideos.showRewardedVideo(uid);
				} else {
					AdsWrapper.onAdsResult(PluginMoPub.this, 5, uid, "");
				}
			}
		});

	}

	public void preLoadInterstitialVideo(final String uid)
	{
		Log.i(TAG,"preLoadInterstitialVideo "+uid);
		PluginWrapper.runOnMainThread(new Runnable() {
			@Override
			public void run() {
				if (ivUid == null || ivUid.compareTo(uid) != 0) {
					if (mInterstitial != null) {
						mInterstitial.setInterstitialAdListener(null);
						mInterstitial.destroy();
						mInterstitial = null;
					}
					ivUid = uid;
				}
				if (mInterstitial == null) {
					PluginMoPub.this.mInterstitial = new MoPubInterstitial(mActivity, ivUid);
					PluginMoPub.this.mInterstitial.setInterstitialAdListener(PluginMoPub.this.InterstitialAdListener);
				}
				PluginMoPub.this.mInterstitial.load();
			}
		});
	}

	public boolean isInterstitialVideoLoaded()
	{
		return false;
	}

	public void showInterstitialVideo()
	{
		if (this.mInterstitial != null){
			PluginWrapper.runOnMainThread(new Runnable() {
				@Override
				public void run() {
					if(mInterstitial.isReady()) {
						PluginMoPub.this.mInterstitial.show();
					} else {
						AdsWrapper.onAdsResult(PluginMoPub.this, 15, "", "");
					}
				}
			});
		}
	}

	@Override
	public void showAds(Hashtable<String, String> adsInfo, int pos) {
	}
	@Override
	public void hideAds(Hashtable<String, String> adsInfo) {
	}

	@Override
	public void queryPoints() {
		// TODO Auto-generated method stub
	}
	@Override
	public void spendPoints(int points) {
		// TODO Auto-generated method stub
	}

	@Override
	public void setDebugMode(boolean debug) {
		// TODO Auto-generated method stub
	}

	@Override
	public String getSDKVersion() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getPluginVersion() {
		// TODO Auto-generated method stub
		return null;
	}


	//PluginListener

	@Override
	public void onResume() {
		if (mActivity != null){
			MoPub.onResume(mActivity);
		}
	}

	@Override
	public void onPause() {
		if (mActivity != null){
			MoPub.onPause(mActivity);
		}
	}

	@Override
	public void onDestroy() {
		Log.i(TAG,"onDestroy");

		if (mImpressionListener != null) {
			ImpressionsEmitter.removeListener(mImpressionListener);
			mImpressionListener = null;
		}

		MoPubRewardedVideos.setRewardedVideoListener(null);
		if (mInterstitial != null){
			mInterstitial.setInterstitialAdListener(null);
			mInterstitial.destroy();
		}
	}

	@Override
	public boolean onActivityResult(int requestCode, int resultCode, Intent data) {
		return false;
	}
}
