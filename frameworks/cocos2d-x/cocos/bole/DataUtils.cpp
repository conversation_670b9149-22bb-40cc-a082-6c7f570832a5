#include "DataUtils.h"
//#include "md5.h"
#include "MD5Utils.h"
#include "base/CrashMsgCollector.h"

USING_NS_CC;

#define ONE_BYTE		0xFF
#define MIN_MEMORY_SIZE	1024

#define RIGHT_SHIFT_N_BYTE(x,n)	((x) >> ((n)*8))

#define GET_LOW_BYTE(x,n)	(RIGHT_SHIFT_N_BYTE(x,n) & ONE_BYTE)

static int default_memory_size = 0;

typedef enum Content_Type{
	ContentTypeChar = 1,
	ContentTypeShort = 2,
	ContentTypeInt = 3,

	ContentTypeListShort = 4,
	ContentTypeListLong = 5,

	ContentTypeCharListShort = 6,
	ContentTypeCharList = 7,
	ContentTypeCharListLong = 8,

	ContentTypeIntList = 9,

	ContentTypeString = 10,
	ContentTypeStringList = 11,

	ContentTypeLong = 12,

	ContentTypeDict = 13,
	ContentTypeDictLarge = 14,
	
    ContentTypeDouble = 15,
    ContentTypeFloat = 16,
	ContentTypeUnknow,
}ContentType;

std::string DataUtils::messsage_key = "";
std::mutex DataUtils::lock;

void DataUtils::dump(int value, unsigned char* out,int *len)
{
	int pos = *len;
	int j = value;
	if( j >= -128 && j <= 127 ){
		out[pos++] = ContentTypeChar;
		out[pos++] = GET_LOW_BYTE(j,0);			
	} else if (j >= -32768 && j <= 32767){
		out[pos++] = ContentTypeShort;
		out[pos++] = GET_LOW_BYTE(j,0);
		out[pos++] = GET_LOW_BYTE(j,1);
	} else {
		out[pos++] = ContentTypeInt;
		out[pos++] = GET_LOW_BYTE(j,0);
		out[pos++] = GET_LOW_BYTE(j,1);
		out[pos++] = GET_LOW_BYTE(j,2);
		out[pos++] = GET_LOW_BYTE(j,3);
	}
	*len = pos;
}
void DataUtils::dump(LONG value, unsigned char* out,int *len)
{
	int pos = *len;
	out[pos++] = ContentTypeLong;
	for (int i = 0; i < 8; i++)
	{
		out[pos++] = GET_LOW_BYTE(value,i);
	}
	*len = pos;
}

void DataUtils::dump(float value, unsigned char* out,int *len)
{
    double d = (double)value;
    dump(d, out,len);
}

void DataUtils::dump(double value, unsigned char* out,int *len)
{
    int pos = *len;
    out[pos++] = ContentTypeDouble;
    
    unsigned char sign;
    int e;
    double f;
    unsigned int fhi, flo;
    int incr = 1;
    
    //    if (le) {
//    out += 8;
    incr = -1;
    //    }
    
    if (value < 0) {
        sign = 1;
        value = -value;
    }
    else
        sign = 0;
    
    f = frexp(value, &e);
    
    /* Normalize f to be in the range [1.0, 2.0) */
    if (0.5 <= f && f < 1.0) {
        f *= 2.0;
        e--;
    }
    else if (f == 0.0)
        e = 0;
    else {
        //        PyErr_SetString(PyExc_SystemError,
        //                        "frexp() result out of range");
        //        return -1;
    }
    
    //    if (e >= 1024)
    //        goto Overflow;
    //    else if (e < -1022) {
    if (e < -1022) {
        /* Gradual underflow */
        f = ldexp(f, 1022 + e);
        e = 0;
    }
    else if (!(e == 0 && f == 0.0)) {
        e += 1023;
        f -= 1.0; /* Get rid of leading 1 */
    }
    
    /* fhi receives the high 28 bits; flo the low 24 bits (== 52 bits) */
    f *= 268435456.0; /* 2**28 */
    fhi = (unsigned int)f; /* Truncate */
    assert(fhi < 268435456);
    
    f -= (double)fhi;
    f *= 16777216.0; /* 2**24 */
    flo = (unsigned int)(f + 0.5); /* Round */
    assert(flo <= 16777216);
    if (flo >> 24) {
        /* The carry propagated out of a string of 24 1 bits. */
        flo = 0;
        ++fhi;
        if (fhi >> 28) {
            /* And it also progagated out of the next 28 bits. */
            fhi = 0;
            ++e;
            //            if (e >= 2047)
            //                goto Overflow;
        }
    }
    
    /* First byte */
    out[pos+7] = (sign << 7) | (e >> 4);
//    out += incr;
    
    /* Second byte */
    out[pos+6] = (unsigned char) (((e & 0xF) << 4) | (fhi >> 24));
//    out += incr;
    
    /* Third byte */
    out[pos+5] = (fhi >> 16) & 0xFF;
//    out += incr;
    
    /* Fourth byte */
    out[pos+4] = (fhi >> 8) & 0xFF;
//    out += incr;
    
    /* Fifth byte */
    out[pos+3] = fhi & 0xFF;
//    out += incr;
    
    /* Sixth byte */
    out[pos+2] = (flo >> 16) & 0xFF;
//    out += incr;
    
    /* Seventh byte */
    out[pos+1] = (flo >> 8) & 0xFF;
//    out += incr;
    
    /* Eighth byte */
    out[pos] = flo & 0xFF;
    /* out += incr; Unneeded (for now) */
    
    
    *len = pos + 8;
}

void DataUtils::dump(unsigned char value[],int srcLen,unsigned char* out,int *len)
{
	int pos = *len;
	if (srcLen <= 255)	{
		out[pos++] = ContentTypeCharListShort;
		out[pos++] = GET_LOW_BYTE(srcLen,0);
	} else if (srcLen <= 65535){
		out[pos++] = ContentTypeCharList;
		out[pos++] = GET_LOW_BYTE(srcLen,0);
		out[pos++] = GET_LOW_BYTE(srcLen,1);
	} else {
		out[pos++] = ContentTypeCharListLong;
		out[pos++] = GET_LOW_BYTE(srcLen,0);
		out[pos++] = GET_LOW_BYTE(srcLen,1);
		out[pos++] = GET_LOW_BYTE(srcLen,2);
		out[pos++] = GET_LOW_BYTE(srcLen,3);
	}
	memcpy(out+pos,value,sizeof(unsigned char)*srcLen);
	*len = pos + srcLen;

}

void DataUtils::dump(std::string str,unsigned char* out,int *len)
{

	int srcLen = str.length();
	int pos = *len;
	out[pos++] = ContentTypeString;	

	const unsigned char *cStr = (unsigned char*)str.c_str();//chinses has some difference
	out[pos++] = GET_LOW_BYTE(srcLen,0);
	out[pos++] = GET_LOW_BYTE(srcLen,1);
	for (int i = 0; i < srcLen; i++)
	{		
		out[pos++] = GET_LOW_BYTE(cStr[i],0);
		out[pos++] = GET_LOW_BYTE(cStr[i],1);
	}
	*len = pos;
}

void DataUtils::dump(int value[],int srcLen,unsigned char* out,int *len)
{
	int pos = *len;
	out[pos++] = ContentTypeIntList;
	out[pos++] = GET_LOW_BYTE(srcLen,0);
	out[pos++] = GET_LOW_BYTE(srcLen,1);
	for (int i = 0; i < srcLen; i++)
	{
		out[pos++] = GET_LOW_BYTE(value[i],0);
		out[pos++] = GET_LOW_BYTE(value[i],1);
		out[pos++] = GET_LOW_BYTE(value[i],2);
		out[pos++] = GET_LOW_BYTE(value[i],3);
	}
	*len = pos;
}

void DataUtils::autoReSize(unsigned char **cache, int pos, int addSize, const char *tag)
{
    if (pos + addSize > default_memory_size)
    {
        int beforeSize = default_memory_size;
        do {
            default_memory_size *= 2;
        } while(pos + addSize > default_memory_size);
        unsigned char *newCache = new unsigned char[default_memory_size];
        memcpy(newCache, *cache, beforeSize);
        CC_SAFE_DELETE_ARRAY(*cache);
        *cache = newCache;
    }
}
 
void DataUtils::dump(cocos2d::Value &pObject,unsigned char **data,int *len)
{
	if(pObject.getType() == Value::Type::NONE){
		return ;
	}
	unsigned char *out = *data;

	if (0 == *len){
//		default_memory_size = MIN_MEMORY_SIZE;
//		*data = new unsigned char[default_memory_size];
//		out = *data;
//		*len = 4;
        default_memory_size = MIN_MEMORY_SIZE;
        *data = new unsigned char[default_memory_size];
        *len = 4;
	}

    Value::Type type = pObject.getType();
	if (Value::Type::INTEGER == type)	{

//		if (*len + 9 > default_memory_size)
//		{
//			default_memory_size *= 2;
//			unsigned char *newData = new unsigned char[default_memory_size];
//			memcpy(newData,out,*len);
//			CC_SAFE_DELETE_ARRAY(out);
//			*data = newData;
//			out = newData;
//		}
        DataUtils::autoReSize(data, *len, 9, "long"); out = *data;
		LONG value = pObject.asLong();
		dump(value,out,len);
	}else if(Value::Type::STRING == type){
		std::string str = pObject.asString();
		int srcLen = str.length();

//		while((srcLen+2)*2 + *len > default_memory_size)
//		{
//			default_memory_size *= 2;
//			unsigned char *newData = new unsigned char[default_memory_size];
//			memcpy(newData,out,*len);
//			CC_SAFE_DELETE_ARRAY(out);
//			*data = newData;
//			out = newData;
//		}
        DataUtils::autoReSize(data, *len, srcLen*2+3, "string"); out = *data;
        dump(str, out, len);
        
//		int pos = *len;
//		out[pos++] = ContentTypeString;
//
//		const unsigned char *cStr = (unsigned char* )str.c_str();
//		out[pos++] = GET_LOW_BYTE(srcLen,0);
//		out[pos++] = GET_LOW_BYTE(srcLen,1);
//		for (int i = 0; i < srcLen; i++)
//		{		
//			out[pos++] = GET_LOW_BYTE(cStr[i],0);
//			out[pos++] = GET_LOW_BYTE(cStr[i],1);
//		}
//		*len = pos;
    }else if (Value::Type::DOUBLE == type)	{
        
//        if (*len + 9 > default_memory_size)
//        {
//            default_memory_size *= 2;
//            unsigned char *newData = new unsigned char[default_memory_size];
//            memcpy(newData,out,*len);
//            CC_SAFE_DELETE_ARRAY(out);
//            *data = newData;
//            out = newData;
//        }
        DataUtils::autoReSize(data, *len, 9, "double"); out = *data;
        double value = pObject.asDouble();
        dump(value,out,len);
    }else if(Value::Type::VECTOR == type){

		ValueVector ar = pObject.asValueVector();
		int srcLen = ar.size();
		int pos = *len;

		if(srcLen <= 255){
            DataUtils::autoReSize(data, *len, 2, "typeList"); out = *data;
			out[pos++] = ContentTypeListShort;
			out[pos++] = GET_LOW_BYTE(srcLen,0);
		}else{
            DataUtils::autoReSize(data, *len, 3); out = *data;
			out[pos++] = ContentTypeListLong;
			out[pos++] = GET_LOW_BYTE(srcLen,0);
			out[pos++] = GET_LOW_BYTE(srcLen,1);
		}
		*len = pos;
        
        ValueVector::iterator iter;
		for (iter = ar.begin(); iter != ar.end(); iter++) {
            Value obj = *iter;
            dump(obj,data,len);
        }
        
		
	}else if(Value::Type::MAP == type){
		
		ValueMap dt = pObject.asValueMap();
		int srcLen = dt.size();
		int pos = *len;
		if(srcLen <= 255){
            DataUtils::autoReSize(data, *len, 2, "strMap"); out = *data;
			out[pos++] = ContentTypeDict;
			out[pos++] = GET_LOW_BYTE(srcLen,0);
		}else{
            DataUtils::autoReSize(data, *len, 3); out = *data;
			out[pos++] = ContentTypeDictLarge;
			out[pos++] = GET_LOW_BYTE(srcLen,0);
			out[pos++] = GET_LOW_BYTE(srcLen,1);
		}
		*len = pos;

        ValueMap::iterator iter;
        for (iter = dt.begin(); iter != dt.end(); iter++) {
            Value key = Value(iter->first);
            dump(key, data, len);
            Value value = iter->second;
            dump(value, data, len);
        }
    }else if(Value::Type::INT_KEY_MAP == type){
		
		ValueMapIntKey dt = pObject.asIntKeyMap();
		int srcLen = dt.size();
		int pos = *len;
		if(srcLen <= 255){
            DataUtils::autoReSize(data, *len, 2, "intMap"); out = *data;
			out[pos++] = ContentTypeDict;
			out[pos++] = GET_LOW_BYTE(srcLen,0);
		}else{
            DataUtils::autoReSize(data, *len, 3); out = *data;
			out[pos++] = ContentTypeDictLarge;
			out[pos++] = GET_LOW_BYTE(srcLen,0);
			out[pos++] = GET_LOW_BYTE(srcLen,1);
		}
		*len = pos;
        
        ValueMapIntKey::iterator iter;
        for (iter = dt.begin(); iter != dt.end(); iter++) {
            Value key = Value(iter->first);
            dump(key, data, len);
            Value value = iter->second;
            dump(value, data, len);
        }
	}else{
		
	}

	return ;
}

// boleSocket 线程将二进制流转成c++ value，再转成luaValue
cocos2d::LuaValue* DataUtils::loadLuaValue(unsigned char *in, int * outLen) {
    Value va = load(in, outLen);
    cocos2d::LuaValue* ret = reinterpret_cast<LuaValue*>(convertCppValueToLuaValue(&va));
    return ret;
}


static LuaValue staticConvert(Value cppValue)
{
    LuaValue ret;
    switch (cppValue.getType()) {
        case Value::Type::INTEGER:
        {
            ret = LuaValue::intValue(cppValue.asInt());
            break;
        }
        case Value::Type::DOUBLE:
        {
            //            char tmp[128]={0};
            //            sprintf(tmp, "%.0f",cppValue.asDouble());
            //            ret = LuaValue::stringValue(tmp);
            ret = LuaValue::floatValue(cppValue.asDouble());
            break;
        }
        case Value::Type::STRING:
        {
            ret = LuaValue::stringValue(cppValue.asString());
            break;
        }
        case Value::Type::VECTOR:
        {
            LuaValueArray array = LuaValueArray();
            ValueVector vec = cppValue.asValueVector();
            ValueVector::iterator iter;
            for (iter = vec.begin(); iter != vec.end(); iter++) {
                array.push_back(staticConvert(*iter));
            }
            ret = LuaValue::arrayValue(array);
            break;
        }
        case Value::Type::MAP:
        {
            LuaValueDict dict = LuaValueDict();
            ValueMap map = cppValue.asValueMap();
            ValueMap::iterator iter;
            for (iter = map.begin(); iter != map.end(); iter++) {
                std::string key = iter->first;
                Value value = iter->second;
                dict.insert(LuaValueDict::value_type(key,staticConvert(value)));
            }
            ret = LuaValue::dictValue(dict);
            break;
        }
        case Value::Type::INT_KEY_MAP:
        {
            LuaValueDict dict = LuaValueDict();
            ValueMapIntKey map = cppValue.asIntKeyMap();
            ValueMapIntKey::iterator iter;
            for (iter = map.begin(); iter != map.end(); iter++) {
                char intKey[16] = {0};
                sprintf(intKey, "%d",iter->first);
                Value value = iter->second;
                dict.insert(LuaValueDict::value_type(intKey,staticConvert(value)));
            }
            ret = LuaValue::dictValue(dict);
            break;
        }
        default:
            break;
    }
    return ret;
}

// 与LuaEngin解耦 【2023/8/14 by sunyungao】
void* DataUtils::convertCppValueToLuaValue(void* v) {
    Value* cppV = (Value*)v;
    LuaValue ret = staticConvert(*cppV);
    return new LuaValue(ret);
}

// socket二进制流转c++的value
Value DataUtils::load(unsigned char *in, int * outLen)
{
    std::lock_guard<std::mutex> locking(lock);
	if(NULL == in) return Value();
    messsage_key = "";
	unsigned char *content = in;
	return loadContent(&content,outLen);
}

double _PyFloat_Unpack8(const unsigned char *p)
{
    unsigned char sign;
    int e;
    unsigned int fhi, flo;
    double x;
    int incr = 1;
    
    //if (le) {
    p += 7;
    incr = -1;
    //}
    
    /* First byte */
    sign = (*p >> 7) & 1;
    e = (*p & 0x7F) << 4;
    
    p += incr;
    
    /* Second byte */
    e |= (*p >> 4) & 0xF;
    fhi = (*p & 0xF) << 24;
    p += incr;
    
    if (e == 2047) {
//        PyErr_SetString(
//                        PyExc_ValueError,
//                        "can't unpack IEEE 754 special value "
//                        "on non-IEEE platform");
        return -1.0;
    }
    
    /* Third byte */
    fhi |= *p << 16;
    p += incr;
    
    /* Fourth byte */
    fhi |= *p  << 8;
    p += incr;
    
    /* Fifth byte */
    fhi |= *p;
    p += incr;
    
    /* Sixth byte */
    flo = *p << 16;
    p += incr;
    
    /* Seventh byte */
    flo |= *p << 8;
    p += incr;
    
    /* Eighth byte */
    flo |= *p;
    
    x = (double)fhi + (double)flo / 16777216.0; /* 2**24 */
    x /= 268435456.0; /* 2**28 */
    
    if (e == 0)
        e = -1022;
    else {
        x += 1.0;
        e -= 1023;
    }
    x = ldexp(x, e);
    
    if (sign)
        x = -x;
    
    return x;
    
}

Value DataUtils::loadContent(unsigned char **out, int * outLen)
{
	Value ret;
	unsigned char *content = *out;
	ContentType type = (ContentType)content[0];
	content += 1;
	*out = content;
	switch (type)
	{
	case ContentTypeChar:
	{
        char a = (char)content[0];
		content += 1;
		*out = content;
		*outLen = 1;
		ret = Value(a);
        break;
	}
	case ContentTypeShort:
	{
		short i = content[0] | (((short)content[1]) << 8);
		content += 2;
		*out = content;
		*outLen = 2;
        ret = Value(i);
		break;
	}
	case ContentTypeInt:
	{
		int i = content[0];
		i |= ((int)content[1]) << 8;
		i |= ((int)content[2]) << 16;
		i |= ((int)content[3]) << 24;
		content += 4;
		*out = content;
		*outLen = 4;
        ret = Value(i);
		break;
	}
    case ContentTypeDouble:
    {
        double d = _PyFloat_Unpack8(content);
        content += 8;
        *out = content;
        *outLen = 8;
        ret = Value((double)d);
        break;
    }
	case ContentTypeListShort:		
	case ContentTypeListLong:		
	{
		int len = content[0];
		content += 1;		
		if (type == ContentTypeListLong){
			len |=  ((int)content[0]) << 8;	
			content += 1;
			
		}
		*out = content;
		ValueVector list = ValueVector();
		for (int i = 0; i < len; i++)
		{
			Value ref = loadContent(out,outLen);
            if (messsage_key.empty() && ref.getType() == Value::Type::STRING)
            {
                messsage_key = ref.asString();
                CrashMsgCollector::getInstance()->addMsg("load_content_key", messsage_key);
            }
            
			list.push_back(ref);
		}
		ret = Value(list);
        break;
	}
	case ContentTypeCharListShort:		
	case ContentTypeCharList:		
	case ContentTypeCharListLong:		
	{
		int len = content[0];
		content += 1;
		if (type == ContentTypeCharList){
			len |=  ((int)content[0]) << 8;
			content += 1;
		}else if (type == ContentTypeCharListLong){
			len |=  ((int)content[0]) << 8;
			len |=  ((int)content[1]) << 16;
			len |=  ((int)content[2]) << 24;
			content += 3;
		}
        
        std::string tmp = std::string("");
        int tmpLen = 0;
        while (tmpLen<len) {
            char c = content[tmpLen++];
            tmp.append(1,c);
        }
		content += len;
		*out = content;
		*outLen = len;
        ret = Value(tmp);
		break;
	}
	case ContentTypeIntList:		
	{
		int len = content[0];
		len |= ((int)content[1]) << 8;
		content += 2;
		int *ar = new int[len];
		for (int i = 0; i < len; i++)
		{
			ar[i] = content[0];
			ar[i] |= ((int)content[1]) << 8;
			ar[i] |= ((int)content[2]) << 16;
			ar[i] |= ((int)content[3]) << 24;
			content += 4;
		}	
		*out = content;
		*outLen = len;
		ret = ar;
		break;
	}
	case ContentTypeString:		
	{
		int len = content[0];
		len |= ((int)content[1]) << 8;
		content += 2;
		std::string str = std::string("");
		for (int i = 0; i < len; i++)
		{
			char c = content[0];
			c |= ((char)content[1]) << 8;
			str.append(1,c);
			content += 2;
		}
		*out = content;
		*outLen = len;
        ret = Value(str);
		break;
	}
	case ContentTypeStringList:
		break;
	case ContentTypeLong:
	{
		LONG low = (LONG)content[0] |
        ((LONG)content[1] << 8)  |
        ((LONG)content[2] << 16) |
        ((LONG)content[3] << 24) |
        ((LONG)content[4] << 32) |
        ((LONG)content[5] << 40) |
        ((LONG)content[6] << 48) |
        ((LONG)content[7] << 56);
        
		content += 8;
		*out = content;
		*outLen = 8;
        ret = Value((double)low);
		break;
	}
	case ContentTypeDict:		
	case ContentTypeDictLarge:		
	{
		int len = content[0];
		content += 1;
		if (type == ContentTypeDictLarge){
			len |=  ((int)content[0]) << 8;
			content += 1;
		}
		*out = content;
//        ContentType subType = (ContentType)content[0];
        ContentType subType = ContentTypeUnknow;
        if (len > 0) {
            subType = (ContentType)content[0];
        }
        if (subType == ContentTypeString ||
            subType == ContentTypeCharListShort ||
            subType == ContentTypeCharList ||
            subType == ContentTypeCharListLong) {
            ValueMap dict = ValueMap();
            for (int i = 0; i < len; i++){
                Value key = loadContent(out,outLen);                
                Value value = loadContent(out,outLen);
                if (key.asStringValid())
                {
                    std::string strKey = key.asString();
                    dict.insert(ValueMap::value_type(strKey, value));
                }
                else
                {
                    CrashMsgCollector::getInstance()->addMsg("load_content", (int)key.getType());
                }
            }
            ret = Value(dict);
        }else{
            ValueMapIntKey dict = ValueMapIntKey();
            for (int i = 0; i < len; i++){
                Value key = loadContent(out,outLen);
                Value value = loadContent(out,outLen);
                int intKey = key.asInt();
                dict.insert(ValueMapIntKey::value_type(intKey, value));
            }
            ret = Value(dict);
        }
		break;
	}
	default:
		break;
	}
	return ret;
}


std::string DataUtils::md5(void *data, unsigned long size)
{
    MD5 md5 = MD5();
    md5.update((unsigned char *)data, size);
    md5.finalize();
    return md5.hexdigest();
}
