/****************************************************************************
Copyright (c) 2010-2012 cocos2d-x.org
Copyright (c) 2013-2017 Chukong Technologies Inc.

http://www.cocos2d-x.org

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
****************************************************************************/
#include "base/CCUserDefault.h"
#include "platform/CCPlatformConfig.h"
#include "base/ccUtils.h"
#include "platform/CCCommon.h"
#include "base/base64.h"
#include "base/CrashMsgCollector.h"

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
#include "platform/android/jni/JniHelper.h"

// root name of xml
#define USERDEFAULT_ROOT_NAME    "userDefaultRoot"

#include "platform/CCFileUtils.h"

// static const std::string helperClassName = "org/cocos2dx/lib/Cocos2dxHelper";

using namespace std;
NS_CC_BEGIN

/**
 * implements of UserDefault
 */

UserDefault* UserDefault::_userDefault = nullptr;
string UserDefault::_filePath = string("");
bool UserDefault::_isFilePathInitialized = false;



UserDefault::~UserDefault()
{
}

UserDefault::UserDefault()
{
    MMKV::initializeMMKV(FileUtils::getInstance()->getWritablePath() + "mmkv");
    _mmkv = MMKV::defaultMMKV();
}

// FIXME:: deprecated
void UserDefault::purgeSharedUserDefault()
{
    UserDefault::destroyInstance();
}

void UserDefault::destroyInstance()
{
   CC_SAFE_DELETE(_userDefault);
}

bool UserDefault::getBoolForKey(const char* pKey)
{
    // CrashMsgCollector::getInstance()->addMsg("getUserDefault", pKey);
    return getBoolForKey(pKey, false);
}

bool UserDefault::getBoolForKey(const char* pKey, bool defaultValue)
{
    return _mmkv->getBool(pKey, defaultValue);
    // return JniHelper::callStaticBooleanMethod(helperClassName, "getMMKVBoolForKey", pKey, defaultValue);
}

int UserDefault::getIntegerForKey(const char* pKey)
{
    // CrashMsgCollector::getInstance()->addMsg("getUserDefault", pKey);
    return getIntegerForKey(pKey, 0);
}

int UserDefault::getIntegerForKey(const char* pKey, int defaultValue)
{
    return _mmkv->getInt32(pKey, defaultValue);
	// return JniHelper::callStaticIntMethod(helperClassName, "getMMKVIntegerForKey", pKey, defaultValue);
}

float UserDefault::getFloatForKey(const char* pKey)
{
    // CrashMsgCollector::getInstance()->addMsg("getUserDefault", pKey);
    return getFloatForKey(pKey, 0.0f);
}

float UserDefault::getFloatForKey(const char* pKey, float defaultValue)
{
    return _mmkv->getFloat(pKey, defaultValue);
    // return JniHelper::callStaticFloatMethod(helperClassName, "getMMKVFloatForKey", pKey, defaultValue);
}

double  UserDefault::getDoubleForKey(const char* pKey)
{
    // CrashMsgCollector::getInstance()->addMsg("getUserDefault", pKey);
    return getDoubleForKey(pKey, 0.0);
}

double UserDefault::getDoubleForKey(const char* pKey, double defaultValue)
{
    return _mmkv->getDouble(pKey, defaultValue);
	// return JniHelper::callStaticDoubleMethod(helperClassName, "getMMKVDoubleForKey", pKey, defaultValue);
}

std::string UserDefault::getStringForKey(const char* pKey)
{
    // CrashMsgCollector::getInstance()->addMsg("getUserDefault", pKey);
    return getStringForKey(pKey, "");
}

string UserDefault::getStringForKey(const char* pKey, const std::string & defaultValue)
{
    std::string result;
    if (_mmkv->getString(pKey, result))
    {
        return result;
    }
    else
    {
        return defaultValue;
    }
    // return JniHelper::callStaticStringMethod(helperClassName, "getMMKVStringForKey", pKey, defaultValue);
}

Data UserDefault::getDataForKey(const char* pKey)
{
    // CrashMsgCollector::getInstance()->addMsg("getUserDefault", pKey);
    return getDataForKey(pKey, Data::Null);
}

Data UserDefault::getDataForKey(const char* pKey, const Data& defaultValue)
{
    // char * encodedDefaultData = NULL;
    // unsigned int encodedDefaultDataLen = !defaultValue.isNull() ? base64Encode(defaultValue.getBytes(), defaultValue.getSize(), &encodedDefaultData) : 0;

    // string encodedStr = JniHelper::callStaticStringMethod(helperClassName, "getMMKVStringForKey", pKey, (const char*)encodedDefaultData);

    // if (encodedDefaultData)
    //     free(encodedDefaultData);

    // CCLOG("ENCODED STRING: --%s--%d", encodedStr.c_str(), encodedStr.length());

    // unsigned char * decodedData = NULL;
    // int decodedDataLen = base64Decode((unsigned char*)encodedStr.c_str(), (unsigned int)encodedStr.length(), &decodedData);

    // CCLOG("DECODED DATA: %s %d", decodedData, decodedDataLen);

    // if (decodedData && decodedDataLen) {
    //     Data ret;
    //     ret.fastSet(decodedData, decodedDataLen);
    //     return ret;
    // }

    return defaultValue;
}


void UserDefault::setBoolForKey(const char* pKey, bool value)
{
    // CrashMsgCollector::getInstance()->addMsg("setUserDefault", pKey);
    _mmkv->set(value, pKey);
    // JniHelper::callStaticVoidMethod(helperClassName, "setMMKVBoolForKey", pKey, value);
}

void UserDefault::setIntegerForKey(const char* pKey, int value)
{
    // CrashMsgCollector::getInstance()->addMsg("setUserDefault", pKey);
    _mmkv->set(value, pKey);
    // JniHelper::callStaticVoidMethod(helperClassName, "setMMKVIntegerForKey", pKey, value);
}

void UserDefault::setFloatForKey(const char* pKey, float value)
{
    // CrashMsgCollector::getInstance()->addMsg("setUserDefault", pKey);
    _mmkv->set(value, pKey);
    // JniHelper::callStaticVoidMethod(helperClassName, "setMMKVFloatForKey", pKey, value);
}

void UserDefault::setDoubleForKey(const char* pKey, double value)
{
    // CrashMsgCollector::getInstance()->addMsg("setUserDefault", pKey);
    _mmkv->set(value, pKey);
    // JniHelper::callStaticVoidMethod(helperClassName, "setMMKVDoubleForKey", pKey, value);
}

void UserDefault::setStringForKey(const char* pKey, const std::string& value)
{
    // CrashMsgCollector::getInstance()->addMsg("setUserDefault", pKey);
    _mmkv->set(value, pKey);
    // JniHelper::callStaticVoidMethod(helperClassName, "setMMKVStringForKey", pKey, value);
}

void UserDefault::setDataForKey(const char* pKey, const Data& value)
{
    // CrashMsgCollector::getInstance()->addMsg("setUserDefault", pKey);

    // CCLOG("SET DATA FOR KEY: --%s--%d", value.getBytes(), (int)(value.getSize()));
    // char * encodedData = nullptr;
    // unsigned int encodedDataLen = base64Encode(value.getBytes(), value.getSize(), &encodedData);

    // CCLOG("SET DATA ENCODED: --%s", encodedData);

    // JniHelper::callStaticVoidMethod(helperClassName, "setMMKVStringForKey", pKey, (const char*)encodedData);

    // if (encodedData)
    //     free(encodedData);
}

void UserDefault::transSPToMMKV()
{
    // JniHelper::callStaticVoidMethod(helperClassName, "transSPToMMKV");
}

void UserDefault::setUseSP(bool isUseSP)
{
    // JniHelper::callStaticVoidMethod(helperClassName, "setUseSP", isUseSP);
}

bool UserDefault::isSPCanTransMMKV()
{
    // return JniHelper::callStaticBooleanMethod(helperClassName, "isSPCanTransMMKV");
    return false;
}

size_t UserDefault::getMMKVTotalSize()
{
    return _mmkv->totalSize();
}

size_t UserDefault::getMMKVActualSize()
{
    return _mmkv->actualSize();
}

void UserDefault::trimMMKV()
{
    _mmkv->trim();
}

void UserDefault::clearMemoryCacheMMKV()
{
    _mmkv->clearMemoryCache();
}

void UserDefault::clearAllMMKV()
{
    _mmkv->clearAll();
}

// FIXME:: deprecated
UserDefault* UserDefault::sharedUserDefault()
{
    return UserDefault::getInstance();
}

UserDefault* UserDefault::getInstance()
{
    if (! _userDefault)
    {
        _userDefault = new (std::nothrow) UserDefault();
    }

    return _userDefault;
}

bool UserDefault::isXMLFileExist()
{
    return FileUtils::getInstance()->isFileExist(_filePath);
}

void UserDefault::initXMLFilePath()
{

}

// create new xml file
bool UserDefault::createXMLFile()
{
    return false;
}

const string& UserDefault::getXMLFilePath()
{
    return _filePath;
}

void UserDefault::flush()
{
}

void UserDefault::deleteValueForKey(const char* key)
{
    // check the params
    if (!key)
    {
        CCLOG("the key is invalid");
    }
    _mmkv->removeValueForKey(key);
    // JniHelper::callStaticVoidMethod(helperClassName, "deleteValueForKey", key);

    flush();
}
NS_CC_END

#endif // (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
