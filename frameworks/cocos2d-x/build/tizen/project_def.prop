
# Project Name
APPNAME = cocos2dx

# Project Type
type = staticLib

# Project Profile
profile = mobile-2.4

# C.cpp \Sources
USER_SRCS = ../../cocos/2d/CCAction.cpp \
            ../../cocos/2d/CCActionCamera.cpp \
            ../../cocos/2d/CCActionCatmullRom.cpp \
            ../../cocos/2d/CCActionEase.cpp \
            ../../cocos/2d/CCActionGrid.cpp \
            ../../cocos/2d/CCActionGrid3D.cpp \
            ../../cocos/2d/CCActionInstant.cpp \
            ../../cocos/2d/CCActionInterval.cpp \
            ../../cocos/2d/CCActionManager.cpp \
            ../../cocos/2d/CCActionPageTurn3D.cpp \
            ../../cocos/2d/CCActionProgressTimer.cpp \
            ../../cocos/2d/CCActionTiledGrid.cpp \
            ../../cocos/2d/CCActionTween.cpp \
            ../../cocos/2d/CCAnimation.cpp \
            ../../cocos/2d/CCAnimationCache.cpp \
            ../../cocos/2d/CCAtlasNode.cpp \
            ../../cocos/2d/CCAutoPolygon.cpp \
            ../../cocos/2d/CCCamera.cpp \
            ../../cocos/2d/CCCameraBackgroundBrush.cpp \
            ../../cocos/2d/CCClippingNode.cpp \
            ../../cocos/2d/CCClippingRectangleNode.cpp \
            ../../cocos/2d/CCComponent.cpp \
            ../../cocos/2d/CCComponentContainer.cpp \
            ../../cocos/2d/CCDrawNode.cpp \
            ../../cocos/2d/CCDrawingPrimitives.cpp \
            ../../cocos/2d/CCFastTMXLayer.cpp \
            ../../cocos/2d/CCFastTMXTiledMap.cpp \
            ../../cocos/2d/CCFont.cpp \
            ../../cocos/2d/CCFontAtlas.cpp \
            ../../cocos/2d/CCFontAtlasCache.cpp \
            ../../cocos/2d/CCFontCharMap.cpp \
            ../../cocos/2d/CCFontFNT.cpp \
            ../../cocos/2d/CCFontFreeType.cpp \
            ../../cocos/2d/CCGLBufferedNode.cpp \
            ../../cocos/2d/CCGrabber.cpp \
            ../../cocos/2d/CCGrid.cpp \
            ../../cocos/2d/CCLabel.cpp \
            ../../cocos/2d/CCLabelAtlas.cpp \
            ../../cocos/2d/CCLabelBMFont.cpp \
            ../../cocos/2d/CCLabelTTF.cpp \
            ../../cocos/2d/CCLabelTextFormatter.cpp \
            ../../cocos/2d/CCLayer.cpp \
            ../../cocos/2d/CCLight.cpp \
            ../../cocos/2d/CCMenu.cpp \
            ../../cocos/2d/CCMenuItem.cpp \
            ../../cocos/2d/CCMotionStreak.cpp \
            ../../cocos/2d/CCNode.cpp \
            ../../cocos/2d/CCNodeGrid.cpp \
            ../../cocos/2d/CCParallaxNode.cpp \
            ../../cocos/2d/CCParticleBatchNode.cpp \
            ../../cocos/2d/CCParticleExamples.cpp \
            ../../cocos/2d/CCParticleSystem.cpp \
            ../../cocos/2d/CCParticleSystemQuad.cpp \
            ../../cocos/2d/CCProgressTimer.cpp \
            ../../cocos/2d/CCProtectedNode.cpp \
            ../../cocos/2d/CCRenderTexture.cpp \
            ../../cocos/2d/CCScene.cpp \
            ../../cocos/2d/CCSprite.cpp \
            ../../cocos/2d/CCSpriteBatchNode.cpp \
            ../../cocos/2d/CCSpriteFrame.cpp \
            ../../cocos/2d/CCSpriteFrameCache.cpp \
            ../../cocos/2d/CCTMXLayer.cpp \
            ../../cocos/2d/CCTMXObjectGroup.cpp \
            ../../cocos/2d/CCTMXTiledMap.cpp \
            ../../cocos/2d/CCTMXXMLParser.cpp \
            ../../cocos/2d/CCTextFieldTTF.cpp \
            ../../cocos/2d/CCTileMapAtlas.cpp \
            ../../cocos/2d/CCTransition.cpp \
            ../../cocos/2d/CCTransitionPageTurn.cpp \
            ../../cocos/2d/CCTransitionProgress.cpp \
            ../../cocos/2d/CCTweenFunction.cpp \
            ../../cocos/3d/CCAABB.cpp \
            ../../cocos/3d/CCAnimate3D.cpp \
            ../../cocos/3d/CCAnimation3D.cpp \
            ../../cocos/3d/CCAttachNode.cpp \
            ../../cocos/3d/CCBillBoard.cpp \
            ../../cocos/3d/CCBundle3D.cpp \
            ../../cocos/3d/CCBundleReader.cpp \
            ../../cocos/3d/CCFrustum.cpp \
            ../../cocos/3d/CCMesh.cpp \
            ../../cocos/3d/CCMeshSkin.cpp \
            ../../cocos/3d/CCMeshVertexIndexData.cpp \
            ../../cocos/3d/CCMotionStreak3D.cpp \
            ../../cocos/3d/CCOBB.cpp \
            ../../cocos/3d/CCObjLoader.cpp \
            ../../cocos/3d/CCPlane.cpp \
            ../../cocos/3d/CCRay.cpp \
            ../../cocos/3d/CCSkeleton3D.cpp \
            ../../cocos/3d/CCSkybox.cpp \
            ../../cocos/3d/CCSprite3D.cpp \
            ../../cocos/3d/CCSprite3DMaterial.cpp \
            ../../cocos/3d/CCTerrain.cpp \
            ../../cocos/audio/AudioEngine.cpp \
            ../../cocos/audio/tizen/AudioEngine-tizen.cpp \
            ../../cocos/audio/tizen/SimpleAudioEngineTizen.cpp \
            ../../cocos/base/CCAsyncTaskPool.cpp \
            ../../cocos/base/CCAutoreleasePool.cpp \
            ../../cocos/base/CCConfiguration.cpp \
            ../../cocos/base/CCConsole.cpp \
            ../../cocos/base/CCController-android.cpp \
            ../../cocos/base/CCController.cpp \
            ../../cocos/base/CCData.cpp \
            ../../cocos/base/CCDataVisitor.cpp \
            ../../cocos/base/CCDirector.cpp \
            ../../cocos/base/CCEvent.cpp \
            ../../cocos/base/CCEventAcceleration.cpp \
            ../../cocos/base/CCEventController.cpp \
            ../../cocos/base/CCEventCustom.cpp \
            ../../cocos/base/CCEventDispatcher.cpp \
            ../../cocos/base/CCEventFocus.cpp \
            ../../cocos/base/CCEventKeyboard.cpp \
            ../../cocos/base/CCEventListener.cpp \
            ../../cocos/base/CCEventListenerAcceleration.cpp \
            ../../cocos/base/CCEventListenerController.cpp \
            ../../cocos/base/CCEventListenerCustom.cpp \
            ../../cocos/base/CCEventListenerFocus.cpp \
            ../../cocos/base/CCEventListenerKeyboard.cpp \
            ../../cocos/base/CCEventListenerMouse.cpp \
            ../../cocos/base/CCEventListenerTouch.cpp \
            ../../cocos/base/CCEventMouse.cpp \
            ../../cocos/base/CCEventTouch.cpp \
            ../../cocos/base/CCIMEDispatcher.cpp \
            ../../cocos/base/CCNS.cpp \
            ../../cocos/base/CCNinePatchImageParser.cpp \
            ../../cocos/base/CCProfiling.cpp \
            ../../cocos/base/CCProperties.cpp \
            ../../cocos/base/CCRef.cpp \
            ../../cocos/base/CCScheduler.cpp \
            ../../cocos/base/CCScriptSupport.cpp \
            ../../cocos/base/CCStencilStateManager.cpp \
            ../../cocos/base/CCTouch.cpp \
            ../../cocos/base/CCUserDefault-android.cpp \
            ../../cocos/base/CCUserDefault-winrt.cpp \
            ../../cocos/base/CCUserDefault.cpp \
            ../../cocos/base/CCValue.cpp \
            ../../cocos/base/ObjectFactory.cpp \
            ../../cocos/base/TGAlib.cpp \
            ../../cocos/base/ZipUtils.cpp \
            ../../cocos/base/allocator/CCAllocatorDiagnostics.cpp \
            ../../cocos/base/allocator/CCAllocatorGlobal.cpp \
            ../../cocos/base/allocator/CCAllocatorGlobalNewDelete.cpp \
            ../../cocos/base/atitc.cpp \
            ../../cocos/base/base64.cpp \
            ../../cocos/base/ccCArray.cpp \
            ../../cocos/base/ccFPSImages.c \
            ../../cocos/base/ccRandom.cpp \
            ../../cocos/base/ccTypes.cpp \
            ../../cocos/base/ccUTF8.cpp \
            ../../cocos/base/ccUtils.cpp \
            ../../cocos/base/etc1.cpp \
            ../../cocos/base/pvr.cpp \
            ../../cocos/base/s3tc.cpp \
            ../../cocos/cc_dummy.c \
            ../../cocos/cocos2d.cpp \
            ../../cocos/deprecated/CCArray.cpp \
            ../../cocos/deprecated/CCDeprecated.cpp \
            ../../cocos/deprecated/CCDictionary.cpp \
            ../../cocos/deprecated/CCNotificationCenter.cpp \
            ../../cocos/deprecated/CCSet.cpp \
            ../../cocos/deprecated/CCString.cpp \
            ../../cocos/editor-support/cocosbuilder/CCBAnimationManager.cpp \
            ../../cocos/editor-support/cocosbuilder/CCBFileLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCBKeyframe.cpp \
            ../../cocos/editor-support/cocosbuilder/CCBReader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCBSequence.cpp \
            ../../cocos/editor-support/cocosbuilder/CCBSequenceProperty.cpp \
            ../../cocos/editor-support/cocosbuilder/CCControlButtonLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCControlLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCLabelBMFontLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCLabelTTFLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCLayerColorLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCLayerGradientLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCLayerLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCMenuItemImageLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCMenuItemLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCNode+CCBRelativePositioning.cpp \
            ../../cocos/editor-support/cocosbuilder/CCNodeLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCNodeLoaderLibrary.cpp \
            ../../cocos/editor-support/cocosbuilder/CCParticleSystemQuadLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCScale9SpriteLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCScrollViewLoader.cpp \
            ../../cocos/editor-support/cocosbuilder/CCSpriteLoader.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCActionTimeline.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCActionTimelineCache.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCActionTimelineNode.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCBoneNode.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCFrame.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCSkeletonNode.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCSkinNode.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CCTimeLine.cpp \
            ../../cocos/editor-support/cocostudio/ActionTimeline/CSLoader.cpp \
            ../../cocos/editor-support/cocostudio/CCActionFrame.cpp \
            ../../cocos/editor-support/cocostudio/CCActionFrameEasing.cpp \
            ../../cocos/editor-support/cocostudio/CCActionManagerEx.cpp \
            ../../cocos/editor-support/cocostudio/CCActionNode.cpp \
            ../../cocos/editor-support/cocostudio/CCActionObject.cpp \
            ../../cocos/editor-support/cocostudio/CCArmature.cpp \
            ../../cocos/editor-support/cocostudio/CCArmatureAnimation.cpp \
            ../../cocos/editor-support/cocostudio/CCArmatureDataManager.cpp \
            ../../cocos/editor-support/cocostudio/CCArmatureDefine.cpp \
            ../../cocos/editor-support/cocostudio/CCBatchNode.cpp \
            ../../cocos/editor-support/cocostudio/CCBone.cpp \
            ../../cocos/editor-support/cocostudio/CCColliderDetector.cpp \
            ../../cocos/editor-support/cocostudio/CCComAttribute.cpp \
            ../../cocos/editor-support/cocostudio/CCComAudio.cpp \
            ../../cocos/editor-support/cocostudio/CCComController.cpp \
            ../../cocos/editor-support/cocostudio/CCComExtensionData.cpp \
            ../../cocos/editor-support/cocostudio/CCComRender.cpp \
            ../../cocos/editor-support/cocostudio/CCDataReaderHelper.cpp \
            ../../cocos/editor-support/cocostudio/CCDatas.cpp \
            ../../cocos/editor-support/cocostudio/CCDecorativeDisplay.cpp \
            ../../cocos/editor-support/cocostudio/CCDisplayFactory.cpp \
            ../../cocos/editor-support/cocostudio/CCDisplayManager.cpp \
            ../../cocos/editor-support/cocostudio/CCInputDelegate.cpp \
            ../../cocos/editor-support/cocostudio/CCProcessBase.cpp \
            ../../cocos/editor-support/cocostudio/CCSGUIReader.cpp \
            ../../cocos/editor-support/cocostudio/CCSSceneReader.cpp \
            ../../cocos/editor-support/cocostudio/CCSkin.cpp \
            ../../cocos/editor-support/cocostudio/CCSpriteFrameCacheHelper.cpp \
            ../../cocos/editor-support/cocostudio/CCTransformHelp.cpp \
            ../../cocos/editor-support/cocostudio/CCTween.cpp \
            ../../cocos/editor-support/cocostudio/CCUtilMath.cpp \
            ../../cocos/editor-support/cocostudio/CocoLoader.cpp \
            ../../cocos/editor-support/cocostudio/CocoStudio.cpp \
            ../../cocos/editor-support/cocostudio/CocosStudioExtension.cpp \
            ../../cocos/editor-support/cocostudio/DictionaryHelper.cpp \
            ../../cocos/editor-support/cocostudio/FlatBuffersSerialize.cpp \
            ../../cocos/editor-support/cocostudio/LocalizationManager.cpp \
            ../../cocos/editor-support/cocostudio/TriggerBase.cpp \
            ../../cocos/editor-support/cocostudio/TriggerMng.cpp \
            ../../cocos/editor-support/cocostudio/TriggerObj.cpp \
            ../../cocos/editor-support/cocostudio/WidgetCallBackHandlerProtocol.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ArmatureNodeReader/ArmatureNodeReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ButtonReader/ButtonReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/CheckBoxReader/CheckBoxReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ComAudioReader/ComAudioReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/GameMapReader/GameMapReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/GameNode3DReader/GameNode3DReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ImageViewReader/ImageViewReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/LayoutReader/LayoutReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/Light3DReader/Light3DReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ListViewReader/ListViewReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/LoadingBarReader/LoadingBarReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/Node3DReader/Node3DReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/NodeReader/NodeReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/NodeReaderDefine.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/NodeReaderProtocol.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/PageViewReader/PageViewReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/Particle3DReader/Particle3DReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ParticleReader/ParticleReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ProjectNodeReader/ProjectNodeReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/ScrollViewReader/ScrollViewReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/SingleNodeReader/SingleNodeReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/SkeletonReader/BoneNodeReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/SkeletonReader/SkeletonNodeReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/SliderReader/SliderReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/Sprite3DReader/Sprite3DReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/SpriteReader/SpriteReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/TabControlReader/TabControlReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/TextAtlasReader/TextAtlasReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/TextBMFontReader/TextBMFontReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/TextFieldReader/TextFieldReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/TextReader/TextReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/UserCameraReader/UserCameraReader.cpp \
            ../../cocos/editor-support/cocostudio/WidgetReader/WidgetReader.cpp \
            ../../cocos/editor-support/spine/Animation.c \
            ../../cocos/editor-support/spine/AnimationState.c \
            ../../cocos/editor-support/spine/AnimationStateData.c \
            ../../cocos/editor-support/spine/Atlas.c \
            ../../cocos/editor-support/spine/AtlasAttachmentLoader.c \
            ../../cocos/editor-support/spine/Attachment.c \
            ../../cocos/editor-support/spine/AttachmentLoader.c \
            ../../cocos/editor-support/spine/AttachmentVertices.cpp \
            ../../cocos/editor-support/spine/Bone.c \
            ../../cocos/editor-support/spine/BoneData.c \
            ../../cocos/editor-support/spine/BoundingBoxAttachment.c \
            ../../cocos/editor-support/spine/Cocos2dAttachmentLoader.cpp \
            ../../cocos/editor-support/spine/Event.c \
            ../../cocos/editor-support/spine/EventData.c \
            ../../cocos/editor-support/spine/IkConstraint.c \
            ../../cocos/editor-support/spine/IkConstraintData.c \
            ../../cocos/editor-support/spine/Json.c \
            ../../cocos/editor-support/spine/MeshAttachment.c \
            ../../cocos/editor-support/spine/PathAttachment.c \
            ../../cocos/editor-support/spine/PathConstraint.c \
            ../../cocos/editor-support/spine/PathConstraintData.c \
            ../../cocos/editor-support/spine/RegionAttachment.c \
            ../../cocos/editor-support/spine/Skeleton.c \
            ../../cocos/editor-support/spine/SkeletonAnimation.cpp \
            ../../cocos/editor-support/spine/SkeletonBatch.cpp \
            ../../cocos/editor-support/spine/SkeletonBinary.c \
            ../../cocos/editor-support/spine/SkeletonBounds.c \
            ../../cocos/editor-support/spine/SkeletonData.c \
            ../../cocos/editor-support/spine/SkeletonJson.c \
            ../../cocos/editor-support/spine/SkeletonRenderer.cpp \
            ../../cocos/editor-support/spine/Skin.c \
            ../../cocos/editor-support/spine/Slot.c \
            ../../cocos/editor-support/spine/SlotData.c \
            ../../cocos/editor-support/spine/TransformConstraint.c \
            ../../cocos/editor-support/spine/TransformConstraintData.c \
            ../../cocos/editor-support/spine/VertexAttachment.c \
            ../../cocos/editor-support/spine/extension.c \
            ../../cocos/editor-support/spine/spine-cocos2dx.cpp \
            ../../cocos/math/CCAffineTransform.cpp \
            ../../cocos/math/CCGeometry.cpp \
            ../../cocos/math/CCVertex.cpp \
            ../../cocos/math/Mat4.cpp \
            ../../cocos/math/MathUtil.cpp \
            ../../cocos/math/Quaternion.cpp \
            ../../cocos/math/TransformUtils.cpp \
            ../../cocos/math/Vec2.cpp \
            ../../cocos/math/Vec3.cpp \
            ../../cocos/math/Vec4.cpp \
            ../../cocos/navmesh/CCNavMesh.cpp \
            ../../cocos/navmesh/CCNavMeshAgent.cpp \
            ../../cocos/navmesh/CCNavMeshDebugDraw.cpp \
            ../../cocos/navmesh/CCNavMeshObstacle.cpp \
            ../../cocos/navmesh/CCNavMeshUtils.cpp \
            ../../cocos/network/CCDownloader-curl.cpp \
            ../../cocos/network/CCDownloader.cpp \
            ../../cocos/network/HttpClient.cpp \
            ../../cocos/network/HttpCookie.cpp \
            ../../cocos/network/SocketIO.cpp \
            ../../cocos/network/Uri.cpp \
            ../../cocos/network/WebSocket.cpp \
            ../../cocos/physics/CCPhysicsBody.cpp \
            ../../cocos/physics/CCPhysicsContact.cpp \
            ../../cocos/physics/CCPhysicsJoint.cpp \
            ../../cocos/physics/CCPhysicsShape.cpp \
            ../../cocos/physics/CCPhysicsWorld.cpp \
            ../../cocos/physics3d/CCPhysics3D.cpp \
            ../../cocos/physics3d/CCPhysics3DComponent.cpp \
            ../../cocos/physics3d/CCPhysics3DConstraint.cpp \
            ../../cocos/physics3d/CCPhysics3DDebugDrawer.cpp \
            ../../cocos/physics3d/CCPhysics3DObject.cpp \
            ../../cocos/physics3d/CCPhysics3DShape.cpp \
            ../../cocos/physics3d/CCPhysics3DWorld.cpp \
            ../../cocos/physics3d/CCPhysicsSprite3D.cpp \
            ../../cocos/platform/CCFileUtils.cpp \
            ../../cocos/platform/CCGLView.cpp \
            ../../cocos/platform/CCImage.cpp \
            ../../cocos/platform/CCSAXParser.cpp \
            ../../cocos/platform/CCThread.cpp \
            ../../cocos/platform/tizen/CCApplication-tizen.cpp \
            ../../cocos/platform/tizen/CCCommon-tizen.cpp \
            ../../cocos/platform/tizen/CCDevice-tizen.cpp \
            ../../cocos/platform/tizen/CCFileUtils-tizen.cpp \
            ../../cocos/platform/tizen/CCGLViewImpl-tizen.cpp \
            ../../cocos/platform/tizen/CCStdC-tizen.cpp \
            ../../cocos/renderer/CCBatchCommand.cpp \
            ../../cocos/renderer/CCCustomCommand.cpp \
            ../../cocos/renderer/CCFrameBuffer.cpp \
            ../../cocos/renderer/CCGLProgram.cpp \
            ../../cocos/renderer/CCGLProgramCache.cpp \
            ../../cocos/renderer/CCGLProgramState.cpp \
            ../../cocos/renderer/CCGLProgramStateCache.cpp \
            ../../cocos/renderer/CCGroupCommand.cpp \
            ../../cocos/renderer/CCMaterial.cpp \
            ../../cocos/renderer/CCMeshCommand.cpp \
            ../../cocos/renderer/CCPass.cpp \
            ../../cocos/renderer/CCPrimitive.cpp \
            ../../cocos/renderer/CCPrimitiveCommand.cpp \
            ../../cocos/renderer/CCQuadCommand.cpp \
            ../../cocos/renderer/CCRenderCommand.cpp \
            ../../cocos/renderer/CCRenderState.cpp \
            ../../cocos/renderer/CCRenderer.cpp \
            ../../cocos/renderer/CCTechnique.cpp \
            ../../cocos/renderer/CCTexture2D.cpp \
            ../../cocos/renderer/CCTextureAtlas.cpp \
            ../../cocos/renderer/CCTextureCache.cpp \
            ../../cocos/renderer/CCTextureCube.cpp \
            ../../cocos/renderer/CCTrianglesCommand.cpp \
            ../../cocos/renderer/CCVertexAttribBinding.cpp \
            ../../cocos/renderer/CCVertexIndexBuffer.cpp \
            ../../cocos/renderer/CCVertexIndexData.cpp \
            ../../cocos/renderer/ccGLStateCache.cpp \
            ../../cocos/renderer/ccShaders.cpp \
            ../../cocos/storage/local-storage/LocalStorage-android.cpp \
            ../../cocos/storage/local-storage/LocalStorage.cpp \
            ../../cocos/ui/CocosGUI.cpp \
            ../../cocos/ui/UIAbstractCheckButton.cpp \
            ../../cocos/ui/UIButton.cpp \
            ../../cocos/ui/UICheckBox.cpp \
            ../../cocos/ui/UIDeprecated.cpp \
            ../../cocos/ui/UIEditBox/UIEditBox.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-android.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-common.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-linux.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-stub.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-tizen.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-win32.cpp \
            ../../cocos/ui/UIEditBox/UIEditBoxImpl-winrt.cpp \
            ../../cocos/ui/UIHBox.cpp \
            ../../cocos/ui/UIHelper.cpp \
            ../../cocos/ui/UIImageView.cpp \
            ../../cocos/ui/UILayout.cpp \
            ../../cocos/ui/UILayoutComponent.cpp \
            ../../cocos/ui/UILayoutManager.cpp \
            ../../cocos/ui/UILayoutParameter.cpp \
            ../../cocos/ui/UIListView.cpp \
            ../../cocos/ui/UILoadingBar.cpp \
            ../../cocos/ui/UIPageView.cpp \
            ../../cocos/ui/UIPageViewIndicator.cpp \
            ../../cocos/ui/UIRadioButton.cpp \
            ../../cocos/ui/UIRelativeBox.cpp \
            ../../cocos/ui/UIRichText.cpp \
            ../../cocos/ui/UIScale9Sprite.cpp \
            ../../cocos/ui/UIScrollView.cpp \
            ../../cocos/ui/UIScrollViewBar.cpp \
            ../../cocos/ui/UISlider.cpp \
            ../../cocos/ui/UITabControl.cpp \
            ../../cocos/ui/UIText.cpp \
            ../../cocos/ui/UITextAtlas.cpp \
            ../../cocos/ui/UITextBMFont.cpp \
            ../../cocos/ui/UITextField.cpp \
            ../../cocos/ui/UIVBox.cpp \
            ../../cocos/ui/UIVideoPlayer-android.cpp \
            ../../cocos/ui/UIVideoPlayer-tizen.cpp \
            ../../cocos/ui/UIWebView.cpp \
            ../../cocos/ui/UIWebViewImpl-android.cpp \
            ../../cocos/ui/UIWebViewImpl-tizen.cpp \
            ../../cocos/ui/UIWidget.cpp \
            ../../cocos/vr/CCVRDistortion.cpp \
            ../../cocos/vr/CCVRDistortionMesh.cpp \
            ../../cocos/vr/CCVRGenericHeadTracker.cpp \
            ../../cocos/vr/CCVRGenericRenderer.cpp \
            ../../extensions/ExtensionDeprecated.cpp \
            ../../extensions/GUI/CCControlExtension/CCControl.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlButton.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlColourPicker.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlHuePicker.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlPotentiometer.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlSaturationBrightnessPicker.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlSlider.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlStepper.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlSwitch.cpp \
            ../../extensions/GUI/CCControlExtension/CCControlUtils.cpp \
            ../../extensions/GUI/CCControlExtension/CCInvocation.cpp \
            ../../extensions/GUI/CCScrollView/CCScrollView.cpp \
            ../../extensions/GUI/CCScrollView/CCTableView.cpp \
            ../../extensions/GUI/CCScrollView/CCTableViewCell.cpp \
            ../../extensions/Particle3D/CCParticle3DAffector.cpp \
            ../../extensions/Particle3D/CCParticle3DEmitter.cpp \
            ../../extensions/Particle3D/CCParticle3DRender.cpp \
            ../../extensions/Particle3D/CCParticleSystem3D.cpp \
            ../../extensions/Particle3D/PU/CCPUAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUAffectorManager.cpp \
            ../../extensions/Particle3D/PU/CCPUAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUAlignAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUAlignAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUBaseCollider.cpp \
            ../../extensions/Particle3D/PU/CCPUBaseColliderTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUBaseForceAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUBaseForceAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUBeamRender.cpp \
            ../../extensions/Particle3D/PU/CCPUBehaviour.cpp \
            ../../extensions/Particle3D/PU/CCPUBehaviourManager.cpp \
            ../../extensions/Particle3D/PU/CCPUBehaviourTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUBillboardChain.cpp \
            ../../extensions/Particle3D/PU/CCPUBoxCollider.cpp \
            ../../extensions/Particle3D/PU/CCPUBoxColliderTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUBoxEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUBoxEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUCircleEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUCircleEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUCollisionAvoidanceAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUCollisionAvoidanceAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUColorAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUColorAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoAffectorEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoAffectorEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoEnableComponentEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoEnableComponentEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoExpireEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoExpireEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoFreezeEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoFreezeEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoPlacementParticleEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoPlacementParticleEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoScaleEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoScaleEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDoStopSystemEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUDoStopSystemEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUDynamicAttribute.cpp \
            ../../extensions/Particle3D/PU/CCPUDynamicAttributeTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUEmitterManager.cpp \
            ../../extensions/Particle3D/PU/CCPUEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUEventHandler.cpp \
            ../../extensions/Particle3D/PU/CCPUEventHandlerManager.cpp \
            ../../extensions/Particle3D/PU/CCPUEventHandlerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUFlockCenteringAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUFlockCenteringAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUForceField.cpp \
            ../../extensions/Particle3D/PU/CCPUForceFieldAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUForceFieldAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUGeometryRotator.cpp \
            ../../extensions/Particle3D/PU/CCPUGeometryRotatorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUGravityAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUGravityAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUInterParticleCollider.cpp \
            ../../extensions/Particle3D/PU/CCPUInterParticleColliderTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUJetAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUJetAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPULineAffector.cpp \
            ../../extensions/Particle3D/PU/CCPULineAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPULineEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPULineEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPULinearForceAffector.cpp \
            ../../extensions/Particle3D/PU/CCPULinearForceAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUListener.cpp \
            ../../extensions/Particle3D/PU/CCPUMaterialManager.cpp \
            ../../extensions/Particle3D/PU/CCPUMaterialTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUMeshSurfaceEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUMeshSurfaceEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUNoise.cpp \
            ../../extensions/Particle3D/PU/CCPUObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUObserverManager.cpp \
            ../../extensions/Particle3D/PU/CCPUObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnClearObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnClearObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnCollisionObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnCollisionObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnCountObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnCountObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnEmissionObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnEmissionObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnEventFlagObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnEventFlagObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnExpireObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnExpireObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnPositionObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnPositionObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnQuotaObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnQuotaObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnRandomObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnRandomObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnTimeObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnTimeObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUOnVelocityObserver.cpp \
            ../../extensions/Particle3D/PU/CCPUOnVelocityObserverTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUParticleFollower.cpp \
            ../../extensions/Particle3D/PU/CCPUParticleFollowerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUParticleSystem3D.cpp \
            ../../extensions/Particle3D/PU/CCPUParticleSystem3DTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUPathFollower.cpp \
            ../../extensions/Particle3D/PU/CCPUPathFollowerTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUPlane.cpp \
            ../../extensions/Particle3D/PU/CCPUPlaneCollider.cpp \
            ../../extensions/Particle3D/PU/CCPUPlaneColliderTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUPointEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUPointEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUPositionEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUPositionEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPURandomiser.cpp \
            ../../extensions/Particle3D/PU/CCPURandomiserTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPURender.cpp \
            ../../extensions/Particle3D/PU/CCPURendererTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPURibbonTrail.cpp \
            ../../extensions/Particle3D/PU/CCPURibbonTrailRender.cpp \
            ../../extensions/Particle3D/PU/CCPUScaleAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUScaleAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUScaleVelocityAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUScaleVelocityAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUScriptCompiler.cpp \
            ../../extensions/Particle3D/PU/CCPUScriptLexer.cpp \
            ../../extensions/Particle3D/PU/CCPUScriptParser.cpp \
            ../../extensions/Particle3D/PU/CCPUScriptTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUSimpleSpline.cpp \
            ../../extensions/Particle3D/PU/CCPUSineForceAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUSineForceAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUSlaveBehaviour.cpp \
            ../../extensions/Particle3D/PU/CCPUSlaveBehaviourTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUSlaveEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUSlaveEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUSphere.cpp \
            ../../extensions/Particle3D/PU/CCPUSphereCollider.cpp \
            ../../extensions/Particle3D/PU/CCPUSphereColliderTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUSphereSurfaceEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUSphereSurfaceEmitterTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUTechniqueTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUTextureAnimator.cpp \
            ../../extensions/Particle3D/PU/CCPUTextureAnimatorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUTextureRotator.cpp \
            ../../extensions/Particle3D/PU/CCPUTextureRotatorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUTranslateManager.cpp \
            ../../extensions/Particle3D/PU/CCPUUtil.cpp \
            ../../extensions/Particle3D/PU/CCPUVelocityMatchingAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUVelocityMatchingAffectorTranslator.cpp \
            ../../extensions/Particle3D/PU/CCPUVertexEmitter.cpp \
            ../../extensions/Particle3D/PU/CCPUVortexAffector.cpp \
            ../../extensions/Particle3D/PU/CCPUVortexAffectorTranslator.cpp \
            ../../extensions/assets-manager/AssetsManager.cpp \
            ../../extensions/assets-manager/AssetsManagerEx.cpp \
            ../../extensions/assets-manager/CCEventAssetsManagerEx.cpp \
            ../../extensions/assets-manager/CCEventListenerAssetsManagerEx.cpp \
            ../../extensions/assets-manager/Manifest.cpp \
            ../../extensions/physics-nodes/CCPhysicsDebugNode.cpp \
            ../../extensions/physics-nodes/CCPhysicsSprite.cpp \
            ../../external/Box2D/Collision/Shapes/b2ChainShape.cpp \
            ../../external/Box2D/Collision/Shapes/b2CircleShape.cpp \
            ../../external/Box2D/Collision/Shapes/b2EdgeShape.cpp \
            ../../external/Box2D/Collision/Shapes/b2PolygonShape.cpp \
            ../../external/Box2D/Collision/b2BroadPhase.cpp \
            ../../external/Box2D/Collision/b2CollideCircle.cpp \
            ../../external/Box2D/Collision/b2CollideEdge.cpp \
            ../../external/Box2D/Collision/b2CollidePolygon.cpp \
            ../../external/Box2D/Collision/b2Collision.cpp \
            ../../external/Box2D/Collision/b2Distance.cpp \
            ../../external/Box2D/Collision/b2DynamicTree.cpp \
            ../../external/Box2D/Collision/b2TimeOfImpact.cpp \
            ../../external/Box2D/Common/b2BlockAllocator.cpp \
            ../../external/Box2D/Common/b2Draw.cpp \
            ../../external/Box2D/Common/b2Math.cpp \
            ../../external/Box2D/Common/b2Settings.cpp \
            ../../external/Box2D/Common/b2StackAllocator.cpp \
            ../../external/Box2D/Common/b2Timer.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2ChainAndCircleContact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2ChainAndPolygonContact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2CircleContact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2Contact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2ContactSolver.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2EdgeAndCircleContact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2EdgeAndPolygonContact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2PolygonAndCircleContact.cpp \
            ../../external/Box2D/Dynamics/Contacts/b2PolygonContact.cpp \
            ../../external/Box2D/Dynamics/Joints/b2DistanceJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2FrictionJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2GearJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2Joint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2MotorJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2MouseJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2PrismaticJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2PulleyJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2RevoluteJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2RopeJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2WeldJoint.cpp \
            ../../external/Box2D/Dynamics/Joints/b2WheelJoint.cpp \
            ../../external/Box2D/Dynamics/b2Body.cpp \
            ../../external/Box2D/Dynamics/b2ContactManager.cpp \
            ../../external/Box2D/Dynamics/b2Fixture.cpp \
            ../../external/Box2D/Dynamics/b2Island.cpp \
            ../../external/Box2D/Dynamics/b2World.cpp \
            ../../external/Box2D/Dynamics/b2WorldCallbacks.cpp \
            ../../external/Box2D/Rope/b2Rope.cpp \
            ../../external/ConvertUTF/ConvertUTF.c \
            ../../external/ConvertUTF/ConvertUTFWrapper.cpp \
            ../../external/md5/md5.c \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btAxisSweep3.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btBroadphaseProxy.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btDbvt.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btDbvtBroadphase.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btDispatcher.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btMultiSapBroadphase.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btOverlappingPairCache.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btQuantizedBvh.cpp \
            ../../external/bullet/BulletCollision/BroadphaseCollision/btSimpleBroadphase.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/SphereTriangleDetector.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btActivatingCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btBox2dBox2dCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btBoxBoxCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btBoxBoxDetector.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btCollisionDispatcher.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btCollisionObject.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btCollisionWorld.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btCompoundCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btCompoundCompoundCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btConvex2dConvex2dAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btConvexConcaveCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btConvexConvexAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btConvexPlaneCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btDefaultCollisionConfiguration.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btEmptyCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btGhostObject.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btHashedSimplePairCache.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btInternalEdgeUtility.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btManifoldResult.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btSimulationIslandManager.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btSphereBoxCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btSphereSphereCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btSphereTriangleCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/CollisionDispatch/btUnionFind.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btBox2dShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btBoxShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btBvhTriangleMeshShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btCapsuleShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btCollisionShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btCompoundShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConcaveShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConeShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvex2dShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvexHullShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvexInternalShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvexPointCloudShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvexPolyhedron.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvexShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btConvexTriangleMeshShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btCylinderShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btEmptyShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btHeightfieldTerrainShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btMinkowskiSumShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btMultiSphereShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btMultimaterialTriangleMeshShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btOptimizedBvh.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btPolyhedralConvexShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btScaledBvhTriangleMeshShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btShapeHull.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btSphereShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btStaticPlaneShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btStridingMeshInterface.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTetrahedronShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTriangleBuffer.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTriangleCallback.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTriangleIndexVertexArray.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTriangleIndexVertexMaterialArray.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTriangleMesh.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btTriangleMeshShape.cpp \
            ../../external/bullet/BulletCollision/CollisionShapes/btUniformScalingShape.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btContactProcessing.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btGImpactBvh.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btGImpactCollisionAlgorithm.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btGImpactQuantizedBvh.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btGImpactShape.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btGenericPoolAllocator.cpp \
            ../../external/bullet/BulletCollision/Gimpact/btTriangleShapeEx.cpp \
            ../../external/bullet/BulletCollision/Gimpact/gim_box_set.cpp \
            ../../external/bullet/BulletCollision/Gimpact/gim_contact.cpp \
            ../../external/bullet/BulletCollision/Gimpact/gim_memory.cpp \
            ../../external/bullet/BulletCollision/Gimpact/gim_tri_collision.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btContinuousConvexCollision.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btConvexCast.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btGjkConvexCast.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btGjkEpa2.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btGjkEpaPenetrationDepthSolver.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btGjkPairDetector.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btMinkowskiPenetrationDepthSolver.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btPersistentManifold.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btPolyhedralContactClipping.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btRaycastCallback.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btSubSimplexConvexCast.cpp \
            ../../external/bullet/BulletCollision/NarrowPhaseCollision/btVoronoiSimplexSolver.cpp \
            ../../external/bullet/BulletDynamics/Character/btKinematicCharacterController.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btConeTwistConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btContactConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btFixedConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btGearConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btGeneric6DofConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btGeneric6DofSpringConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btHinge2Constraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btHingeConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btPoint2PointConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btSequentialImpulseConstraintSolver.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btSliderConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btSolve2LinearConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btTypedConstraint.cpp \
            ../../external/bullet/BulletDynamics/ConstraintSolver/btUniversalConstraint.cpp \
            ../../external/bullet/BulletDynamics/Dynamics/Bullet-C-API.cpp \
            ../../external/bullet/BulletDynamics/Dynamics/btDiscreteDynamicsWorld.cpp \
            ../../external/bullet/BulletDynamics/Dynamics/btRigidBody.cpp \
            ../../external/bullet/BulletDynamics/Dynamics/btSimpleDynamicsWorld.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBody.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBodyConstraint.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBodyConstraintSolver.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBodyDynamicsWorld.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBodyJointLimitConstraint.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBodyJointMotor.cpp \
            ../../external/bullet/BulletDynamics/Featherstone/btMultiBodyPoint2Point.cpp \
            ../../external/bullet/BulletDynamics/MLCPSolvers/btDantzigLCP.cpp \
            ../../external/bullet/BulletDynamics/MLCPSolvers/btMLCPSolver.cpp \
            ../../external/bullet/BulletDynamics/Vehicle/btRaycastVehicle.cpp \
            ../../external/bullet/BulletDynamics/Vehicle/btWheelInfo.cpp \
            ../../external/bullet/BulletMultiThreaded/PosixThreadSupport.cpp \
            ../../external/bullet/BulletMultiThreaded/SequentialThreadSupport.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuCollisionObjectWrapper.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuCollisionTaskProcess.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuContactManifoldCollisionAlgorithm.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuFakeDma.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuGatheringCollisionDispatcher.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuLibspe2Support.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuNarrowPhaseCollisionTask/SpuCollisionShapes.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuNarrowPhaseCollisionTask/SpuContactResult.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuNarrowPhaseCollisionTask/SpuGatheringCollisionTask.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuNarrowPhaseCollisionTask/SpuMinkowskiPenetrationDepthSolver.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuNarrowPhaseCollisionTask/boxBoxDistance.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuSampleTask/SpuSampleTask.cpp \
            ../../external/bullet/BulletMultiThreaded/SpuSampleTaskProcess.cpp \
            ../../external/bullet/BulletMultiThreaded/Win32ThreadSupport.cpp \
            ../../external/bullet/BulletMultiThreaded/btGpu3DGridBroadphase.cpp \
            ../../external/bullet/BulletMultiThreaded/btParallelConstraintSolver.cpp \
            ../../external/bullet/BulletMultiThreaded/btThreadSupportInterface.cpp \
            ../../external/bullet/BulletSoftBody/btDefaultSoftBodySolver.cpp \
            ../../external/bullet/BulletSoftBody/btSoftBody.cpp \
            ../../external/bullet/BulletSoftBody/btSoftBodyConcaveCollisionAlgorithm.cpp \
            ../../external/bullet/BulletSoftBody/btSoftBodyHelpers.cpp \
            ../../external/bullet/BulletSoftBody/btSoftBodyRigidBodyCollisionConfiguration.cpp \
            ../../external/bullet/BulletSoftBody/btSoftRigidCollisionAlgorithm.cpp \
            ../../external/bullet/BulletSoftBody/btSoftRigidDynamicsWorld.cpp \
            ../../external/bullet/BulletSoftBody/btSoftSoftCollisionAlgorithm.cpp \
            ../../external/bullet/LinearMath/btAlignedAllocator.cpp \
            ../../external/bullet/LinearMath/btConvexHull.cpp \
            ../../external/bullet/LinearMath/btConvexHullComputer.cpp \
            ../../external/bullet/LinearMath/btGeometryUtil.cpp \
            ../../external/bullet/LinearMath/btPolarDecomposition.cpp \
            ../../external/bullet/LinearMath/btQuickprof.cpp \
            ../../external/bullet/LinearMath/btSerializer.cpp \
            ../../external/bullet/LinearMath/btVector3.cpp \
            ../../external/bullet/MiniCL/MiniCL.cpp \
            ../../external/bullet/MiniCL/MiniCLTask/MiniCLTask.cpp \
            ../../external/bullet/MiniCL/MiniCLTaskScheduler.cpp \
            ../../external/clipper/clipper.cpp \
            ../../external/edtaa3func/edtaa3func.cpp \
            ../../external/flatbuffers/flatc.cpp \
            ../../external/flatbuffers/idl_gen_cpp.cpp \
            ../../external/flatbuffers/idl_gen_fbs.cpp \
            ../../external/flatbuffers/idl_gen_general.cpp \
            ../../external/flatbuffers/idl_gen_go.cpp \
            ../../external/flatbuffers/idl_gen_text.cpp \
            ../../external/flatbuffers/idl_parser.cpp \
            ../../external/poly2tri/common/shapes.cc \
            ../../external/poly2tri/sweep/advancing_front.cc \
            ../../external/poly2tri/sweep/cdt.cc \
            ../../external/poly2tri/sweep/sweep.cc \
            ../../external/poly2tri/sweep/sweep_context.cc \
            ../../external/recast/DebugUtils/DebugDraw.cpp \
            ../../external/recast/DebugUtils/DetourDebugDraw.cpp \
            ../../external/recast/DebugUtils/RecastDebugDraw.cpp \
            ../../external/recast/DebugUtils/RecastDump.cpp \
            ../../external/recast/Detour/DetourAlloc.cpp \
            ../../external/recast/Detour/DetourCommon.cpp \
            ../../external/recast/Detour/DetourNavMesh.cpp \
            ../../external/recast/Detour/DetourNavMeshBuilder.cpp \
            ../../external/recast/Detour/DetourNavMeshQuery.cpp \
            ../../external/recast/Detour/DetourNode.cpp \
            ../../external/recast/DetourCrowd/DetourCrowd.cpp \
            ../../external/recast/DetourCrowd/DetourLocalBoundary.cpp \
            ../../external/recast/DetourCrowd/DetourObstacleAvoidance.cpp \
            ../../external/recast/DetourCrowd/DetourPathCorridor.cpp \
            ../../external/recast/DetourCrowd/DetourPathQueue.cpp \
            ../../external/recast/DetourCrowd/DetourProximityGrid.cpp \
            ../../external/recast/DetourTileCache/DetourTileCache.cpp \
            ../../external/recast/DetourTileCache/DetourTileCacheBuilder.cpp \
            ../../external/recast/Recast/Recast.cpp \
            ../../external/recast/Recast/RecastAlloc.cpp \
            ../../external/recast/Recast/RecastArea.cpp \
            ../../external/recast/Recast/RecastContour.cpp \
            ../../external/recast/Recast/RecastFilter.cpp \
            ../../external/recast/Recast/RecastLayers.cpp \
            ../../external/recast/Recast/RecastMesh.cpp \
            ../../external/recast/Recast/RecastMeshDetail.cpp \
            ../../external/recast/Recast/RecastRasterization.cpp \
            ../../external/recast/Recast/RecastRegion.cpp \
            ../../external/recast/fastlz/fastlz.c \
            ../../external/tinyxml2/tinyxml2.cpp \
            ../../external/unzip/ioapi.cpp \
            ../../external/unzip/ioapi_mem.cpp \
            ../../external/unzip/unzip.cpp \
            ../../external/xxhash/xxhash.c \
            ../../external/xxtea/xxtea.cpp

# EDC Sources
USER_EDCS =  

# PO Sources
USER_POS = 

# User Defines
USER_DEFS = TIZEN CC_ENABLE_BULLET_INTEGRATION CC_USE_3D_PHYSICS HAVE_PTHREAD=1 USE_MINICL 
USER_CPP_DEFS = TIZEN CC_ENABLE_BULLET_INTEGRATION CC_USE_3D_PHYSICS HAVE_PTHREAD=1 USE_MINICL 

# User Undefines
USER_UNDEFS = 
USER_CPP_UNDEFS = 

# User Libraries
USER_LIBS = 

# User Objects
USER_OBJS = 

# User Includes
## C Compiler
USER_C_INC_DIRS = ../../ \
                  ../../cocos \
                  ../../cocos/editor-support \
                  ../../cocos/platform \
                  ../../extensions \
                  ../../external \
                  ../../external/Box2D \
                  ../../external/ConvertUTF \
                  ../../external/bullet \
                  ../../external/chipmunk/include/ \
                  ../../external/chipmunk/include/chipmunk \
                  ../../external/edtaa3func \
                  ../../external/jpeg/include/tizen \
                  ../../external/json \
                  ../../external/png/include/tizen \
                  ../../external/tiff/include/tizen \
                  ../../external/tinyxml2 \
                  ../../external/unzip \
                  ../../external/webp/include/tizen \
                  ../../external/websockets/include/tizen \
                  ../../external/xxhash \
                  ../../external/xxtea

USER_INC_FILES = 
## C++ Compiler
USER_CPP_INC_DIRS = ../../ \
                    ../../cocos \
                    ../../cocos/editor-support \
                    ../../cocos/platform \
                    ../../extensions \
                    ../../external \
                    ../../external/Box2D \
                    ../../external/ConvertUTF \
                    ../../external/bullet \
                    ../../external/chipmunk/include \
                    ../../external/chipmunk/include/chipmunk \
                    ../../external/edtaa3func \
                    ../../external/jpeg/include/tizen \
                    ../../external/json \
                    ../../external/png/include/tizen \
                    ../../external/tiff/include/tizen \
                    ../../external/tinyxml2 \
                    ../../external/unzip \
                    ../../external/webp/include/tizen \
                    ../../external/websockets/include/tizen \
                    ../../external/xxhash \
                    ../../external/xxtea

USER_CPP_INC_FILES = 

USER_INC_DIRS = $(USER_C_INC_DIRS) $(USER_CPP_INC_DIRS)

# User Library Path
USER_LIB_DIRS = 

# EDC Resource Path
USER_EDCS_IMAGE_DIRS = ${OUTPUT_DIR} 
USER_EDCS_SOUND_DIRS = ${OUTPUT_DIR} 
USER_EDCS_FONT_DIRS = ${OUTPUT_DIR} 

# EDC Flags
USER_EXT_EDC_KEYS = 

# Resource Filter
USER_RES_INCLUDE = 
USER_RES_EXCLUDE = 
