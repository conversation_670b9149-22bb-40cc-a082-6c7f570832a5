#include "CrashMsgCollector.h"
#include "cocos2d.h"
#include "scripting/lua-bindings/manual/CCLuaEngine.h"
#include <sstream>

#if (CC_TARGET_PLATFORM == CC_PLATFORM_ANDROID)
#include <jni.h>

extern "C" {

JNIEXPORT jstring JNICALL
Java_org_cocos2dx_lua_CustomCrashHandler_getCollectedMsg(JNIEnv *env, jclass clazz)
{
    return env->NewStringUTF(CrashMsgCollector::getInstance()->toString().c_str());
}

JNIEXPORT jstring JNICALL
Java_com_frontier_sdkbase_SDKManager_getCollectedMsg(JNIEnv *env, jclass clazz)
{
    return env->NewStringUTF(CrashMsgCollector::getInstance()->toString().c_str());
}

JNIEXPORT void JNICALL
Java_com_frontier_sdkbase_SDKManager_testNativeCrash(JNIEnv *env, jclass clazz)
{
    CrashMsgCollector::getInstance()->testCrash();
}

std::string jstring2string(JNIEnv *env, jstring jstr)
{
    const char *cstr = env->GetStringUTFChars(jstr, NULL);
    std::string str = std::string(cstr);
    env->ReleaseStringUTFChars(jstr, cstr);
    return str;
}

JNIEXPORT void JNICALL
Java_com_frontier_sdkbase_SDKManager_collectTest(JNIEnv *env, jclass clazz, jstring key, jstring value)
{
    CrashMsgCollector::getInstance()->addMsg(jstring2string(env, key), jstring2string(env, value));
}

JNIEXPORT void JNICALL
Java_com_frontier_sdkbase_SDKManager_clearCollectedMsg(JNIEnv *env, jclass clazz)
{
    CrashMsgCollector::getInstance()->clear();
}
}

#endif

CrashMsgCollector::CrashMsgCollector()
{
    interface_index = 0;
}

CrashMsgCollector *CrashMsgCollector::getInstance()
{
    if(!ins) {
        ins = new CrashMsgCollector();
    }
    return ins;
}

void CrashMsgCollector::addMsg(const std::string key, const char *value)
{
    addMsg(key, std::string(value));
}

void CrashMsgCollector::addMsg(const std::string key, const std::string value)
{
    std::lock_guard<std::mutex> locking(lock);
    if(key == "interface") {
        std::ostringstream ss;
        ss << "interface" << interface_index;
        ++ interface_index;
        std::string new_key = ss.str();
        msgs[new_key] = value;
    } else {
        msgs[key] = value;
    }
}

void CrashMsgCollector::addMsg(const std::string key, const int value)
{
    std::stringstream ss;
    ss << value;
    std::string value_str;
    ss >> value_str;
    addMsg(key, value_str);
}

void CrashMsgCollector::addMsg(const std::string key, const unsigned int value)
{
    std::stringstream ss;
    ss << value;
    std::string value_str;
    ss >> value_str;
    addMsg(key, value_str);
}

void CrashMsgCollector::addMsg(const std::string key, bool value)
{
    addMsg(key, value ? "true" : "false");
}

void CrashMsgCollector::addMsg(const std::string key, const float value)
{
    std::stringstream ss;
    ss << value;
    std::string value_str;
    ss >> value_str;
    addMsg(key, value_str);
}

void CrashMsgCollector::addMsg(const std::string key, const unsigned char *rawData, const int len)
{
    std::ostringstream ss;
    for(int i = 0; i < len; ++ i) {
        if(i % 16 == 0) {
            ss << "\n";
        }
        ss << std::hex << int(rawData[i]) << " ";
    }
    std::string value;
    value = ss.str();
    addMsg(key, value);
}

void CrashMsgCollector::removeMsg(const std::string key)
{
    std::lock_guard<std::mutex> locking(lock);
    msgs.erase(key);
}

std::string CrashMsgCollector::getInterface0()
{
    std::lock_guard<std::mutex> locking(lock);
    auto iter = msgs.find("interface0");
    if(iter == msgs.end()) {
        return "";
    }
    return msgs["interface0"];
}

void CrashMsgCollector::clear()
{
    std::lock_guard<std::mutex> locking(lock);
    interface_index = 0;
    msgs.clear();
}

std::string CrashMsgCollector::toString()
{
    std::lock_guard<std::mutex> locking(lock);
    std::ostringstream ss;
    for(auto iter = msgs.begin(); iter != msgs.end(); ++ iter) {
        ss << iter->first;
        ss << ":";
        ss << iter->second;
        ss << "\n";
    }
    std::string ret;
    ret = ss.str();

    return ret;
}

void CrashMsgCollector::testCrash()
{
    int *p = 0;
    *p = 9527;
}

CrashMsgCollector *CrashMsgCollector::ins = 0;


void CrashMsgCollector::clearAssetManagerMsg()
{
    assetManagerMap.clear();
}

void CrashMsgCollector::pushAssetManagerMsg(std::string key, int value)
{
    assetManagerMap[key] = value;
}

void CrashMsgCollector::printAssetManagerMsg()
{
    std::ostringstream ss;
    for(auto iter = assetManagerMap.begin(); iter != assetManagerMap.end(); ++ iter) {
        ss << iter->first;
        ss << ":";
        ss << iter->second;
        ss << "\n";
    }
    std::string ret;
    ret = ss.str();

    cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineSendLog",ret.c_str(),true);
}

const std::string CrashMsgCollector::ON_FINISH_SUCCESS = "onFinishSuccess";
const std::string CrashMsgCollector::ON_FINISH_SUCCESS_2 = "onFinishSuccess2";
const std::string CrashMsgCollector::NATIVE_INIT = "nativeInit";
const std::string CrashMsgCollector::SPRITE_3D = "sprite3D";
const std::string CrashMsgCollector::HTTP_CLIENT = "httpClient";
const std::string CrashMsgCollector::HTTP_CLIENT_2 = "httpClient2";
const std::string CrashMsgCollector::ASSETS_SUCCESS = "assetsSuccess";
const std::string CrashMsgCollector::ASSETS_SUCCESS_2 = "assetsSuccess2";
const std::string CrashMsgCollector::ASSETS_SUCCESS_3 = "assetsSuccess3";
const std::string CrashMsgCollector::ASSETS_SUCCESS_4 = "assetsSuccess4";
const std::string CrashMsgCollector::ASSETS_SUCCESS_5 = "assetsSuccess5";
const std::string CrashMsgCollector::ASSETS_SUCCESS_6 = "assetsSuccess6";
const std::string CrashMsgCollector::BOLE_SOCKET = "boleSocket";
const std::string CrashMsgCollector::SPRITE_FRAMES = "spriteFrames";
const std::string CrashMsgCollector::SPRITE_FRAMES_RE = "spriteFramesRe";
const std::string CrashMsgCollector::IMAGE_ASYNC = "imageAsync";

// 耗时存储数据的结构体
struct CrashMsgCollector::TimeConsumeStruct
{
public:
    explicit TimeConsumeStruct
    (TimePoint p_startTime)
      : startTime(p_startTime)
    {

    }

    TimePoint startTime;
    std::vector<std::pair<std::string, TimePoint>> steps;
};

// 耗时处理开始函数
void CrashMsgCollector::timeConsumeStart(const std::string& key)
{
    auto iter = timeConsumeMap.find(key);
    if (iter != timeConsumeMap.end())
    {
        delete iter->second;
        iter->second = nullptr;
        timeConsumeMap.erase(iter);
        CCLOG("timeConsumeStart key %s already exist!!!!!", key.c_str());
    }
    auto *tcStruct = new (std::nothrow) TimeConsumeStruct(std::chrono::steady_clock::now());
    timeConsumeMap[key] = tcStruct;
}

// 耗时处理开始后的分步阶段
// 可以在开始后不用，也可以在开始后多次调用
void CrashMsgCollector::timeConsumeStep(const std::string& key, const std::string& step)
{
    auto iter = timeConsumeMap.find(key);
    if (iter != timeConsumeMap.end())
    {
        iter->second->steps.emplace_back(step, std::chrono::steady_clock::now());
    }
    else
    {
        CCLOG("timeConsumeStep key %s not exist!!!!!!", key.c_str());
    }
}

// 耗时处理结束函数
// 与开始函数timeConsumeStart配对使用
void CrashMsgCollector::timeConsumeEnd(const std::string& key)
{
    auto iter = timeConsumeMap.find(key);
    if (iter != timeConsumeMap.end())
    {
        auto end = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - iter->second->startTime).count();
        if (duration >= 300) // 耗时！！！！！！
        {
            std::ostringstream ss_step;
            TimePoint lastTimePoint = iter->second->startTime;
            for(const auto& step : iter->second->steps)
            {
                auto step_duration = std::chrono::duration_cast<std::chrono::milliseconds>(step.second - lastTimePoint).count();
                lastTimePoint = step.second;
                ss_step << step.first << "--" << step_duration << "ms ";
            }
            auto step_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - lastTimePoint).count();
            ss_step << "end--" << step_duration << "ms "; // 如果没有step，表示从开始到结束的时间。有的话表示最后一步到结束的时间
            
            std::ostringstream ss;
            ss << R"({"funcKey":")";
            ss << key;
            ss << "\",";
            ss << R"("duration":")";
            ss << long(duration);
            ss << "\",";
            ss << R"("stepMsg":")";
            ss << ss_step.str();
            ss << "\"}";

            std::string ret = ss.str();
            cocos2d::LuaEngine::getInstance()->executeGlobalFunctionWithString("EngineTimeConsumeSendSplunk",ret.c_str(), true);
        }

        delete iter->second;
        iter->second = nullptr;
        timeConsumeMap.erase(iter);
    }
    else
    {
        CCLOG("timeConsumeEnd key %s not exist!!!!!", key.c_str());
    }
}