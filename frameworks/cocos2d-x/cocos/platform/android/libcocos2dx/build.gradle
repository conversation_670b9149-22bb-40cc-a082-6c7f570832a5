apply plugin: 'com.android.library'

android {
    namespace "org.cocos2dx.lib"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
    }

    sourceSets.main {
        aidl.srcDir "src/main/aidl"
        java.srcDir "../java/src"
        res.srcDir "res"
        manifest.srcFile "AndroidManifest.xml"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        prefab true
    }

    packagingOptions {
        pickFirst '**/libshadowhook.so'
        pickFirst '**/libshadowhook_nothing.so'
    }
}

dependencies {
    implementation fileTree(dir: '../java/libs', include: ['*.jar'])
//    api('com.tencent:mmkv:1.3.7')
    implementation 'com.bytedance.android:shadowhook:1.1.1'
}
