apply plugin: 'com.android.library'

android {
    namespace "com.bole.pluginmopub"
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        useLibrary 'org.apache.http.legacy'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets.main {
        java.srcDir  "src/main/java"
        res.srcDir "src/main/res"
        jniLibs.srcDir "libs"
        manifest.srcFile "src/main/AndroidManifest.xml"
    }

}

repositories {
    mavenCentral()
    maven { url "https://s3.amazonaws.com/moat-sdk-builds" }
    flatDir {
        dirs 'libs'
    }

    // Add the following repositories
    maven { url 'https://adcolony.bintray.com/AdColony' }
    maven { url 'https://dl.bintray.com/ironsource-mobile/android-sdk' }
    maven { url 'https://jitpack.io' }

    maven { url "https://s3.amazonaws.com/moat-sdk-builds" }
    maven { url 'https://maven.google.com' }
    maven { url 'https://chartboostmobile.bintray.com/Chartboost' }
    mavenCentral()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation project(':comonprotocol')

    // implementation "androidx.core:core:1.1.0"
    // implementation "androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"

    // mopub
    api('com.mopub:mopub-sdk:5.16.4@aar') {
        transitive = true
    }

    // For banners
    implementation('com.mopub:mopub-sdk-banner:5.16.4@aar') {
        transitive = true
    }

    // For interstitials and rewarded ads
    implementation('com.mopub:mopub-sdk-fullscreen:5.16.4@aar') {
        transitive = true
    }

    // For native static (images).
    implementation('com.mopub:mopub-sdk-native-static:5.16.4@aar') {
        transitive = true
    }

    implementation 'com.adcolony:sdk:4.4.0'
    implementation 'com.mopub.mediation:adcolony:4.4.0.0'

    implementation 'com.chartboost:chartboost-sdk:9.0.0'
    implementation 'com.mopub.mediation:chartboost:8.2.0.3'

// ironSource
    implementation 'com.ironsource.sdk:mediationsdk:7.1.5'
    implementation 'com.mopub.mediation:ironsource:7.1.5.0'
//
// Facebook Audience Network
    implementation 'com.facebook.android:audience-network-sdk:6.2.0'
    implementation 'com.mopub.mediation:facebookaudiencenetwork:6.2.0.2'


    implementation 'com.unity3d.ads:unity-ads:3.6.0'
    implementation 'com.mopub.mediation:unityads:3.6.0.1'

    // AppLovin
    implementation 'com.applovin:applovin-sdk:10.1.2'
    implementation 'com.mopub.mediation:applovin:10.1.2.0'

// Vungle
    implementation 'com.vungle:publisher-sdk-android:6.9.1'
    implementation 'com.mopub.mediation:vungle:6.9.1.0'
    implementation "androidx.core:core:1.1.0"
    implementation "androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"

    implementation 'com.google.android.gms:play-services-ads:24.4.0'
    implementation 'com.mopub.mediation:admob:19.6.0.1'

    // inmobi
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    implementation 'com.google.android.gms:play-services-location:17.1.0'
    implementation 'androidx.browser:browser:1.3.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation 'com.inmobi.monetization:inmobi-ads:9.1.9'
    implementation 'com.mopub.mediation:inmobi:9.1.9.0'
}
