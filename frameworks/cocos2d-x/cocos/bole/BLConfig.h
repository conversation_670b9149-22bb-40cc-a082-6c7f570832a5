//
//  BLConfig.h
//  cocos2d_libs
//
//  Created by wodes on 14-9-24.
//
//

#ifndef cocos2d_libs_BLConfig_h
#define cocos2d_libs_BLConfig_h

#include "cocos2d.h"
//using namespace std;
using namespace cocos2d;
NS_CC_BEGIN
//using namespace std;
class CC_DLL BLConfig :public Ref{
    ValueMap _map;
    static BLConfig* instance;
    void setConfigMap(ValueMap m);
    void init();
public:
    static BLConfig* getInstance();
    ValueMap getConfig();
    std::string getConfig(std::string key);
    
};
NS_CC_END
#endif
