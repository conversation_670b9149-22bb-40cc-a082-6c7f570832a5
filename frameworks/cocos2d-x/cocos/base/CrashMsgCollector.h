#ifndef CRASH_MSG_COLLECTOR
#define CRASH_MSG_COLLECTOR

#include <map>
#include <string>
#include <mutex>

class CrashMsgCollector {
public:
	static CrashMsgCollector *getInstance();
	void init() {}
	void release() {
		if(ins) {
			delete ins;
			ins = 0;
		}
	}
	void addMsg(const std::string key, const char *value);
	void addMsg(const std::string key, const std::string value);
	void addMsg(const std::string key, const int value);
	void addMsg(const std::string key, const unsigned int value);
	void addMsg(const std::string key, const float value);
	void addMsg(const std::string key, bool value);
	void removeMsg(const std::string key);
	void addMsg(const std::string key, const unsigned char *rawData, const int len);
	std::string getInterface0();
	void clear();
	std::string toString();
	void testCrash();

private:
	CrashMsgCollector();

private:
	static CrashMsgCollector *ins;
	std::map<std::string, std::string> msgs;
	std::mutex lock;
	int interface_index;
};

#endif // CRASH_MSG_COLLECTOR